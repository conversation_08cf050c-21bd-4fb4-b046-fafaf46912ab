# PLATO
The steps below will guide you on how to setup the repository on your local machine

## Prerequisites
Check and confirm that you have the following tools installed
- PHP
- Composer
- Node & npm
- PostgreSQL
- Redis

## Installation
1. Clone the repository
```
git clone https://github.com/SMSONE/plato.git
```

2. Navigate to the project repository
```
cd plato
```

3. Install dependencies in the composer.json file

```
composer install
```

In case you get an error, run the command below to skip the system's platform requirements check.

```
composer install --ignore-platform-reqs
```

4. Install passport keys
```
php artisan passport:keys
```

5. Install packages in the package.json file
```
npm install
```

6. Create a .env file using the .env_example. Update the database configurations in the .env file i.e. database name, username and password to match your local settings
```
DB_DATABASE=yourdbname
DB_USERNAME=yourusername
DB_PASSWORD=yourpasssword
```

7. Update .env with configurations for caching using Redis.
Set
```
CACHE_DRIVER=redis
```
Set the default queue connection to Redis
```
QUEUE_CONNECTION=redis
```

8. Run the command below to set the APP_KEY value in the .env file

```
php artisan key:generate
```

9. Run the command below to start the php server
```
php artisan serve
```

10. Start the node server by running the command below
```
npm run dev
```

11. Run queues
```
php artisan queue:work redis --queue=emails,exports,nira,default
```

## Setup on Docker using Sail
1. Update .env file using .env.sail.example

2. Configure shell alias to execute Sail commands. Add the shell configuration in your home directory i.e. ```~/.zshrc``` or ```~/.bashrc``` to make sure it's always available
```
alias sail='sh $([ -f sail ] && echo sail || echo vendor/bin/sail)'
```

3. Restart the shell to apply changes. Replace with your shell environment i.e. ```~/.zshrc``` or ```~/.bashrc```
```
source ~/.zshrc
```

4. Build the Docker image using Sail
```
sail build
```

5. Start the Docker image
```
sail up
```

6. Copy the DB dump to the Postgres image running in Docker
```
docker exec -i <container_name> psql -U <username> -d <database_name> < </path/to/dbdump.sql>
```

7. Install node packages
```
sail npm install
```

8. Run dev
```
sail npm run dev
```

9. Run the Redis queues
```
sail artisan queue:work redis --queue=emails,exports,nira,default
```