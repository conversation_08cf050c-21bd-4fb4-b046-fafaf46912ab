<?php

namespace Tests\Feature\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Modules\Admin\Livewire\AdminLearnerPreRegistrationApplicationsIndex;
use Modules\Admin\Livewire\AdminLearnerPreRegistrationApplicationsShow;
use Modules\Core\Models\User;
use Tests\TestCase;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class AdminLearnerEnrolmentApplicationsApiControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        // Create a country and use its ID for country_id and parent_country_id
        $country = \DB::table('admin_unit_countries')->insertGetId([
            'name' => 'TestCountry',
            'code' => 'TC',
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create a school type and use its ID for school_type_id
        $schoolType = \DB::table('setting_school_types')->insertGetId([
            'name' => 'Test School Type',
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create a survey and use its ID for survey_id
        $survey = \DB::table('setting_surveys')->insertGetId([
            'name' => 'Test Survey',
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create an academic year and use its ID for academic_year_id
        $academicYear = \DB::table('setting_academic_years')->insertGetId([
            'name' => '2025 Academic Year',
            'start_date' => now()->startOfYear(),
            'end_date' => now()->endOfYear(),
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create a teaching period and use its ID for teaching_period_id
        $teachingPeriod = \DB::table('setting_teaching_periods')->insertGetId([
            'name' => 'Test Teaching Period',
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create a school and use its ID for school_id
        $school = \DB::table('schools')->insertGetId([
            'name' => 'Test School',
            'emis_number' => 'TST001',
            'school_type_id' => $schoolType,
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create an education grade and use its ID for education_grade_id
        $educationGrade = \DB::table('setting_education_grades')->insertGetId([
            'name' => 'Test Grade',
            'grade_rank' => 1,
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        // Create required identity document types in the correct table
        foreach ([
            'NATIONAL ID', 'PASSPORT', 'STUDENT PASS', 'REFUGEE NUMBER'
        ] as $type) {
            \DB::table('setting_identity_document_types')->insert([
                'name' => $type,
                'date_created' => now(),
                'date_updated' => now(),
            ]);
        }
        // Create a dummy survey section and section item for the survey
        $sectionId = \DB::table('setting_survey_sections')->insertGetId([
            'name' => 'General',
            'date_created' => now(),
            'date_updated' => now(),
        ]);
        $sectionItemId = \DB::table('setting_survey_section_items')->insertGetId([
            'section_id' => $sectionId,
            'name' => 'General Item',
            'date_created' => now(),
            'date_updated' => now(),
        ]);

        // Create a user and authenticate
        $admin = User::factory()->create();
        $this->actingAs($admin);
        // Store IDs for use in tests
        $this->countryId = $country;
        $this->schoolId = $school;
        $this->surveyId = $survey;
        $this->academicYearId = $academicYear;
        $this->teachingPeriodId = $teachingPeriod;
        $this->educationGradeId = $educationGrade;
        $this->withoutMiddleware();
    }

     public function test_index_returns_paginated_applications()
     {
         LearnerEnrolmentApplication::factory()->count(3)->state([
             'country_id' => $this->countryId,
             'parent_country_id' => $this->countryId,
             'school_id' => $this->schoolId,
             'survey_id' => $this->surveyId,
             'academic_year_id' => $this->academicYearId,
             'teaching_period_id' => $this->teachingPeriodId,
             'education_grade_id' => $this->educationGradeId,
         ])->create();

         $component = Livewire::test(AdminLearnerPreRegistrationApplicationsIndex::class, ['type' => 'pending']);
         $applications = $component->get('applications');
         $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $applications);
         $this->assertCount(3, $applications->items());
     }

    public function test_applications_filtering()
    {
        // Create applications with different attributes
        $app1 = LearnerEnrolmentApplication::factory()->pending()->state([
            'first_name' => 'John',
            'surname' => 'Doe',
            'application_number' => 'APP001',
            'country_id' => $this->countryId,
            'parent_country_id' => $this->countryId,
            'school_id' => $this->schoolId,
            'survey_id' => $this->surveyId,
            'academic_year_id' => $this->academicYearId,
            'teaching_period_id' => $this->teachingPeriodId,
            'education_grade_id' => $this->educationGradeId,
        ])->create();
        $app2 = LearnerEnrolmentApplication::factory()->pending()->state([
            'first_name' => 'Jane',
            'surname' => 'Smith',
            'application_number' => 'APP002',
            'country_id' => $this->countryId,
            'parent_country_id' => $this->countryId,
            'school_id' => $this->schoolId,
            'survey_id' => $this->surveyId,
            'academic_year_id' => $this->academicYearId,
            'teaching_period_id' => $this->teachingPeriodId,
            'education_grade_id' => $this->educationGradeId,
        ])->create();

        // Filter by application_number
        $component = \Livewire\Livewire::test(\Modules\Admin\Livewire\AdminLearnerPreRegistrationApplicationsIndex::class, ['type' => 'pending']);
        $component->set('filter.application_number', 'APP001');
        $applications = $component->get('applications');
        $this->assertCount(1, $applications->items());
        $this->assertEquals('APP001', $applications->items()[0]->application_number);

        // Clear application_number filter before next filter
        $component->set('filter.application_number', '');

        // Filter by search_term (first_name)
        $component->set('filter.search_term', 'Jane');
        $applications = $component->get('applications');
        $this->assertCount(1, $applications->items());
        $this->assertEquals('Jane', $applications->items()[0]->first_name);

        // Clear search_term filter before next filter
        $component->set('filter.search_term', '');

        // Filter by search_term (surname)
        $component->set('filter.search_term', 'Doe');
        $applications = $component->get('applications');
        $this->assertCount(1, $applications->items());
        $this->assertEquals('Doe', $applications->items()[0]->surname);
    }

    public function test_show_single_application()
    {
        $application = LearnerEnrolmentApplication::factory()->pending()->state([
            'first_name' => 'Alice',
            'surname' => 'Wonderland',
            'application_number' => 'APP100',
            'country_id' => $this->countryId,
            'parent_country_id' => $this->countryId,
            'school_id' => $this->schoolId,
            'survey_id' => $this->surveyId,
            'academic_year_id' => $this->academicYearId,
            'teaching_period_id' => $this->teachingPeriodId,
            'education_grade_id' => $this->educationGradeId,
        ])->create();

        $component = Livewire::test(AdminLearnerPreRegistrationApplicationsShow::class, [
            'status' => 'pending',
            'applicationNumber' => $application->application_number
        ]);
        $appData = $component->get('application');
        $this->assertEquals('APP100', $appData['application_number']);
        $this->assertEquals('Alice', $appData['first_name']);
        $this->assertEquals('Wonderland', $appData['surname']);
    }

     public function test_reject_application_successfully()
     {
         $application = LearnerEnrolmentApplication::factory()->pending()->state([
             'country_id' => $this->countryId,
             'parent_country_id' => $this->countryId,
             'school_id' => $this->schoolId,
             'survey_id' => $this->surveyId,
             'academic_year_id' => $this->academicYearId,
             'teaching_period_id' => $this->teachingPeriodId,
             'education_grade_id' => $this->educationGradeId,
         ])->create();

         $component = Livewire::test(
             AdminLearnerPreRegistrationApplicationsShow::class,
             ['status' => 'pending', 'applicationNumber' => $application->application_number]
         );
         $component->set('reason', 'Incomplete documents')
             ->call('commitRejectReason');
         $application->refresh();
         $this->assertEquals('rejected', $application->approval_status);
         $this->assertEquals('Incomplete documents', $application->reject_reason);
     }

     public function test_reject_application_requires_reason()
     {
         $application = LearnerEnrolmentApplication::factory()->pending()->state([
             'country_id' => $this->countryId,
             'parent_country_id' => $this->countryId,
             'school_id' => $this->schoolId,
             'survey_id' => $this->surveyId,
             'academic_year_id' => $this->academicYearId,
             'teaching_period_id' => $this->teachingPeriodId,
             'education_grade_id' => $this->educationGradeId,
         ])->create();

         $component = Livewire::test(
             AdminLearnerPreRegistrationApplicationsShow::class,
             ['status' => 'pending', 'applicationNumber' => $application->application_number]
         );
         $component->set('reason', '')
             ->call('commitRejectReason')
             ->assertHasErrors(['reason' => 'required']);
         $application->refresh();
         $this->assertNotEquals('rejected', $application->approval_status);
     }
}
