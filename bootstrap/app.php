<?php

use App\Providers\AppServiceProvider;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders()
    ->withRouting(
        //web: __DIR__.'/../routes/web.php',
        //api: __DIR__.'/../routes/api.php',
        // service: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        // channels: __DIR__.'/../routes/channels.php',
        health: '/up',
//        then: function () {
//            Route::middleware(['web','track_online_users'])
//                ->prefix(config('emis.service_endpoint'))
//                ->name('services.')
//                ->group(base_path('routes/services.php'));
//        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(fn () => route('institution-login'));
        $middleware->redirectUsersTo(AppServiceProvider::HOME);

        $middleware->validateCsrfTokens(except: [
            //
        ]);

        $middleware->web([
            // \Laravel\Passport\Http\Middleware\CreateFreshApiToken::class,
           \App\Http\Middleware\InactivityTimeout::class,
            \App\Http\Middleware\TrackOnlineUser::class,
            \App\Http\Middleware\StoreSelectedYear::class,
        ]);

        $middleware->throttleApi('1000');
        $middleware->api([
            // \App\Http\Middleware\InactivityTimeout::class,
            \App\Http\Middleware\TrackOnlineUser::class
        ]);

        $middleware->alias([
            'academicYear' => \App\Http\Middleware\AcademicYear::class,
            'admin' => \App\Http\Middleware\Admin::class,
            'adminTwoFactor' => \App\Http\Middleware\AdminTwoFactor::class,
            'cluster' => \App\Http\Middleware\Cluster::class,
            'developer' => \App\Http\Middleware\Developer::class,
            'lowerLocalGovernment' => \App\Http\Middleware\LowerLocalGovernment::class,
            'pendingSurveys' => \App\Http\Middleware\PendingSurveys::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'school' => \App\Http\Middleware\SchoolAccount::class,
            'schoolTwoFactor' => \App\Http\Middleware\SchoolTwoFactor::class,
            'upperLocalGovernment' => \App\Http\Middleware\UpperLocalGovernmentMiddleware::class,
            'track_online_users' => \App\Http\Middleware\TrackOnlineUser::class,
            'nira.auth' => \App\Http\Middleware\NiraProxyAuth::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->reportable(function (Throwable $e) {
            //
        });
        $exceptions->reportable(function (QueryException $e) {
            //
            // if ($e->errorInfo[0] == '08006') {
            //     abort(500, 'This academic year has been archived.'); //TODO improve error handling for archived year
            //     // exit;
            //     // exit;
            //     // exit('feef');
            // }
            // exit;
        });

        $exceptions->renderable(function (\Illuminate\Session\TokenMismatchException $e, $request) {
            //dd($request);
            logger('token mismatch');
            return redirect()->route('institution-login');
        });
    })->create();
