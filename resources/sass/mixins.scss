@use "sass:color";
@mixin button-variant($color, $background, $border) {
    color: $color;
    background-color: $background;
    border-color: $border;

    &:focus,
    &.focus {
        color: $color;
        background-color: color.adjust($background, $lightness: -10%);
        border-color: color.adjust($border, $lightness: -25%);
    }
    &:hover {
        color: $color;
        background-color: color.adjust($background, $lightness: -10%);
        border-color: color.adjust($border, $lightness: -12%);
    }
    &:active,
    &.active,
    .open > &.dropdown-toggle {
        color: $color;
        background-color: color.adjust($background, $lightness: -10%);
        background-image: none;
        border-color: color.adjust($border, $lightness: -12%);

        &:hover,
        &:focus,
        &.focus {
            color: $color;
            background-color: color.adjust($background, $lightness: -17%);
            border-color: color.adjust($border, $lightness: -25%);
        }
    }
    &.disabled,
    &[disabled],
    fieldset[disabled] & {
        &:hover,
        &:focus,
        &.focus {
            background-color: $background;
            border-color: $border;
        }
    }

    .badge {
        color: $background;
        background-color: $color;
    }
}
