@use "dark-teal";

@use "../mixins.scss" as purple;
$purple: #800080;
dark-teal.$white: #FFFFFF;

.scrollbar-purple .simplebar-scrollbar::before {
    background-color: $purple !important;
}

.alert-purple { color: $purple; background-color: #e9f9fc; border-color: $purple; }

.alert-pro.alert-purple { border-color: $purple; }

.alert-pro.alert-purple > .icon { color: $purple; }

.bg-purple {
    background-color: $purple !important;
    color: dark-teal.$white !important;
}

.bg-purple:after {
    color: dark-teal.$white !important;
}

.btn-purple {
    @include purple.button-variant(dark-teal.$white, $purple, $purple)
}

.text-purple {
    color: $purple !important;
}

.border-purple {
    border-color: $purple !important;
}

.badge-purple {
    color: dark-teal.$white !important;
    border-color: $purple !important;
    background-color: $purple !important;
}

.badge-outline-purple {
    color: $purple !important;
    border-color: $purple !important;
}

.badge-outline-purple:hover {
    background-color: $purple !important;
    border-color: $purple !important;
    color: dark-teal.$white !important;
}

.btn-outline-purple {
    border-color: $purple !important;
    color: $purple !important;
}

.btn-outline-purple:hover {
    background-color: $purple !important;
    border-color: $purple !important;
    color: dark-teal.$white !important;
}

.page-item.active-purple .page-link,
.custom-control-input-purple:checked ~ .custom-control-label-purple::before,
.custom-control-input-purple:not(:disabled):active ~ .custom-control-label-purple::before {
    background-color: $purple !important;
    border-color: $purple !important;
}

