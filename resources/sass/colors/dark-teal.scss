@use "../mixins.scss" as dark;
$dark-teal: #00879B;
$white: #FFFFFF;

.scrollbar-dark-teal .simplebar-scrollbar::before {
  background-color: $dark-teal !important;
}

.alert-dark-teal { color: $dark-teal; background-color: #e9f9fc; border-color: $dark-teal; }

.alert-pro.alert-dark-teal { border-color: $dark-teal; }

.alert-pro.alert-dark-teal > .icon { color: $dark-teal; }

.bg-dark-teal {
	background-color: $dark-teal !important;
	color: $white !important;
}

.bg-dark-teal:after {
	color: $white !important;
}

.btn-dark-teal {
    @include dark.button-variant($white, $dark-teal, $dark-teal)
}

.text-dark-teal {
	color: $dark-teal !important;
}

.border-dark-teal {
	border-color: $dark-teal !important;
}

.badge-dark-teal {
	color: $white !important;
	border-color: $dark-teal !important;
	background-color: $dark-teal !important;
}

.badge-outline-dark-teal {
    color: $dark-teal !important;
    border-color: $dark-teal !important;
}

.badge-outline-dark-teal:hover {
    background-color: $dark-teal !important;
    border-color: $dark-teal !important;
    color: $white !important;
}

.btn-outline-dark-teal {
	border-color: $dark-teal !important;
	color: $dark-teal !important;
}

.btn-outline-dark-teal:hover {
	background-color: $dark-teal !important;
	border-color: $dark-teal !important;
	color: $white !important;
}

.page-item.active-dark-teal .page-link,
.custom-control-input-dark-teal:checked ~ .custom-control-label-dark-teal::before,
.custom-control-input-dark-teal:not(:disabled):active ~ .custom-control-label-dark-teal::before {
	background-color: $dark-teal !important;
	border-color: $dark-teal !important;
}

