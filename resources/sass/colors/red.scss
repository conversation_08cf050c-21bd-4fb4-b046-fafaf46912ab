@use "dark-teal";

@use "../mixins.scss" as red;
$red: #D00000;
dark-teal.$white: #FFFFFF;

.scrollbar-red .simplebar-scrollbar::before {
    background-color: $red !important;
}

.alert-red { color: $red; background-color: #e9f9fc; border-color: $red; }

.alert-pro.alert-red { border-color: $red; }

.alert-pro.alert-red > .icon { color: $red; }

.bg-red {
    background-color: $red !important;
    color: dark-teal.$white !important;
}

.bg-red:after {
    color: dark-teal.$white !important;
}

.btn-red {
    @include red.button-variant(dark-teal.$white, $red, $red)
}

.text-red {
    color: $red !important;
}

.border-red {
    border-color: $red !important;
}

.badge-red {
    color: dark-teal.$white !important;
    border-color: $red !important;
    background-color: $red !important;
}

.badge-outline-red {
    color: $red !important;
    border-color: $red !important;
}

.badge-outline-red:hover {
    background-color: $red !important;
    border-color: $red !important;
    color: dark-teal.$white !important;
}

.btn-outline-red {
    border-color: $red !important;
    color: $red !important;
}

.btn-outline-red:hover {
    background-color: $red !important;
    border-color: $red !important;
    color: dark-teal.$white !important;
}

.page-item.active-red .page-link,
.custom-control-input-red:checked ~ .custom-control-label-red::before,
.custom-control-input-red:not(:disabled):active ~ .custom-control-label-red::before {
    background-color: $red !important;
    border-color: $red !important;
}
