@use "dark-teal";

@use "../mixins.scss" as amaranth;
$amaranth: #9F2B68;
dark-teal.$white: #FFFFFF;

.scrollbar-amaranth .simplebar-scrollbar::before {
  background-color: $amaranth !important;
}

.alert-amaranth { color: $amaranth; background-color: #e9f9fc; border-color: $amaranth; }

.alert-pro.alert-amaranth { border-color: $amaranth; }

.alert-pro.alert-amaranth > .icon { color: $amaranth; }

.bg-amaranth {
	background-color: $amaranth !important;
	color: dark-teal.$white !important;
}

.bg-amaranth:after {
	color: dark-teal.$white !important;
}

.btn-amaranth {
    @include amaranth.button-variant(dark-teal.$white, $amaranth, $amaranth)
}

.text-amaranth {
	color: $amaranth !important;
}

.border-amaranth {
	border-color: $amaranth !important;
}

.badge-amaranth {
	color: dark-teal.$white !important;
	border-color: $amaranth !important;
	background-color: $amaranth !important;
}

.badge-outline-amaranth {
    color: $amaranth !important;
    border-color: $amaranth !important;
}

.badge-outline-amaranth:hover {
    background-color: $amaranth !important;
    border-color: $amaranth !important;
    color: dark-teal.$white !important;
}

.btn-outline-amaranth {
	border-color: $amaranth !important;
	color: $amaranth !important;
}

.btn-outline-amaranth:hover {
	background-color: $amaranth !important;
	border-color: $amaranth !important;
	color: dark-teal.$white !important;
}

.page-item.active-amaranth .page-link,
.custom-control-input-amaranth:checked ~ .custom-control-label-amaranth::before,
.custom-control-input-amaranth:not(:disabled):active ~ .custom-control-label-amaranth::before {
	background-color: $amaranth !important;
	border-color: $amaranth !important;
}

