@use "dark-teal";

@use "../mixins.scss" as flirt;
$flirt: #A4036F;
dark-teal.$white: #FFFFFF;

.scrollbar-flirt .simplebar-scrollbar::before {
    background-color: $flirt !important;
}

.alert-flirt { color: $flirt; background-color: #e9f9fc; border-color: $flirt; }

.alert-pro.alert-flirt { border-color: $flirt; }

.alert-pro.alert-flirt > .icon { color: $flirt; }

.bg-flirt {
    background-color: $flirt !important;
    color: dark-teal.$white !important;
}

.bg-flirt:after {
    color: dark-teal.$white !important;
}

.btn-flirt {
    @include flirt.button-variant(dark-teal.$white, $flirt, $flirt)
}

.text-flirt {
    color: $flirt !important;
}

.border-flirt {
    border-color: $flirt !important;
}

.badge-flirt {
    color: dark-teal.$white !important;
    border-color: $flirt !important;
    background-color: $flirt !important;
}

.badge-outline-flirt {
    color: $flirt !important;
    border-color: $flirt !important;
}

.badge-outline-flirt:hover {
    background-color: $flirt !important;
    border-color: $flirt !important;
    color: dark-teal.$white !important;
}

.btn-outline-flirt {
    border-color: $flirt !important;
    color: $flirt !important;
}

.btn-outline-flirt:hover {
    background-color: $flirt !important;
    border-color: $flirt !important;
    color: dark-teal.$white !important;
}

.page-item.active-flirt .page-link,
.custom-control-input-flirt:checked ~ .custom-control-label-flirt::before,
.custom-control-input-flirt:not(:disabled):active ~ .custom-control-label-flirt::before {
    background-color: $flirt !important;
    border-color: $flirt !important;
}
