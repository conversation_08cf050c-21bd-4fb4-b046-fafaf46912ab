@use "sass:meta";
@use "mixins";
@use "colors/dark-teal.scss";
@use "colors/flirt.scss";
@use "colors/yellow.scss";
@use "colors/red.scss";
@use "colors/amaranth.scss";
@use "colors/purple.scss";
// @import "~intl-tel-input/build/css/intlTelInput.css";
@import 'intl-tel-input/build/css/intlTelInput.css';


// @tailwind base;

// @tailwind components;

// @tailwind utilities;


.stats {
    font-size: 40px;
}

.text-disabled {
    color: rgba(82, 100, 132, 0.5) !important;
}

.iti {
	width: 100% !important;
}

.cursor {
	cursor: pointer !important;
}

.border-1 {
    border-width:1px !important;
}

.swal2-styled.swal2-confirm {
	background-color: #00879B !important;
}

.bg-orange-test {
	background-color: #ff8a65;
	color: white;
}

.bg-purple-test {
	background-color: #9575cd;
	color: white;
}

.bg-purple-dark {
	background-color: #7D3C98;
	color: white;
}

.bg-pink-test {
	background-color: #f06292;
	color: white;
}

.text-pink-test {
	color: #f06292;
}

.bg-teal-test {
	background-color: #4db6ac;
	color: white;
}

.bg-blue-green-test {
	background-color: #009688;
	color: white;
}

.bg-blue-gray-test {
	background-color: #607d8b;
	color: white;
}

.bg-yellow-test {
	background-color: #fbc02d;
	color: white;
}

.bg-teal-bright-test {
	background-color: #00bcd4;
	color: white;
}

.bg-sidebar-test {
	background-color: #777777;
	color: white;
}

.dashboard-icon {
	font-size: 40px;
}


@include meta.load-css("survey-progress");

// @import "colors/dark-teal.scss";
// @import "colors/flirt.scss";
// @import "colors/yellow.scss";
// @import "colors/red.scss";
// @import "colors/amaranth.scss";
// @import "colors/purple.scss";

// @import "~animate.css/animate.min.css";
@import "animate.css/animate.min.css";

::selection {
    color: dark-teal.$white;
    background: #2263b3;
}

.emis-label {
    color: dark-teal.$dark-teal;
    letter-spacing: 1px !important;
    font-size: 10px !important;
    line-height: 1rem !important;
    top: calc(-0.625rem + 1.5px) !important;
}

.dual-listbox {
	display: flex;
	flex-direction: column;
}

.dual-listbox .dual-listbox__item:active, .dual-listbox .dual-listbox__item.dual-listbox__item--selected {
	background-color: #00879B !important;
	color: white;
}

.dual-listbox .dual-listbox__container {
	display: flex;
	align-items: center;
	flex-direction: row;
	flex-wrap: wrap;
}

.dual-listbox .dual-listbox__search {
	max-width: 100%;
}

.dual-listbox .dual-listbox__search--hidden {
	display: none;
}

.dual-listbox .dual-listbox__available, .dual-listbox .dual-listbox__selected {
	border: 1px solid #e5e9f2;
	height: 300px;
	overflow-y: auto;
	padding: 0;
	width: 280px !important;
	margin-top: 0;
	border-radius: 0 0 4px 4px;
}

.dual-listbox .dual-listbox__buttons {
	display: flex;
	flex-direction: column;
	margin: 0 10px;
}

.dual-listbox .dual-listbox__button {
	margin-bottom: 5px;
	justify-content: center;
	text-transform: capitalize;
}

.dual-listbox .dual-listbox__title {
	font-size: 1rem;
	font-weight: 500;
	padding: .5rem 1rem;
	border-left: 1px solid #e5e9f2;
	border-right: 1px solid #e5e9f2;
	border-top: 1px solid #e5e9f2;
	margin-top: 1rem;
	-webkit-margin-before: 1rem;
	border-radius: 4px 4px 0 0;
}

.dual-listbox .dual-listbox__item {
	display: block;
	padding: .5rem 1rem;
	cursor: pointer;
	user-select: none;
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	border-bottom: 1px solid #e5e9f2;
	transition: background-color 0.2s ease;
}

.login-bg {
    background-size: 60%;
    background: url(@images/login-bg.png) no-repeat fixed 100% 50%;
}

@media screen and (max-width: 992px) {
	.login-bg {
		background-position: center;
		background-size: 200%;
		background-repeat: repeat-y;
	}
}
.dash3 {

    h6{
        color: #212229;
        font-size: 30px;
    }

    // @media screen and (min-width: 992px) {
    //     h6{
    //         font-size: 26px;
    //         margin-left: 8px;
    //     }
    //     span{
    //         margin-left:8px;
    //     }
    // }
}
@media screen and (min-width: 990px) and (max-width: 1200px) {
    .dash3{
        padding-right: 0px!important;
        padding-left: 15px!important;
        flex:0 0 80.33333%!important;
        max-width:80.33333%!important;

        h6{
            font-size: 25px;
            margin-left: 8px;
        }
        span{
            margin-left:8px;
        }
    }
}
@media screen and (min-width: 1200px) and (max-width: 1460px) {
    .dash3{
        padding-right: 0px!important;
        padding-left: 26px!important;
        flex:0 0 80.33333%!important;
        max-width:80.33333%!important;
        h6{
            font-size: 22px;
        }
    }
}


#schoolInitModal  button.close{
    top: 16px!important;
    right: 8px!important;
}
