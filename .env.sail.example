APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_TIMEZONE=UTC
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

PASSPORT_PERSONAL_ACCESS_CLIENT_ID=
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=pgsql
DB_PORT=5432
DB_DATABASE=plato
DB_USERNAME=sail
DB_PASSWORD=password

BROADCAST_CONNECTION=log
CACHE_STORE=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MEMCACHED_HOST=127.0.0.1

GOOGLE_MAPS_API="AIzaSyDLa_Ldn4Uvc1NcakpVxW0xRQfco5h-jj4"

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

LICENSE_DURATION=2
LICENSE_DURATION_UNITS=years
LICENSE_REMINDER_DURATION=6
LICENSE_REMINDER_DURATION_UNITS=months

REGISTRATION_DURATION=1
REGISTRATION_DURATION_UNITS=years
REGISTRATION_REMINDER_DURATION=3
REGISTRATION_REMINDER_DURATION_UNITS=months

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

NIRA_USERNAME="MinistryofEducation@ROOT"
NIRA_PASSWORD="Emis@sms1"
NIRA_SERVER="**************:8080"
NIRA_SERVER_PATH="pilatusp2-tpi2-ws/ThirdPartyInterfaceNewWS"
NIRA_NAMESPACE="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/"
NIRA_USE_PROXY=true
NIRA_PROXY="https://dev.demis.emis.go.ug/services/v0.0.1/test-nira"

WWWGROUP=1000
WWWUSER=1000

SERVICE_ENDPOINT="/services/v0.0.1"