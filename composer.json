{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-json": "*", "ext-simplexml": "*", "ext-zip": "*", "afromansr/laravel-nira-api": "^1.0", "barryvdh/laravel-dompdf": "*", "devmarketer/easynav": "^1.0", "doctrine/dbal": "^3.5", "filament/filament": "^3.3", "filament/tables": "^3.3", "halaxa/json-machine": "^0.3.3", "intervention/image": "^2.5", "jmikola/geojson": "^1.0", "kirschbaum-development/eloquent-power-joins": "*", "kyslik/column-sortable": "*", "laravel/framework": "^12.0", "laravel/horizon": "^5.22", "laravel/legacy-factories": "^1.4", "laravel/scout": "^10.11", "laravel/slack-notification-channel": "^3.2", "laravel/tinker": "^2.9", "laravel/ui": "^4.4", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^3.6", "livewire/volt": "^1.7", "maatwebsite/excel": "^3.1", "mhmiton/laravel-modules-livewire": "^5.1", "nesbot/carbon": "^3.0", "nwidart/laravel-modules": "^12.0", "opcodesio/log-viewer": "^3.4", "phpoffice/phpspreadsheet": "^1.17", "phpoffice/phpword": "^0.18.2", "poing/earmark": "^0.1.8", "rap2hpoutre/fast-excel": "^5.4", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-permission": "^6.2", "tecnickcom/tcpdf": "^6.7"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.0", "brianium/paratest": "*", "driftingly/rector-laravel": "^2.0", "fakerphp/faker": "^1.23", "imanghafoori/laravel-microscope": "^1.0", "laravel/sail": "^1.31", "mockery/mockery": "^1.6", "nunomaduro/collision": "*", "nunomaduro/larastan": "^3.6", "peckphp/peck": "^0.1.3", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"ext-pcntl": "8.0", "ext-posix": "8.0"}, "allow-plugins": {"php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}