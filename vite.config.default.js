import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import vue from "@vitejs/plugin-vue2";
import tailwindcss from "@tailwindcss/vite";
import collectModuleAssetsPaths from "./vite-module-loader.js";

// Now only including module paths, no root resources
const paths = [];
const allPaths = await collectModuleAssetsPaths(paths, "Modules");
// console.log("All paths:", allPaths);
export default defineConfig({
    build: {
        rollupOptions: {
            external: [
                "/images/default_female.jpg",
                "/images/default_male.jpg",
            ],
        },
    },
    plugins: [
        laravel({
            input: allPaths,
            // postcss,
            refresh: true,
        }),
        vue(),
        tailwindcss(),
        // vue({
        //     template: {
        //         transformAssetUrls: {
        //             base: null,
        //             includeAbsolute: false,
        //         },
        //     },
        // }),
    ],
    resolve: {
        alias: {
            vue: "vue/dist/vue.esm.js", // alias for the compiler-included build
            "~": "/Modules/Core/resources/assets/js",
            "@images": "/public/images",
        },
    },
});
