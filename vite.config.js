import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import tailwindcssV3 from "tailwindcss";
// import tailwindcssV4 from "tailwindcss/lib/index.js";
// import tailwindcssV4 from "tailwindcss-v4";
import tailwindcssV4 from "@tailwindcss/postcss";
import tailwind3Config from "./tailwind.v3.config.js";
import tailwind4Config from "./tailwind.v4.config.js";
import vue from "@vitejs/plugin-vue2";
import collectModuleAssetsPaths from "./vite-module-loader.js";

const paths = [
    "resources/css/app.css",
    "resources/sass/app.scss",
    "resources/css/filament.css",
    "resources/css/tailwind4.css",
    "resources/js/app.js",
    "resources/js/filament.js",
];
const allPaths = await collectModuleAssetsPaths(paths, "Modules");

export default defineConfig({
    build: {
        rollupOptions: {
            external: [
                "/images/default_female.jpg",
                "/images/default_male.jpg",
            ],
        },
    },
    server: {
	    cors:true
    },
    plugins: [
        laravel({
            input: allPaths,
            refresh: true,
        }),
        vue(),
        // tailwindcss(),
    ],
    css: {
        postcss: {
            plugins: [
                tailwindcssV3(tailwind3Config), // default Tailwind v3
                tailwindcssV4(tailwind4Config), // prefixed Tailwind v4
                // require("autoprefixer"),
            ],
        },
    },
    resolve: {
        alias: {
            vue: "vue/dist/vue.esm.js", // alias for the compiler-included build
            "~": "/resources/js",
            "@images": "/public/images",
        },
    },
});
