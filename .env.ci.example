APP_DEBUG=true
APP_KEY=
# DB_CONNECTION=pgsql
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=plato_test
DB_USERNAME=postgres
DB_PASSWORD=password4testdb

LOGGING_DB_HOST=localhost
LOGGING_DB_PORT=5432
LOGGING_DB_DATABASE=plato_test
LOGGING_DB_USERNAME=postgres
LOGGING_DB_PASSWORD=password4testdb

OTP_ENABLED=true
OTP_EXPIRATION_TIME=10
OTP_NOTIFY_ERROR=true


DB_CONNECTION=testing
# DB_DATABASE=storage/database.sqlite

SESSION_DRIVER=redis
CACHE_DRIVER=redis

REDIS_HOST=localhost
REDIS_PASSWORD=null
REDIS_PORT=6379

REDIS_DB=5
REDIS_CACHE_DB=6
REDIS_SESSION_DB=7
REDIS_USER_TRACKING_DB=9
