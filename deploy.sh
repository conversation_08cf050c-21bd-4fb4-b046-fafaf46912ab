#!/bin/bash

# echo "good"
# echo `git pull`
# echo $gitpull
# echo $noask
# printenv
# exit;

if [ -f .env ]; then
    # Load Environment Variables
    # export $(cat .env | grep -v '#' | sed 's/\r$//' | awk '/=/ {print $1}' )

    #export $(echo $(grep -v '^#' .env | xargs -d '\n') | envsubst)
    #export $(echo $(cat .env | sed "s/#.*//g" | xargs) | envsubst)
    source .env
fi

#printenv
#echo $GITLAB_SLACK_DEPLOY_WEBHOOK_URL
#exit;

OUTPUT=$(

if [ "$sitedown" == "1" ]; then
    php artisan down
fi

if [ "$noask" != "Y" ]; then
    echo "Are you Sure? Y/n"
    read areyousure

    if [ "$areyousure" != "Y" ]; then
        php artisan up
        exit;
    fi
fi

# Git pull
if [ "$gitpull" == "1" ]; then
    git reset --hard
    git pull
fi

if [ "$composer" == "1" ]; then
    composer install
fi
if [ "$migrate" == "1" ]; then
    php artisan migrate
fi



# Clear caches
php artisan cache:clear
php artisan route:clear
php artisan route:cache
php artisan config:clear
php artisan view:clear

# Clear expired password reset tokens
php artisan auth:clear-resets

php artisan optimize

if [ "$npm" == "1" ]; then
    npm install
    npm run build
fi

if [ "$siteup" == "1" ]; then
    php artisan up
fi
)
#echo $GITLAB_SLACK_DEPLOY_WEBHOOK_URL
#echo $OUTPUT
#exit
#echo $LOG_SLACK_WEBHOOK_URL
#echo $GITLAB_SLACK_DEPLOY_WEBHOOK_URL

curl -X POST --silent --data-urlencode \
    "payload={\"text\": \"$(echo $OUTPUT | sed "s/\"/'/g")\"}" "$GITLAB_SLACK_DEPLOY_WEBHOOK_URL"



