<?php
//require('../vendor/afromansr/laravel-nira-api/src/NiraClient.php');
//require_once '../vendor/autoload.php';
//$client = new NiraClient ($username, $password);
//exit;
function print_screen($obj)
{
    echo '<pre>';
    var_dump($obj);
    echo '</pre>';
}

//namespace Nira;
require 'includes/NiraClient.php';

/*
 *
 * #Credentials
 * NIRA_USERNAME="MinistryofEducation@TPI"
 * #NIRA_PASSWORD=Yt56!Nj8
 * NIRA_PASSWORD=Sp33d@dem
 * #NIRA Server:Port
 * NIRA_SERVER="**************:14460"
 * */

$nira_wsdl = dirname(__FILE__).'/includes/nira.wsdl';
$pub_key_path = dirname(__FILE__).'/includes/niragoug.crt';

//information provided by the ministry
$username = 'MinistryofEducation@ROOT';
$password = 'Emis@sms1';

$username = 'MinistryofEducation@TPI';
$password = 'Sp33d@dem';

//server address of API
$nira_server = '**************:8080';
$nira_server = '**************:14460';

//generated by system

//$test_env = FALSE;

//$client = new NiraClient ($username, $password);
//$client->initiateClient($nira_wsdl, $nira_server, $enable_trace = TRUE );
//$client->initiateClient($enable_trace = TRUE );

//$person = $client->getPerson('CEFDxxxxxxe343434');

//commandline script

//get the enviromnet
$environment = isset($_GET['env']) ? $_GET['env'] : false;

if (! $environment) {

    while (true) {

        $environment = readline("Use Test Server or Production. Reply with\n0. for Test environment\n1. for Production\n2. to Quit\n");
        $environment = trim($environment);

        if (intval($environment) < 0) {
            echo "Wrong input. Reply with 1 to use Production or 0 to use test server\n2. to Quit";
        } elseif (intval($environment) > 2) {
            echo "Wrong input. Reply with 1 to use Production or 0 to use test server\n2. to Quit";
        } elseif (intval($environment) == 2) {
            echo 'exiting app';
            break;
        } else {

            $environment = intval($environment);
            if ($environment == 1) {

                $nira_server = readline("Enter the serverIP:port for the production server:\n");

                $username = readline("Enter the username provided by NIRA below:\n");

                $password = readline("Enter Current Password:\n");

                echo "Received: nira_server: $nira_server, username: $username, password: $password\n";

                $client = new NiraClient($username, $password);
                $client->initiateClient($nira_wsdl, $nira_server, $enable_trace = true);

                while (true) {
                    $action = readline("Select action\n1. Change Password\n2. Get Person\n3. to Quit\n");

                    if (intval($action) == 3) {
                        break;
                    } else {
                        switch ($action) {
                            case 1:
                                $new_password = readline('Enter New Password: ');

                                echo "Changing Password ...\n";
                                //test Create new Password.
                                $responseObj = $client->createNewPassword($new_password, $pub_key_path);

                                echo "Returned with response: \n";
                                print_screen($responseObj);
                                break;
                            case 2:

                                $nin = readline("Enter NIN\n\r");

                                echo "Getting Person of nin: $nin\n -----------\n";

                                $responseObj = $client->getPerson($nin);
                                echo "Returned with Response:\n";
                                print_screen($responseObj);
                                break;
                            default:
                                print "Unknown selection...\n";
                                break;

                        }
                    }

                }

            }

            echo "\n----------------------- \m";

        }

    }

}

?>
 
