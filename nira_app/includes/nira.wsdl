<?xml version='1.0' encoding='UTF-8'?><wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns2="http://schemas.xmlsoap.org/soap/http" xmlns:ns1="http://de.muehlbauer.tidis.thirdparty.pilatus" name="ThirdPartyInterfaceNewWSService" targetNamespace="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/">
    <wsdl:types>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/" targetNamespace="myNamespace" version="1.0">

            <xs:import namespace="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/"/>

            <xs:complexType name="GetVoterDetailsResponse">
                <xs:complexContent>
                    <xs:extension base="ns1:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                            <xs:element minOccurs="0" name="surname" type="xs:string"/>
                            <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirth" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirthEstimated" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="votersNo" type="xs:string"/>
                            <xs:element minOccurs="0" name="disabilityBlind" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityDeaf" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityPhysical" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityOther" type="xs:string"/>
                            <xs:element minOccurs="0" name="district" type="xs:string"/>
                            <xs:element minOccurs="0" name="constituency" type="xs:string"/>
                            <xs:element minOccurs="0" name="county" type="xs:string"/>
                            <xs:element minOccurs="0" name="subCounty" type="xs:string"/>
                            <xs:element minOccurs="0" name="parish" type="xs:string"/>
                            <xs:element minOccurs="0" name="pollingStation" type="xs:string"/>
                            <xs:element minOccurs="0" name="photo" type="xs:base64Binary"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>

        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/" xmlns:ns3="myNamespace" xmlns:ns2="GetPlaceOfBirthResponse" xmlns:ns1="GetPlaceOfOriginResponse" attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/">
            <xs:import namespace="GetPlaceOfOriginResponse"/>
            <xs:import namespace="GetPlaceOfBirthResponse"/>
            <xs:import namespace="myNamespace"/>
            <xs:element name="activatePayment" type="tns:activatePayment"/>
            <xs:element name="activatePaymentResponse" type="tns:activatePaymentResponse"/>
            <xs:element name="changePassword" type="tns:changePassword"/>
            <xs:element name="changePasswordResponse" type="tns:changePasswordResponse"/>
            <xs:element name="checkAccount" type="tns:checkAccount"/>
            <xs:element name="checkAccountResponse" type="tns:checkAccountResponse"/>
            <xs:element name="getApplicationStatus" type="tns:getApplicationStatus"/>
            <xs:element name="getApplicationStatusResponse" type="tns:getApplicationStatusResponse"/>
            <xs:element name="getConstituencys" type="tns:getConstituencys"/>
            <xs:element name="getConstituencysResponse" type="tns:getConstituencysResponse"/>
            <xs:element name="getCountrys" type="tns:getCountrys"/>
            <xs:element name="getCountrysResponse" type="tns:getCountrysResponse"/>
            <xs:element name="getCountys" type="tns:getCountys"/>
            <xs:element name="getCountysH" type="tns:getCountysH"/>
            <xs:element name="getCountysHResponse" type="tns:getCountysHResponse"/>
            <xs:element name="getCountysResponse" type="tns:getCountysResponse"/>
            <xs:element name="getDictionaryEntries" type="tns:getDictionaryEntries"/>
            <xs:element name="getDictionaryEntriesResponse" type="tns:getDictionaryEntriesResponse"/>
            <xs:element name="getDistricts" type="tns:getDistricts"/>
            <xs:element name="getDistrictsH" type="tns:getDistrictsH"/>
            <xs:element name="getDistrictsHResponse" type="tns:getDistrictsHResponse"/>
            <xs:element name="getDistrictsResponse" type="tns:getDistrictsResponse"/>
            <xs:element name="getIdCard" type="tns:getIdCard"/>
            <xs:element name="getIdCardResponse" type="tns:getIdCardResponse"/>
            <xs:element name="getParents" type="tns:getParents"/>
            <xs:element name="getParentsResponse" type="tns:getParentsResponse"/>
            <xs:element name="getParishes" type="tns:getParishes"/>
            <xs:element name="getParishesH" type="tns:getParishesH"/>
            <xs:element name="getParishesHResponse" type="tns:getParishesHResponse"/>
            <xs:element name="getParishesResponse" type="tns:getParishesResponse"/>
            <xs:element name="getPerson" type="tns:getPerson"/>
            <xs:element name="getPersonRequest" type="tns:getPersonRequest"/>
            <xs:element name="getPersonResponse" type="tns:getPersonResponse"/>
            <xs:element name="getPlaceOfBirth" type="tns:getPlaceOfBirth"/>
            <xs:element name="getPlaceOfBirthResponse" type="tns:getPlaceOfBirthResponse"/>
            <xs:element name="getPlaceOfOrigin" type="tns:getPlaceOfOrigin"/>
            <xs:element name="getPlaceOfOriginResponse" type="tns:getPlaceOfOriginResponse"/>
            <xs:element name="getPlaceOfResidence" type="tns:getPlaceOfResidence"/>
            <xs:element name="getPlaceOfResidenceResponse" type="tns:getPlaceOfResidenceResponse"/>
            <xs:element name="getPollingStations" type="tns:getPollingStations"/>
            <xs:element name="getPollingStationsResponse" type="tns:getPollingStationsResponse"/>
            <xs:element name="getSpouses" type="tns:getSpouses"/>
            <xs:element name="getSpousesResponse" type="tns:getSpousesResponse"/>
            <xs:element name="getSubCountys" type="tns:getSubCountys"/>
            <xs:element name="getSubCountysH" type="tns:getSubCountysH"/>
            <xs:element name="getSubCountysHResponse" type="tns:getSubCountysHResponse"/>
            <xs:element name="getSubCountysResponse" type="tns:getSubCountysResponse"/>
            <xs:element name="getVillages" type="tns:getVillages"/>
            <xs:element name="getVillagesH" type="tns:getVillagesH"/>
            <xs:element name="getVillagesHResponse" type="tns:getVillagesHResponse"/>
            <xs:element name="getVillagesResponse" type="tns:getVillagesResponse"/>
            <xs:element name="getVoterDetails" type="tns:getVoterDetails"/>
            <xs:element name="getVoterDetailsResponse" type="tns:getVoterDetailsResponse"/>
            <xs:element name="identifyPerson" type="tns:identifyPerson"/>
            <xs:element name="identifyPersonFullSearch" type="tns:identifyPersonFullSearch"/>
            <xs:element name="identifyPersonFullSearchResponse" type="tns:identifyPersonFullSearchResponse"/>
            <xs:element name="identifyPersonResponse" type="tns:identifyPersonResponse"/>
            <xs:element name="logHeartbeat" type="tns:logHeartbeat"/>
            <xs:element name="logHeartbeatResponse" type="tns:logHeartbeatResponse"/>
            <xs:element name="updateNonCriticalData" type="tns:updateNonCriticalData"/>
            <xs:element name="updateNonCriticalDataRequest" type="tns:updateNonCriticalDataRequest"/>
            <xs:element name="updateNonCriticalDataResponse" type="tns:updateNonCriticalDataResponse"/>
            <xs:element name="verifyPerson" type="tns:verifyPerson"/>
            <xs:element name="verifyPersonInformation" type="tns:verifyPersonInformation"/>
            <xs:element name="verifyPersonInformationResponse" type="tns:verifyPersonInformationResponse"/>
            <xs:element name="verifyPersonResponse" type="tns:verifyPersonResponse"/>
            <xs:complexType name="getDistrictsH">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="getDistrictsHResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetDistrictsResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetDistrictsResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="districts" nillable="true" type="tns:DistrictData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="TransactionStatusResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="transactionStatus" type="tns:ThirdPartyTransactionStatus"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="DistrictData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="AddressUnitBaseData">
                <xs:sequence>
                    <xs:element minOccurs="0" name="code" type="xs:string"/>
                    <xs:element minOccurs="0" name="deprecated" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="id" type="xs:int"/>
                    <xs:element minOccurs="0" name="idContemporary" type="xs:int"/>
                    <xs:element minOccurs="0" name="name" type="xs:string"/>
                    <xs:element minOccurs="0" name="revision" type="xs:int"/>
                    <xs:element minOccurs="0" name="successor" type="xs:int"/>
                    <xs:element minOccurs="0" name="validFrom" type="xs:string"/>
                    <xs:element minOccurs="0" name="validTo" type="xs:string"/>
                    <xs:element minOccurs="0" name="version" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ThirdPartyTransactionStatus">
                <xs:sequence>
                    <xs:element minOccurs="0" name="transactionStatus" type="xs:string"/>
                    <xs:element minOccurs="0" name="passwordDaysLeft" type="xs:string"/>
                    <xs:element minOccurs="0" name="executionCost" type="xs:decimal"/>
                    <xs:element minOccurs="0" name="error" type="tns:ThirdPartyTransactionError"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ThirdPartyTransactionError">
                <xs:sequence>
                    <xs:element minOccurs="0" name="code" type="xs:int"/>
                    <xs:element minOccurs="0" name="message" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfOrigin">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getPlaceOfOriginRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfOriginRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getPersonRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfOriginResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="ns1:GetPlaceOfOriginResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ThirdPartyLocalAddress">
                <xs:sequence>
                    <xs:element minOccurs="0" name="district" type="xs:string"/>
                    <xs:element minOccurs="0" name="county" type="xs:string"/>
                    <xs:element minOccurs="0" name="subCounty" type="xs:string"/>
                    <xs:element minOccurs="0" name="parish" type="xs:string"/>
                    <xs:element minOccurs="0" name="village" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getCountrys">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="getCountrysResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetCountrysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetCountrysResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="countries" nillable="true" type="tns:CountryData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="CountryData">
                <xs:sequence>
                    <xs:element minOccurs="0" name="icaoCode" type="xs:string"/>
                    <xs:element minOccurs="0" name="id" type="xs:int"/>
                    <xs:element minOccurs="0" name="name" type="xs:string"/>
                    <xs:element minOccurs="0" name="ninCode" type="xs:string"/>
                    <xs:element minOccurs="0" name="sortingIndex" type="xs:int"/>
                    <xs:element minOccurs="0" name="version" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPollingStations">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getAddressUnitsRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="superUnitId" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPollingStationsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetPollingStationsResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetPollingStationsResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="pollingStations" nillable="true" type="tns:PollingStationData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="PollingStationData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idParish" type="xs:int"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getDictionaryEntries">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getDictionaryEntriesRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getDictionaryEntriesRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="dictionaryType" type="tns:eDictionaryType"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getDictionaryEntriesResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetDictionaryEntriesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetDictionaryEntriesResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="dictionaryEntries" nillable="true" type="tns:DictionaryEntry"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="DictionaryEntry">
                <xs:sequence>
                    <xs:element minOccurs="0" name="code" type="xs:string"/>
                    <xs:element minOccurs="0" name="deleted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="editable" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="id" type="xs:int"/>
                    <xs:element minOccurs="0" name="name" type="xs:string"/>
                    <xs:element minOccurs="0" name="sortingIndex" type="xs:int"/>
                    <xs:element minOccurs="0" name="version" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getSubCountys">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getSubCountysResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetSubCountysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetSubCountysResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="subCountys" nillable="true" type="tns:SubCountyData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="SubCountyData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idCounty" type="xs:int"/>
                            <xs:element minOccurs="0" name="idConstituency" type="xs:int"/>
                            <xs:element minOccurs="0" name="constituencyCode" type="xs:string"/>
                            <xs:element minOccurs="0" name="constituencyName" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getPlaceOfResidence">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getPlaceOfResidenceRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfResidenceRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getPlaceOfResidenceResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetPlaceOfResidenceResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetPlaceOfResidenceResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="country" type="xs:string"/>
                            <xs:element minOccurs="0" name="foreignResCountry" type="xs:string"/>
                            <xs:element minOccurs="0" name="foreignAddressLine1" type="xs:string"/>
                            <xs:element minOccurs="0" name="foreignAddressLine2" type="xs:string"/>
                            <xs:element minOccurs="0" name="foreignAddressLine3" type="xs:string"/>
                            <xs:element minOccurs="0" name="street" type="xs:string"/>
                            <xs:element minOccurs="0" name="plotNumber" type="xs:string"/>
                            <xs:element minOccurs="0" name="houseNumber" type="xs:string"/>
                            <xs:element minOccurs="0" name="address" type="tns:ThirdPartyLocalAddress"/>
                            <xs:element minOccurs="0" name="districtOfPreviousResidential" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getDistricts">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="getDistrictsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetDistrictsResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="identifyPersonFullSearch">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:identifyPersonRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="identifyPersonRequest">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="fingerprints" nillable="true" type="tns:ThirdPartyPersonFingerPrint"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ThirdPartyPersonFingerPrint">
                <xs:sequence>
                    <xs:element name="position" type="xs:int"/>
                    <xs:element minOccurs="0" name="wsq" type="xs:base64Binary"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="identifyPersonFullSearchResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:IdentifyPersonResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="IdentifyPersonResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="persons" nillable="true" type="tns:ThirdPartyPersonScores"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ThirdPartyPersonScores">
                <xs:sequence>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="scores" nillable="true" type="tns:ThirdPartyFingerprintScores"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ThirdPartyFingerprintScores">
                <xs:sequence>
                    <xs:element name="hitPosition" type="xs:int"/>
                    <xs:element name="position" type="xs:int"/>
                    <xs:element minOccurs="0" name="score" type="xs:int"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="checkAccount">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:checkAccountRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="checkAccountRequest">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="checkAccountResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:CheckAccountResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="CheckAccountResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="activatedUnits" type="xs:long"/>
                            <xs:element minOccurs="0" name="remainingUnits" type="xs:long"/>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="payments" nillable="true" type="tns:ThirdPartyPayment"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ThirdPartyPayment">
                <xs:sequence>
                    <xs:element minOccurs="0" name="paymentRegistrationNumber" type="xs:string"/>
                    <xs:element minOccurs="0" name="amountPaid" type="xs:double"/>
                    <xs:element minOccurs="0" name="quantity" type="xs:long"/>
                    <xs:element minOccurs="0" name="feesPerUnit" type="xs:double"/>
                    <xs:element minOccurs="0" name="minimumUnit" type="xs:long"/>
                    <xs:element minOccurs="0" name="maximumUnit" type="xs:long"/>
                    <xs:element minOccurs="0" name="datePaid" type="xs:string"/>
                    <xs:element minOccurs="0" name="mdaName" type="xs:string"/>
                    <xs:element minOccurs="0" name="taxHeadCode" type="xs:string"/>
                    <xs:element minOccurs="0" name="taxHeadName" type="xs:string"/>
                    <xs:element minOccurs="0" name="activatedAt" type="xs:string"/>
                    <xs:element minOccurs="0" name="depleted" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="depletedAt" type="xs:string"/>
                    <xs:element minOccurs="0" name="remainingUnits" type="xs:long"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPerson">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getPersonRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPersonResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetPersonResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetPersonResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                            <xs:element minOccurs="0" name="surname" type="xs:string"/>
                            <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                            <xs:element minOccurs="0" name="maidenNames" type="xs:string"/>
                            <xs:element minOccurs="0" name="otherNames" type="xs:string"/>
                            <xs:element minOccurs="0" name="previousSurnames" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirth" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirthEstimated" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="gender" type="xs:string"/>
                            <xs:element minOccurs="0" name="nationality" type="xs:string"/>
                            <xs:element minOccurs="0" name="livingStatus" type="xs:string"/>
                            <xs:element minOccurs="0" name="citizenshipType" type="xs:string"/>
                            <xs:element minOccurs="0" name="maritalStatus" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone1" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone2" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone3" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone4" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone5" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone6" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone7" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone8" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone9" type="xs:string"/>
                            <xs:element minOccurs="0" name="cellPhone10" type="xs:string"/>
                            <xs:element minOccurs="0" name="homePhone" type="xs:string"/>
                            <xs:element minOccurs="0" name="eMail1" type="xs:string"/>
                            <xs:element minOccurs="0" name="eMail2" type="xs:string"/>
                            <xs:element minOccurs="0" name="eMail3" type="xs:string"/>
                            <xs:element minOccurs="0" name="addressLine1" type="xs:string"/>
                            <xs:element minOccurs="0" name="addressLine2" type="xs:string"/>
                            <xs:element minOccurs="0" name="addressLine3" type="xs:string"/>
                            <xs:element minOccurs="0" name="highestLevelOfEducation" type="xs:string"/>
                            <xs:element minOccurs="0" name="profession" type="xs:string"/>
                            <xs:element minOccurs="0" name="occupation" type="xs:string"/>
                            <xs:element minOccurs="0" name="disabilityBlind" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityDeaf" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityPhysical" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="disabilityOther" type="xs:string"/>
                            <xs:element minOccurs="0" name="photo" type="xs:base64Binary"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="changePassword">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:changePasswordRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="changePasswordRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="newPassword" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="changePasswordResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:TransactionStatusResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParishesH">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParishesHResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetParishesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetParishesResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="parishes" nillable="true" type="tns:ParishData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ParishData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idSubCounty" type="xs:int"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="identifyPerson">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:identifyPersonRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="identifyPersonResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:IdentifyPersonResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getVillagesH">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getVillagesHResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetVillagesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetVillagesResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="villages" nillable="true" type="tns:VillageData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="VillageData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idParish" type="xs:int"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getSubCountysH">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getSubCountysHResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetSubCountysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getIdCard">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getIdCardRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getIdCardRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="cardNumber" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getIdCardResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetIdCardResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetIdCardResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                            <xs:element minOccurs="0" name="surname" type="xs:string"/>
                            <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirth" type="xs:string"/>
                            <xs:element minOccurs="0" name="dateOfBirthEstimated" type="xs:boolean"/>
                            <xs:element minOccurs="0" name="gender" type="xs:string"/>
                            <xs:element minOccurs="0" name="nationality" type="xs:string"/>
                            <xs:element minOccurs="0" name="cardNumber" type="xs:string"/>
                            <xs:element minOccurs="0" name="cardExpiryDate" type="xs:string"/>
                            <xs:element minOccurs="0" name="cardStatus" type="xs:string"/>
                            <xs:element minOccurs="0" name="photo" type="xs:base64Binary"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getSpouses">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getSpousesRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getSpousesRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getSpousesResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetSpousesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetSpousesResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="spouses" nillable="true" type="tns:ThirdPartySpouseData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ThirdPartySpouseData">
                <xs:sequence>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                    <xs:element minOccurs="0" name="surname" type="xs:string"/>
                    <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="maidenNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="otherNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="previousSurnames" type="xs:string"/>
                    <xs:element minOccurs="0" name="nationality" type="xs:string"/>
                    <xs:element minOccurs="0" name="livingStatus" type="xs:string"/>
                    <xs:element minOccurs="0" name="citizenshipType" type="xs:string"/>
                    <xs:element minOccurs="0" name="placeOfMarriage" type="xs:string"/>
                    <xs:element minOccurs="0" name="dateOfMarriage" type="xs:string"/>
                    <xs:element minOccurs="0" name="typeOfMarriage" type="xs:string"/>
                    <xs:element minOccurs="0" name="marriageCertificateNumber" type="xs:string"/>
                    <xs:element minOccurs="0" name="countryNonUgandanCit" type="xs:string"/>
                    <xs:element minOccurs="0" name="citizenshipReason" type="xs:string"/>
                    <xs:element minOccurs="0" name="citizenshipCertificateNumber" type="xs:string"/>
                    <xs:element minOccurs="0" name="dateOfIssueCitizenshipCertificate" type="xs:string"/>
                    <xs:element minOccurs="0" name="countryPreviousCitizenship" type="xs:string"/>
                    <xs:element minOccurs="0" name="dualCitizenship" type="xs:string"/>
                    <xs:element minOccurs="0" name="countryDualCitizenship" type="xs:string"/>
                    <xs:element minOccurs="0" name="dualCitizenshipCertificateNumber" type="xs:string"/>
                    <xs:element minOccurs="0" name="dateOfIssueDualCitizenshipCertificate" type="xs:string"/>
                    <xs:element minOccurs="0" name="immigrationFileNumber" type="xs:string"/>
                    <xs:element minOccurs="0" name="passportNumberPreviousCountry" type="xs:string"/>
                    <xs:element minOccurs="0" name="passportNumberOtherCountry" type="xs:string"/>
                    <xs:element minOccurs="0" name="clan" type="xs:string"/>
                    <xs:element minOccurs="0" name="indigenousCommunity" type="xs:string"/>
                    <xs:element minOccurs="0" name="preliminaryCitizenshipConfirmation" type="xs:string"/>
                    <xs:element minOccurs="0" name="preliminaryCitizenshipConfirmationOther" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfBirth">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getPlaceOfBirthRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getPlaceOfBirthRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getPlaceOfBirthResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="ns2:GetPlaceOfBirthResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="verifyPerson">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:verifyPersonRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="verifyPersonRequest">
                <xs:complexContent>
                    <xs:extension base="tns:identifyPersonRequest">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="verifyPersonResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:VerifyPersonResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="VerifyPersonResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="matchingStatus" type="xs:boolean"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getConstituencys">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getConstituencysResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetConstituencysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetConstituencysResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="constituencys" nillable="true" type="tns:ConstituencyData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ConstituencyData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idDistrict" type="xs:int"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getCountysH">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getCountysHResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetCountysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetCountysResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" minOccurs="0" name="countys" nillable="true" type="tns:CountyData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="CountyData">
                <xs:complexContent>
                    <xs:extension base="tns:AddressUnitBaseData">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="idDistrict" type="xs:int"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="updateNonCriticalData">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:updateNonCriticalDataRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="updateNonCriticalDataRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                    <xs:element minOccurs="0" name="xmlData" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="updateNonCriticalDataResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:TransactionStatusResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getCountys">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getCountysResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetCountysResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParents">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getParentsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParentsRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getParentsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetParentsResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetParentsResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="father" type="tns:ThirdPartyParentData"/>
                            <xs:element minOccurs="0" name="mother" type="tns:ThirdPartyParentData"/>
                            <xs:element minOccurs="0" name="guardian1" type="tns:ThirdPartyGuardianData"/>
                            <xs:element minOccurs="0" name="guardian2" type="tns:ThirdPartyGuardianData"/>
                            <xs:element minOccurs="0" name="wellfarePerson" type="tns:ThirdPartyGuardianData"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ThirdPartyParentData">
                <xs:complexContent>
                    <xs:extension base="tns:ThirdPartyGuardianData">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ThirdPartyGuardianData">
                <xs:sequence>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                    <xs:element minOccurs="0" name="surname" type="xs:string"/>
                    <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="otherNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="previousSurnames" type="xs:string"/>
                    <xs:element minOccurs="0" name="maidenNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="dateOfBirth" type="xs:string"/>
                    <xs:element minOccurs="0" name="dateOfBirthEstimated" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="gender" type="xs:string"/>
                    <xs:element minOccurs="0" name="citizenshipType" type="xs:string"/>
                    <xs:element minOccurs="0" name="nationality" type="xs:string"/>
                    <xs:element minOccurs="0" name="livingStatus" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getVoterDetails">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getVoterDetailsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getVoterDetailsRequest">
                <xs:complexContent>
                    <xs:extension base="tns:getPersonRequest">
                        <xs:sequence/>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getVoterDetailsResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="ns3:GetVoterDetailsResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="verifyPersonInformation">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:verifyPersonInformationRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="verifyPersonInformationRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="dateOfBirth" type="xs:string"/>
                    <xs:element minOccurs="0" name="documentId" type="xs:string"/>
                    <xs:element minOccurs="0" name="givenNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="nationalId" type="xs:string"/>
                    <xs:element minOccurs="0" name="otherNames" type="xs:string"/>
                    <xs:element minOccurs="0" name="surname" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="verifyPersonInformationResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:VerifyPersonInformationResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="VerifyPersonInformationResponse">
                <xs:complexContent>
                    <xs:extension base="tns:VerifyPersonResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="cardStatus" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="logHeartbeat">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="logHeartbeatResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:TransactionStatusResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getApplicationStatus">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getApplicationStatusRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getApplicationStatusRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="applicantsSurname" type="xs:string"/>
                    <xs:element minOccurs="0" name="applicationId" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getApplicationStatusResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetApplicationStatusResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="GetApplicationStatusResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="applicationId" type="xs:string"/>
                            <xs:element minOccurs="0" name="applicationStatus" type="xs:string"/>
                            <xs:element minOccurs="0" name="enrollmentAddressDistrict" type="xs:string"/>
                            <xs:element minOccurs="0" name="enrollmentAddressCounty" type="xs:string"/>
                            <xs:element minOccurs="0" name="enrollmentAddressSubCounty" type="xs:string"/>
                            <xs:element minOccurs="0" name="enrollmentAddressParish" type="xs:string"/>
                            <xs:element minOccurs="0" name="enrollmentAddressStation" type="xs:string"/>
                            <xs:element minOccurs="0" name="issuanceLocation" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="activatePayment">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:activatePaymentRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="activatePaymentRequest">
                <xs:sequence>
                    <xs:element minOccurs="0" name="paymentRegistrationNumber" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="activatePaymentResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:ActivatePaymentResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ActivatePaymentResponse">
                <xs:complexContent>
                    <xs:extension base="tns:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="taxHeadName" type="xs:string"/>
                            <xs:element minOccurs="0" name="taxHeadCode" type="xs:string"/>
                            <xs:element minOccurs="0" name="amountPaid" type="xs:double"/>
                            <xs:element minOccurs="0" name="quantity" type="xs:long"/>
                            <xs:element minOccurs="0" name="feesPerUnit" type="xs:double"/>
                            <xs:element minOccurs="0" name="minimumUnit" type="xs:long"/>
                            <xs:element minOccurs="0" name="maximumUnit" type="xs:long"/>
                            <xs:element minOccurs="0" name="remainingUnits" type="xs:long"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="getVillages">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getVillagesResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetVillagesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParishes">
                <xs:sequence>
                    <xs:element minOccurs="0" name="request" type="tns:getAddressUnitsRequest"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getParishesResponse">
                <xs:sequence>
                    <xs:element minOccurs="0" name="return" type="tns:GetParishesResponse"/>
                </xs:sequence>
            </xs:complexType>
            <xs:simpleType name="eDictionaryType">
                <xs:restriction base="xs:string">
                    <xs:enumeration value="OCCUPATIONS"/>
                    <xs:enumeration value="PROFESSIONS"/>
                    <xs:enumeration value="EDUCATION"/>
                    <xs:enumeration value="MARRIAGE_TYPE"/>
                    <xs:enumeration value="MARITAL_STATUS"/>
                    <xs:enumeration value="INDIGENOUS_COMMUNITY"/>
                    <xs:enumeration value="CITIZENSHIP_TYPE"/>
                    <xs:enumeration value="PREFFERED_ISSUING_SITE"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:element name="getDistricsH" nillable="true" type="tns:getDistrictsH"/>
            <xs:element name="getDistricsHResponse" nillable="true" type="tns:getDistrictsHResponse"/>
        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/" targetNamespace="GetPlaceOfOriginResponse" version="1.0">

            <xs:import namespace="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/"/>

            <xs:complexType name="GetPlaceOfOriginResponse">
                <xs:complexContent>
                    <xs:extension base="ns1:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="country" type="xs:string"/>
                            <xs:element minOccurs="0" name="address" type="ns1:ThirdPartyLocalAddress"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>

        </xs:schema>
        <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/" targetNamespace="GetPlaceOfBirthResponse" version="1.0">

            <xs:import namespace="http://facade.server.pilatus.thirdparty.tidis.muehlbauer.de/"/>

            <xs:complexType name="GetPlaceOfBirthResponse">
                <xs:complexContent>
                    <xs:extension base="ns1:TransactionStatusResponse">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="country" type="xs:string"/>
                            <xs:element minOccurs="0" name="city" type="xs:string"/>
                            <xs:element minOccurs="0" name="healthFacility" type="xs:string"/>
                            <xs:element minOccurs="0" name="address" type="ns1:ThirdPartyLocalAddress"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>

        </xs:schema>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://de.muehlbauer.tidis.thirdparty.pilatus" attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://de.muehlbauer.tidis.thirdparty.pilatus">
            <xsd:element name="ThirdpartyFault" type="tns:ThirdpartyFault"/>
            <xsd:complexType name="ThirdpartyFault">
                <xsd:sequence>
                    <xsd:element minOccurs="0" name="parametersString" type="xsd:string"/>
                    <xsd:element minOccurs="0" name="code" type="xsd:string"/>
                    <xsd:element maxOccurs="unbounded" minOccurs="0" name="parameters" type="xsd:string"/>
                    <xsd:element minOccurs="0" name="message" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="checkAccountResponse">
        <wsdl:part element="tns:checkAccountResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountrys">
        <wsdl:part element="tns:getCountrys" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDictionaryEntries">
        <wsdl:part element="tns:getDictionaryEntries" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfResidenceResponse">
        <wsdl:part element="tns:getPlaceOfResidenceResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfResidence">
        <wsdl:part element="tns:getPlaceOfResidence" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDistricts">
        <wsdl:part element="tns:getDistricts" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSubCountysResponse">
        <wsdl:part element="tns:getSubCountysResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="checkAccount">
        <wsdl:part element="tns:checkAccount" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPerson">
        <wsdl:part element="tns:getPerson" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="identifyPersonFullSearchResponse">
        <wsdl:part element="tns:identifyPersonFullSearchResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParishesH">
        <wsdl:part element="tns:getParishesH" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParishesHResponse">
        <wsdl:part element="tns:getParishesHResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="identifyPerson">
        <wsdl:part element="tns:identifyPerson" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSubCountysH">
        <wsdl:part element="tns:getSubCountysH" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSpousesResponse">
        <wsdl:part element="tns:getSpousesResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPollingStationsResponse">
        <wsdl:part element="tns:getPollingStationsResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getIdCard">
        <wsdl:part element="tns:getIdCard" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfBirth">
        <wsdl:part element="tns:getPlaceOfBirth" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountysHResponse">
        <wsdl:part element="tns:getCountysHResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfOriginResponse">
        <wsdl:part element="tns:getPlaceOfOriginResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountrysResponse">
        <wsdl:part element="tns:getCountrysResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="verifyPerson">
        <wsdl:part element="tns:verifyPerson" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVillagesResponse">
        <wsdl:part element="tns:getVillagesResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="updateNonCriticalData">
        <wsdl:part element="tns:updateNonCriticalData" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVillagesHResponse">
        <wsdl:part element="tns:getVillagesHResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDictionaryEntriesResponse">
        <wsdl:part element="tns:getDictionaryEntriesResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="identifyPersonResponse">
        <wsdl:part element="tns:identifyPersonResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="logHeartbeat">
        <wsdl:part element="tns:logHeartbeat" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getApplicationStatus">
        <wsdl:part element="tns:getApplicationStatus" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="activatePayment">
        <wsdl:part element="tns:activatePayment" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParishes">
        <wsdl:part element="tns:getParishes" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParentsResponse">
        <wsdl:part element="tns:getParentsResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDistricsH">
        <wsdl:part element="tns:getDistricsH" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfOrigin">
        <wsdl:part element="tns:getPlaceOfOrigin" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="verifyPersonResponse">
        <wsdl:part element="tns:verifyPersonResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPollingStations">
        <wsdl:part element="tns:getPollingStations" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSubCountys">
        <wsdl:part element="tns:getSubCountys" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="identifyPersonFullSearch">
        <wsdl:part element="tns:identifyPersonFullSearch" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="changePassword">
        <wsdl:part element="tns:changePassword" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDistricsHResponse">
        <wsdl:part element="tns:getDistricsHResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVillagesH">
        <wsdl:part element="tns:getVillagesH" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="logHeartbeatResponse">
        <wsdl:part element="tns:logHeartbeatResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getApplicationStatusResponse">
        <wsdl:part element="tns:getApplicationStatusResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="verifyPersonInformationResponse">
        <wsdl:part element="tns:verifyPersonInformationResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="activatePaymentResponse">
        <wsdl:part element="tns:activatePaymentResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSpouses">
        <wsdl:part element="tns:getSpouses" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getDistrictsResponse">
        <wsdl:part element="tns:getDistrictsResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="updateNonCriticalDataResponse">
        <wsdl:part element="tns:updateNonCriticalDataResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getConstituencys">
        <wsdl:part element="tns:getConstituencys" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVoterDetailsResponse">
        <wsdl:part element="tns:getVoterDetailsResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountysH">
        <wsdl:part element="tns:getCountysH" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountys">
        <wsdl:part element="tns:getCountys" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPersonResponse">
        <wsdl:part element="tns:getPersonResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getCountysResponse">
        <wsdl:part element="tns:getCountysResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParents">
        <wsdl:part element="tns:getParents" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getParishesResponse">
        <wsdl:part element="tns:getParishesResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getPlaceOfBirthResponse">
        <wsdl:part element="tns:getPlaceOfBirthResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVoterDetails">
        <wsdl:part element="tns:getVoterDetails" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="verifyPersonInformation">
        <wsdl:part element="tns:verifyPersonInformation" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getConstituencysResponse">
        <wsdl:part element="tns:getConstituencysResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="ThirdPartyFault">
        <wsdl:part element="ns1:ThirdpartyFault" name="ThirdPartyFault">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getSubCountysHResponse">
        <wsdl:part element="tns:getSubCountysHResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getIdCardResponse">
        <wsdl:part element="tns:getIdCardResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="getVillages">
        <wsdl:part element="tns:getVillages" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:message name="changePasswordResponse">
        <wsdl:part element="tns:changePasswordResponse" name="parameters">
        </wsdl:part>
    </wsdl:message>
    <wsdl:portType name="ThirdPartyInterfaceFacadeNew">
        <wsdl:operation name="getDistricsH">
            <wsdl:input message="tns:getDistricsH" name="getDistricsH">
            </wsdl:input>
            <wsdl:output message="tns:getDistricsHResponse" name="getDistricsHResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfOrigin">
            <wsdl:input message="tns:getPlaceOfOrigin" name="getPlaceOfOrigin">
            </wsdl:input>
            <wsdl:output message="tns:getPlaceOfOriginResponse" name="getPlaceOfOriginResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountrys">
            <wsdl:input message="tns:getCountrys" name="getCountrys">
            </wsdl:input>
            <wsdl:output message="tns:getCountrysResponse" name="getCountrysResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPollingStations">
            <wsdl:input message="tns:getPollingStations" name="getPollingStations">
            </wsdl:input>
            <wsdl:output message="tns:getPollingStationsResponse" name="getPollingStationsResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDictionaryEntries">
            <wsdl:input message="tns:getDictionaryEntries" name="getDictionaryEntries">
            </wsdl:input>
            <wsdl:output message="tns:getDictionaryEntriesResponse" name="getDictionaryEntriesResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSubCountys">
            <wsdl:input message="tns:getSubCountys" name="getSubCountys">
            </wsdl:input>
            <wsdl:output message="tns:getSubCountysResponse" name="getSubCountysResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfResidence">
            <wsdl:input message="tns:getPlaceOfResidence" name="getPlaceOfResidence">
            </wsdl:input>
            <wsdl:output message="tns:getPlaceOfResidenceResponse" name="getPlaceOfResidenceResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDistricts">
            <wsdl:input message="tns:getDistricts" name="getDistricts">
            </wsdl:input>
            <wsdl:output message="tns:getDistrictsResponse" name="getDistrictsResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="identifyPersonFullSearch">
            <wsdl:input message="tns:identifyPersonFullSearch" name="identifyPersonFullSearch">
            </wsdl:input>
            <wsdl:output message="tns:identifyPersonFullSearchResponse" name="identifyPersonFullSearchResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="checkAccount">
            <wsdl:input message="tns:checkAccount" name="checkAccount">
            </wsdl:input>
            <wsdl:output message="tns:checkAccountResponse" name="checkAccountResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPerson">
            <wsdl:input message="tns:getPerson" name="getPerson">
            </wsdl:input>
            <wsdl:output message="tns:getPersonResponse" name="getPersonResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="changePassword">
            <wsdl:input message="tns:changePassword" name="changePassword">
            </wsdl:input>
            <wsdl:output message="tns:changePasswordResponse" name="changePasswordResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParishesH">
            <wsdl:input message="tns:getParishesH" name="getParishesH">
            </wsdl:input>
            <wsdl:output message="tns:getParishesHResponse" name="getParishesHResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="identifyPerson">
            <wsdl:input message="tns:identifyPerson" name="identifyPerson">
            </wsdl:input>
            <wsdl:output message="tns:identifyPersonResponse" name="identifyPersonResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVillagesH">
            <wsdl:input message="tns:getVillagesH" name="getVillagesH">
            </wsdl:input>
            <wsdl:output message="tns:getVillagesHResponse" name="getVillagesHResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSubCountysH">
            <wsdl:input message="tns:getSubCountysH" name="getSubCountysH">
            </wsdl:input>
            <wsdl:output message="tns:getSubCountysHResponse" name="getSubCountysHResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getIdCard">
            <wsdl:input message="tns:getIdCard" name="getIdCard">
            </wsdl:input>
            <wsdl:output message="tns:getIdCardResponse" name="getIdCardResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSpouses">
            <wsdl:input message="tns:getSpouses" name="getSpouses">
            </wsdl:input>
            <wsdl:output message="tns:getSpousesResponse" name="getSpousesResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfBirth">
            <wsdl:input message="tns:getPlaceOfBirth" name="getPlaceOfBirth">
            </wsdl:input>
            <wsdl:output message="tns:getPlaceOfBirthResponse" name="getPlaceOfBirthResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyPerson">
            <wsdl:input message="tns:verifyPerson" name="verifyPerson">
            </wsdl:input>
            <wsdl:output message="tns:verifyPersonResponse" name="verifyPersonResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getConstituencys">
            <wsdl:input message="tns:getConstituencys" name="getConstituencys">
            </wsdl:input>
            <wsdl:output message="tns:getConstituencysResponse" name="getConstituencysResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountysH">
            <wsdl:input message="tns:getCountysH" name="getCountysH">
            </wsdl:input>
            <wsdl:output message="tns:getCountysHResponse" name="getCountysHResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="updateNonCriticalData">
            <wsdl:input message="tns:updateNonCriticalData" name="updateNonCriticalData">
            </wsdl:input>
            <wsdl:output message="tns:updateNonCriticalDataResponse" name="updateNonCriticalDataResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountys">
            <wsdl:input message="tns:getCountys" name="getCountys">
            </wsdl:input>
            <wsdl:output message="tns:getCountysResponse" name="getCountysResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParents">
            <wsdl:input message="tns:getParents" name="getParents">
            </wsdl:input>
            <wsdl:output message="tns:getParentsResponse" name="getParentsResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVoterDetails">
            <wsdl:input message="tns:getVoterDetails" name="getVoterDetails">
            </wsdl:input>
            <wsdl:output message="tns:getVoterDetailsResponse" name="getVoterDetailsResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyPersonInformation">
            <wsdl:input message="tns:verifyPersonInformation" name="verifyPersonInformation">
            </wsdl:input>
            <wsdl:output message="tns:verifyPersonInformationResponse" name="verifyPersonInformationResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="logHeartbeat">
            <wsdl:input message="tns:logHeartbeat" name="logHeartbeat">
            </wsdl:input>
            <wsdl:output message="tns:logHeartbeatResponse" name="logHeartbeatResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getApplicationStatus">
            <wsdl:input message="tns:getApplicationStatus" name="getApplicationStatus">
            </wsdl:input>
            <wsdl:output message="tns:getApplicationStatusResponse" name="getApplicationStatusResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="activatePayment">
            <wsdl:input message="tns:activatePayment" name="activatePayment">
            </wsdl:input>
            <wsdl:output message="tns:activatePaymentResponse" name="activatePaymentResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVillages">
            <wsdl:input message="tns:getVillages" name="getVillages">
            </wsdl:input>
            <wsdl:output message="tns:getVillagesResponse" name="getVillagesResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParishes">
            <wsdl:input message="tns:getParishes" name="getParishes">
            </wsdl:input>
            <wsdl:output message="tns:getParishesResponse" name="getParishesResponse">
            </wsdl:output>
            <wsdl:fault message="tns:ThirdPartyFault" name="ThirdPartyFault">
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="ThirdPartyInterfaceNewWSServiceSoapBinding" type="tns:ThirdPartyInterfaceFacadeNew">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="getDistricsH">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getDistricsH">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getDistricsHResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfOrigin">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getPlaceOfOrigin">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPlaceOfOriginResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountrys">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getCountrys">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCountrysResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPollingStations">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getPollingStations">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPollingStationsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDictionaryEntries">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getDictionaryEntries">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getDictionaryEntriesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSubCountys">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getSubCountys">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getSubCountysResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfResidence">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getPlaceOfResidence">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPlaceOfResidenceResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getDistricts">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getDistricts">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getDistrictsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="identifyPersonFullSearch">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="identifyPersonFullSearch">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="identifyPersonFullSearchResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="checkAccount">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="checkAccount">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="checkAccountResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPerson">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getPerson">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPersonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="changePassword">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="changePassword">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="changePasswordResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParishesH">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getParishesH">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getParishesHResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="identifyPerson">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="identifyPerson">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="identifyPersonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVillagesH">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getVillagesH">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getVillagesHResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSubCountysH">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getSubCountysH">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getSubCountysHResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getIdCard">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getIdCard">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getIdCardResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getSpouses">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getSpouses">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getSpousesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getPlaceOfBirth">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getPlaceOfBirth">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getPlaceOfBirthResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyPerson">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="verifyPerson">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="verifyPersonResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getConstituencys">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getConstituencys">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getConstituencysResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountysH">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getCountysH">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCountysHResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="updateNonCriticalData">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="updateNonCriticalData">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="updateNonCriticalDataResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getCountys">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getCountys">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getCountysResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParents">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getParents">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getParentsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVoterDetails">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getVoterDetails">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getVoterDetailsResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="verifyPersonInformation">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="verifyPersonInformation">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="verifyPersonInformationResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="logHeartbeat">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="logHeartbeat">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="logHeartbeatResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getApplicationStatus">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getApplicationStatus">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getApplicationStatusResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="activatePayment">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="activatePayment">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="activatePaymentResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getVillages">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getVillages">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getVillagesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
        <wsdl:operation name="getParishes">
            <soap:operation soapAction="" style="document"/>
            <wsdl:input name="getParishes">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="getParishesResponse">
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="ThirdPartyFault">
                <soap:fault name="ThirdPartyFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="ThirdPartyInterfaceNewWSService">
        <wsdl:port binding="tns:ThirdPartyInterfaceNewWSServiceSoapBinding" name="ThirdPartyInterfaceNewWSPort">
		<soap:address location="http://**************:14460/pilatusp2-tpi2-ws/ThirdPartyInterfaceNewWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
