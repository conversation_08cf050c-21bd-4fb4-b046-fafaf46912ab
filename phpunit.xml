<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" bootstrap="vendor/autoload.php" colors="true" backupGlobals="false" processIsolation="false" stopOnFailure="false" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Modules">
            <directory suffix="Test.php">./Modules/*/tests/Feature</directory>
            <directory suffix="Test.php">./Modules/*/tests/Unit</directory>
        </testsuite>
        <!-- <testsuite name="Performance">
            <directory suffix="Test.php">./tests/Performance</directory>
        </testsuite> -->
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
            <directory suffix=".php">./Modules</directory>
        </include>
        <exclude>
            <directory suffix=".php">./Modules/*/config</directory>
            <directory suffix=".php">./Modules/*/database</directory>
            <directory suffix=".php">./Modules/*/resources</directory>
            <directory suffix=".php">./Modules/*/routes</directory>
            <directory suffix=".php">./Modules/*/tests</directory>
        </exclude>
    </source>
</phpunit>
