{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@googlemaps/js-api-loader": "^1.12.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue2": "^1.1.2", "animate.css": "^4.1.1", "autoprefixer": "^10.4.21", "axios": "^1.6.4", "block-ui": "^2.70.1", "bootstrap": "^4.0.0", "chart.js": "^3.2.1", "dropify": "^0.2.2", "dual-listbox": "^1.3.1", "intl-tel-input": "^17.0.12", "jquery": "^3.2", "laravel-vite-plugin": "^1.2.0", "lodash": "^4.17.19", "moment": "^2.29.1", "popper.js": "^1.12", "postcss": "^8.5.3", "postcss-nesting": "^13.0.1", "resolve-url-loader": "^3.1.3", "sass": "^1.20.1", "sass-loader": "^8.0.0", "tailwindcss": "^3.4.17", "tailwindcss-v4": "npm:tailwindcss@^4.1.7", "vite": "^5.4.19", "vue": "^2.7.16", "vue-loader": "^15.9.5", "vue-template-compiler": "^2.6.10", "wnumb": "^1.2.0"}, "dependencies": {"@tailwindcss/postcss": "^4.1.6", "@tailwindcss/vite": "^4.1.6", "chartjs-plugin-datalabels": "^2.2.0", "vue-form-generator": "^2.3.4", "vue-sorted-table": "^1.3.0", "vue2-editor": "^2.10.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.18.6/xlsx-0.18.6.tgz"}}