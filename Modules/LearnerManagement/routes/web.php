<?php

use Illuminate\Support\Facades\Route;
use Modules\LearnerManagement\Http\Controllers\LearnerManagementController;
use Modules\LearnerManagement\Livewire\LearnerAttendance;
use Modules\LearnerManagement\Livewire\LearnerNumbers;
use Modules\LearnerManagement\Livewire\LearnerPerformance\LearnerPerformanceSection;
use Mo<PERSON>les\LearnerManagement\Livewire\LearnerPerformance\LearnerPerformanceSummarySection;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('learnermanagements', LearnerManagementController::class)->names('learnermanagement');
});

Route::get('institution/learners/attendance', LearnerAttendance::class)->name('learner-attendance');

Route::get('institution/learners/learner-numbers', LearnerNumbers::class)->name('learner-numbers');
Route::get('institution/learners/performance', LearnerPerformanceSection::class)->name('learner-performance');
Route::get('institution/learners/performance/overview', LearnerPerformanceSummarySection::class)->name('learner-performance-summary');
