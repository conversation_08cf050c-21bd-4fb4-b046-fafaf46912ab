<div class="components-preview mx-auto">
    <div class="nk-block nk-block-lg">

        <div class="nk-block-head-content">
            <h3 class="nk-block-title page-title">Learner Performance Overview</h3>
            <nav class="nk-block-des">
                <ul class="breadcrumb breadcrumb-arrow">
                    <li class="breadcrumb-item">
                        <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>

                    </li>
                    <li class="breadcrumb-item active text-soft">
                        <a :href="'/institution/learners/performance'" class="text-primary">Learner Performance</a>
                    </li>
                    <li class="breadcrumb-item active text-soft">
                        <a :href="'/institution/learners/performance/overview'" class="text-primary">Overview</a>
                    </li>
                </ul>
            </nav>
        </div>

        <div class="nk-block mt-2">
            <div class="card-inner position-relative card-tools-toggle">
                <div class="card-title-group">
                    <div class="card-tools">
                        <form wire:submit.prevent="countLearnerPerformanceGrades">
                            <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                <div class="form-wrap">
                                    <div class="form-group ">
                                        <div class="form-control-wrap ">
                                            @if($institution->school_type_id === 2)
                                            <select style="width: 200px !important" wire:model="selectedClassId" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                <option value="">All Classes</option>
                                                @foreach($primary_education_grades as $grade)
                                                <option value="{{$grade->id}}">{{$grade->name}}</option>
                                                @endforeach
                                            </select>
                                            @elseif($institution->school_type_id === 3)
                                            <select style="width: 200px !important"  wire:model="selectedClassId" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                <option value="">All Classes</option>
                                                @foreach($secondary_education_grades as $grade)
                                                <option value="{{$grade->id}}">{{$grade->name}}</option>
                                                @endforeach
                                            </select>
                                            @endif
                                            <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                            </span>
                                        </div>
                                    </div>

                                </div>

                                <div class="form-wrap" >
                                    <div class="form-group ">
                                        <div class="form-control-wrap">
                                            @if($institution->school_type_id === 2)
                                            <select wire:model="selectedSubjectId" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                <option value="">All Subjects</option>
                                                @foreach($primary_subjects as $subject)
                                                <option value="{{$subject->id}}">{{$subject->name}}</option>
                                                @endforeach
                                            </select>
                                            @elseif($institution->school_type_id === 3)
                                            <select  wire:model="selectedSubjectId" class=" text-uppercase max-h-10 scrollable form-control  custom-select pr-5 cursor-pointer">
                                                <option value="">All Subjects</option>
                                                @foreach($secondary_subjects as $subject)
                                                <option value="{{$subject->id}}">{{$subject->name}}</option>
                                                @endforeach
                                            </select>
                                            @endif
                                            <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                            </span>
                                        </div>
                                    </div>

                                </div>

                                <div style="width: 400px !important" class="form-wrap">
                                    <div class="input-group">

                                        <div class="input-group-append">
                                            @if($filtersApplied)
                                            <button wire:click.prevent="resetFilters" wire:input="countLearnersPerformanceGrades" class="btn  rounded-0 bg-secondary px-2 text-white" type="button">
                                                <em class="icon ni ni-cross"></em>
                                            </button>
                                            @endif
                                            <button style="width: 150px;" class="btn rounded  bg-dark-teal " type="submit">
                                                <em class="icon ni ni-filter mr-1"></em>Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div><!-- .card-tools -->
                </div><!-- .card-title-group -->
            </div><!-- .card-inner -->

            <div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="table-responsive">

                            <h5 class="">Total Performances by Sex</h5>

                            <table class="table border border-dark-teal">
                                <thead class="bg-secondary">
                                    <tr>
                                        <th class="text-white align-middle text-uppercase w-45" rowspan="2">Sex</th>
                                    </tr>
                                    <tr>
                                        <th class="py-2 text-center text-white text-uppercase border-left border-white">Number Of Subject Performances</th>
                                    </tr>
                                </thead>
                                <tbody class="border-top-0 border-dark-teal">
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">FEMALE</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{$totalFemale}}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">MALE</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{$totalMale}}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">TOTAL</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{$totalLearners}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="table-responsive">
                            <h5 class="pt-2 pb-2 mt-2">Learners' Performance Grades by Subject Per Class</h5>

                            <table class="table table-sm table-hover table-bordered">
                                <thead>
                                    <tr class="bg-secondary">
                                        <th class="py-2 text-uppercase border-secondary text-white">CLASS</th>
                                        <th class="py-2 text-uppercase border-secondary text-white">SUBJECT</th>
                                        <th class="py-2 text-uppercase border-secondary text-white">GRADE</th>
                                        <th class="py-2 text-uppercase border-secondary text-white">SEX</th>
                                        <th class="py-2 text-uppercase border-secondary text-white text-center">PERCENTAGE</th>
                                        <th class="py-2 text-uppercase border-secondary text-white text-center">TOTAL BY SEX</th>
                                        <th class="py-2 text-uppercase border-secondary text-white text-center">OVERALL TOTAL BY SEX</th>
                                    </tr>
                                </thead>
                                @if ($selectedClassId || $selectedSubjectId)
                                <tbody class="border-secondary border-top">

                                    @php
                                    $filtered = collect($performanceStats)->filter(function($item) {
                                    $classMatch = $this->education_grade_id ? $item['education_grade_id'] == $this->education_grade_id : true;
                                    if($this->institution->school_type_id === 2){
                                    $subjectMatch = $this->primary_subject_id ? $item['primary_subject_id'] == $this->primary_subject_id : true;
                                    } elseif($this->institution->school_type_id === 3){
                                    $subjectMatch = $this->secondary_subject_id ? $item['secondary_subject_id'] == $this->secondary_subject_id : true;
                                    }
                                    return $classMatch && $subjectMatch;
                                    });

                                    if($this->institution->school_type_id === 2){
                                    $grouped = $filtered->groupBy(['education_grade_id', 'primary_subject_id', 'performance_grade_id']);
                                    } elseif($this->institution->school_type_id === 3){
                                    $grouped = $filtered->groupBy(['education_grade_id', 'secondary_subject_id', 'performance_grade_id']);
                                    }
                                    @endphp

                                    @php
                                    function countPerformanceRows($subjects) {
                                    $total = 0;
                                    foreach ($subjects as $grades) {
                                    $total += count($grades) * 2;
                                    }
                                    return $total;
                                    }
                                    @endphp


                                    @foreach ($grouped as $classId => $subjects)
                                    @php $firstClassRow = true; @endphp

                                    @foreach ($subjects as $subjectId => $grades)
                                    @php $firstSubjectRow = true; @endphp
                                    @foreach ($grades as $gradeId => $entries)
                                    @php
                                    $male = $entries->firstWhere('gender', 'M');
                                    $female = $entries->firstWhere('gender', 'F');

                                    $maleTotal = $male['total'] ?? 0;
                                    $femaleTotal = $female['total'] ?? 0;
                                    $overallTotal = $maleTotal + $femaleTotal;

                                    $malePercentage = $overallTotal ? round(($maleTotal / $overallTotal) * 100, 1).'%' : '0%';
                                    $femalePercentage = $overallTotal ? round(($femaleTotal / $overallTotal) * 100, 1).'%' : '0%';
                                    @endphp
                                    <tr>
                                        @if ($firstClassRow)
                                        <th rowspan="{{ countPerformanceRows($subjects) }}" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                            {{ $this->get_class_name($classId) }}
                                        </th>
                                        @php $firstClassRow = false; @endphp
                                        @endif

                                        @if ($firstSubjectRow)
                                        <th rowspan="{{ count($grades) * 2 }}" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                            {{ $this->get_subject_name($subjectId) }}
                                        </th>
                                        @php $firstSubjectRow = false; @endphp
                                        @endif


                                        <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                            {{ $this->get_grade_name($gradeId) }}
                                        </th>
                                        <td class="align-middle text-uppercase text-dark border-secondary px-2">MALE</td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">{{ $malePercentage }}</td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">{{ $maleTotal }}</td>
                                        <td rowspan="2" class="align-middle text-uppercase text-dark border-secondary text-center bg-secondary-dim">
                                            {{ $overallTotal }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-uppercase text-dark border-secondary px-2">FEMALE</td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">{{ $femalePercentage }}</td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">{{ $femaleTotal }}</td>
                                    </tr>
                                    @endforeach
                                    @endforeach
                                    @endforeach

                                </tbody>
                                @else

                                <tbody class="border-secondary border-top">

                                    @php

                                    $hasFilter = $education_grade_id || $primary_subject_id;

                                    if ($hasFilter) {
                                    $filtered = collect($performanceStats)->filter(function ($item) use ($education_grade_id, $primary_subject_id) {
                                    $classMatch = $education_grade_id ? $item['education_grade_id'] == $education_grade_id : true;
                                    $subjectMatch = $primary_subject_id ? $item['primary_subject_id'] == $primary_subject_id : true;
                                    return $classMatch && $subjectMatch;
                                    });

                                    $groupedByGrade = $filtered->groupBy('performance_grade_id');
                                    $firstRow = true;

                                    foreach ($groupedByGrade as $gradeId => $entries) {

                                    }
                                    } else {

                                    if($this->institution->school_type_id === 2){
                                    $grouped = collect($performanceStats)->groupBy([
                                    'education_grade_id',
                                    'primary_subject_id',
                                    'performance_grade_id'
                                    ]);
                                    } elseif($this->institution->school_type_id === 3){
                                    $grouped = collect($performanceStats)->groupBy([
                                    'education_grade_id',
                                    'secondary_subject_id',
                                    'performance_grade_id'
                                    ]);
                                    }

                                    foreach ($grouped as $classId => $subjects) {
                                    $classRowspan = 0;
                                    foreach ($subjects as $subjectGrades) {
                                    $classRowspan += count($subjectGrades) * 2; // 2 rows per grade (M & F)
                                    }
                                    $firstClassRow = true;

                                    foreach ($subjects as $subjectId => $grades) {
                                    $subjectRowspan = count($grades) * 2;
                                    $firstSubjectRow = true;

                                    foreach ($grades as $gradeId => $entries) {
                                    $male = $entries->firstWhere('gender', 'M');
                                    $female = $entries->firstWhere('gender', 'F');

                                    $maleTotal = $male['total'] ?? 0;
                                    $femaleTotal = $female['total'] ?? 0;
                                    $overallTotal = $maleTotal + $femaleTotal;

                                    $malePercentage = $overallTotal ? round(($maleTotal / $overallTotal) * 100, 1).'%' : '0%';
                                    $femalePercentage = $overallTotal ? round(($femaleTotal / $overallTotal) * 100, 1).'%' : '0%';

                                    echo '<tr>';
                                        if ($firstClassRow) {
                                        echo '<th rowspan="'.$classRowspan.'" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">'
                                            . $this->get_class_name($classId) . '</th>';
                                        $firstClassRow = false;
                                        }
                                        if ($firstSubjectRow) {
                                        echo '<th rowspan="'.$subjectRowspan.'" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">'
                                            . $this->get_subject_name($subjectId) . '</th>';
                                        $firstSubjectRow = false;
                                        }
                                        echo '<th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">'
                                            . $this->get_grade_name($gradeId) . '</th>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary px-2">MALE</td>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary text-center px-2">'.$malePercentage.'</td>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary text-center px-2">'.$maleTotal.'</td>';
                                        echo '<td rowspan="2" class="align-middle text-uppercase text-dark border-secondary text-center bg-secondary-dim">'.$overallTotal.'</td>';
                                        echo '</tr>';

                                    echo '<tr>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary px-2">FEMALE</td>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary text-center px-2">'.$femalePercentage.'</td>';
                                        echo '<td class="align-middle text-uppercase text-dark border-secondary text-center px-2">'.$femaleTotal.'</td>';
                                        echo '</tr>';
                                    }
                                    }
                                    }
                                    }
                                    @endphp

                                </tbody>
                                @endif

                            </table>
                            @if(count($performanceStats) === 0)
                            <div class="card-inner p-0">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There is no data to display at the moment.
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
</div>