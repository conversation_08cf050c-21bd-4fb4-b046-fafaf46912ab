<div>
    <style>
        .attendance-cell {
            width: 50px;
            height: 40px;
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .attendance-cell:hover {
            background-color: #f8f9fa;
        }

        .status-present {
            color: #28a745;
        }

        .status-absent {
            color: #dc3545;
        }

        .student-row:hover {
            background-color: #f8f9fa;
        }

        .today-column {
            background-color: #e3f2fd;
        }

    </style>
    <div class="container-fluid">
        <!-- Quick Stats Row -->
        <div class="row mb-2 flex justify-content-between align-items-center mr-1">
            <h4 class="mb-2 ml-2">Manage Learner Attendance</h4>
            <div>
                <a href="{{ route('learner-numbers') }}" wire:navigate class="btn btn-dark-teal text-uppercase">Attendance Summary</a>
                <button type="button" class="btn btn-dark-teal text-uppercase" data-toggle="modal" data-target="#attendanceModal">Add Attendance</button>
            </div>
        </div>

        <div class="border rounded p-3 border border-dark-teal mb-3">
            <!-- filter part -->
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-2">
                        <div class="">
                            <div class="filter-section">
                                <div class="row">
                                    <div class="col-md-2">
                                        <select wire:model="selected_grade_id" class="form-control">
                                            <option value="">-- Select Grade --</option>
                                            @forelse ($grades as $grade)
                                            <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                                            @empty
                                            AN8XFQ2K <option disabled>No grades available</option>
                                            @endforelse
                                        </select>
                                    </div>


                                    <div class="col-md-2">
                                        <select wire:model="selected_gender" class="form-control">
                                            <option value="">Select Sex</option>
                                            <option value="M">Male</option>
                                            <option value="F">Female</option>
                                        </select>
                                    </div>

                                    <div class="col-md-2">
                                        <select wire:model="selected_status" class="form-control">
                                            <option value="">Select Status</option>
                                            <option value="present">Present</option>
                                            <option value="absent">Absent</option>
                                        </select>
                                    </div>

                                    <div class="col-md-2">
                                        <input type="date" wire:model="date" class="form-control" placeholder="date">
                                    </div>

                                    <div class="col-md-4">
                                        <form wire:submit="searchLearner">
                                            <div class="input-group">
                                                <input type="text" wire:model="searchTerm" class="form-control bg-primary-dim" placeholder="Search learner">

                                                <div class="input-group-append">
                                                    @if ($searchActive)
                                                    <button type="button" wire:click="resetSearch" class="btn rounded-0 bg-secondary px-2 text-white">
                                                        <em class="icon ni ni-cross"></em>
                                                    </button>
                                                    @endif

                                                    <button type="submit" class="btn rounded-right bg-dark-teal">
                                                        <em class="icon ni ni-filter mr-1"></em>
                                                        Apply
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Attendance Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body p-0 mt-1">
                            <div class="d-flex justify-content-between align-items-center">
                                <button wire:click="previousWeek" class="btn btn-outline-primary btn-sm mb-1">← Previous
                                    Week</button>
                                <strong>
                                    Week of {{ \Carbon\Carbon::parse($weekDays[0]['date'])->format('M d') }} -
                                    {{ \Carbon\Carbon::parse(end($weekDays)['date'])->format('M d, Y') }}
                                </strong>
                                <button wire:click="nextWeek" class="btn btn-outline-primary btn-sm mb-1">Next Week
                                    →</button>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover mb-0">
                                    <thead class="bg-secondary text-white">
                                        <tr>
                                            {{-- <th style="width: 40px;">
                                            <input type="checkbox" wire:click="toggleSelectAll">
                                        </th> --}}
                                            <th style="width: 250px;">Student</th>
                                            @if (!empty($weekDays) && is_array($weekDays))
                                            @foreach ($weekDays as $day)
                                            <th class="text-center ">
                                                {{ $day['day'] }}<br>
                                                <small>{{ $day['label'] }}</small>
                                            </th>
                                            @endforeach
                                            @else
                                            <th colspan="7" class="text-center">No week data available</th>
                                            @endif
                                        </tr>
                                    </thead>

                                    <tbody id="attendanceTableBody">
                                        @forelse ($this->students ?? [] as $student)
                                        <tr class="student-row">
                                            {{-- Student Info --}}
                                            {{-- <td class="text-center">
                                                <input type="checkbox" class="student-checkbox"
                                                    wire:model="selectedStudents" value="{{ $student->id }}">
                                            </td> --}}
                                            <td>
                                                <strong>{{ $student->first_name }}
                                                    {{ $student->surname }}</strong><br>

                                                <small>{{ $student->learner->lin ?? 'No lin' }}</small>
                                            </td>

                                            {{-- Attendance cells --}}

                                            @if (!empty($weekDays))
                                            @foreach ($weekDays as $day)
                                            @php
                                            $personId = $student->id;
                                            $status = $attendanceMap[$personId][$day['date']] ?? null;
                                            @endphp
                                            <td class="text-center attendance-cell {{ $day['is_today'] ? 'today-column' : '' }} {{ $day['is_weekend'] ? 'weekend-day' : '' }}">
                                                @if ($status === 'present')
                                                <span class="text-dark-teal fw-bold">P</span>
                                                @elseif ($status === 'absent')
                                                <span class="text-danger fw-bold">A</span>
                                                @else
                                                <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            @endforeach
                                            @endif

                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="{{ 3 + (is_array($weekDays) ? count($weekDays) : 7) }}" class="text-center text-muted py-4">
                                                No students found for the selected filters.
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                <div class="py-3 justify-content-center d-flex">
                                    {{-- Pagination --}}
                                    {{ $students->links() }}
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- add attendance modal --}}
    <div class="modal fade" id="attendanceModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="attendanceModalLabel" aria-hidden="true" wire:ignore.self>
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-uppercase" id="attendanceModalLabel">Record Attendance</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                @if (session()->has('success'))
                <div class="alert alert-dark-teal alert-dismissible fade show mt-2 mb-0  mx-4" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif

                @if (session()->has('error'))
                <div class="alert alert-danger alert-dismissible fade show mt-2 mb-0 mx-4" role="alert">
                    {{ session('error') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif
                <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                    <div class="alert alert-info alert-icon">
                        <em class="icon ni ni-alert-circle"></em>
                        <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                        <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select class, then click the Apply button to load the learners' information.</h6>
                        <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Select the learner's attendance status.</h6>
                        <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Click the submit button.</h6>
                    </div>

                    <div class="card-title-group mb-4">
                        <div class="card-tools">
                            <div class="d-flex flex-lg-row gx-3">
                                <div class="form-wrap">
                                    <div class="input-group">
                                        <select wire:model="selected_class" class="form-control form-select" required>
                                            <option value="">SELECT CLASS</option>
                                            @foreach($available_classes as $class)
                                            <option value="{{ $class->id }}">{{ strtoupper($class->name) }}</option>
                                            @endforeach
                                        </select>
                                        <div class="input-group-append">
                                            @if($show_learners)
                                            <button wire:click="resetFilter" type="button" class="btn rounded-0 bg-secondary px-2 text-white">
                                                <em class="icon ni ni-cross"></em>
                                            </button>
                                            @endif
                                            <button wire:click="applyFilter" type="button" class="btn btn-sm text-white bg-dark-teal">
                                                <span><em class="icon ni ni-filter mr-1"></em>Apply</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!-- .card-tools -->
                    </div><!-- .card-title-group -->
                    <form wire:submit="submitAttendance">
                        @if($show_learners)
                        <div class="mb-4">
                            <div class="form-group">
                                <label class="form-label text-uppercase">Attendance Date <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input required wire:model="attendance_date" placeholder="eg. 23 May, 2022" id="attendanceDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                                @error('attendance_date') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr class="nk-tb-item nk-tb-head bg-secondary">
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase border-1">
                                            <span class="sub-text ucap text-white">NAMES</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase border-1 text-center">
                                            <span class="sub-text ucap text-white">LIN</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase border-1 text-center">
                                            <span class="sub-text ucap text-white">ATTENDANCE</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if($show_learners && $learners && $learners->count() > 0)
                                    @foreach($learners as $index => $learner)
                                    <tr class="nk-tb-item">
                                        <td class="nk-tb-col">
                                            <span class="tb-lead">{{ $learner['name'] }}</span>
                                        </td>
                                        <td class="nk-tb-col text-center">
                                            <span class="tb-lead">{{ $learner['lin'] }}</span>
                                        </td>
                                        <td class="nk-tb-col text-center">
                                            <div class="form-check form-check-inline text-uppercase">
                                                <div class="form-group">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input class="custom-control-input" wire:model="attendanceData.{{ $learner['person_id'] }}" type="radio" id="present_{{ $learner['person_id'] }}" value="present" name="attendance_{{ $learner['person_id'] }}" required>
                                                        <label class="custom-control-label" for="present_{{ $learner['person_id'] }}">Present</label>
                                                    </div>
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input class="custom-control-input" wire:model="attendanceData.{{ $learner['person_id'] }}" type="radio" id="absent_{{ $learner['person_id'] }}" value="absent" name="attendance_{{ $learner['person_id'] }}" required>
                                                        <label class="custom-control-label" for="absent_{{ $learner['person_id'] }}">Absent</label>
                                                    </div>
                                                </div>
                                            </div>
                                            @error('attendanceData.' . $learner['person_id']) <span class="text-danger">{{ $message }}</span> @enderror
                                        </td>
                                    </tr>
                                    @endforeach
                                    @elseif($show_learners && $selected_class)
                                    <tr>
                                        <td colspan="3" class="py-4">
                                            <div class="alert alert-secondary alert-icon mb-0">
                                                <em class="icon ni ni-alert-circle"></em> No learners found for the selected class.
                                            </div>
                                        </td>
                                    </tr>
                                    @elseif(!$show_learners && $selected_class)
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            <div class="alert alert-warning alert-icon mb-0">
                                                <em class="icon ni ni-info-circle"></em>
                                                Click the Apply button to load learners for the selected class.
                                            </div>
                                        </td>
                                    </tr>
                                    @else
                                    <tr>
                                        <td colspan="3" class="py-4">
                                            <div class="alert alert-secondary alert-icon mb-0">
                                                <em class="icon ni ni-alert-circle"></em> There are no learners to display at the moment.
                                            </div>
                                        </td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-light" style="position: sticky; bottom: 0; z-index: 1000; border-top: 1px solid #dee2e6;">
                    <button type="button" class="btn btn-secondary text-uppercase" data-dismiss="modal">Close</button>
                    @if($show_learners && $learners && $learners->count() > 0)
                    <button type="submit" wire:click="submitAttendance" class="btn btn-primary text-uppercase" wire:loading.attr="disabled">
                        <span wire:loading.remove>Submit</span>
                        <span wire:loading>
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Submitting...
                        </span>
                    </button>
                    @endif
                </div>

            </div>
        </div>
    </div>

</div>

@push('scripts')
<script>
    $(document).ready(function() {
        $('#attendanceDate').datepicker({
            format: 'd MM, yyyy'
            , autoclose: true
            , todayHighlight: true
            , endDate: new Date()
        }).on('hide', function(e) {
            if (e.date) {
                @this.set('attendance_date', moment(e.date).format('D MMMM, YYYY'));
            }
        });

        $('#attendanceDate').datepicker('setDate', new Date());
        @this.set('attendance_date', moment().format('D MMMM, YYYY'));

        $('#attendanceModal').on('hidden.bs.modal', function() {
            @this.call('resetForm');
        });
    });

</script>
@endpush
