<?php

namespace Modules\LearnerManagement\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

class LearnerDuplicationDetectionService
{
    /**
     * Detect and store duplicate learners for a given enrollment application
     */
    public function detectAndStoreLearnerDuplicates($application): void
    {
        try {
            $applicationId = is_object($application) ? $application->id : $application['id'];
            
            Log::info("Starting duplicate detection for application {$applicationId}");
            
            $learnerDuplicates = $this->findLearnerDuplicates($application);
            
            $this->storeLearnerDuplicateMatches($applicationId, $learnerDuplicates);
            
            // Update application status if duplicates found
            if ($learnerDuplicates->isNotEmpty()) {
                Log::info("Found {$learnerDuplicates->count()} potential learner duplicates for application {$applicationId}");
            } else {
                Log::info("No learner duplicates found for application {$applicationId}");
            }
            
        } catch (Exception $e) {
            $applicationId = is_object($application) ? $application->id : $application['id'];
            Log::error("Error detecting learner duplicates for application {$applicationId}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Find learner duplicates using the PostgreSQL function with proper error handling
     */
    private function findLearnerDuplicates($application): Collection
    {
        $data = $this->extractApplicationData($application);
        
        Log::info("Searching for duplicates using PostgreSQL function", [
            'name' => ($data['first_name'] ?? '') . ' ' . ($data['surname'] ?? ''),
            'birth_date' => $data['birth_date'],
            'gender' => $data['gender'] ?? '',
            'nin' => $data['nin'] ?? ''
        ]);

        try {
            // First, let's try with a simplified parameter set to avoid type issues
            $results = DB::select("
                SELECT 
                    learner_id,
                    match_type,
                    confidence_score,
                    learner_name,
                    learner_birth_date,
                    learner_gender,
                    learner_id_number,
                    school_id,
                    parent_name,
                    parent_birth_date,
                    parent_id_number,
                    name_similarity,
                    parent_similarity,
                    date_created
                FROM find_duplicate_learners(
                    ?::text,          -- p_learner_first_name
                    ?::text,          -- p_learner_surname
                    ?::date,          -- p_learner_birth_date
                    ?::text,          -- p_learner_gender
                    ?::text,          -- p_parent_first_name
                    ?::text,          -- p_parent_surname
                    ?::date,          -- p_parent_birth_date
                    ?::text,          -- p_learner_other_names
                    ?::text,          -- p_learner_id_number
                    ?::text,          -- p_parent_other_names
                    ?::text,          -- p_parent_id_number
                    ?::real,          -- p_similarity_threshold
                    ?::bigint         -- p_exclude_person_id
                )
            ", [
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['birth_date'],
                $data['gender'] ?? '',
                $data['parent_first_name'] ?? '',
                $data['parent_surname'] ?? '',
                $data['parent_birth_date'],
                $data['other_names'] ?? '',
                $data['nin'] ?? '',
                $data['parent_other_names'] ?? '',
                $data['parent_nin'] ?? '',
                0.7,  // similarity threshold
                null  // exclude person id
            ]);

            Log::info("PostgreSQL function found " . count($results) . " potential matches");
            return collect($results);

        } catch (Exception $e) {
            Log::warning("PostgreSQL function failed, trying fallback approach: " . $e->getMessage());
            
            // Fallback to simpler approach if the function fails
            return $this->findLearnerDuplicatesFallback($data);
        }
    }

    /**
     * Fallback method if PostgreSQL function has issues
     */
    private function findLearnerDuplicatesFallback($data): Collection
    {
        Log::info("Using fallback duplicate detection method");
        
        $results = collect();
        
        if (!empty($data['nin'])) {
            $idMatches = DB::select("
                SELECT 
                    p.id as learner_id,
                    'EXACT_ID_MATCH' as match_type,
                    1.0 as confidence_score,
                    COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, '') as learner_name,
                    p.birth_date as learner_birth_date,
                    p.gender as learner_gender,
                    p.id_number as learner_id_number,
                    NULL::bigint as school_id,
                    '' as parent_name,
                    NULL::date as parent_birth_date,
                    '' as parent_id_number,
                    1.0 as name_similarity,
                    0.0 as parent_similarity,
                    p.date_created
                FROM persons p
                WHERE p.deleted_at IS NULL
                AND p.id_number = ?
                LIMIT 5
            ", [$data['nin']]);
            
            $results = $results->concat(collect($idMatches));
        }
        
        if (!empty($data['birth_date'])) {
            $nameMatches = DB::select("
                SELECT 
                    p.id as learner_id,
                    CASE 
                        WHEN similarity(
                            standardize_names(?, ?, ?),
                            COALESCE(p.standardized_name, '')
                        ) >= 0.85 THEN 'HIGH_CONFIDENCE'
                        ELSE 'MEDIUM_CONFIDENCE'
                    END as match_type,
                    similarity(
                        standardize_names(?, ?, ?),
                        COALESCE(p.standardized_name, '')
                    ) as confidence_score,
                    COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, '') as learner_name,
                    p.birth_date as learner_birth_date,
                    p.gender as learner_gender,
                    p.id_number as learner_id_number,
                    NULL::bigint as school_id,
                    '' as parent_name,
                    NULL::date as parent_birth_date,
                    '' as parent_id_number,
                    similarity(
                        standardize_names(?, ?, ?),
                        COALESCE(p.standardized_name, '')
                    ) as name_similarity,
                    0.0 as parent_similarity,
                    p.date_created
                FROM persons p
                WHERE p.deleted_at IS NULL
                AND p.birth_date = ?::date
                AND COALESCE(p.gender, '') = ?
                AND similarity(
                    standardize_names(?, ?, ?),
                    COALESCE(p.standardized_name, '')
                ) >= 0.6
                AND p.id NOT IN (
                    SELECT learner_id FROM unnest(?::bigint[]) AS learner_id WHERE learner_id IS NOT NULL
                )
                ORDER BY confidence_score DESC
                LIMIT 10
            ", [
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['other_names'] ?? '',
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['other_names'] ?? '',
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['other_names'] ?? '',
                $data['birth_date'],
                $data['gender'] ?? '',
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['other_names'] ?? '',
                array_filter($results->pluck('learner_id')->toArray()) // Exclude already found IDs
            ]);
            
            $results = $results->concat(collect($nameMatches));
        }
        
        Log::info("Fallback method found " . $results->count() . " potential matches");
        
        return $results->unique('learner_id')->sortByDesc('confidence_score')->take(10);
    }

    /**
     * Extract application data in a flexible way
     */
    private function extractApplicationData($application): array
    {
        if (is_array($application)) {
            return $application;
        }
        
        if (is_object($application)) {
            return [
                'first_name' => $application->first_name ?? null,
                'surname' => $application->surname ?? null,
                'birth_date' => $application->birth_date ?? null,
                'gender' => $application->gender ?? null,
                'other_names' => $application->other_names ?? null,
                'nin' => $application->nin ?? null,
                'parent_first_name' => $application->parent_first_name ?? null,
                'parent_surname' => $application->parent_surname ?? null,
                'parent_birth_date' => $application->parent_birth_date ?? null,
                'parent_other_names' => $application->parent_other_names ?? null,
                'parent_nin' => $application->parent_nin ?? null,
            ];
        }
        
        throw new Exception('Application data must be an array or object');
    }

    /**
     * Store learner duplicate matches in the database
     */
    private function storeLearnerDuplicateMatches(int $preRegistrationId, Collection $duplicates): void
    {
        foreach ($duplicates as $duplicate) {
            $exists = DB::table('potential_duplicate_learners')
                ->where('pre_registration_id', $preRegistrationId)
                ->where('person_id', $duplicate->learner_id)
                ->exists();

            if (!$exists) {
                DB::table('potential_duplicate_learners')->insert([
                    'pre_registration_id' => $preRegistrationId,
                    'person_id' => $duplicate->learner_id,
                    'match_type' => $duplicate->match_type,
                    'confidence_score' => $duplicate->confidence_score,
                    'metadata' => json_encode([
                        'learner_name' => $duplicate->learner_name,
                        'learner_birth_date' => $duplicate->learner_birth_date,
                        'learner_gender' => $duplicate->learner_gender,
                        'learner_id_number' => $duplicate->learner_id_number,
                        'school_id' => $duplicate->school_id,
                        'parent_name' => $duplicate->parent_name,
                        'parent_birth_date' => $duplicate->parent_birth_date,
                        'parent_id_number' => $duplicate->parent_id_number,
                        'name_similarity' => $duplicate->name_similarity,
                        'parent_similarity' => $duplicate->parent_similarity,
                        'detected_at' => now()->toISOString(),
                        'detection_method' => 'postgresql_function_with_fallback'
                    ]),
                    'verification_status' => 'pending',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                Log::info("Stored learner duplicate match", [
                    'pre_registration_id' => $preRegistrationId,
                    'matched_person_id' => $duplicate->learner_id,
                    'match_type' => $duplicate->match_type,
                    'confidence_score' => $duplicate->confidence_score
                ]);
            }
        }
    }


    /**
     * Clear all learner duplicate matches for an application
     */
    public function clearLearnerDuplicates(int $preRegistrationId): void
    {
        DB::table('potential_duplicate_learners')
            ->where('pre_registration_id', $preRegistrationId)
            ->delete();
            
        Log::info("Cleared learner duplicates for application {$preRegistrationId}");
    }

    /**
     * Get stored duplicate matches for a learner application
     */
    public function getLearnerDuplicates(int $preRegistrationId): Collection
    {
        $results = DB::table('potential_duplicate_learners')
            ->where('pre_registration_id', $preRegistrationId)
            ->get();
            
        return collect($results);
    }

    /**
     * Check if learner has any potential duplicates
     */
    public function hasLearnerDuplicates(int $preRegistrationId): bool
    {
        return DB::table('potential_duplicate_learners')
            ->where('pre_registration_id', $preRegistrationId)
            ->exists();
    }

    /**
     * Test the PostgreSQL function directly to debug issues
     */
    public function testPostgreSQLFunction($application): array
    {
        $data = $this->extractApplicationData($application);
        
        try {
            $results = DB::select("
                SELECT * FROM find_duplicate_learners(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $data['first_name'] ?? '',
                $data['surname'] ?? '',
                $data['birth_date'],
                $data['gender'] ?? '',
                $data['parent_first_name'] ?? '',
                $data['parent_surname'] ?? '',
                $data['parent_birth_date'],
                $data['other_names'] ?? '',
                $data['nin'] ?? '',
                $data['parent_other_names'] ?? '',
                $data['parent_nin'] ?? '',
                0.7,
                null
            ]);
            
            return [
                'success' => true,
                'results' => $results,
                'count' => count($results)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'results' => []
            ];
        }
    }
}