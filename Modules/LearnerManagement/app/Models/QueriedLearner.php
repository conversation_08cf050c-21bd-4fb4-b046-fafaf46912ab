<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Model;

class QueriedLearner extends Model
{
    protected $table = 'queried_learners';
    
    // Use custom timestamp columns
    const CREATED_AT = 'date_created';
    const UPDATED_AT = 'date_updated';
    
    protected $fillable = [
        'pre_registration_id',
        'duplicate_count',
        'duplicate_records',
        'query_status',
    ];

    protected $casts = [
        'duplicate_records' => 'array',
        'duplicate_count' => 'integer',
        'date_created' => 'datetime',
        'date_updated' => 'datetime',
    ];

    // Relationships
    public function learnerEnrolmentApplication()
    {
        return $this->belongsTo('Modules\EmisReturns\Models\LearnerEnrolmentApplication', 'pre_registration_id');
    }

    public function potentialDuplicates()
    {
        return $this->hasMany(PotentialDuplicateLearner::class, 'pre_registration_id', 'pre_registration_id');
    }

    // Scopes for filtering by query status
    public function scopePendingReview($query)
    {
        return $query->where('query_status', 'pending_review');
    }

    public function scopeInReview($query)
    {
        return $query->where('query_status', 'in_review');
    }

    public function scopeResolved($query)
    {
        return $query->where('query_status', 'resolved');
    }

    // Helper methods
    public function isPendingReview()
    {
        return $this->query_status === 'pending_review';
    }

    public function isInReview()
    {
        return $this->query_status === 'in_review';
    }

    public function isResolved()
    {
        return $this->query_status === 'resolved';
    }

    public function markAsInReview()
    {
        $this->update(['query_status' => 'in_review']);
    }

    public function markAsResolved()
    {
        $this->update(['query_status' => 'resolved']);
    }

    // Get duplicate records with additional details
    public function getDuplicatePersons()
    {
        if (empty($this->duplicate_records)) {
            return collect([]);
        }

        $personIds = collect($this->duplicate_records)->pluck('person_id');
        
        return \Modules\Core\Models\Person::whereIn('id', $personIds)->get();
    }

    // Add a duplicate record to the JSON array
    public function addDuplicateRecord($personId, $personName, $matchType, $confidence)
    {
        $records = $this->duplicate_records ?: [];
        
        $records[] = [
            'person_id' => $personId,
            'person_name' => $personName,
            'match_type' => $matchType,
            'confidence_score' => $confidence,
            'added_at' => now()->toISOString()
        ];

        $this->update([
            'duplicate_records' => $records,
            'duplicate_count' => count($records)
        ]);
    }
}