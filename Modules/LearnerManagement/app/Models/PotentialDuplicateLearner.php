<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Model;

class PotentialDuplicateLearner extends Model
{
    protected $table = 'potential_duplicate_learners';
    
    protected $fillable = [
        'pre_registration_id',
        'person_id',
        'match_type',
        'confidence_score',
        'metadata',
        'verification_status',
        'verified_by',
        'verified_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'confidence_score' => 'float',
        'verified_at' => 'datetime',
    ];

    public function learnerEnrolmentApplication()
    {
        return $this->belongsTo('Modules\EmisReturns\Models\LearnerEnrolmentApplication', 'pre_registration_id');
    }

    public function person()
    {
        return $this->belongsTo('Modules\Core\Models\Person', 'person_id');
    }

    public function verifiedBy()
    {
        return $this->belongsTo('App\Models\User', 'verified_by');
    }

    // Scopes for filtering by verification status
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    public function scopeVerifiedDuplicate($query)
    {
        return $query->where('verification_status', 'verified_duplicate');
    }

    public function scopeVerifiedNotDuplicate($query)
    {
        return $query->where('verification_status', 'verified_not_duplicate');
    }

    public function scopeSkipped($query)
    {
        return $query->where('verification_status', 'skipped');
    }
}