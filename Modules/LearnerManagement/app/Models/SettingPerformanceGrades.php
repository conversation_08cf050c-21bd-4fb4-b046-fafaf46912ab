<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\Core\Models\Settings\SchoolType;
use Modules\LearnerManagement\Models\PrimaryLearnerPerformance;
use Modules\LearnerManagement\Models\SecondaryLearnerPerformance;
use Modules\Core\Traits\Initials;

class SettingPerformanceGrades extends Model
{
    use HasFactory, Initials;

    protected $table = 'setting_performance_grades';

    protected $hidden = ['date_created', 'date_updated'];

    protected $appends = ['initials'];

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    public function wordForInitials()
    {
        return $this->name;
    }

    public function primary_learner_performances()
    {
        return $this->hasMany(PrimaryLearnerPerformance::class, 'performance_grade_id');
    }

    public function secondary_learner_performances()
    {
        return $this->hasMany(SecondaryLearnerPerformance::class, 'performance_grade_id');
    }

    public function school_type()
    {
        return $this->belongsTo(SchoolType::class, 'school_type_id');
    }
}
