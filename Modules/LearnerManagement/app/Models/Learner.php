<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\LearnerManagement\Database\Factories\LearnerFactory;

class Learner extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    // protected static function newFactory(): LearnerFactory
    // {
    //     // return LearnerFactory::new();
    // }
}
