<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Settings\SettingPrimarySchoolSubject;
use Modules\LearnerManagement\Models\SettingPerformanceGrades;
use Modules\Core\Traits\Initials;

class PrimaryLearnerPerformance extends Model
{
    use HasFactory, Initials;

    protected $table = 'primary_learner_performances';
    
    protected $guarded = [];


    protected $hidden = ['date_created', 'date_updated'];

    protected $appends = ['initials'];

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    public function wordForInitials()
    {
        return $this->name;
    }

    // Learner
    public function learner()
    {
        return $this->belongsTo(Person::class, 'learner_id');
    }

    // School
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id');
    }

    // Education Grade
    public function education_grade()
    {
        return $this->belongsTo(SchoolEducationGrade::class, 'education_grade_id');
    }

    // Primary Subject
    public function primary_subject()
    {
        return $this->belongsTo(SettingPrimarySchoolSubject::class, 'primary_subject_id');
    }

    //  // Performance Grade
     public function performance_grade()
     {
         return $this->belongsTo(SettingPerformanceGrades::class, 'performance_grade_id');
     }
}
