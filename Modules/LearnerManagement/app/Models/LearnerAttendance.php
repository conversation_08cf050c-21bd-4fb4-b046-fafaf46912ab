<?php

namespace Modules\LearnerManagement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Core\Models\Person;
use Modules\Core\Models\Institutions\School;

class LearnerAttendance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'learner_id',
        'school_id',
        'status',
        'date'
    ];

    public function learner()
    {
        return $this->belongsTo(Person::class, 'learner_id');
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }
}
