<?php

namespace Modules\LearnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\LearnerManagement\Services\LearnerDuplicationDetectionService;
use Modules\LearnerManagement\Jobs\DetectLearnerDuplicatesJob;

class TestDuplicateDetection extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'learner:test-duplicates {--application-id= : Test with specific application ID} {--job : Test using Job instead of direct service} {--test-function : Test PostgreSQL function directly}';

    /**
     * The console command description.
     */
    protected $description = 'Test duplicate detection functionality for learner enrollment applications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Learner Duplicate Detection System');
        $this->info('================================================');

        try {
            // Check if PostgreSQL functions exist
            $this->checkPostgreSQLFunctions();

            // Get application to test with
            $application = $this->getTestApplication();
            
            if (!$application) {
                $this->error('❌ No applications found to test with');
                return 1;
            }

            $this->info("✅ Testing with application ID: {$application->id}");
            $this->info("   Learner: {$application->first_name} {$application->surname}");
            $this->info("   Birth Date: {$application->birth_date}");

            // Create service instance
            $service = new LearnerDuplicationDetectionService();

            // Test PostgreSQL function directly if requested
            if ($this->option('test-function')) {
                $this->testPostgreSQLFunction($service, $application);
                return 0;
            }

            // Test using Job or Service directly
            if ($this->option('job')) {
                $this->testWithJob($application);
            } else {
                $this->testWithService($service, $application);
            }

            // Show results
            $this->showResults($application->id);

        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    private function checkPostgreSQLFunctions(): void
    {
        $this->info('🔧 Checking PostgreSQL functions...');
        
        // Check if standardize_names function exists
        $functions = DB::select("
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name IN ('standardize_names', 'find_duplicate_learners')
        ");

        $functionNames = collect($functions)->pluck('routine_name');
        
        if ($functionNames->contains('standardize_names')) {
            $this->info('   ✅ standardize_names function found');
        } else {
            $this->warn('   ⚠️  standardize_names function not found');
        }

        if ($functionNames->contains('find_duplicate_learners')) {
            $this->info('   ✅ find_duplicate_learners function found');
        } else {
            $this->warn('   ⚠️  find_duplicate_learners function not found');
        }

        // Check if trigram extension is enabled
        $extensions = DB::select("SELECT * FROM pg_extension WHERE extname = 'pg_trgm'");
        if (count($extensions) > 0) {
            $this->info('   ✅ pg_trgm extension enabled');
        } else {
            $this->warn('   ⚠️  pg_trgm extension not found');
        }
    }

    private function getTestApplication(): ?object
    {
        $applicationId = $this->option('application-id');
        
        if ($applicationId) {
            $application = DB::table('learner_enrolment_applications')
                ->where('id', $applicationId)
                ->first();
                
            if (!$application) {
                $this->error("Application with ID {$applicationId} not found");
                return null;
            }
            
            return $application;
        }

        // Get the most recent application
        return DB::table('learner_enrolment_applications')
            ->orderBy('id', 'desc')
            ->first();
    }

    private function testWithService(LearnerDuplicationDetectionService $service, object $application): void
    {
        $this->info('🧪 Testing with Service directly...');
        
        // Clear any existing duplicates first
        $service->clearLearnerDuplicates($application->id);
        
        // Convert to array for service
        $applicationArray = (array) $application;
        
        // Run detection
        $startTime = microtime(true);
        $service->detectAndStoreLearnerDuplicates($applicationArray);
        $endTime = microtime(true);
        
        $duration = round(($endTime - $startTime) * 1000, 2);
        $this->info("   ✅ Detection completed in {$duration}ms");
    }

    private function testWithJob(object $application): void
    {
        $this->info('🧪 Testing with Job (async)...');
        
        // Dispatch the job
        DetectLearnerDuplicatesJob::dispatch($application->id);
        $this->info('   ✅ Job dispatched successfully');
        $this->info('   ⏳ Job will run asynchronously - check queue worker logs');
    }

    private function testPostgreSQLFunction(LearnerDuplicationDetectionService $service, object $application): void
    {
        $this->info('🧪 Testing PostgreSQL function directly...');
        
        $result = $service->testPostgreSQLFunction($application);
        
        if ($result['success']) {
            $this->info("   ✅ PostgreSQL function executed successfully");
            $this->info("   📊 Found {$result['count']} results");
            
            if ($result['count'] > 0) {
                $this->info("   🔍 Sample results:");
                foreach (array_slice($result['results'], 0, 3) as $match) {
                    $this->info("      - ID: {$match->learner_id}, Name: {$match->learner_name}, Match: {$match->match_type}");
                }
            }
        } else {
            $this->error("   ❌ PostgreSQL function failed: {$result['error']}");
        }
    }

    private function showResults(int $applicationId): void
    {
        $this->info('📊 Results:');
        $this->info('============');

        // Count duplicates found
        $duplicates = DB::table('potential_duplicate_learners')
            ->where('pre_registration_id', $applicationId)
            ->get();

        if ($duplicates->isEmpty()) {
            $this->info('   ✅ No duplicates found');
        } else {
            $this->info("   🔍 Found {$duplicates->count()} potential duplicates:");
            
            foreach ($duplicates as $duplicate) {
                $metadata = json_decode($duplicate->metadata, true);
                $this->info("      - Person ID: {$duplicate->person_id}");
                $this->info("        Match Type: {$duplicate->match_type}");
                $this->info("        Confidence: " . number_format($duplicate->confidence_score * 100, 1) . "%");
                $this->info("        Name: " . ($metadata['learner_name'] ?? 'N/A'));
                $this->info("        ---");
            }
        }

        // Check application status
        $updatedApp = DB::table('learner_enrolment_applications')
            ->where('id', $applicationId)
            ->first();
            
        $this->info("   📋 Application Status: {$updatedApp->approval_status}");
    }
}