<?php

namespace Modules\LearnerManagement\Livewire;

use Livewire\Component;
use Modules\LearnerManagement\Models\Learner;
use Modules\Core\Models\Settings\SchoolType;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;


class LearnerNumbers extends Component
{
    use InstitutionContext;
    public $totalLearners;
    public $schoolTypes;
    public $institution;
    public $institutionId;
    public $grades = [];
    public $classes = [];
    public $selected_grade_id = '';
    public $searchActive = false;
    public $selected_gender = '';
    public $selected_status = '';
    public $gender = '';
    public $date = '';
    /**
     * @var array<int, array{
     *     class: string,
     *     gender: string,
     *     count: int,
     *     present_percent: float,
     *     absent_percent: float
     * }>
     */
    public $learnersBySexPerClass = [];
    public $searchTerm = '';
    public $startDate = '';
    public $endDate = '';
    public $genderStats = [];
    public $showGenderStats = false;
    public $filtersApplied = false;





    public function mount()
    {
        // $this->totalLearners = Learner::count();
        $this->institution = $this->getInstitution();

        $this->schoolTypes = SchoolType::all();
        $this->grades = SchoolEducationGrade::schoolType($this->institution->school_type_id)->get();

    }

    private function initializeContext(){
        $this->institutionId = $this->institution->id;
    }

     public function updated($propertyName)
     {
         // Set search as active when any filter has a value
         $this->searchActive = !empty($this->selected_grade_id) ||
             !empty($this->selected_gender) ||
             !empty($this->selected_status) ||
             !empty($this->date) ||
             !empty($this->selected_status) ||
            !empty($this->startDate) ||
            !empty($this->endDate) ||
             !empty($this->searchTerm);

         if ($propertyName === 'searchTerm' && strlen($this->searchTerm) > 0 && strlen($this->searchTerm) < 2) {
             return;
         }

         Log::info("Filter updated: {$propertyName}");
     }

     public function search()
     {
         $this->searchActive = true;

         if ($this->date && !$this->isValidDate($this->date)) {
             $this->addError('date', 'Please select a valid date');
             return;
         }
     }


     public function resetSearch()
     {
         $this->reset([
             'selected_grade_id',
             'selected_gender',
             'selected_status',
             'startDate',
             'endDate',
             'searchTerm',
             'genderStats',
             'showGenderStats',
             'selected_status',
             'date',
         ]);
         $this->searchActive = false;
         $this->filtersApplied = false;
     }

     public function fetchLearnerStats()
    {
        $classStats = DB::table('learner_attendances as la')
            ->join('persons as p', 'la.learner_id', '=', 'p.id')
            ->join('learners as l', 'la.learner_id', '=', 'l.person_id')
            ->join('setting_education_grades as seg', 'l.current_education_grade_id', '=', 'seg.id')
            ->where('l.current_school_id', $this->institutionId)
            ->whereDate('la.date', '=', now()->toDateString()) // <-- filter by today
            ->select(
                'seg.name as class',
                'p.gender',
                'la.learner_id',
                DB::raw("MAX(CASE WHEN la.status = 'present' THEN 1 ELSE 0 END) as was_present")
            )
            ->groupBy('seg.name', 'p.gender', 'la.learner_id')
            ->get()
            ->groupBy(fn($row) => $row->class . '_' . $row->gender);

        $this->learnersBySexPerClass = collect();

        foreach ($classStats as $key => $learners) {
            [$class, $gender] = explode('_', $key);
            $total = $learners->count();
            $presentCount = collect($learners)->filter(fn($l) => $l->was_present)->count();
            $absentCount = $total - $presentCount;

            $presentPercent = $total > 0 ? round(($presentCount / $total) * 100, 1) : 0;
            $absentPercent = 100 - $presentPercent;

            $this->learnersBySexPerClass[] = (object)[
                'class' => $class,
                'gender' => $gender,
                'count' => $total,
                'present_percent' => $presentPercent,
                'absent_percent' => $absentPercent,
                'presentCount' => $presentCount,
                'absentCount' => $absentCount,
            ];
        }
    }


        public function applyFilters()
        {
            $this->filtersApplied = true;

            $classQuery = DB::table('learner_attendances as a')
                ->join('persons as p', 'a.learner_id', '=', 'p.id')
                ->join('learners as l', 'p.id', '=', 'l.person_id')
                ->join('setting_education_grades as g', 'l.current_education_grade_id', '=', 'g.id')
                ->where('a.school_id', $this->institution->id);

            if (!empty($this->selected_gender)) {
                $classQuery->where('p.gender', $this->selected_gender);
            }

            if (!empty($this->selected_grade_id)) {
                $classQuery->where('l.current_education_grade_id', $this->selected_grade_id);
            }

            if (!empty($this->startDate)) {
                $classQuery->whereDate('a.date', '>=', $this->startDate);
            }

            if (!empty($this->endDate)) {
                $classQuery->whereDate('a.date', '<=', $this->endDate);
            }

            if (empty($this->startDate) && empty($this->endDate)) {
                $today = now()->toDateString();
                $classQuery->whereDate('a.date', '=', $today);

                $this->startDate = $today;
                $this->endDate = $today;
            }

            $classStats = $classQuery
                ->select(
                    DB::raw('g.name as class'),
                    'p.gender',
                    DB::raw('COUNT(DISTINCT a.learner_id) as total'),
                    DB::raw("COUNT(DISTINCT CASE WHEN a.status = 'present' THEN a.learner_id ELSE NULL END) as present"),
                    DB::raw("COUNT(DISTINCT CASE WHEN a.status = 'absent' THEN a.learner_id ELSE NULL END) as absent")
                )
                ->groupBy('g.name', 'p.gender')
                ->get();


            $this->learnersBySexPerClass = [];
            foreach ($classStats as $row) {
            $presentPercent = $row->total > 0
                ? round(($row->present / ($row->present + $row->absent)) * 100, 1)
                : 0;
            $absentPercent = 100 - $presentPercent;

                // if ($this->selected_status !== null && $this->selected_status !== '') {
                //     if ($presentPercent < (int) $this->selected_status) {
                //         continue;
                //     }
                // }

                $this->learnersBySexPerClass[] = (object)[
                    'class' => $row->class,
                    'gender' => $row->gender,
                    'count' => $row->total,
                    'present_percent' => $presentPercent,
                    'absent_percent' => $absentPercent,
                    'presentCount' => (int) $row->present,
                    'absentCount' => (int) $row->absent
                ];
            }
        }


     public function render()
     {
         $this->initializeContext();

         if ($this->searchActive) {
            $this->applyFilters();
        } else {
            $this->fetchLearnerStats();
        }

         return view('learnermanagement::livewire.learner-numbers')
            ->extends('core::institution.layouts.design');
     }
}
