<?php

namespace Modules\LearnerManagement\Livewire;

use Livewire\Component;
use Modules\LearnerManagement\Models\LearnerAttendance as LearnerAttendanceModel;
use Modules\Core\Models\Person;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Settings\SchoolType;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Livewire\WithPagination;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Institutions\Learner;
class LearnerAttendance extends Component
{
    use WithPagination;
    use InstitutionContext;
    public $class = '';
    public array $weekDays = [];
    public $search = '';
    public $schoolTypes;
    public $institution;
    public $institutionId;
    public $grades = [];
    public $classes = [];
    public $startOfWeek;
    public $currentDate;

    public $selected_grade_id = '';
    public $selected_gender = '';
    public $selected_status = '';
    public $date = null;
    public $searchTerm = '';
    public $searchActive = false;
    public $gender;
    public $status;

    public array $selectedStudents = [];
    public bool $selectAll = false;
    public $selectedClassId = null;
    public $attendance = [];
    protected $updatesQueryString = ['page'];
    public $learnerId;
    protected $paginationTheme = 'bootstrap';

    // add attendance variables
    public $learners;
    public $school;
    public $attendance_date;
    public $attendanceData = [];
    public $selected_class;
    public $available_classes;
    public $show_learners = false;

    protected $rules = [
        'attendance_date' => 'required|date',
        'attendanceData.*' => 'required|in:present,absent',
    ];

    public function mount()
    {
        // add attendance
        $user = auth()->user();

        if ($user->hasRole('institution-admin')) {
            $this->school = $user->school;
        } elseif ($user->hasRole('institution-user')) {
            $this->school = $user->school_contact->school;
        }

        $this->loadAvailableClasses();
        $this->learners = collect();

        $this->institution = $this->getInstitution();
        $this->currentDate = now()->toDateString();
        $this->generateWeekDays($this->currentDate);

        $this->schoolTypes = SchoolType::all();
        if ($this->institution->school_type->name === 'international') {
            $school = School::with([
                'international_curriculums.grades:id,inter_sch_curriculum_id,name,inter_sch_education_level_id'
            ])->find($this->institution->id);
            $this->grades = $school->international_curriculums
                ->flatMap(fn($curriculum) => $curriculum->grades)
                ->values();
        } else {
            $this->grades = SchoolEducationGrade::schoolType($this->institution->school_type_id)
                ->select('id', 'name')
                ->get();
        }
    }

    private function initializeContext($survey): void
    {
        $this->institutionId = $this->institution->id;
    }

    // add attendance methods
    public function loadLearners()
    {
        if (!$this->school || !$this->selected_class) {
            $this->learners = collect();
            return;
        }

        $this->learners = Learner::with(['person'])
            ->where('current_school_id', $this->school->id)
            ->where('current_education_grade_id', $this->selected_class)
            ->whereHas('person')
            ->get()
            ->map(function ($learner) {
                $educationGrade = SchoolEducationGrade::find($this->selected_class);
                return [
                    'person_id' => $learner->person_id,
                    'lin' => $learner->lin,
                    'school_id' => $this->school->id,
                    'name' => $learner->person->full_name ?? ($learner->person->first_name . ' ' . $learner->person->last_name),
                ];
            });
    }

    public function loadAvailableClasses()
    {
        if (!$this->school) {
            return;
        }
        if ($this->school->school_type_id === 7) {
            $this->available_classes = SettingInterSchEducationGrade::select('id', 'name')->get();
        } else {
            $this->available_classes = SchoolEducationGrade::where('school_type_id', $this->school->school_type_id)
                ->select('id', 'name', 'school_type_id', 'grade_rank')
                ->orderBy('grade_rank')
                ->get();
        }
    }

    public function applyFilter()
    {
        $this->validate([
            'selected_class' => 'required'
        ], [
            'selected_class.required' => 'Please select a class to proceed.'
        ]);

        $this->show_learners = true;
        $this->attendanceData = [];
        $this->loadLearners();
    }

    public function resetFilter()
    {
        $this->selected_class = '';
        $this->show_learners = false;
        $this->learners = collect();
        $this->attendanceData = [];
        $this->resetErrorBag();
    }

    public function updatedSelectedClass()
    {
        $this->show_learners = false;
        $this->attendanceData = [];
        $this->learners = collect();
    }

    public function submitAttendance()
    {
        try {
            $attendance_date = Carbon::createFromFormat('j F, Y', $this->attendance_date)->format('Y-m-d');

            foreach ($this->attendanceData as $learnerId => $status) {
                $existingAttendance = LearnerAttendanceModel::where('learner_id', $learnerId)
                    ->where('school_id', $this->school->id)
                    ->where('date', $attendance_date)
                    ->first();
                if ($existingAttendance) {
                    $existingAttendance->update(['status' => $status]);
                } else {
                    LearnerAttendanceModel::create([
                        'learner_id' => $learnerId,
                        'school_id' => $this->school->id,
                        'date' => $attendance_date,
                        'status' => $status
                    ]);
                }
            }

            session()->flash('success', 'Attendance recorded successfully!');
            $this->resetForm();

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to record attendance.' . $e);
        }
    }

    public function resetForm()
    {
        $this->attendance_date = Carbon::now()->format('d F, Y');
        $this->attendanceData = [];
        $this->selected_class = '';
        $this->show_learners = false;
        $this->learners = collect();
        $this->resetErrorBag();
    }

    private function generateWeekDays($referenceDate = null)
    {
        $date = $referenceDate ? Carbon::parse($referenceDate) : Carbon::now();
        $startOfWeek = $date->startOfWeek();
        $this->weekDays = [];

        for ($i = 0; $i < 7; $i++) {
            $day = $startOfWeek->copy()->addDays($i);
            $this->weekDays[] = [
                'label' => $day->format('d M'),
                'day' => $day->format('l'), // Monday, Tuesday...
                'is_today' => $day->isToday(),
                'is_weekend' => $day->isWeekend(),
                'date' => $day->toDateString(),
            ];
        }

        $this->startOfWeek = $startOfWeek->toDateString();
    }

    public function previousWeek()
    {
        $this->currentDate = Carbon::parse($this->currentDate)->subWeek()->toDateString();
        $this->generateWeekDays($this->currentDate);
        $this->searchLearner();
    }

    public function nextWeek()
    {
        $this->currentDate = Carbon::parse($this->currentDate)->addWeek()->toDateString();
        $this->generateWeekDays($this->currentDate);
        $this->searchLearner();
    }

    public function updatedDate($value)
    {
        $this->currentDate = $value;
        $this->generateWeekDays($value);
        $this->searchLearner();
    }

    protected $listeners = ['refreshStudents'];

    private function isValidDate($date)
    {
        return \DateTime::createFromFormat('Y-m-d', $date) !== false;
    }

    /* public function updatedSelectAll($value)
     {
         if ($value) {
             $this->selectedStudents = collect($this->students)->pluck('id')->toArray();
         } else {
             $this->selectedStudents = [];
         }
     } */

    /* public function toggleSelectAll()
    {
        if (count($this->selectedStudents) < $this->students->count()) {
            $this->selectedStudents = $this->students->pluck('id')->toArray();
        } else {
            $this->selectedStudents = [];
        }
    } */

    // Add a property to track if filters should be applied
    public $filtersApplied = false;

    private function applyFilters($query)
    {
        if (!$this->filtersApplied) {
            return;
        }

        if (!empty($this->selected_grade_id)) {
            $query->whereHas('learner', function ($learnerQuery) {
                if ($this->institution->school_type->name === 'international') {
                    $learnerQuery->where('inter_sch_education_grade_id', $this->selected_grade_id);
                } else {
                    $learnerQuery->where('current_education_grade_id', $this->selected_grade_id);
                }
            });
        }

        if (!empty($this->selected_gender)) {
            $query->where('gender', strtoupper($this->selected_gender));
        }

        if (!empty($this->selected_status) || !empty($this->date)) {
            $attendanceQuery = LearnerAttendanceModel::query();

            if (!empty($this->selected_status)) {
                $attendanceQuery->where('status', $this->selected_status);
            }

            if (!empty($this->date)) {
                $attendanceQuery->whereDate('date', $this->date);
            }

            $personIdsWithAttendance = $attendanceQuery->distinct()->pluck('learner_id');
            $query->whereIn('id', $personIdsWithAttendance);
        }

        if (!empty($this->searchTerm)) {
            $searchTerm = trim($this->searchTerm);
            if (strlen($searchTerm) >= 2) {
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('persons.first_name', 'like', "%{$searchTerm}%")
                        ->orWhere('persons.surname', 'like', "%{$searchTerm}%")
                        ->orWhereRaw("CONCAT(persons.first_name, ' ', persons.surname) LIKE ?", ["%{$searchTerm}%"]);
                    $q->orWhereHas('learner', function ($learnerQuery) use ($searchTerm) {
                        $learnerQuery->where('lin', 'like', "%{$searchTerm}%");
                    });
                });
            }
        }
    }

    private function getActiveFilters()
    {
        return [
            'grade' => $this->selected_grade_id,
            'gender' => $this->selected_gender,
            'status' => $this->selected_status,
            'date' => $this->date,
            'search' => $this->searchTerm
        ];
    }

    private function updateSearchState()
    {
        $this->searchActive = !empty($this->selected_grade_id) ||
            !empty($this->selected_gender) ||
            !empty($this->selected_status) ||
            !empty($this->date) ||
            !empty($this->searchTerm);
    }

    public function searchLearner()
    {
        if (!empty($this->searchTerm)) {
            $this->searchTerm = strtoupper($this->searchTerm);
        }

        if ($this->date && !$this->isValidDate($this->date)) {
            $this->addError('date', 'Please select a valid date');
            return;
        }

        $this->filtersApplied = true;
        $this->updateSearchState();
        $this->resetPage();
    }

    public function resetSearch()
    {
        $this->reset(['selected_grade_id', 'selected_gender', 'selected_status', 'date', 'searchTerm']);
        $this->filtersApplied = false;
        $this->searchActive = false;
        $this->resetPage();
    }

    public function getStudentsProperty()
    {
        try {
            $personIds = LearnerAttendanceModel::distinct('learner_id')->pluck('learner_id');

            $query = Person::query()
                ->whereIn('id', $personIds)
                ->whereHas('learner', function ($learnerQuery) {
                    $learnerQuery->where('current_school_id', $this->institution->id);
                })
                ->with(['learner.school']);

            // Apply filters only when search button has been clicked
            $this->applyFilters($query);
            $students = $query->orderBy('persons.first_name')->paginate(10);

            return $students;
        } catch (\Exception $e) {
            Log::error('Error in getStudentsProperty: ' . $e->getMessage());
            return collect()->paginate(10);
        }
    }

    public function getAttendanceMapProperty()
    {
        $attendanceMap = [];

        try {
            $students = $this->getStudentsProperty();
            $weekDaysCollection = collect($this->weekDays);
            $startDate = Carbon::parse($weekDaysCollection->first()['date'])->toDateString();
            $endDate = Carbon::parse($weekDaysCollection->last()['date'])->toDateString();

            foreach ($students as $student) {
                $personId = $student->id;
                $records = LearnerAttendanceModel::where('learner_id', $personId)
                    ->whereBetween('date', [$startDate, $endDate])
                    ->get();

                foreach ($records as $record) {
                    $attendanceMap[$personId][$record->date] = $record->status;
                }
            }

            return $attendanceMap;
        } catch (\Exception $e) {
            Log::error('Error in getAttendanceMapProperty: ' . $e->getMessage());
            return [];
        }
    }

    public function render()
    {
        try {
            return view('learnermanagement::livewire.learner-attendance', [
                'weekDays' => $this->weekDays,
                'students' => $this->students,
                'attendanceMap' => $this->attendanceMap,
            ])
                ->extends('core::institution.layouts.design')
                ->section('content');
        } catch (\Exception $e) {
            Log::error('Error in render method: ' . $e->getMessage());

            return view('learnermanagement::livewire.learner-attendance', [
                'weekDays' => $this->weekDays,
                'students' => collect(),
                'attendanceMap' => [],
            ]);
        }
    }
}
