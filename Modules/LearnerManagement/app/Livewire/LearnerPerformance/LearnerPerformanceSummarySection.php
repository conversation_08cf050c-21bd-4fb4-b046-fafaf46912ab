<?php

namespace Modules\LearnerManagement\Livewire\LearnerPerformance;

use Livewire\Component;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\LearnerManagement\Models\PrimaryLearnerPerformance;
use Modules\LearnerManagement\Models\SettingPerformanceGrades;
use Illuminate\Support\Facades\DB;
use Modules\Core\Models\Settings\SecondarySchoolSubject;
use Modules\Core\Models\Settings\SettingPrimarySchoolSubject;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\LearnerManagement\Models\SecondaryLearnerPerformance;

class LearnerPerformanceSummarySection extends Component
{
    use InstitutionContext;
    public $primary_performance_grades = [];
    public $secondary_performance_grades  = [];
    public $primary_education_grades = [];
    public $secondary_education_grades = [];
    public $performanceStats = [];
    public $classes = [];

    public $filtersApplied = false;

    public $institution;
    public $institutionId;
    public $termId;
    public $academicYearId;
    public $isLoading = false;

    public $education_grade_id;
    public $primary_subject_id;
    public $secondary_subject_id;

    public $selectedClassId;
    public $selectedSubjectId;

    public $primary_subjects = [];
    public $secondary_subjects = [];

    public $totalMale = 0;
    public $totalFemale = 0;
    public $totalLearners = 0;


    public function mount()
    {
        $this->initializeContext();

        $this->loadPerformanceGrades();
        $this->loadEducationGrades();
        $this->countLearnerPerformanceGrades();
        $this->loadSubjects();
    }


    // Initialize the context with institution
    private function initializeContext(): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }


    //reactive function to get class name(education grade )
    function get_class_name($gradeId)
    {
        if ($this->institution->school_type_id === 2) {
            return SchoolEducationGrade::find($gradeId)?->name ?? 'CLASS-' . $gradeId;
        } elseif ($this->institution->school_type_id === 3) {
            return SchoolEducationGrade::find($gradeId)?->name ?? 'CLASS-' . $gradeId;
        }
    }

    //reactive function to get subject names
    function get_subject_name($subjectId)
    {
        if ($this->institution->school_type_id === 2) {
            return $subjectId && is_numeric($subjectId)
                ? (SettingPrimarySchoolSubject::find($subjectId)?->name ?? 'SUBJ-' . $subjectId)
                : 'SUBJ-' . ($subjectId ?: 'N/A');
        } elseif ($this->institution->school_type_id === 3) {
            return $subjectId && is_numeric($subjectId)
                ? (SecondarySchoolSubject::find($subjectId)?->name ?? 'SUBJ-' . $subjectId)
                : 'SUBJ-' . ($subjectId ?: 'N/A');
        }
    }

    //reactive function to get performance grades
    function get_grade_name($performanceGradeId)
    {
        return SettingPerformanceGrades::find($performanceGradeId)?->name ?? 'GRADE-' . $performanceGradeId;
    }



    //reactive properties
    public function updatedEducationGradeId()
    {
        $this->countLearnerPerformanceGrades();
    }

    public function updatedPrimarySubjectId()
    {
        $this->countLearnerPerformanceGrades();
    }

    public function updatedSecondarySubjectId()
    {
        $this->countLearnerPerformanceGrades();
    }


    //load subjects
    public function loadSubjects()
    {
        $this->primary_subjects = SettingPrimarySchoolSubject::select('id', 'name')->get();
        $this->secondary_subjects = SecondarySchoolSubject::select('id', 'name')->get();
    }

    //load education grades
    public function loadEducationGrades()
    {
        $this->primary_education_grades = SchoolEducationGrade::where('school_type_id', 2)->orderBy('name', 'asc')->get();
        $this->secondary_education_grades = SchoolEducationGrade::where('school_type_id', 3)->orderBy('name', 'asc')->get();
    }

    //load performance grades
    public function loadPerformanceGrades()
    {
        $this->primary_performance_grades = SettingPerformanceGrades::where('is_for_a_level_yn', false)->get();
        $this->secondary_performance_grades = SettingPerformanceGrades::all();
    }



    //aggregates for all learner performances
    public function countLearnerPerformanceGrades()
    {
        $schoolId = $this->institution->id;

        // Determine if filters are applied
        $this->filtersApplied = $this->selectedClassId || $this->selectedSubjectId;
        // $this->academicYearId = $this->institution->academic_year_id ?? get_active_academic_year()->id;

        if ($this->institution->school_type_id === 2) {
            $query = PrimaryLearnerPerformance::query()
                ->join('persons', 'persons.id', '=', 'primary_learner_performances.learner_id')
                // ->join('learner_enrolments', 'learner_enrolnments.academeic_year_id', '=', 'primary_learner_performances.teaching_period_id')
                ->where('primary_learner_performances.school_id', $schoolId)
                // ->where('learner_enrolnments.academic_year_id', $this->academicYearId)
                ->where('primary_learner_performances.teaching_period_id', $this->termId);

            if ($this->selectedClassId) {
                $query->where('primary_learner_performances.education_grade_id', $this->selectedClassId);
            }

            if ($this->selectedSubjectId) {
                $query->where('primary_learner_performances.primary_subject_id', $this->selectedSubjectId);
            }

            $this->performanceStats = $query
                ->select(
                    'primary_learner_performances.education_grade_id',
                    'primary_learner_performances.primary_subject_id',
                    'primary_learner_performances.performance_grade_id',
                    DB::raw("persons.gender AS gender"),
                    DB::raw("COUNT(*) AS total")
                )
                ->groupBy(
                    'primary_learner_performances.education_grade_id',
                    'primary_learner_performances.primary_subject_id',
                    'primary_learner_performances.performance_grade_id',
                    'persons.gender'
                )
                ->orderBy('primary_learner_performances.education_grade_id')
                ->orderBy('primary_learner_performances.primary_subject_id')
                ->orderBy('primary_learner_performances.performance_grade_id')
                ->get()
                ->toArray();

            $this->totalMale = 0;
            $this->totalFemale = 0;
            $this->totalLearners = 0;

            foreach ($this->performanceStats as $stat) {
                if (strtolower($stat['gender']) === 'm') {
                    $this->totalMale += $stat['total'];
                } elseif (strtolower($stat['gender']) === 'f') {
                    $this->totalFemale += $stat['total'];
                }
            }

            $this->totalLearners = $this->totalMale + $this->totalFemale;
        } elseif ($this->institution->school_type_id === 3) {
            $query = SecondaryLearnerPerformance::query()
                ->join('persons', 'persons.id', '=', 'secondary_learner_performances.learner_id')
                // ->join('learner_enrolments', 'learner_enrolnments.academeic_year_id', '=', 'primary_learner_performances.teaching_period_id')
                ->where('secondary_learner_performances.school_id', $schoolId)
                // ->where('learner_enrolnments.academic_year_id', $this->academicYearId)
                ->where('secondary_learner_performances.teaching_period_id', $this->termId);

            if ($this->selectedClassId) {
                $query->where('secondary_learner_performances.education_grade_id', $this->selectedClassId);
            }

            if ($this->selectedSubjectId) {
                $query->where('secondary_learner_performances.secondary_subject_id', $this->selectedSubjectId);
            }

            $this->performanceStats = $query
                ->select(
                    'secondary_learner_performances.education_grade_id',
                    'secondary_learner_performances.secondary_subject_id',
                    'secondary_learner_performances.performance_grade_id',
                    DB::raw("persons.gender AS gender"),
                    DB::raw("COUNT(*) AS total")
                )
                ->groupBy(
                    'secondary_learner_performances.education_grade_id',
                    'secondary_learner_performances.secondary_subject_id',
                    'secondary_learner_performances.performance_grade_id',
                    'persons.gender'
                )
                ->orderBy('secondary_learner_performances.education_grade_id')
                ->orderBy('secondary_learner_performances.secondary_subject_id')
                ->orderBy('secondary_learner_performances.performance_grade_id')
                ->get()
                ->toArray();

            $this->totalMale = 0;
            $this->totalFemale = 0;
            $this->totalLearners = 0;

            foreach ($this->performanceStats as $stat) {
                $gender = strtolower(trim($stat['gender'] ?? ''));

                if ($gender === 'm') {
                    $this->totalMale += $stat['total'];
                } elseif ($gender === 'f') {
                    $this->totalFemale += $stat['total'];
                }
            }

            $this->totalLearners = $this->totalMale + $this->totalFemale;
        }
    }


    //reset filters
    public function resetFilters()
    {

        $this->selectedClassId = '';
        $this->selectedSubjectId = '';
        $this->countLearnerPerformanceGrades();
    }


    public function render()
    {
        return view('learnermanagement::livewire.learner-performance.learner-performance-summary-section')
            ->extends('core::institution.layouts.design')
            ->section('content');
    }
}
