<?php

namespace Modules\LearnerManagement\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Modules\LearnerManagement\Models\SettingPerformanceGrades;

class SettingPerformanceGradesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
       

        $now = Carbon::now();

        // primary and O level grades
        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'D1'],
            ['is_for_a_level_yn' => false,  'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'D2'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'C3'],
            ['is_for_a_level_yn' => false,  'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'C4'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'C5'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'C6'],
            ['is_for_a_level_yn' => false,  'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'P7'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'P8'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'F9'],
            ['is_for_a_level_yn' => false, 'date_created' => $now, 'date_updated' => $now]
        );

        // A-Level grades
        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'A'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'B'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'C'],
            ['is_for_a_level_yn' => true,  'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'D'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'E'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'O'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );

        SettingPerformanceGrades::firstOrCreate(
            ['name' => 'F'],
            ['is_for_a_level_yn' => true, 'date_created' => $now, 'date_updated' => $now]
        );
    }
}
