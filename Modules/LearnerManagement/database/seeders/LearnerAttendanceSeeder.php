<?php

namespace Modules\LearnerManagement\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class LearnerAttendanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
public function run()
{
    $now = Carbon::now();

    for ($i = 1; $i <= 10; $i++) {
        DB::table('learner_attendances')->insert([
            'learner_id' => rand(1, 5), // random ids
            'school_id' => rand(1, 3),
            'date' => Carbon::now()->subDays($i)->toDateString(),
            'status' => rand(0, 1) ? 'present' : 'absent',
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }
}
}
