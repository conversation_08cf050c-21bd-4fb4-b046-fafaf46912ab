<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('setting_performance_grades', function (Blueprint $table) {

            $table->id()
                ->comment('unique autogenerated primary key');

            $table->string('name')
                ->nullable(false)
                ->comment('The name of the performance grade');

            $table->boolean('is_for_a_level_yn')
                ->nullable(false)
                ->default(false)
                ->comment('1 if grade only applies to A-Level. O if not');

            $table->foreignId('school_type_id')
                ->nullable(false)
                ->constrained('setting_school_types')
                ->onDelete('cascade')
                ->comment('Foreign key from setting_school_types table');

            $table->timestamp('date_created')
                ->comment('Date-time timestamp assigned at creation of the record')
                ->nullable(false);

            $table->timestamp('date_updated')
                ->comment('Date-time timestamp assigned at creation of the record')
                ->nullable(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('setting_performance_grades', function (Blueprint $table) {

            $table->dropForeign(['school_type_id']);


            $table->dropColumn([
                'name',
                'is_for_a_level_yn',
                'school_type_id',
                'date_created',
                'date_updated',
            ]);
        });

        Schema::dropIfExists('setting_performance_grades');
    }
};
