<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('potential_duplicate_learners', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pre_registration_id')->constrained('learner_enrolment_applications')->onDelete('cascade');
            $table->foreignId('person_id')->constrained('persons')->onDelete('cascade');
            $table->string('match_type');
            $table->float('confidence_score');
            $table->json('metadata')->nullable();         
            $table->enum('verification_status', ['pending', 'verified_duplicate', 'verified_not_duplicate', 'skipped'])->default('pending');
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('verified_at')->nullable();            
            $table->timestamps();         
        });

        DB::statement("COMMENT ON TABLE potential_duplicate_learners IS 'System-generated possible duplicate person matches for pre-registrations with manual verification tracking.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.pre_registration_id IS 'Reference to the incoming pre-registration record.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.person_id IS 'Matched person ID from the canonical persons table.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.match_type IS 'Match classification such as EXACT_ID_MATCH, HIGH_CONFIDENCE, etc.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.confidence_score IS 'Numeric score between 0–1 indicating similarity strength.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.metadata IS 'JSON containing match details like learner name, birth date, similarity scores, etc.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.verification_status IS 'Manual verification status: pending, verified_duplicate, verified_not_duplicate, skipped.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.verified_by IS 'User ID who performed the manual verification.'");
        DB::statement("COMMENT ON COLUMN potential_duplicate_learners.verified_at IS 'Timestamp when manual verification was completed.'");
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('potential_duplicate_learners');
    }
};
