<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_learners(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, REAL, BIGINT)');
        
        DB::statement("
            CREATE OR REPLACE FUNCTION find_duplicate_learners(
                p_learner_first_name TEXT,
                p_learner_surname TEXT,
                p_learner_birth_date DATE,
                p_learner_gender TEXT,
                p_parent_first_name TEXT DEFAULT '',
                p_parent_surname TEXT DEFAULT '',
                p_parent_birth_date DATE DEFAULT NULL,
                p_learner_other_names TEXT DEFAULT '',
                p_learner_id_number TEXT DEFAULT '',
                p_parent_other_names TEXT DEFAULT '',
                p_parent_id_number TEXT DEFAULT '',
                p_similarity_threshold REAL DEFAULT 0.7,
                p_exclude_person_id BIGINT DEFAULT NULL
            )
            RETURNS TABLE(
                learner_id BIGINT,
                match_type TEXT,
                confidence_score REAL,
                learner_name TEXT,
                learner_birth_date DATE,
                learner_gender TEXT,
                learner_id_number TEXT,
                school_id BIGINT,
                parent_name TEXT,
                parent_birth_date DATE,
                parent_id_number TEXT,
                name_similarity REAL,
                parent_similarity REAL,
                date_created TIMESTAMP
            ) AS \$function\$
            BEGIN
                RETURN QUERY
                SELECT 
                    p.id,
                    CASE 
                        WHEN COALESCE(p.id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, '')
                            THEN 'EXACT_ID_MATCH'::TEXT
                        WHEN similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= 0.95 AND p.birth_date = p_learner_birth_date AND COALESCE(p.gender, '') = COALESCE(p_learner_gender, '')
                            THEN 'EXACT_LEARNER_MATCH'::TEXT
                        WHEN similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= 0.85 AND p.birth_date = p_learner_birth_date
                            THEN 'HIGH_CONFIDENCE'::TEXT
                        ELSE 'MEDIUM_CONFIDENCE'::TEXT
                    END,
                    
                    GREATEST(
                        CASE 
                            WHEN COALESCE(p.id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, '') 
                            THEN 1.0::REAL 
                            ELSE 0.0::REAL 
                        END,
                        similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        )::REAL
                    ),
                    
                    (COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, ''))::TEXT,
                    p.birth_date,
                    COALESCE(p.gender, '')::TEXT,
                    COALESCE(p.id_number, '')::TEXT,
                    NULL::BIGINT,
                    ''::TEXT,
                    NULL::DATE,
                    ''::TEXT,
                    similarity(
                        standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                        COALESCE(p.standardized_name, '')
                    )::REAL,
                    0.0::REAL,
                    p.date_created
                    
                FROM persons p
                WHERE p.deleted_at IS NULL 
                AND (p_exclude_person_id IS NULL OR p.id != p_exclude_person_id)
                AND (
                    -- Exact ID matches
                    (COALESCE(p_learner_id_number, '') != '' AND COALESCE(p.id_number, '') = COALESCE(p_learner_id_number, ''))
                    
                    OR
                    
                    -- Name and birth date similarity
                    (
                        p.birth_date = p_learner_birth_date
                        AND COALESCE(p.gender, '') = COALESCE(p_learner_gender, '')
                        AND similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) >= (p_similarity_threshold - 0.2)
                    )
                    
                    OR
                    
                    -- Trigram search with birth date
                    (
                        p.standardized_name IS NOT NULL
                        AND p.standardized_name != ''
                        AND p.standardized_name % standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names)
                        AND p.birth_date = p_learner_birth_date
                        AND similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            p.standardized_name
                        ) >= (p_similarity_threshold - 0.2)
                    )
                )
                AND similarity(
                    standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                    COALESCE(p.standardized_name, '')
                ) >= (p_similarity_threshold - 0.2)
                ORDER BY 3 DESC, 12 DESC, p.date_created ASC
                LIMIT 20;
            END;
            \$function\$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_learners(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, REAL, BIGINT)');
    }
};