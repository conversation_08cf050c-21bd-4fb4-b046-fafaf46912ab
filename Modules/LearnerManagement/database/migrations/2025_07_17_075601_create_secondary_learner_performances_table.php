<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('secondary_learner_performances', function (Blueprint $table) {

            $table->id()
                ->comment('unique autogenerated primary key');

            $table->foreignId('learner_id')
                ->nullable(false)
                ->constrained('persons')
                ->onDelete('cascade')
                ->comment('Foreign key from PERSONS supertype table');

            $table->foreignId('school_id')
                ->nullable(false)
                ->constrained('schools')
                ->onDelete('cascade')
                ->comment('Foreign key from schools table');

            $table->foreignId('education_grade_id')
                ->nullable(false)
                ->constrained('setting_education_grades')
                ->onDelete('cascade')
                ->comment('The class/grade of the learner. Foreign key from setting_education_grades table');

            $table->foreignId('teaching_period_id')
                ->nullable(false)
                ->constrained('setting_teaching_periods')
                ->onDelete('cascade')
                ->comment('Foreign key from setting_teaching_periods table');

            $table->foreignId('secondary_subject_id')
                ->nullable(false)
                ->constrained('setting_secondary_school_subjects')
                ->onDelete('cascade')
                ->comment('Foreign key from setting_secondary_school_subjects table');

            $table->foreignId('performance_grade_id')
                ->nullable(false)
                ->constrained('setting_performance_grades')
                ->onDelete('cascade')
                ->comment('Foreign key from setting_performance_grades table');

            $table->timestamp('date_created')
                ->comment('Date-time timestamp assigned at creation of the record')
                ->nullable(false);

            $table->timestamp('date_updated')
                ->comment('Date-time timestamp assigned at creation of the record')
                ->nullable(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('secondary_learner_performances', function (Blueprint $table) {

            $table->dropForeign(['learner_id']);
            $table->dropForeign(['school_id']);
            $table->dropForeign(['education_grade_id']);
            $table->dropForeign(['teaching_period_id']);
            $table->dropForeign(['secondary_subject_id']);
            $table->dropForeign(['performance_grade_id']);


            $table->dropColumn([
                'learner_id',
                'school_id',
                'education_grade_id',
                'teaching_period_id',
                'secondary_subject_id',
                'performance_grade_id',
                'date_created',
                'date_updated',
            ]);
        });


        Schema::dropIfExists('secondary_learner_performances');
    }
};
