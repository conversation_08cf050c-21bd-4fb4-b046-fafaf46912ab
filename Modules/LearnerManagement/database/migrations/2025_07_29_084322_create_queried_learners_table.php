<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (Schema::hasTable('queried_learners') === false) {
            Schema::create('queried_learners', function (Blueprint $table) {
                $table->id()->comment('autogenerated unique id');
                $table->foreignId('pre_registration_id')->constrained('learner_enrolment_applications')->onDelete('cascade');
                $table->integer('duplicate_count');
                $table->json('duplicate_records'); 
                $table->enum('query_status', ['pending_review', 'in_review', 'resolved'])->default('pending_review');
                $table->timestamp('date_created')->nullable()->comment('Date-time timestamp assigned at creation of the record');
                $table->timestamp('date_updated')->nullable()->comment('Date-time timestamp assigned on update of the record');
                
                $table->index('pre_registration_id');
                $table->index('query_status');
            });

            DB::statement("COMMENT ON TABLE queried_learners IS 'Applications queried for having multiple potential duplicate learners requiring manual review.'");
            DB::statement("COMMENT ON COLUMN queried_learners.pre_registration_id IS 'Reference to the learner enrollment application.'");
            DB::statement("COMMENT ON COLUMN queried_learners.duplicate_count IS 'Number of potential duplicate persons found for this application.'");
            DB::statement("COMMENT ON COLUMN queried_learners.duplicate_records IS 'JSON array containing basic info of all duplicate persons found.'");
            DB::statement("COMMENT ON COLUMN queried_learners.query_status IS 'Status of the duplicate review process: pending_review, in_review, resolved.'");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('queried_learners');
    }
};
