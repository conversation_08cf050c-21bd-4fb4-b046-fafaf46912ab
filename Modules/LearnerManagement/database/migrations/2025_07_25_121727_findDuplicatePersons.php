<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void {
         DB::statement('CREATE EXTENSION IF NOT EXISTS pg_trgm');
        
        // Create standardize_names function for name normalization
        DB::statement("
            CREATE OR REPLACE FUNCTION standardize_names(first_name TEXT, surname TEXT, other_names TEXT DEFAULT '')
            RETURNS TEXT AS \$\$
            BEGIN
                -- Handle NULL values and combine all names in alphabetical order
                RETURN array_to_string(
                    ARRAY(
                        SELECT UPPER(TRIM(unnest))
                        FROM unnest(string_to_array(
                            COALESCE(first_name, '') || ' ' || 
                            COALESCE(surname, '') || ' ' || 
                            COALESCE(other_names, ''), ' '
                        ))
                        WHERE TRIM(unnest) != '' AND LENGTH(TRIM(unnest)) > 0
                        ORDER BY UPPER(TRIM(unnest))
                    ), ' '
                );
            END;
            \$\$ LANGUAGE plpgsql IMMUTABLE;
        ");
        
        // function to handle name updates
        DB::statement("
            CREATE OR REPLACE FUNCTION update_person_standardized_name()
            RETURNS TRIGGER AS \$\$
            BEGIN
                -- The GENERATED column will automatically update, but we can add logging here
                -- Log name changes for audit if needed
                IF OLD.first_name IS DISTINCT FROM NEW.first_name 
                   OR OLD.surname IS DISTINCT FROM NEW.surname 
                   OR OLD.other_names IS DISTINCT FROM NEW.other_names THEN
                    -- Could insert into audit log here if needed
                    -- INSERT INTO person_name_changes_log (person_id, old_names, new_names, changed_at)
                    -- VALUES (NEW.id, OLD.first_name || ' ' || OLD.surname, NEW.first_name || ' ' || NEW.surname, NOW());
                    NULL;
                END IF;
                RETURN NEW;
            END;
            \$\$ LANGUAGE plpgsql;
        ");
        
        // trigger to automatically update standardized names when person data changes
        DB::statement("
            DROP TRIGGER IF EXISTS trigger_update_person_standardized_names ON persons;
        ");
        
        DB::statement("
            CREATE TRIGGER trigger_update_person_standardized_names
                AFTER UPDATE ON persons
                FOR EACH ROW
                WHEN (OLD.first_name IS DISTINCT FROM NEW.first_name 
                      OR OLD.surname IS DISTINCT FROM NEW.surname 
                      OR OLD.other_names IS DISTINCT FROM NEW.other_names)
                EXECUTE FUNCTION update_person_standardized_name();
        ");
        
        // Add computed column to persons table for standardized names
        DB::statement("
            ALTER TABLE persons 
            ADD COLUMN IF NOT EXISTS standardized_name TEXT 
            GENERATED ALWAYS AS (standardize_names(first_name::text, surname::text, other_names::text)) STORED
        ");
        
        // Create GIN index for fast trigram searching on persons
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_persons_standardized_name_gin 
            ON persons USING gin(standardized_name gin_trgm_ops)
        ");
        
        // Additional indexes for exact matching
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_persons_birth_date 
            ON persons(birth_date) WHERE birth_date IS NOT NULL AND deleted_at IS NULL
        ");
        
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_persons_id_number_active 
            ON persons(id_number) WHERE id_number IS NOT NULL AND deleted_at IS NULL
        ");
        
        // Composite index for learner duplicate detection
        DB::statement("
            CREATE INDEX IF NOT EXISTS idx_persons_learner_composite 
            ON persons(birth_date, gender, id_number) 
            WHERE birth_date IS NOT NULL AND deleted_at IS NULL
        ");
        
        // Function 1: Find LEARNER duplicates with parent context
        DB::statement("
            CREATE OR REPLACE FUNCTION find_duplicate_learners(
                p_learner_first_name TEXT,
                p_learner_surname TEXT,
                p_learner_birth_date DATE,
                p_learner_gender TEXT,
                p_parent_first_name TEXT DEFAULT '',
                p_parent_surname TEXT DEFAULT '',
                p_parent_birth_date DATE DEFAULT NULL,
                p_learner_other_names TEXT DEFAULT '',
                p_learner_id_number TEXT DEFAULT '',
                p_parent_other_names TEXT DEFAULT '',
                p_parent_id_number TEXT DEFAULT '',
                p_similarity_threshold REAL DEFAULT 0.7,
                p_exclude_person_id BIGINT DEFAULT NULL
            )
            RETURNS TABLE(
                learner_id BIGINT,
                match_type TEXT,
                confidence_score REAL,
                learner_name TEXT,
                learner_birth_date DATE,
                learner_gender TEXT,
                learner_id_number TEXT,
                school_id BIGINT,
                parent_name TEXT,
                parent_birth_date DATE,
                parent_id_number TEXT,
                name_similarity REAL,
                parent_similarity REAL,
                date_created TIMESTAMP
            ) AS \$\$
            BEGIN
                RETURN QUERY
                WITH learner_matches AS (
                    SELECT 
                        p.id as learner_person_id,
                        COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, '') AS learner_full_name,
                        p.birth_date,
                        p.gender,
                        p.id_number,
                        p.date_created,
                        l.current_school_id,
                        
                        -- Calculate learner name similarity
                        similarity(
                            standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                            COALESCE(p.standardized_name, '')
                        ) AS learner_name_sim
                        
                    FROM persons p
                    JOIN learners l ON p.id = l.person_id
                    WHERE p.deleted_at IS NULL 
                    AND l.deleted_at IS NULL
                    AND (p_exclude_person_id IS NULL OR p.id != p_exclude_person_id)
                    AND (
                        -- Tier 1: Exact ID matches
                        (p_learner_id_number != '' AND p.id_number = p_learner_id_number)
                        
                        -- Tier 2: High name similarity with birth date match
                        OR (
                            p.birth_date = p_learner_birth_date
                            AND COALESCE(p.gender, '') = p_learner_gender
                            AND similarity(
                                standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                                COALESCE(p.standardized_name, '')
                            ) >= p_similarity_threshold
                        )
                        
                        -- Tier 3: Trigram search with birth date
                        OR (
                            COALESCE(p.standardized_name, '') % standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names)
                            AND p.birth_date = p_learner_birth_date
                            AND similarity(
                                standardize_names(p_learner_first_name, p_learner_surname, p_learner_other_names),
                                COALESCE(p.standardized_name, '')
                            ) >= (p_similarity_threshold - 0.2)
                        )
                    )
                ),
                learner_with_parents AS (
                    SELECT 
                        lm.*,
                        COALESCE(parent_p.first_name, '') || ' ' || COALESCE(parent_p.surname, '') AS parent_full_name,
                        parent_p.birth_date as parent_birth_date,
                        parent_p.id_number as parent_id_number,
                        
                        -- Calculate parent similarity if parent info provided
                        CASE 
                            WHEN p_parent_first_name != '' OR p_parent_surname != '' THEN
                                similarity(
                                    standardize_names(p_parent_first_name, p_parent_surname, p_parent_other_names),
                                    COALESCE(parent_p.standardized_name, '')
                                )
                            ELSE 0.0
                        END AS parent_name_sim
                        
                    FROM learner_matches lm
                    LEFT JOIN learner_parents lp ON lm.learner_person_id = lp.learner_id AND lp.deleted_at IS NULL
                    LEFT JOIN persons parent_p ON lp.parent_id = parent_p.id AND parent_p.deleted_at IS NULL
                )
                SELECT 
                    lwp.learner_person_id,
                    CASE 
                        WHEN lwp.id_number IS NOT NULL AND lwp.id_number = p_learner_id_number
                            THEN 'EXACT_ID_MATCH'
                        WHEN lwp.learner_name_sim >= 0.95 AND lwp.birth_date = p_learner_birth_date AND lwp.gender = p_learner_gender
                            THEN 'EXACT_LEARNER_MATCH'
                        WHEN lwp.learner_name_sim >= 0.85 AND lwp.birth_date = p_learner_birth_date
                            THEN 'HIGH_CONFIDENCE'
                        ELSE 'MEDIUM_CONFIDENCE'
                    END AS match_type,
                    
                    -- Confidence score based primarily on learner data
                    GREATEST(
                        CASE WHEN (lwp.id_number IS NOT NULL AND lwp.id_number = p_learner_id_number) THEN 1.0::REAL ELSE 0.0::REAL END,
                        lwp.learner_name_sim::REAL
                    ) AS confidence_score,
                    
                    lwp.learner_full_name,
                    lwp.birth_date,
                    lwp.gender,
                    lwp.id_number,
                    lwp.current_school_id,
                    lwp.parent_full_name,
                    lwp.parent_birth_date,
                    lwp.parent_id_number,
                    lwp.learner_name_sim::REAL,
                    lwp.parent_name_sim::REAL,
                    lwp.date_created
                    
                FROM learner_with_parents lwp
                WHERE lwp.learner_name_sim >= (p_similarity_threshold - 0.2)  -- Filter based on learner similarity
                ORDER BY confidence_score DESC, learner_name_sim DESC, parent_name_sim DESC, date_created ASC;
            END;
            \$\$ LANGUAGE plpgsql;
        ");
        
        // Function 2: Find PARENT duplicates (focus on parent data only)
        DB::statement("
            CREATE OR REPLACE FUNCTION find_duplicate_parents(
                p_first_name TEXT,
                p_surname TEXT,
                p_birth_date DATE,
                p_gender TEXT,
                p_other_names TEXT DEFAULT '',
                p_id_number TEXT DEFAULT '',
                p_similarity_threshold REAL DEFAULT 0.8,
                p_exclude_person_id BIGINT DEFAULT NULL
            )
            RETURNS TABLE(
                person_id BIGINT,
                match_type TEXT,
                confidence_score REAL,
                person_name TEXT,
                birth_date DATE,
                gender TEXT,
                id_number TEXT,
                name_similarity REAL,
                total_children INTEGER,
                date_created TIMESTAMP
            ) AS \$\$
            BEGIN
                RETURN QUERY
                WITH candidate_matches AS (
                    SELECT 
                        p.id,
                        COALESCE(p.first_name, '') || ' ' || COALESCE(p.surname, '') AS person_name,
                        p.birth_date,
                        p.gender,
                        p.id_number,
                        p.date_created,
                        
                        -- Calculate name similarity
                        similarity(
                            standardize_names(p_first_name, p_surname, p_other_names),
                            COALESCE(p.standardized_name, '')
                        ) AS name_sim,
                        
                        -- Count how many children this parent has (for context)
                        (
                            SELECT COUNT(DISTINCT lp.learner_id)
                            FROM learner_parents lp
                            WHERE lp.parent_id = p.id 
                            AND lp.deleted_at IS NULL
                        ) AS child_count
                        
                    FROM persons p
                    WHERE p.deleted_at IS NULL
                    AND (p_exclude_person_id IS NULL OR p.id != p_exclude_person_id)
                    AND (
                        -- Tier 1: Exact ID matches
                        (p_id_number != '' AND p.id_number = p_id_number)
                        
                        -- Tier 2: High name similarity with birth date match
                        OR (
                            p.birth_date = p_birth_date
                            AND COALESCE(p.gender, '') = p_gender
                            AND similarity(
                                standardize_names(p_first_name, p_surname, p_other_names),
                                COALESCE(p.standardized_name, '')
                            ) >= p_similarity_threshold
                        )
                        
                        -- Tier 3: Trigram search with birth date
                        OR (
                            COALESCE(p.standardized_name, '') % standardize_names(p_first_name, p_surname, p_other_names)
                            AND p.birth_date = p_birth_date
                            AND similarity(
                                standardize_names(p_first_name, p_surname, p_other_names),
                                COALESCE(p.standardized_name, '')
                            ) >= (p_similarity_threshold - 0.1)
                        )
                    )
                )
                SELECT 
                    cm.id,
                    CASE 
                        WHEN cm.id_number IS NOT NULL AND cm.id_number = p_id_number
                            THEN 'EXACT_ID_MATCH'
                        WHEN cm.name_sim >= 0.95 AND cm.birth_date = p_birth_date AND cm.gender = p_gender
                            THEN 'EXACT_PARENT_MATCH'
                        WHEN cm.name_sim >= 0.85 AND cm.birth_date = p_birth_date
                            THEN 'HIGH_CONFIDENCE'
                        ELSE 'MEDIUM_CONFIDENCE'
                    END AS match_type,
                    
                    -- Confidence score based on parent data only
                    GREATEST(
                        CASE WHEN (cm.id_number IS NOT NULL AND cm.id_number = p_id_number) THEN 1.0::REAL ELSE 0.0::REAL END,
                        cm.name_sim::REAL
                    ) AS confidence_score,
                    
                    cm.person_name,
                    cm.birth_date,
                    cm.gender,
                    cm.id_number,
                    cm.name_sim::REAL,
                    cm.child_count::INTEGER,
                    cm.date_created
                    
                FROM candidate_matches cm
                WHERE cm.name_sim >= (p_similarity_threshold - 0.1)  -- Filter low-confidence matches
                ORDER BY confidence_score DESC, name_sim DESC, date_created ASC;
            END;
            \$\$ LANGUAGE plpgsql;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
          // Drop trigger first
        DB::statement('DROP TRIGGER IF EXISTS trigger_update_person_standardized_names ON persons');
        
        // Drop trigger function
        DB::statement('DROP FUNCTION IF EXISTS update_person_standardized_name()');
        
        // Drop main functions
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_parents(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, REAL, BIGINT)');
        DB::statement('DROP FUNCTION IF EXISTS find_duplicate_learners(TEXT, TEXT, DATE, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, REAL, BIGINT)');
        
        // Drop indexes
        DB::statement('DROP INDEX IF EXISTS idx_persons_learner_composite');
        DB::statement('DROP INDEX IF EXISTS idx_persons_id_number_active');
        DB::statement('DROP INDEX IF EXISTS idx_persons_birth_date');
        DB::statement('DROP INDEX IF EXISTS idx_persons_standardized_name_gin');
        
        // Drop computed column
        DB::statement('ALTER TABLE persons DROP COLUMN IF EXISTS standardized_name');
        
        // Drop standardize_names function
        DB::statement('DROP FUNCTION IF EXISTS standardize_names(TEXT, TEXT, TEXT)');
    }
};
