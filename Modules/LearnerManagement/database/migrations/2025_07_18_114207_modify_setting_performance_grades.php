<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('setting_performance_grades', function (Blueprint $table) {
            
            $table->dropForeign(['school_type_id']);

         
            $table->foreignId('school_type_id')
                ->nullable()
                ->change();

         
            $table->foreign('school_type_id')
                ->references('id')
                ->on('setting_school_types')
                ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('setting_performance_grades', function (Blueprint $table) {
            $table->dropForeign(['school_type_id']);

            $table->foreignId('school_type_id')
                ->nullable(false)
                ->change();

            $table->foreign('school_type_id')
                ->references('id')
                ->on('setting_school_types')
                ->onDelete('cascade');
        });
    }
};
