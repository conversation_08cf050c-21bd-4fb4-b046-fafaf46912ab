<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learner_attendances', function (Blueprint $table) {
            $table->id()->comment('The unique ID generated automatically.');

            $table->foreignId('learner_id')
                ->constrained('persons')
                ->onDelete('cascade')
                ->comment('The foreign key from the persons table.');

            $table->foreignId('school_id')
                ->constrained('schools')
                ->onDelete('cascade')
                ->comment('The foreign key from the schools table.');

            $table->date('date')
                ->comment('The date of the attendance record.');

            $table->enum('status', ['present', 'absent'])
                ->default('present')
                ->comment('The attendance status of the learner for the given date.');

            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learner_attendances');
    }
};
