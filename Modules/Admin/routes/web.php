<?php

use Illuminate\Support\Facades\Route;
use Modules\Admin\Http\Controllers\AdminController;
use Modules\Admin\Livewire\AdminUnitsDistricts;
use Modules\Admin\Livewire\RegionAdminUnit;
use Modules\Admin\Livewire\AdminUnitsSubCounties;
use Modules\Admin\Livewire\AdminUnitSubRegions;
use Modules\Admin\Livewire\AdminUnitsCounties;
use Modules\Admin\Livewire\AdminUnitVillages;
use Modules\Admin\Livewire\AdminUnitsParishes;
use Modules\Admin\Livewire\Clusters;
use Modules\Admin\Livewire\AdminUnitsUpperLocalGovernments;
use Modules\Admin\Livewire\AdminUnitsLowerLocalGovernments;
use Modules\Admin\Livewire\Settings\LearnerFlaggingReasons;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('admins', AdminController::class)->names('admin');
});

Route::get('/admin/manage-districts', AdminUnitsDistricts::class)->name('admin.manage-districts');
Route::get('/admin/manage-regions', RegionAdminUnit::class)->name('admin.manage-regions');
Route::get('/admin/manage-subcounties', AdminUnitsSubCounties::class)->name('admin.manage-subcounties');
Route::get('/admin/manage-sub-regions', AdminUnitSubRegions::class)->name('admin.manage-subregions');
Route::get('/admin/manage-counties',AdminUnitsCounties::class)->name('admin.manage-counties');
Route::get('/admin/manage-villages', AdminUnitVillages::class)->name('admin.manage-villages');
Route::get('/admin/manage-parishes', AdminUnitsParishes::class)->name('admin.manage-parishes');
Route::get('/admin/manage-clusters', Clusters::class)->name('admin.manage-clusters');
Route::get('/admin/manage-upper-local-governments',AdminUnitsUpperLocalGovernments::class)->name('admin.manage-upper-local-governments');
Route::get('/admin/manage-lower-local-governments', AdminUnitsLowerLocalGovernments::class)->name('admin.manage-lower-local-governments');
Route::get('admin/learner-flagging-reasons', LearnerFlaggingReasons::class)->name('learner-flagging-reasons');


Route::get('/subcounties-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/subcounties-sample.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/subregions-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/sample_sub_regions_import.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/counties-sample', function () {
    $samplepath = base_path('Modules/Admin/public/assets/files/counties-sample.xlsx');

    if (!file_exists($samplepath)) {
        abort(404);
    }

    return response()->download($samplepath);
});


Route::get('/villages-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/villages-sample.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/parishes-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/parishes-sample.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/upper-local-governments-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/upper-local-governments-sample.xlsx';

   if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/lower-local-governments-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/lower-local-governments-sample.xlsx';

   if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});


Route::get('/districts-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/districts-sample.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});

Route::get('/clusters-sample', function () {
    $path = module_path('Admin') . '/public/assets/files/clusters-sample.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});



Route::get('/sample-regions', function () {
    $path = module_path('Admin') . '/public/assets/files/sample-regions-import.xlsx';

    if (!file_exists($path)) {
        abort(404);
    }

    return response()->download($path);
});

