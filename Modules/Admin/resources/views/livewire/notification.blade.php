<div style="position: relative;">
    @if($message)
        <div style="
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            background-color: {!! $type === 'error' ? '#ffe0e0' : '#e0f0ff' !!};
            color: {{ $type === 'error' ? '#660000' : '#003366' }};
            padding: 15px 20px;
            border: 1px solid {{ $type === 'error' ? '#ffb3b3' : '#b3d8ff' }};
            border-radius: 4px;
            font-size: 16px;
            width: 100%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        ">
            <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                <div style="display: flex; flex-direction: column; gap: 6px; flex: 1;">
                    <span style="display: inline-flex; align-items: center; gap: 6px;">
                        @if($type === 'error')
                            <!-- Error icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                 stroke-width="1.5" stroke="currentColor" width="30" height="30" style="flex-shrink: 0;">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M12 9v3m0 3h.01M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                        @else
                            <!-- Success/info icon -->
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                 stroke-width="1.5" stroke="currentColor" width="30" height="30" style="flex-shrink: 0;">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                        @endif
                        {{ $message }}
                    </span>

                    @if (!empty($details))
                        <ul style="
                            margin-top: 8px;
                            padding-left: 36px;
                            list-style-type: disc;
                            font-size: 14px;
                            color: {{ $type === 'error' ? '#990000' : '#004080' }};
                        ">
                            @foreach ($details as $detail)
                                <li>{{ $detail }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>

                <button 
                    onclick="this.parentElement.parentElement.style.display='none';" 
                    style="
                        background: none;
                        border: none;
                        color: inherit;
                        font-size: 20px;
                        font-weight: bold;
                        cursor: pointer;
                        padding: 0;
                        margin-left: 15px;
                        line-height: 1;
                    " 
                    onmouseover="this.style.color='#ff3333';" 
                    onmouseout="this.style.color='inherit';"
                >&times;</button>
            </div>
        </div>
    @endif
</div>
