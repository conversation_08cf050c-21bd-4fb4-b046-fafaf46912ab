<div class="row">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">School Name:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ $application['school']['name'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Name:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $application['full_name'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ $application['gender'] === 'M' ? 'MALE' : ($application['gender'] === 'F' ? 'FEMALE' : '') }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Date Of Birth:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ $application['birth_date'] ? \Carbon\Carbon::parse($application['birth_date'])->format('d M, Y') : '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Country:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $application['country']['name'] ?? '' }}</h6>
    </div>
</div>
@if(!empty($application['nin']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">NIN:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['nin'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['student_pass']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Student Pass:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['student_pass'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['student_refugee_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Refugee Number:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['student_refugee_number'] }}</h6>
        </div>
    </div>
@endif
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Class:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $application['education_grade']['name'] ?? '' }}</h6>
    </div>
</div>
@if(!empty($application['district_of_birth_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">District Of Birth:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['district_of_birth']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['index_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Index Number:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['index_number'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['exam_year']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Exam Year:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['exam_year'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['equated_code']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Equated Code:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['equated_code'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['equated_year']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Equated Year:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['equated_year'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['exam_level']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Exam Level:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['exam_level'] }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['code_type']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Code Type:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['code_type'] }}</h6>
        </div>
    </div>
@endif
@if((!empty($application['post_primary_institution_course_id']) && !empty($application['post_primary_institution_course'])) || (!empty($application['institution_examined_course_id']) && !empty($application['institution_examined_course'])))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Course:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">
                {{ $application['post_primary_institution_course']['name'] ?? ($application['institution_examined_course']['name'] ?? '') }}
            </h6>
        </div>
    </div>
@endif
@if(!empty($application['orphan_type']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Orphanage Status:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">
                @if($application['orphan_type'] === 'father') ONLY FATHER DEAD
                @elseif($application['orphan_type'] === 'mother') ONLY MOTHER DEAD
                @elseif($application['orphan_type'] === 'both-dead') BOTH PARENTS DEAD
                @endif
            </h6>
        </div>
    </div>
@endif
@if(!empty($application['familiar_language_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Familiar Language:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['familiar_language']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['inter_sch_calendar_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Calendar:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['international_calendar']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['inter_sch_curriculum_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Curriculum:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['international_curriculum']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['inter_sch_education_grade_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Grade:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['international_education_grade']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif
@if(!empty($application['inter_sch_education_level_id']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Level:</span></h6>
        </div>
        <div class="col-lg-8">
            <h6 class="style-head text-uppercase">{{ $application['international_education_level']['name'] ?? '' }}</h6>
        </div>
    </div>
@endif

