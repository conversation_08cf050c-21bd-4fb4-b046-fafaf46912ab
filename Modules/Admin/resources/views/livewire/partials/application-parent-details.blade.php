<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Names:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head text-uppercase">{{ $application['parent_full_name'] ?? '' }}</h6>
    </div>
</div>
@if(!empty($application['parent_nin']) || !empty($application['parent_passport']) || !empty($application['parent_refugee_number']))
    <div class="row mt-1">
        <div class="col-lg-4">
            @if(!empty($application['parent_nin']))
                <h6 class="style-head"><span class="text-gray">NIN:</span></h6>
            @endif
            @if(!empty($application['parent_passport']))
                <h6 class="style-head"><span class="text-gray">Passport No:</span></h6>
            @endif
            @if(!empty($application['parent_refugee_number']))
                <h6 class="style-head"><span class="text-gray">Refugee No:</span></h6>
            @endif
        </div>
        <div class="col-lg-8">
            @if(!empty($application['parent_nin']))
                <h6 class="style-head text-uppercase">{{ $application['parent_nin'] }}</h6>
            @endif
            @if(!empty($application['parent_passport']))
                <h6 class="style-head text-uppercase">{{ $application['parent_passport'] }}</h6>
            @endif
            @if(!empty($application['parent_refugee_number']))
                <h6 class="style-head text-uppercase">{{ $application['parent_refugee_number'] }}</h6>
            @endif
        </div>
    </div>
@endif
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="style-head">{{ $application['parent_gender'] === 'M' ? 'MALE' : ($application['parent_gender'] === 'F' ? 'FEMALE' : '') }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Relationship:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6 class="text-uppercase">{{ $application['parent_relationship'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Country:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $application['parent_country']['name'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Email:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $application['parent_email'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Phone No.1:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $application['parent_phone_1'] ?? '' }}</h6>
    </div>
</div>
<div class="row mt-1">
    <div class="col-lg-4">
        <h6 class="style-head"><span class="text-gray">Phone No.2:</span></h6>
    </div>
    <div class="col-lg-8">
        <h6>{{ $application['parent_phone_2'] ?? '' }}</h6>
    </div>
</div>
@if(!empty($application['parent_photo']))
    <div class="row mt-1">
        <div class="col-lg-4">
            <h6 class="style-head"><span class="text-gray">Photo:</span></h6>
        </div>
        <div class="col-lg-8">
            <img class="py-3 rounded-0" src="{{ $application['parent_photo'] }}" alt="{{ $application['parent_full_name'] }}" style="max-width: 120px;">
        </div>
    </div>
@endif
