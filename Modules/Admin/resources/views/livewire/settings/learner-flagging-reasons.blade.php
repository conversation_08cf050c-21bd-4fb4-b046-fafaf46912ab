<div>
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Manage Learner Flagging Reasons</h3>
                <div class="nk-block-des text-soft">
                    <p>Total Learner Flagging Reasons: {{ $flaggingReasons->total() }}</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a class="cursor btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu">
                        <em class="icon ni ni-menu-alt-r"></em>
                    </a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li>
                                <a href="#" class="cursor btn bg-dark-teal" data-toggle="modal"
                                    data-target="#flaggingReasonModal">
                                    <em class="icon ni ni-plus-circle-fill text-white"></em>
                                    <span>Add Learner Flagging Reason</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-stretch">
            <div class="card-inner-group">
                <div class="row card-inner">
                    <div class="col-lg-8">
                        <div class="row">
                            <div class="form-wrap col-lg-6">
<form wire:submit.prevent="search">
    <div class="input-group">
        <input type="text"
               wire:model.debounce.500ms="searchTerm"
               class="form-control text-lowercase bg-primary-dim"
               placeholder="Quick Search">

        <div class="input-group-append">
            {{-- Clear button appears only after Apply --}}
            @if ($searchActive)
                <button type="button"
                        wire:click="resetSearch"
                        class="btn rounded-0 bg-secondary px-2 text-white">
                    <em class="icon ni ni-cross"></em>
                </button>
            @endif

            <button type="submit" class="btn rounded-right bg-dark-teal">
                <em class="icon ni ni-filter mr-1"></em>
                Apply
            </button>
        </div>
    </div>
</form>


                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-inner p-0">
                    <livewire:emis-returns.notification-banner />
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col nk-tb-col-check">
                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                    <input type="checkbox" class="custom-control-input" id="checkIdAll">
                                    <label class="custom-control-label" for="checkIdAll"></label>
                                </div>
                            </div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">Display Name</span>
                            </div>
                            <div class="nk-tb-col w-20 text-center text-uppercase"><span
                                    class="sub-text text-white">ACTIONS</span></div>
                        </div>

                        @forelse ($flaggingReasons as $reason)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input type="checkbox" class="custom-control-input"
                                            id="checkId{{ $reason->id }}">
                                        <label class="custom-control-label" for="checkId{{ $reason->id }}"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-dark text-uppercase">
                                    <span class="d-block">{{ $reason->name }}</span>
                                </div>
                                <div class="nk-tb-col text-center text-dark text-uppercase">
                                    <span class="cursor badge badge-dark-teal btn bg-dark-teal"
                                        wire:click="edit({{ $reason->id }})" data-toggle="modal"
                                        data-target="#flaggingReasonModal">EDIT</span>

                                    @if (!$reason->is_visible)
                                        <span class="cursor badge badge-primary"
                                            wire:click="toggleVisibility({{ $reason->id }})" style="cursor: pointer;">
                                            ENABLE
                                        </span>
                                    @else
                                        <span class="cursor badge badge-danger"
                                            wire:click="toggleVisibility({{ $reason->id }})" style="cursor: pointer;">
                                            DISABLE
                                        </span>
                                    @endif
                                </div>

                            </div>
                        @empty
                            <div class="p-5">
                                <div class="alert alert-secondary alert-icon">
                                    <em class="icon ni ni-alert-circle"></em> No learner flagging reasons to display at
                                    the moment...
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>

                <div class="card-inner d-flex flex-row justify-content-between align-items-center">
                    <div class="pagination-wrapper">
                        <nav aria-label="Pagination Navigation">
                            <ul class="pagination mb-0">
                                @if ($flaggingReasons->onFirstPage())
                                    <li class="page-item disabled">
                                        <span class="page-link">Previous</span>
                                    </li>
                                @else
                                    <li class="page-item">
                                        <a class="page-link" href="{{ $flaggingReasons->previousPageUrl() }}"
                                            rel="prev">Previous</a>
                                    </li>
                                @endif

                                {{--  Laravel's built-in pagination links --}}
                                {{ $flaggingReasons->appends(request()->query())->links('pagination::bootstrap-4') }}

                                {{-- Next Page Link --}}
                                @if ($flaggingReasons->hasMorePages())
                                    <li class="page-item">
                                        <a class="page-link" href="{{ $flaggingReasons->nextPageUrl() }}"
                                            rel="next">Next</a>
                                    </li>
                                @else
                                    <li class="page-item disabled">
                                        <span class="page-link">Next</span>
                                    </li>
                                @endif
                            </ul>
                        </nav>
                    </div>

                    <div class="ml-4 small">
                        @if ($flaggingReasons->total() > 0)
                            Showing <span class="text-primary">{{ $flaggingReasons->firstItem() }}</span>
                            to <span class="text-primary">{{ $flaggingReasons->lastItem() }}</span>
                            of <span class="text-primary">{{ $flaggingReasons->total() }}</span> results
                        @else
                            <span class="text-muted">No results found</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" data-backdrop="static" tabindex="-1" id="flaggingReasonModal" wire:ignore.self>
        <div class="modal-dialog">
            <div class="modal-content">
                <button type="button" class="close cursor" data-dismiss="modal" wire:click="resetForm">
                    <em class="icon ni ni-cross"></em>
                </button>
                <div class="modal-header">
                    <h5 class="h5">
                        {{ $editing_id ? 'Edit Learner Flagging Reason' : 'Add Learner Flagging Reason' }}
                    </h5>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="save">
                        <div class="form-group">
                            <label for="learnerFlaggingReasonDisplayName">Display Name</label>
                            <input id="learnerFlaggingReasonDisplayName" type="text" autocomplete="off"
                                wire:model.defer="reasonName"
                                class="form-control text-uppercase @error('reasonName') is-invalid @enderror">
                            @error('reasonName')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label>School Types</label>
                            <div
                                style="border:1px solid #ced4da;border-radius:4px;
                        padding:10px;max-height:40vh;overflow-y:auto;">
                                @foreach ($schoolTypes as $type)
                                    <div class="form-check">
                                        <input type="checkbox" id="school_type_{{ $type['id'] }}"
                                            value="{{ $type['id'] }}" wire:model.defer="school_type_ids"
                                            wire:key="type-{{ $type['id'] }}" class="form-check-input">
                                        <label class="form-check-label" for="school_type_{{ $type['id'] }}">
                                            {{ $type['name'] }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            @error('school_type_ids')
                                <div class="text-danger small">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row mt-5">
                            <div class="col-lg-6 mb-2 mb-lg-0">
                                <button type="button" class="btn btn-block btn-outline-primary" data-dismiss="modal"
                                    wire:click="resetForm" wire:loading.attr="disabled">
                                    Cancel
                                </button>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn btn-primary btn-block"
                                    wire:loading.attr="disabled">
                                    <em class="icon ni ni-check"></em>
                                    <span>{{ $editing_id ? 'Update' : 'Submit' }}</span>
                                </button>
                            </div>
                        </div>

                    </form>
                </div>

            </div>
        </div>
    </div>

    <!-- Loading -->
    <div style="display:none;" id="loadingMessage" class="card card-preview">
        <div class="card-inner">
            <div class="d-flex align-items-center">
                <strong>Loading...</strong>
                <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const modalSelector = '#flaggingReasonModal';

            window.addEventListener('close-flagging-modal', () => {
                $(modalSelector).modal('hide');
            });

            $(modalSelector).on('hidden.bs.modal', cleanUpModals);
        });
    </script>


</div>
