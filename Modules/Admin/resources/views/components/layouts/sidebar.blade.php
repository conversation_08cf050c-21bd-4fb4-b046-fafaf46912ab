<div style="margin-left:-20px;" class="flex  ">
  <style>
    .hover\:text-white:hover {
      color: white;
    }

    .hover\:bg-submenu:hover {
      background-color: #00879B;
    }

    .active-link {
      background-color: #00879B !important;
      color: white !important;
    }
  </style>
  <!-- Sidebar -->
  <div style="width:210px; " class="border border-gray-500 bg-white text-black p-1 text-uppercase">
    <h2 class="text-xl font-bold  mb-4 ml-4 mt-5">Admin Units</h2>
    <ul class="space-y-2">


      <li class="flex items-center justify-between py-2  hover:bg-submenu hover:text-white   cursor-pointer {{ request()->routeIs('admin.manage-regions*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-regions') }}" class="flex-grow px-4 hover:text-white">
          Regions
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white  cursor-pointer  {{ request()->routeIs('admin.manage-subregions*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-subregions') }}" class="flex-grow px-4 hover:text-white">
          Sub Regions
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white  cursor-pointer  {{ request()->routeIs('admin.manage-districts*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-districts') }}" class="flex-grow px-4 hover:text-white">
          Districts
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white rcursor-pointer  {{ request()->routeIs('admin.manage-counties*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-counties') }}" class="flex-grow px-4 hover:text-white">
          Counties
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between py-2 hover:bg-submenu  hover:text-white  cursor-pointer  {{ request()->routeIs('admin.manage-subcounties*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-subcounties') }}" class="flex-grow px-4 hover:text-white">
          Sub Counties
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>

      <li class="flex items-center justify-between py-2 hover:bg-submenu hover:text-white  cursor-pointer  {{ request()->routeIs('admin.manage-parishes*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-parishes') }}" class="flex-grow px-4 hover:text-white">
          Parishes
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white  cursor-pointer  {{ request()->routeIs('admin.manage-villages*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-villages') }}" class="flex-grow px-4 hover:text-white">
          Villages
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white cursor-pointer  {{ request()->routeIs('admin.manage-clusters*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-clusters') }}" class="flex-grow px-4 hover:text-white">
          Clusters
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex items-center justify-between  py-2 hover:bg-submenu hover:text-white cursor-pointer  {{ request()->routeIs('admin.manage-upper-local-governments*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-upper-local-governments') }}" class="flex-grow px-4 hover:text-white">
          Upper Local Governments
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>


      <li class="flex  items-center justify-between  py-2 hover:bg-submenu hover:text-white cursor-pointer  {{ request()->routeIs('admin.manage-lower-local-governments*') ? 'active-link' : '' }}">

        <a href="{{ route('admin.manage-lower-local-governments') }}" class=" flex-grow px-4 hover:text-white">
          Lower Local Governments
        </a>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
      </li>



    </ul>
  </div>

  <!-- Main content -->
  <div class="flex-1 p-6 overflow-auto">
    <!--  page content -->
    @yield('content')
  </div>
</div>