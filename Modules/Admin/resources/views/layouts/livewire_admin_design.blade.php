<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="js">

<head>
	<meta charset="utf-8">
	<meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="refresh" content="{{ config('session.lifetime') * 60 }}">
	<meta name="author" content="DEMIS">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content="Education Information Management System">
	<!-- Fav Icon  -->
	<link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">
	<!-- Page Title  -->
	<title>@yield('title') - Admin : EMIS</title>
	<!-- StyleSheets  -->
    @include('includes.assets.dash-lite')
	<link id="skin-default" rel="stylesheet" href="{{ asset('assets/css/skins/theme-egyptian.css?ver=3.3.0') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css?ver=2') }}">
    <?php echo \Filament\Support\Facades\FilamentAsset::renderStyles() ?>
	@yield('stylesheets')
</head>
<body class="nk-body bg-lighter npc-default has-sidebar no-touch nk-nio-theme">
	<div id="demis-livewire-app" class="nk-app-root">
		<!-- main @s -->
		<div class="nk-main">
			<!-- sidebar @s -->
			@include('core::admin.layouts.sidebar')
			<!-- sidebar @e -->
			<!-- wrap @s -->
			<div class="nk-wrap" style="background-color: #F5F6FA">
				<!-- main header @s -->
				@include('core::admin.layouts.header')
				<!-- main header @e -->
				<!-- content @s -->
				@yield('content')
				<!-- content @e -->
				<!-- footer @s -->
				@include('core::admin.layouts.footer')
				<!-- footer @e -->
				<!-- app-root end -->
			</div>
			<!-- wrap @e -->
		</div>
		<!-- main @e -->
	</div>

	@include('core::admin.layouts.flag')
	@include('includes.assets.dash-js')
	@yield('scripts')
    @livewireScripts
    <?php echo \Filament\Support\Facades\FilamentAsset::renderScripts() ?>

</body>

</html>
