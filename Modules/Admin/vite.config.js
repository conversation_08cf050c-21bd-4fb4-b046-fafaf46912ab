// This file is used by the main vite.config.js to collect all module assets
// Update the paths as needed to match your module's asset locations
export const paths = [
    'Modules/Admin/resources/assets/sass/app.scss',
    'Modules/Admin/resources/assets/js/app.js',
    'Modules/Admin/resources/assets/css/filament.css',
];
// Scen all resources for assets file. Return array
//function getFilePaths(dir) {
//    const filePaths = [];
//
//    function walkDirectory(currentPath) {
//        const files = readdirSync(currentPath);
//        for (const file of files) {
//            const filePath = join(currentPath, file);
//            const stats = statSync(filePath);
//            if (stats.isFile() && !file.startsWith('.')) {
//                const relativePath = 'Modules/Admin/'+relative(__dirname, filePath);
//                filePaths.push(relativePath);
//            } else if (stats.isDirectory()) {
//                walkDirectory(filePath);
//            }
//        }
//    }
//
//    walkDirectory(dir);
//    return filePaths;
//}

//const __filename = fileURLToPath(import.meta.url);
//const __dirname = dirname(__filename);

//const assetsDir = join(__dirname, 'resources/assets');
//export const paths = getFilePaths(assetsDir);


//export const paths = [
//    'Modules/Admin/resources/assets/sass/app.scss',
//    'Modules/Admin/resources/assets/js/app.js',
//];
