<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Admin\Models\SettingUpperLocalGovernmentTypes;
use Illuminate\Support\Facades\DB;

class SettingUpperLocalGovernmentTypesTableSeeder extends Seeder
{

    public function run(): void
    {

        SettingUpperLocalGovernmentTypes::firstOrCreate(['name' => 'City']);
        SettingUpperLocalGovernmentTypes::firstOrCreate(['name' => 'Municipality']);
        SettingUpperLocalGovernmentTypes::firstOrCreate(['name' => 'District']);
    }
}
