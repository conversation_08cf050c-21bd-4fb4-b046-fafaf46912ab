<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('admin_unit_regions', function (Blueprint $table) {
            $table->id()->nullable(false)->comment('The unique ID generated automatically.');
            $table->string('name', 255)->unique()->nullable(false)->comment('The name of the region');
            $table->string('region_code', 25)->unique()->nullable(false)->comment('The code for the region');
            $table->enum('status', ['Active', 'Inactive'])->nullable(false)->default('Active')->comment('The status of the region. Active or Inactive. ');
            $table->timestamp('date_created')->useCurrent()->nullable(false)->comment('Date-time timestamp assigned at the creation of the record');
            $table->timestamp('date_updated')->useCurrent()->nullable(false)->comment('Date-time timestamp assigned at the update of the record');

        });
    }

    public function down(): void
    {
        Schema::dropIfExists('admin_unit_regions');
    }
};

