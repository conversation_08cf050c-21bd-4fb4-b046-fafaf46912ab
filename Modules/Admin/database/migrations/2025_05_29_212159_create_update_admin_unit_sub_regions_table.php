<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('admin_unit_sub_regions', function (Blueprint $table) {
            // Rename column
            $table->renameColumn('region_code', 'sub_region_code');
            $table->integer('region_id')->after('id')->nullable()->comment('Foreign key from the admin_unit_region table');
            $table->foreign('region_id')
                  ->references('id')
                  ->on('admin_unit_regions')
                  ->onDelete('cascade');

            $table->enum('status', ['Active', 'Inactive'])->default('Active')->after('sub_region_code')->nullable(false)->comment('The status of the sub-region. Active or Inactive');
            // Drop cemis_update_status column
            $table->dropColumn('cemis_update_status');
        });
    }

    public function down(): void
    {
        Schema::table('admin_unit_sub_regions', function (Blueprint $table) {
            $table->renameColumn('sub_region_code', 'region_code');

            $table->dropForeign(['region_id']);
            $table->dropColumn('region_id');
            $table->dropColumn('status');

            $table->string('cemis_update_status')->nullable();
        });
    }
};
