<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('setting_upper_local_government_types', function (Blueprint $table) {
            $table->id()->comment('Unique autogenerated primary key');
            $table->string('name', 200)->comment('Name of the local government type');
            $table->timestamp('date_created')->comment('The date and time when this record was created')->nullable();
            $table->timestamp('date_updated')->comment('The date and time when this record was last updated')->nullable();
        });

        db_table_comment("COMMENT ON TABLE admin_unit_upper_local_governments IS 'This table stores stores upper local government types. e.g., Cities, District Local governments, Municipalities'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('setting_upper_local_government_types');
    }
};
