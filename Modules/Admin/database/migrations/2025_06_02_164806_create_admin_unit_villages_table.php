<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_unit_villages', function (Blueprint $table) {
            $table->id()->comment('The unique ID generated automatically.');
            $table->string('name', 255)->comment('The name of the village');
            $table->string('village_code', 25)->unique()->comment('The village code from UBOS');
            $table
            ->foreignId('parish_id')
            ->constrained('admin_unit_parishes')
            ->onDelete('cascade');
            $table->enum('status', ['Active', 'Inactive'])->default('Active')->comment('The status of the village. Active or Inactive');
            $table->boolean('is_archived_yn')->default(false)->after('status')->comment('Indicates whether the village is archived or not. Default is false (not archived)');
            $table->timestamp('date_created')->comment('Date-time timestamp assigned at the creation of the record');
            $table->timestamp('date_updated')->comment('Date-time timestamp assigned at the update of the record');
        });

        Schema::table('admin_unit_villages', function (Blueprint $table) {
            $table->unsignedBigInteger('parish_id')
            ->comment('The foreign key from the admin_unit_parishes table. Note: This indicates the parish to which this village is attached')
            ->change();
        });
        db_table_comment("COMMENT ON TABLE admin_unit_villages IS 'This table stores all the lists of villages as provided by the Ministry of Local Government.'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_unit_villages');
    }
};
