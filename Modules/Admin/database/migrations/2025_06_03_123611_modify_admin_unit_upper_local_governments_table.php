<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('admin_unit_upper_local_governments', function (Blueprint $table) {
    
            $table->enum('status', ['Active', 'Inactive'])
                ->default('Active')
                ->after('vote_code')
                ->comment('The status of the upper local governments. Active or Inactive');
            
            $table->unsignedTinyInteger('local_gov_type')
                ->comment('The upper local goverment types')
                ->nullable()
                ->change();

          
            $table->boolean('archived_yn')
                ->default(false)
                ->after('status')
                ->comment('Indicates whether the upper local government is archived or not.');

         
            $table->foreignId('type_id')
                ->nullable()
                ->after('archived_yn') 
                ->constrained('setting_upper_local_government_types')
                ->onDelete('cascade')
                ->comment('The type of upper local government (linked to settings table)');

          
            $table->dropColumn('cemis_update_status');
            $table->unique('vote_code');
        });
    }

    public function down(): void
    {
        Schema::table('admin_unit_upper_local_governments', function (Blueprint $table) {
            $table->dropConstrainedForeignId('type_id');

            $table->dropColumn('status');
            $table->dropColumn('archived_yn');

            $table->unsignedTinyInteger('local_gov_type')
            ->nullable(false)
            ->comment('The upper local government types')
            ->change();

            $table->boolean('cemis_update_status')
                ->nullable()
                ->comment('Used to track if the model was updated in CEMIS.');

            $table->dropUnique(['vote_code']);
        });
    }
};
