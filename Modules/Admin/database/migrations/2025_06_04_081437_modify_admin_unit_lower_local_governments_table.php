<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('admin_unit_lower_local_governments', function (Blueprint $table) {


            $table->string('vote_code', 25)
                ->nullable()
                ->unique()
                ->after('id') 
                ->comment('The vote code for the local government issued by the Ministry of finance.');

      
            $table->enum('status', ['Active', 'Inactive'])
                ->default('Active')
                ->after('vote_code')
                ->comment('The status of the lower local governments. Active or Inactive');


          
            $table->boolean('archived_yn')
                ->default(false)
                ->after('status')
                ->comment('Indicates whether the lower local government is archived or not.');

      

        });
    }

    public function down(): void
    {
        Schema::table('admin_unit_lower_local_governments', function (Blueprint $table) {

          
            $table->dropColumn('status');
            $table->dropColumn('archived_yn');
            $table->dropColumn('vote_code');


 
        });
    }
};
