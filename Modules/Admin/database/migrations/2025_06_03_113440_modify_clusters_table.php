<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('clusters', function (Blueprint $table) {
            // drop cemis_update_status column
            $table->dropColumn('cemis_update_status');

            $table->unique(['name']);

            $table->string('cluster_code', 25)->unique()->nullable()->after('name')->comment('The code for the cluster issued by Ministry');

            $table->foreignId('sub_county_id')
            ->nullable()
            ->after('cluster_code')
            ->comment('The foreign key from the admin_unit_sub_counties table. Note: This indicates the sub-county to which this cluster is attached');
        
            
            $table->enum('status', ['Active', 'Inactive'])->default('Active')->after('sub_county_id')->comment('The status of the cluster. Active or Inactive');
            $table->boolean('archived_yn')->default(false)->after('status')->comment('Indicates whether the cluster is archived or not. Default is false (not archived)');
        });

        Schema::table('clusters', function (Blueprint $table) {
            $table->foreign('sub_county_id')
            ->references('id')
            ->on('admin_unit_sub_counties')
            ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('clusters', function (Blueprint $table) {
            $table->boolean('cemis_update_status')->nullable()->comment('Used to track if the details of the model were updated in the CEMIS. TRUE if is updated, FALSE if not yet updated on CEMIS.');

            // drop constraints
            $table->dropForeign(['sub_county_id']);
            $table->dropUnique(['name']);
            // drop added columns
            $table->dropColumn('sub_county_id');
            $table->dropColumn('cluster_code');
            $table->dropColumn('status');
            $table->dropColumn('archived_yn');

        });
    }
};
