<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('admin_unit_districts', function (Blueprint $table) {
            // rename columns
            $table->renameColumn('vote_code', 'district_code');
            $table->renameColumn('region_id', 'sub_region_id');
            // add new columns
            $table->enum('status', ['Active', 'Inactive'])->default('Active')->after('district_code')->comment('The status of the district. Active or Inactive');
            $table->boolean('is_archived_yn')->default(false)->after('status')->comment('Indicates whether the district is archived or not. Default is false (not archived)');
            // drop cemis_update_status column
            $table->dropColumn('cemis_update_status');
            // add unique constraints
            $table->unique('name');
            $table->unique('district_code');
        });

        Schema::table('admin_unit_districts', function (Blueprint $table) {
            $table->unsignedBigInteger('sub_region_id')
            ->nullable()
            ->comment('The foreign key from the admin_unit_sub_regions table. Note: This indicates the sub-region to which this district is attached')
            ->change();

            $table->foreign('sub_region_id')
            ->references('id')
            ->on('admin_unit_sub_regions')
            ->onDelete('cascade');
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('admin_unit_districts', function (Blueprint $table) {
            // drop constraints
            $table->dropForeign(['sub_region_id']);
            $table->dropUnique(['district_code']);
            $table->dropUnique(['name']);
            // drop added columns
            $table->dropColumn('status');
            $table->dropColumn('is_archived_yn');
            // rename columns back
            $table->renameColumn('district_code', 'vote_code');
            $table->renameColumn('sub_region_id', 'region_id');
            // re-add dropped column
            $table->boolean('cemis_update_status')->nullable()->comment('Used to track if the details of the model were updated in the CEMIS. TRUE if is updated, FALSE if not yet updated on CEMIS.');
        });
    }
};
