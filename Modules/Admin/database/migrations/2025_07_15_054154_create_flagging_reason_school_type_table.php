<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
Schema::create('flagging_reason_school_type', function (Blueprint $table) {
    $table->id();
    $table->foreignId('flagging_reason_id')->constrained('setting_flagged_reasons')->onDelete('cascade');
    $table->foreignId('school_type_id')->constrained('setting_school_types')->onDelete('cascade');
    $table->timestamps();
});

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flagging_reason_school_type');
    }
};
