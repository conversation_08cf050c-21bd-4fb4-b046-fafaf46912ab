<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSchoolTypeIdToSettingFlaggedReasonsTable extends Migration
{
    public function up(): void
    {
        Schema::table('setting_flagged_reasons', function (Blueprint $table) {
            $table->unsignedBigInteger('school_type_id')->nullable()->after('name');

            // Add foreign key constraint (optional, recommended)
            $table->foreign('school_type_id')
                  ->references('id')
                  ->on('setting_school_types')
                  ->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('setting_flagged_reasons', function (Blueprint $table) {
            $table->dropForeign(['school_type_id']);
            $table->dropColumn('school_type_id');
        });
    }
}

