<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::table('admin_unit_sub_counties', function (Blueprint $table) {
            $table->enum('status', ['Active', 'Inactive'])->default('Active')->after('vote_code')->comment('The status of the district. Active or Inactive');
            $table->boolean('archived_yn')->default(false)->after('status')->comment('Indicates whether the sub county is archived or not. Default is false (not archived)');
            // drop cemis_update_status column
            $table->dropColumn('cemis_update_status');
            // add unique constraints
            $table->unique('vote_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::table('admin_unit_sub_counties', function (Blueprint $table) {
            // drop constraints
            $table->dropForeign(['county_id']);
            $table->dropUnique(['vote_code']);
            $table->dropUnique(['name']);
            // drop added columns
            $table->dropColumn('status');
            $table->dropColumn('archived_yn');
            // re-add dropped column
            $table->boolean('cemis_update_status')->nullable()->comment('Used to track if the details of the model were updated in the CEMIS. TRUE if is updated, FALSE if not yet updated on CEMIS.');
        });
    }
};