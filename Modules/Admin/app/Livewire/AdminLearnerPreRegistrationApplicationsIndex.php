<?php

namespace Modules\Admin\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Modules\Core\Models\Settings\AdminUnits\UpperLocalGovernment;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;
use Modules\Core\Models\Settings\SchoolType;

class AdminLearnerPreRegistrationApplicationsIndex extends Component
{
    use WithPagination;

    public $type;
    public $selected_applications = [];
    public $select_all_applications = false;
    public $schoolTypes = [];
    public $localGovernments = [];
    public $filter = [];

    public function mount($type)
    {
        $this->type = $type;
        $this->schoolTypes = SchoolType::select('id', 'name', 'display_name')->get();
        $this->localGovernments = UpperLocalGovernment::select('id', 'name')->get();

        // Initialize filter array, preserving Livewire-hydrated values if they exist
        $this->filter['search_term'] = $this->filter['search_term'] ?? '';
        $this->filter['application_number'] = $this->filter['application_number'] ?? '';
        $this->filter['status'] = $this->filter['status'] ?? '';
        $this->filter['school_type_id'] = $this->filter['school_type_id'] ?? '';
        $this->filter['local_government_id'] = $this->filter['local_government_id'] ?? '';
        $this->filter['per_page'] = $this->filter['per_page'] ?? 10;
    }

    public function getApplicationsProperty()
    {
        $query = LearnerEnrolmentApplication::query()
            ->with(['school.school_type', 'education_grade'])
            ->where('is_draft_yn', false)
            ->latest()
            ->when($this->type, function ($q) {
                if (strtolower($this->type) === 'pending') {
                    $q->pending();
                } elseif (strtolower($this->type) === 'approved') {
                    $q->approved();
                } elseif (strtolower($this->type) === 'rejected') {
                    $q->rejected();
                }
            })
            ->when($this->filter['application_number'], function ($q) {
                $q->where('application_number', 'iLike', '%' . $this->filter['application_number'] . '%');
            })
            ->when($this->filter['search_term'], function ($q) {
                $q->where(function ($sq) {
                    $sq->where('first_name', 'iLike', '%' . $this->filter['search_term'] . '%')
                        ->orWhere('surname', 'iLike', '%' . $this->filter['search_term'] . '%');
                });
            })
            ->when($this->filter['school_type_id'], function ($q) {
                $q->whereHas('school.school_type', function ($sq) {
                    $sq->where('id', $this->filter['school_type_id']);
                });
            })
            ->when($this->filter['local_government_id'], function ($q) {
                $q->whereHas('school', function ($sq) {
                    $sq->where('local_government_id', $this->filter['local_government_id']);
                });
            });
        return $query->paginate((int)($this->filter['per_page'] ?: 15));
    }

    public function toggleAllApplications()
    {
        $this->selected_applications = $this->select_all_applications
            ? $this->applications->pluck('id')->toArray()
            : [];
    }

    public function toggleOneApplication()
    {
        $this->select_all_applications = count($this->selected_applications) === $this->applications->count();
    }

    public function resetFilter()
    {
        $this->filter = [
            'search_term' => '',
            'application_number' => '',
            'status' => '',
            'school_type_id' => '',
            'local_government_id' => '',
            'per_page' => 15,
        ];
        $this->resetPage();
    }

    public function applyFilter()
    {
        // This method just triggers a re-render
    }

    public function updatedFilter($value, $name)
    {
        if ($name === 'per_page') {
            $this->resetPage();
        }
    }

    public function render()
    {
        return view('admin::livewire.admin-learner-pre-registration-applications-index');
    }
}
