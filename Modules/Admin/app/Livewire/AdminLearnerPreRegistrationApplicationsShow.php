<?php

namespace Modules\Admin\Livewire;

use Exception;
use Livewire\Component;
use Modules\EmisReturns\Livewire\Traits\Learners\StoreLearnerDataTrait;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdminLearnerPreRegistrationApplicationsShow extends Component
{
    use StoreLearnerDataTrait;
    public $application;
    public $reason = '';
    public $loading = false;
    protected $listeners = ['learner-rejected' => 'commitRejectReason'];

    public function mount($status, $applicationNumber)
    {
        try {
            $application = LearnerEnrolmentApplication::query()
                ->where('application_number', Str::upper($applicationNumber))
                ->with([
                    'user_account',
                    'school.school_type',
                    'education_grade',
                    'country',
                    'parent_country',
                    'district_of_birth',
                    'post_primary_institution_course',
                    'institution_examined_course',
                    'international_calendar',
                    'international_curriculum',
                    'international_education_grade',
                    'international_education_level',
                    'familiar_language',
                    'deo.person',
                    'moes_staff',
                ])
                ->firstOrFail();
            $this->application = $application->toArray();
        } catch (ModelNotFoundException $exception) {
            abort(500, 'We cannot find the application you are trying to view');
        }
    }

    public function approveApplication()
    {
        $this->loading = true;
        try {
            $application = LearnerEnrolmentApplication::where('application_number', Str::upper($this->application['application_number']))->firstOrFail();

            DB::transaction(function () use ($application) {
                $this->saveLearnerEnrolment($application);
                $application->update([
                    'approved_by' => auth()->id(),
                    'date_approved' => now(),
                    'approval_status' => 'approved',
                ]);

                $this->application = $application->refresh();
            });

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Application approved successfully.',
            ]);
        } catch (ModelNotFoundException $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Application not found.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }
        $this->loading = false;
    }

    public function commitRejectReason()
    {
        $this->validate([
            'reason' => 'required|string|max:5000',
        ]);

        $this->loading = true;

        try {
            $application = LearnerEnrolmentApplication::where('application_number', Str::upper($this->application['application_number']))->firstOrFail();

            DB::transaction(function () use ($application) {
                $application->update([
                    'approved_by' => auth()->id(),
                    'date_approved' => now(),
                    'reject_reason' => $this->reason,
                    'approval_status' => 'rejected',
                ]);

                $this->application = $application->refresh();
            });

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Application rejected successfully.',
            ]);
             $this->dispatch('close-reject-modal');
        } catch (ModelNotFoundException $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Application not found.',
            ]);
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage(),
            ]);
        }

        $this->loading = false;
    }

    public function render()
    {
        return view('admin::livewire.admin-learner-pre-registration-applications-show');
    }
}
