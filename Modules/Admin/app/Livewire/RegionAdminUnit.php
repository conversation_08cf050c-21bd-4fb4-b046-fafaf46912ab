<?php
namespace Modules\Admin\Livewire;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Modules\Core\Models\Settings\AdminUnits\Region;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\DateTimePicker;
use Illuminate\Support\Collection;
use Filament\Forms\Components\Grid;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Get;

use Filament\Tables\Actions\Action;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;

use Filament\Forms\Components\Select;
use Filament\Tables\Actions\CreateAction;
use Filament\Forms\Components\Placeholder;
use Modules\Admin\Helpers\AdminUnitHelpers;

class RegionAdminUnit extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    private const INPUT_CLASS = 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const SELECT_CLASS = 'p-1 appearance-none shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function getRegionFormSchema(bool $isEdit = false): array
    {
        $schema = [
            TextInput::make('name')
                ->reactive()
                ->label('Region Name')
                ->required()
                ->maxLength(200)
                ->placeholder('Enter region name')
                ->columnSpanFull()
                ->extraAttributes([
                    'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                ]),

            TextInput::make('region_code')
                ->label('Region Code')
                ->reactive()
                ->required()
                ->unique(Region::class, 'region_code', fn ($record) => $record)
                ->maxLength(25)
                ->placeholder('Enter region code')
                ->extraAttributes(['class' => self::INPUT_CLASS]),
        ];

        if ($isEdit) {
            $schema[] = Select::make('status')
                ->label('Status')
                ->options([
                    'Active' => 'Active',
                    'Inactive' => 'Inactive',
                ])
                ->required()
                ->native(false)
                ->searchable()
                ->extraAttributes([
                    'class' => self::SELECT_CLASS,
                ]);
        }

        return $schema;
    }

    private function getViewFormSchema(): array
    {
        return [
           Grid::make(2)
                ->schema([
                    TextInput::make('name')
                        ->label('Region Name')
                        ->disabled()
                        ->extraAttributes(['class' => self::INPUT_CLASS]),

                    TextInput::make('region_code')
                        ->label('Region Code')
                        ->disabled()
                        ->extraAttributes(['class' => self::INPUT_CLASS]),
                ]),

       

                Grid::make(2)
                ->schema([
                TextInput::make('status')
                ->label('Status')
                ->disabled()
                ->extraAttributes(['class' => self::INPUT_CLASS]),
                ])
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Region::query())
            ->columns([
                TextColumn::make('name')
                    ->sortable()
                    ->searchable()
                    ->label('Region Name')
                    ->limit(50),

                TextColumn::make('region_code')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->label('Region Code'),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->colors([
                        'success' => 'Active',
                        'danger' => 'Inactive',
                    ])
                    ->icons([
                        'heroicon-o-check-circle' => 'Active',
                        'heroicon-o-x-circle' => 'Inactive',
                    ])
            ])
            ->headerActions([
                CreateAction::make()
                    ->label('New Region')
                    ->modalHeading('Create New Region')
                    ->icon('heroicon-o-plus')
                    ->modalWidth('2xl')
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])
                    ->form($this->getRegionFormSchema(false))
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['status'] = 'Active';
                        $data['date_created'] = now();
                        return $data;
                    })
                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Region created successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating a region. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    }),

                Action::make('import')
                    ->label('Import')
                    ->modalWidth('2xl')
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])
                    ->form([
                        FileUpload::make('import_file')
                            ->label('EXCEL File')
                            ->acceptedFileTypes([
                                'application/vnd.ms-excel', // .xls
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // .xlsx
                            ])
                            ->required()
                            ->disk('local')
                            ->directory('imports'),
                    ])
                    ->action(function (array $data) {
                        try {
                            $filePath = Storage::disk('local')->path($data['import_file']);

                            if (!file_exists($filePath)) {
                                throw new \Exception("File not found at $filePath");
                            }

                            $spreadsheet = IOFactory::load($filePath);
                            $worksheet = $spreadsheet->getActiveSheet();
                            $rows = $worksheet->toArray(null, true, true, true);

                            // Validate header row values (A1 and B1)
                            $expectedHeaders = ['name', 'region code'];
                            $headerRow = $rows[1];

                            $actualHeaders = [
                                strtolower(trim($headerRow['A'] ?? '')),
                                strtolower(trim($headerRow['B'] ?? '')),
                            ];

                            if ($actualHeaders !== $expectedHeaders) {
                                throw new \Exception("Invalid headers. Expected: 'Name' and 'Region Code'. Found: '{$headerRow['A']}' and '{$headerRow['B']}'");
                            }

                            unset($rows[1]);

                            $created = 0;
                            $skipped = 0;
                            $duplicates = 0;

                            $skippedRows = [];
                            $rowErrors = [];

                            foreach ($rows as $index => $row) {
                                $name = trim($row['A'] ?? '');
                                $regionCode = trim($row['B'] ?? '');

                                if (empty($name) || empty($regionCode)) {
                                    $skipped++;
                                    $skippedRows[] = "Row $index skipped: missing name or region code.";
                                    continue;
                                }

                                if (strlen($regionCode) > 10) {
                                    $skipped++;
                                    $skippedRows[] = "Row $index skipped: region code too long (max 10 characters).";
                                    continue;
                                }

                                if (!preg_match('/^[A-Z0-9\-]+$/i', $regionCode)) {
                                    $skipped++;
                                    $skippedRows[] = "Row $index skipped: region code contains invalid characters.";
                                    continue;
                                }

                                if (strlen($name) < 2) {
                                    $skipped++;
                                    $skippedRows[] = "Row $index skipped: name too short.";
                                    continue;
                                }

                                if (Region::where('region_code', $regionCode)->exists()) {
                                    $duplicates++;
                                    $skippedRows[] = "Row $index skipped: duplicate region code '$regionCode'.";
                                    continue;
                                }

                                try {
                                    Region::create([
                                        'name' => $name,
                                        'region_code' => $regionCode,
                                        'status' => 'Active',
                                        'date_created' => now(),
                                    ]);
                                    $created++;
                                } catch (\Exception $ex) {
                                    $rowErrors[] = "Row $index error: " . $ex->getMessage();
                                }
                            }

                            $message = "Import completed: $created created, $skipped skipped, $duplicates duplicates.";
                            if (!empty($rowErrors)) {
                                $message .= " " . count($rowErrors) . " errors encountered.";
                            }

                            $this->dispatch('notify', [
                                'type' => $created > 0 ? 'success' : 'warning',
                                'message' => $message,
                                'details' => array_merge($skippedRows, $rowErrors),
                            ]);

                            Notification::make()
                                ->title('Import Result')
                                ->body($message)
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Failed to import Regions: ' . $e->getMessage(),
                            ]);

                            Notification::make()
                                ->title('Import Failed')
                                ->body('Failed to import Regions: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->modalHeading('Import Regions')
                    ->modalDescription('Upload an Excel file with the following columns: Name, Region Code')
                    ->modalContent(fn () => AdminUnitHelpers::importTemplateNote('/sample-regions'))
                    ->color('success')
                    ->icon('heroicon-o-arrow-up-tray'),
            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->modalWidth('xl')
                    ->extraModalWindowAttributes([
                        'class' => 'p-4',
                    ])
                    ->form($this->getViewFormSchema()),

                \Filament\Tables\Actions\EditAction::make()
                    ->modalWidth('2xl')
                    ->modalHeading('Edit Region')
                    ->form($this->getRegionFormSchema(true)) // true = edit mode
                    ->mutateFormDataUsing(function (array $data): array {
                        // Set date_updated when editing
                        $data['date_updated'] = now();
                        return $data;
                    }),

                Action::make('toggle_status')
                    ->label(fn ($record) => $record->status === 'Active' ? 'Disable' : 'Enable')
                    ->icon(fn ($record) => $record->status === 'Active' ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn ($record) => $record->status === 'Active' ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->modalAlignment('start')
                    ->modalHeading(fn ($record) => $record->status === 'Active' ? 'Disable Region' : 'Enable Region')
                    ->modalDescription(fn ($record) => $record->status === 'Active'
                        ? 'Are you sure you want to disable this region?'
                        : 'Are you sure you want to enable this region? ')
                    ->modalSubmitActionLabel(fn ($record) => $record->status === 'Active' ? 'Disable' : 'Enable')
                    ->action(function ($record) {
                        $newStatus = $record->status === 'Active' ? 'Inactive' : 'Active';
                        $record->update([
                            'status' => $newStatus,
                            'date_updated' => now(),
                        ]);

                        $actionWord = $newStatus === 'Active' ? 'enabled' : 'disabled';

                        $this->dispatch('notify', [
                            'type' => 'success',
                            'message' => "Region has been $actionWord successfully.",
                        ]);
                    }),
            ]);
    }

    public function render()
    {
        return view('admin::livewire.region-admin-unit');
    }
}
