<?php

namespace Modules\Admin\Livewire;

use Livewire\Component;
use Modules\Core\Models\Settings\AdminUnits\County;
use Modules\Core\Models\Settings\AdminUnits\District;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\EditAction;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Collection;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use Modules\Admin\Helpers\AdminUnitHelpers;

class AdminUnitsCounties extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function getCountyForm(): array
    {
        return [
            Grid::make(2)
                ->schema([
                    TextInput::make('name')
                        ->label(' County Name')
                        ->required()
                        ->maxLength(200)
                        ->placeholder('Enter  county name')
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),
                    Select::make('district_id')
                        ->label('District')
                        ->options(fn() => District::pluck('name', 'id'))
                        ->searchable()
                        ->required()
                        ->preload()
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),
                ]),

            Grid::make(2)
                ->schema([
                    TextInput::make('vote_code')
                        ->label('County Code')
                        ->required()
                        ->maxLength(25)
                        ->unique(County::class, 'vote_code', fn($record) => $record)
                        ->columnSpan(fn(string $context) => $context === 'create' ? 2 : 1)
                        ->placeholder('Enter county code e.g CC0007')
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none w-[300px] -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),

                    Select::make('status')
                        ->label('Status')
                        ->options([
                            'Active' => 'Active',
                            'Inactive' => 'Inactive',
                        ])
                        ->required()
                        ->extraAttributes([
                            'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-red-400 ',
                        ])
                        ->visible(fn(string $context) => $context === 'edit'),
                ]),
        ];
    }


    private function handleImportAction(Collection $records, array $data): void
    {
        $filePath = Storage::path($data['file']);

        $createdCount = 0;
        $skippedRows = [];
        $rowErrors = [];

        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            if (empty($rows) || !isset($rows[0])) {
                throw new \Exception('Excel file is empty or malformed');
            }

            $headers = array_map(fn($h) => Str::slug(trim($h), '_'), $rows[0]);
            $requiredHeaders = ['name', 'vote_code', 'district_id'];
            $missingHeaders = array_diff($requiredHeaders, $headers);

            if (!empty($missingHeaders)) {
                throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
            }

            for ($i = 1; $i < count($rows); $i++) {
                try {
                    $rowData = array_values($rows[$i]);
                    $row = array_combine($headers, $rowData);

                    if (empty($row['name']) || empty($row['vote_code']) || empty($row['district_id'])) {
                        $skippedRows[] = "Row " . ($i + 1) . ": Required fields are missing.";
                        continue;
                    }

                    $DistrictName = strtolower(trim($row['district_id']));
                    $district = District::whereRaw('LOWER(name) = ?', [$DistrictName])->first();

                    if (!$district) {
                        $skippedRows[] = "Row " . ($i + 1) . ": District '{$row['district_id']}' not found.";
                        continue;
                    }

                    County::create([
                        'name' => $row['name'],
                        'vote_code' => $row['vote_code'],
                        'district_id' => $district->id,
                    ]);

                    $createdCount++;
                } catch (\Exception $rowException) {
                    $rowErrors[] = "Row " . ($i + 1) . ": " . $rowException->getMessage();
                }
            }

            Storage::delete($data['file']);

            // 🎉 Final success summary notification
            $message = "Import complete: {$createdCount}  counties created.";
            if (!empty($skippedRows)) {
                $message .= " " . count($skippedRows) . " rows skipped.";
            }
            if (!empty($rowErrors)) {
                $message .= " " . count($rowErrors) . " errors encountered.";
            }

            $this->dispatch('notify', [
                'type' => $createdCount > 0 ? 'success' : 'warning',
                'message' => $message,
                'details' => array_merge($skippedRows, $rowErrors),
            ]);
        } catch (\Exception $e) {

            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to import  Counties: ' . $e->getMessage() . '. Please try again later!',
            ]);
        }
    }



    public function table(Table $table): Table
    {
        return $table
            ->query(County::query())
            ->defaultSort('date_updated', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(['class' => 'bg-sky-500 pl-60 '])
                    ->label(' County Name')
                    ->limit(50),

                TextColumn::make('vote_code')
                    ->searchable()
                    ->sortable()
                    ->label(' County Code')
                    ->badge(),

                TextColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->badge()
                    ->color(fn($record) => $record->status == 'Active' ? 'success' : 'danger')
                    ->icon(fn($record) => $record->status == 'Active' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                    ->extraAttributes([
                        'class' => 'text-red-500 ',
                    ]),

                TextColumn::make('district.name')
                    ->searchable()
                    ->sortable()
                    ->label('District'),
            ])
            ->headerActions([

                \Filament\Tables\Actions\CreateAction::make('import')
                    ->label('Import ')
                    ->form([
                        FileUpload::make('file')
                            ->label('Upload CSV or Excel File')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            ])
                            ->disk('local')
                            ->required()
                            ->directory('imports'),
                    ])
                    ->action(fn(Collection $records, array $data) => $this->handleImportAction($records, $data))
                    ->modalHeading('Import  Counties')
                    ->modalDescription('Upload a CSV or Excel file to import  counties.')
                    ->modalContent(fn() => AdminUnitHelpers::importTemplateNote('/counties-sample'))

                    ->color('success')
                    ->createAnother(false)
                    ->modalWidth('lg')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->modalSubmitActionLabel('Import')
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS]),

                CreateAction::make()
                    ->label('Create')
                    ->icon('heroicon-o-plus')
                    ->modalHeading('Create  County')
                    ->modalWidth('lg')
                    ->form(
                        $this->getCountyForm()
                    )
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])

                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => ' County created successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating  county. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    })
            ])


            ->actions([
                ViewAction::make()
                    ->modalWidth('2xl')
                    ->form([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label(' County Name')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                                TextInput::make('vote_code')
                                    ->label('County Code')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                            ]),

                        Grid::make(2)
                            ->schema([

                                TextInput::make('status')
                                    ->label('Status')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                                TextInput::make('district.name')
                                    ->label('District')
                                    ->formatStateUsing(fn($state, $record) => $record->district->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                    ]),

                            ]),

                        Grid::make(2)
                            ->schema([

                                TextInput::make('sub_region_name')
                                    ->label('Sub Region')
                                    ->formatStateUsing(fn($state, $record) => $record->district->sub_region->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                    ]),

                                TextInput::make('region_name')
                                    ->label('Region ')
                                    ->formatStateUsing(fn($record) => $record->district->sub_region->region->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),
                            ]),



                    ]),

                EditAction::make()
                    ->label('Edit')
                    ->modalHeading('Edit  County')
                    ->modalWidth('lg')
                    ->form($this->getCountyForm())

                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'County updated successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while updating  county. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    }),


                EditAction::make('status')
                    ->label(fn($record) => $record->status == 'Active' ? 'Disable' : 'Enable')
                    ->requiresConfirmation()
                    ->modalDescription(fn($record) => $record->status == 'Active' ? 'Are you sure you want to disable this County?' : 'Are you sure you want to enable this County?')
                    ->color('primary')
                    ->icon('heroicon-o-information-circle')
                    ->modalAlignment('start')
                    ->using(fn($record) => $record->update([
                        'status' => $record->status == 'Active' ? 'Inactive' : 'Active',
                        'date_updated' => now(),
                    ]))

                    ->after(function ($record) {
                        try {
                            $statusMessage = $record->status === 'Active'
                                ? 'County enabled successfully.'
                                : 'County disabled successfully.';

                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => $statusMessage,
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong. Please try again later. ' . $e->getMessage(),

                            ]);
                        }
                    }),


            ]);
    }
    public function render()
    {
        return view('admin::livewire.admin-units-counties');
    }
}
