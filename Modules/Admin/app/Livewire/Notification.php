<?php

namespace Modules\Admin\Livewire;


use Livewire\Component;

class Notification extends Component
{
  
    protected $listeners = ['notify' => 'showNotification'];

    public $message;
    public $type;
    public $details = []; 

    /**
     * Handle the 'notify' event and update message and type
     *
     * @param array $data
     * @return void
     */
    public function showNotification($data)
    {
        $this->type = $data['type'] ?? 'info';
        $this->message = $data['message'] ?? '';
        $this->details = $data['details'] ?? [];
    }

    public function render()
    {
        return view('admin::livewire.notification');
    }
}