<?php

namespace Modules\Admin\Livewire;

use Modules\Core\Models\Settings\AdminUnits\District;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Modules\Admin\Models\SubRegion;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Modules\Admin\Helpers\AdminUnitHelpers;

class AdminUnitsDistricts extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    private const INPUT_CLASS = 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const SELECT_CLASS = 'p-1 appearance-none shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function handleImportAction(Collection $records, array $data): void 
    {
    $filePath = Storage::path($data['file']);

    $createdCount = 0;
    $skippedRows = [];
    $rowErrors = [];

    try {
        $spreadsheet = IOFactory::load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        if (empty($rows) || !isset($rows[0])) {
            throw new \Exception('Excel file is empty or malformed');
        }

        $headers = array_map(fn($h) => Str::slug(trim($h), '_'), $rows[0]);
        $requiredHeaders = ['name', 'district_code', 'sub_region_id'];
        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
        }

        for ($i = 1; $i < count($rows); $i++) {
            try {
                $rowData = array_values($rows[$i]);
                $row = array_combine($headers, $rowData);

                if (empty($row['name']) || empty($row['district_code']) || empty($row['sub_region_id'])) {
                    $skippedRows[] = "Row " . ($i + 1) . ": Required fields are missing.";
                    continue;
                }

                $SubRegionName = strtolower(trim($row['sub_region_id']));
                $subRegion = SubRegion::whereRaw('LOWER(name) = ?', [$SubRegionName])->first();

                if (!$subRegion) {
                    $skippedRows[] = "Row " . ($i + 1) . ": SubRegion '{$row['sub_region_id']}' not found.";
                    continue;
                }

                District::create([
                    'name' => $row['name'],
                    'district_code' => $row['district_code'],
                    'sub_region_id' => $subRegion->id,
                ]);

                $createdCount++;

            } catch (\Exception $rowException) {
                $rowErrors[] = "Row " . ($i + 1) . ": " . $rowException->getMessage();
            }
        }

        Storage::delete($data['file']);

        // success summary notification
        $message = "Import complete: {$createdCount} Districts/Cities created.";
        if (!empty($skippedRows)) {
            $message .= " " . count($skippedRows) . " rows skipped.";
        }
        if (!empty($rowErrors)) {
            $message .= " " . count($rowErrors) . " errors encountered.";
        }

        $this->dispatch('notify', [
            'type' => $createdCount > 0 ? 'success' : 'warning',
            'message' => $message,
            'details' => array_merge($skippedRows, $rowErrors), 
        ]);

    } catch (\Exception $e) {

        $this->dispatch('notify', [
            'type' => 'error',
            'message' => 'Failed to import Districts/Cities: ' . $e->getMessage().'. Please try again later!',
        ]);
    }
}

    public function table(Table $table): Table
    {
        return $table
            ->query(District::query())
            ->defaultSort('date_updated', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->label('District/City')
                    ->sortable()
                    ->searchable(),
                    
                TextColumn::make('district_code')
                    ->label('District/City Code')
                    ->placeholder('—')
                    ->sortable()
                    ->badge(),

                TextColumn::make('sub_region.name')
                    ->searchable()
                    ->sortable()
                    ->label('SubRegion'), 

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->sortable()
                    ->color( fn ($record) => $record->status == 'Active' ? 'success':'danger')
                    ->icon(fn ($record) => $record->status == 'Active' ? 'heroicon-o-check-circle':'heroicon-o-x-circle')
                    ->extraAttributes([
                        'class' => 'text-red-500 ',
                    ]),
            ])
            ->headerActions([
                \Filament\Tables\Actions\CreateAction::make('import')
                    ->label('Import')
                    ->extraModalWindowAttributes(['class' => 'p-4'])
                    ->form([
                        FileUpload::make('file')
                            ->label('Upload CSV or Excel File')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/vnd.ms-excel', 
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                            ])
                            ->required()
                            ->disk('local')
                            ->directory('imports'),
                    ])
                    ->action(fn(Collection $records, array $data) => $this->handleImportAction($records, $data))
                    ->modalHeading('Import Districts/Cities')
                    ->modalContent(fn () => AdminUnitHelpers::importTemplateNote('/districts-sample'))
                
                ->color('success')
                ->createAnother(false)
                ->modalWidth('lg')
                ->icon('heroicon-o-arrow-down-tray')
                ->modalSubmitActionLabel('Import')
                ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS]),

                CreateAction::make()
                    ->label('Create')
                    ->modalHeading('Add New District/City')
                    ->modalSubmitActionLabel('Create')
                    ->modalWidth('xl')
                    ->icon('heroicon-o-plus')
                    ->modalDescription('To create a new District or City, select a sub-region, enter the name and code.')
                    ->form([
                        Select::make('sub_region_id')
                            ->label('Select SubRegion')
                            ->options(SubRegion::all()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required()
                            ->extraAttributes(['class' => self::SELECT_CLASS]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                ->label('District/City Name')
                                ->placeholder('E.g Gulu')
                                ->unique(District::class, 'name', fn ($record) => $record)
                                ->required()
                                ->maxLength(255)
                                ->extraAttributes(['class' => self::INPUT_CLASS]),
    
                                TextInput::make('district_code')
                                ->label('District/City Code')
                                ->placeholder('E.g GT001')
                                ->unique(District::class, 'district_code', fn ($record) => $record)
                                ->required()
                                ->maxLength(10)
                                ->extraAttributes(['class' => self::INPUT_CLASS]),
                            ])
                        
                    ])
                    ->using(function (array $data): District {
                        return District::create($data);
                    })
                    ->createAnother(false)
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])
                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'District/City created successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating District/City. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    }),
                    
                
            ])
            ->actions([
                    \Filament\Tables\Actions\ViewAction::make()
                    ->modalHeading('District/City Details')
                    ->modalWidth('xl')
                    ->form([
                        Grid::make(2)
                        ->schema([
                            TextInput::make('name')
                                ->label('District/City Name')
                                ->disabled()
                                ->extraAttributes(['class' => self::INPUT_CLASS]),

                            TextInput::make('district_code')
                                ->label('District/City Code')
                                ->disabled()
                                ->extraAttributes(['class' => self::INPUT_CLASS]),
                     

                        ]),
                        
                        Grid::make(2)
                        ->schema([
                               
                            TextInput::make('status')
                                ->label('Status')
                                ->disabled()
                                ->extraAttributes(['class' => self::INPUT_CLASS]),

                            TextInput::make('sub_region.name')
                                ->label('SubRegion')
                                ->formatStateUsing(fn ($record) => $record->sub_region->name ?? '—')
                                ->disabled()
                                ->extraAttributes(['class' => self::INPUT_CLASS]),
                        ]),

                        Grid::make(2)
                        ->schema([
                            TextInput::make('region_name')
                                ->label('Region')
                                ->formatStateUsing(fn($state, $record) => $record->sub_region->region->name ?? '—')
                                ->disabled()
                                ->extraAttributes(['class' => self::INPUT_CLASS]),
                        ])
                        
                      
                    ]),
                        \Filament\Tables\Actions\EditAction::make()
                        ->label('Edit')
                        ->modalHeading('Edit District/City')
                        ->modalSubmitActionLabel('Edit')
                        ->modalWidth('xl')
                        ->modalDescription('To edit a District or City, select a SubRegion, update the name and code.')
                        ->form([
                            Grid::make(2)
                                ->schema([
                                    Select::make('sub_region_id')
                                        ->label('Select SubRegion')
                                        ->options(SubRegion::all()->pluck('name', 'id'))
                                        ->searchable()
                                        ->required()
                                        ->extraAttributes(['class' => self::SELECT_CLASS]),

                                    TextInput::make('name')
                                        ->label('District/City Name')
                                        ->required()
                                        ->unique(District::class, 'name', fn ($record) => $record)
                                        ->maxLength(255)
                                        ->extraAttributes(['class' => self::INPUT_CLASS]),
                                ]),
                            
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('district_code')
                                        ->label('District Code')
                                        ->required()
                                        ->unique(District::class, 'district_code', fn ($record) => $record)
                                        ->maxLength(10)
                                        ->extraAttributes(['class' => self::INPUT_CLASS]),

                                    Select::make('status')
                                        ->label('Status')
                                        ->options([
                                            'Active' => 'Active',
                                            'Inactive' => 'Inactive',
                                        ])
                                        ->required()
                                        ->extraAttributes(['class' => self::SELECT_CLASS]),
                                ]),
                            // TODO: Archive
                        ])
                        ->after(function () {
                            try {
                                $this->dispatch('notify', [
                                    'type' => 'success',
                                    'message' => 'District/City updated successfully.',
                                ]);
                            } catch (\Exception $e) {
                                $this->dispatch('notify', [
                                    'type' => 'error',
                                    'message' => 'Something went wrong. Please try again later! ' . $e->getMessage(),
                                ]);
                            }
                            
                        }),

                        \Filament\Tables\Actions\EditAction::make('status')
                        ->label(fn ($record) => $record->status == 'Active' ? 'Disable':'Enable')
                        ->requiresConfirmation()
                        ->modalDescription(fn ($record) => $record->status == 'Active' ? 'Are you sure you want to disable this district/city?':'Are you sure you want to enable this district/city?')
                        ->color('primary')
                        ->icon('heroicon-o-information-circle')
                        ->modalAlignment('start')
                        ->using(fn ($record) => $record->update([
                            'status' => $record->status == 'Active' ? 'Inactive' : 'Active',
                            'date_updated' => now(),
                        ]))
                        ->after(function ($record) {
                            try {
                                $statusMessage = $record->status === 'Active'
                                    ? 'District/City enabled successfully.'
                                    : 'District/City disabled successfully.';
                    
                                $this->dispatch('notify', [
                                    'type' => 'success',
                                    'message' => $statusMessage,
                                ]);
                            } catch (\Exception $e) {
                                $this->dispatch('notify', [
                                    'type' => 'error',
                                    'message' => 'Something went wrong. Please try again later. ' . $e->getMessage(),
                                ]);
                                
                            }
                        })
            ]);
    }

    public function render()
    {
        return view('admin::livewire.admin-units-districts');
    }
}
