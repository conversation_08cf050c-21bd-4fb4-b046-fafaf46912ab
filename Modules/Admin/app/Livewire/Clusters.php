<?php

namespace Modules\Admin\Livewire;

use Livewire\Component;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Modules\Admin\Models\Cluster;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Modules\Admin\Helpers\AdminUnitHelpers;
use Modules\Core\Models\Settings\AdminUnits\SubCounty;
use Modules\Core\Models\Settings\AdminUnits\District;
use Modules\Core\Models\Settings\AdminUnits\County;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Model;

class Clusters extends Component implements HasForms, HasTable
{

    use InteractsWithForms;
    use InteractsWithTable;

    private const INPUT_CLASS = 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const SELECT_CLASS = 'p-1 appearance-none shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function handleImportAction(Collection $records, array $data): void 
    {
    $filePath = Storage::path($data['file']);

    $createdCount = 0;
    $skippedRows = [];
    $rowErrors = [];

    try {
        $spreadsheet = IOFactory::load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        if (empty($rows) || !isset($rows[0])) {
            throw new \Exception('Excel file is empty or malformed');
        }

        $headers = array_map(fn($h) => Str::slug(trim($h), '_'), $rows[0]);
        $requiredHeaders = ['name', 'cluster_code', 'sub_county_id'];
        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
        }

        for ($i = 1; $i < count($rows); $i++) {
            try {
                $rowData = array_values($rows[$i]);
                $row = array_combine($headers, $rowData);

                if (empty($row['name']) || empty($row['cluster_code']) || empty($row['sub_county_id'])) {
                    $skippedRows[] = "Row " . ($i + 1) . ": Required fields are missing.";
                    continue;
                }

                $subCountyName = strtolower(trim($row['sub_county_id']));
                $subCounty = SubCounty::whereRaw('LOWER(name) = ?', [$subCountyName])->first();

                if (!$subCounty) {
                    $skippedRows[] = "Row " . ($i + 1) . ": SubCounty '{$row['sub_county_id']}' not found.";
                    continue;
                }

                Cluster::create([
                    'name' => $row['name'],
                    'cluster_code' => $row['cluster_code'],
                    'sub_county_id' => $subCounty->id,
                ]);

                $createdCount++;

            } catch (\Exception $rowException) {
                $rowErrors[] = "Row " . ($i + 1) . ": " . $rowException->getMessage();
            }
        }

        Storage::delete($data['file']);

        // success summary notification
        $message = "Import complete: {$createdCount} Clusters created.";
        if (!empty($skippedRows)) {
            $message .= " " . count($skippedRows) . " rows skipped.";
        }
        if (!empty($rowErrors)) {
            $message .= " " . count($rowErrors) . " errors encountered.";
        }

        $this->dispatch('notify', [
            'type' => $createdCount > 0 ? 'success' : 'warning',
            'message' => $message,
            'details' => array_merge($skippedRows, $rowErrors), 
        ]);

    } catch (\Exception $e) {

        $this->dispatch('notify', [
            'type' => 'error',
            'message' => 'Failed to import Clusters: ' . $e->getMessage().'. Please try again later!',
        ]);
    }
    }

    private function getClusterForm(): array
{
    return [
        Grid::make(2)
            ->schema([
                Select::make('district_id')
                    ->label('District')
                    ->options(District::query()->pluck('name', 'id'))
                    ->searchable()
                    ->preload()
                    ->dehydrated(false)
                    ->afterStateUpdated(function (callable $set) {
                        $set('county_id', null);
                        $set('sub_county_id', null);
                    })
                    ->live()
                    ->extraAttributes(['class' => self::SELECT_CLASS]),
                Select::make('county_id')
                    ->label('County')
                    ->options(function (Get $get, ?Model $record): Collection {
                        $districtId = $get('district_id') ?? $record?->county?->district_id;
                        if(!$districtId) {
                            return County::query()->pluck('name', 'id');
                        }
                        return County::where('district_id', $districtId)->pluck('name', 'id');
                    })
                    ->searchable()
                    ->preload()
                    ->dehydrated(false)
                    ->afterStateUpdated(function (callable $set) {
                        $set('sub_county_id', null);
                    })
                    ->live()
                    ->extraAttributes(['class' => self::SELECT_CLASS]),
            ]),
        Grid::make(2)
            ->schema([
                Select::make('sub_county_id')
                    ->label('Sub County')
                    ->options(function (Get $get, ?Model $record): Collection {
                        $countyId = $get('county_id') ?? $record?->sub_county?->county_id;

                        if(!$countyId) {
                            return SubCounty::query()->pluck('name', 'id');
                        }

                        return SubCounty::where('county_id', $countyId)->pluck('name', 'id');
                    })
                    ->searchable()
                    ->required()
                    ->preload()
                    ->extraAttributes(['class' => self::SELECT_CLASS]),
                TextInput::make('name')
                    ->label('Cluster Name')
                    ->placeholder('E.g Gulu Municipality')
                    ->required()
                    ->unique(Cluster::class, 'name', fn ($record) => $record)
                    ->maxLength(255)
                    ->extraAttributes(['class' => self::INPUT_CLASS]),
            ]),

        Grid::make(2)
            ->schema([
                TextInput::make('cluster_code')
                    ->label('Cluster Code')
                    ->placeholder('E.g GM001')
                    ->required()
                    ->unique(Cluster::class, 'cluster_code', fn ($record) => $record)
                    ->maxLength(10)
                    ->extraAttributes(['class' => self::INPUT_CLASS]),

        Select::make('status')
            ->label('Status')
            ->options([
                'Active' => 'Active',
                'Inactive' => 'Inactive',
            ])
            ->required()
            ->extraAttributes(['class' => self::SELECT_CLASS])
            ->visible(fn (string $context) => $context === 'edit'),
        ]),
    ];
}

    public function table(Table $table): Table
    {
        return $table
            ->query(Cluster::query())
            ->defaultSort('date_updated', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->label('Cluster Name')
                    ->sortable()
                    ->searchable(),
                    
                TextColumn::make('cluster_code')
                    ->label('Cluster Code')
                    ->sortable()
                    ->placeholder('—')
                    ->badge(),

                TextColumn::make('subcounty.name')
                    ->searchable()
                    ->sortable()
                    ->label('SubCounty'), 

                TextColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->badge()
                    ->color( fn ($record) => $record->status == 'Active' ? 'success':'danger')
                    ->icon(fn ($record) => $record->status == 'Active' ? 'heroicon-o-check-circle':'heroicon-o-x-circle')
                    ->extraAttributes([
                        'class' => 'text-red-500 ',
                    ]),
            ])
            ->headerActions([
                \Filament\Tables\Actions\CreateAction::make('import')
                    ->label('Import')
                    ->extraModalWindowAttributes(['class' => 'p-4'])
                    ->form([
                        FileUpload::make('file')
                            ->label('Upload CSV or Excel File')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/vnd.ms-excel', 
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                            ])
                            ->required()
                            ->disk('local')
                            ->directory('imports'),
                    ])
                    ->action(fn(Collection $records, array $data) => $this->handleImportAction($records, $data))
                    ->modalHeading('Import Clusters')
                    ->modalContent(fn () => AdminUnitHelpers::importTemplateNote('/clusters-sample'))
                
                ->color('success')
                ->createAnother(false)
                ->modalWidth('lg')
                ->icon('heroicon-o-arrow-down-tray')
                ->modalSubmitActionLabel('Import')
                ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS]),

                CreateAction::make()
                    ->label('Create')
                    ->modalHeading('Add New Cluster')
                    ->modalSubmitActionLabel('Create')
                    ->modalWidth('xl')
                    ->icon('heroicon-o-plus')
                    ->modalDescription('To create a new Cluster, select a SubCounty, enter the name and code.')
                    ->form($this->getClusterForm())
                    ->using(function (array $data): Cluster {
                        return Cluster::create($data);
                    })
                    ->createAnother(false)
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])
                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Cluster created successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating cluster. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    }),
                    
                
            ])
            ->actions([
                    \Filament\Tables\Actions\ViewAction::make()
                    ->modalHeading('Cluster Details')
                    ->modalWidth('2xl')
                    ->form([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Cluster Name')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                                    TextInput::make('cluster_code')
                                    ->label('Cluster Code')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                                    TextInput::make('status')
                                    ->label('Status')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                            
                            ]),
                        
                        Grid::make(3)
                            ->schema([

                                TextInput::make('subcounty.name')
                                ->label('SubCounty')
                                ->formatStateUsing(fn ($record) => $record->subcounty->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                ]),

                                TextInput::make('county_name')
                                ->label('County')
                                ->formatStateUsing(fn($state, $record) => $record->subcounty->county->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                ]),

                                TextInput::make('district_name')
                                    ->label('District')
                                    ->formatStateUsing(fn($state, $record) => $record->subcounty->county->district->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                    ]),

                            ]),
                        
                        Grid::make(3)
                            ->schema([
                                TextInput::make('sub_region_name')
                                    ->label('Sub Region')
                                    ->formatStateUsing(fn($state, $record) => $record->subcounty->county->district->sub_region->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                    ]),

                                TextInput::make('region_name')
                                    ->label('Region')
                                    ->formatStateUsing(fn($state, $record) => $record->subcounty->county->district->sub_region->region->name ?? '—')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                    ]),
                            ]),
                    ]),
                    \Filament\Tables\Actions\EditAction::make()
                        ->label('Edit')
                        ->modalHeading('Edit Cluster')
                        ->modalSubmitActionLabel('Edit')
                        ->modalWidth('xl')
                        ->modalDescription('To edit a Cluster, select a SubCounty, update the name and code.')
                        ->form($this->getClusterForm())
                        ->after(function () {
                            try {
                                $this->dispatch('notify', [
                                    'type' => 'success',
                                    'message' => 'Cluster updated successfully.',
                                ]);
                            } catch (\Exception $e) {
                                $this->dispatch('notify', [
                                    'type' => 'error',
                                    'message' => 'Something went wrong while updating Cluster. Please try again later! ' . $e->getMessage(),
                                ]);
                            }
                            
                        }),
                    \Filament\Tables\Actions\EditAction::make('status')
                    ->label(fn ($record) => $record->status == 'Active' ? 'Disable':'Enable')
                    ->requiresConfirmation()
                    ->modalDescription(fn ($record) => $record->status == 'Active' ? 'Are you sure you want to disable this cluster?':'Are you sure you want to enable this cluster?')
                    ->color('primary')
                    ->icon('heroicon-o-information-circle')
                    ->modalAlignment('start')
                    ->using(fn ($record) => $record->update([
                        'status' => $record->status == 'Active' ? 'Inactive' : 'Active',
                        'date_updated' => now(),
                    ]))
                    ->after(function ($record) {
                        try {
                            $statusMessage = $record->status === 'Active'
                                ? 'Cluster enabled successfully.'
                                : 'Cluster disabled successfully.';
                
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => $statusMessage,
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong. Please try again later. ' . $e->getMessage(),
                            ]);
                            
                        }
                    }),


            ]);
    }

    public function render()
    {
        return view('admin::livewire.clusters');
    }
}
