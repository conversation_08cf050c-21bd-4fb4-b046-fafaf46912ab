<?php

namespace Modules\Admin\Livewire;

use Livewire\Component;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\EditAction;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Collection;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use Modules\Admin\Helpers\AdminUnitHelpers;
use Modules\Core\Models\Settings\SettingLowerLocalGovernmentType;
use Modules\Core\Models\Settings\AdminUnits\UpperLocalGovernment;
use Modules\Core\Models\Settings\AdminUnits\LowerLocalGovernment;
use Modules\Core\Models\Settings\AdminUnits\District;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Model;


class AdminUnitsLowerLocalGovernments extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;




    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function getLowerLocalGovernmentForm(): array
    {
        return [

            Grid::make(2)
                ->schema([
                    Select::make('district_id')
                        ->label('District')
                        ->options(District::query()->pluck('name', 'id'))
                        ->searchable()
                        ->preload()
                        ->dehydrated(false)
                        ->afterStateUpdated(function (callable $set) {
                            $set('upper_local_gov_id', null);
                        })
                        ->live()
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),
                    Select::make('upper_local_gov_id')
                        ->label('Upper Local Government')
                        ->options(function (Get $get, ?Model $record): Collection {
                            $UpperLocalGovId = $get('district_id') ?? $record?->upper_local_government?->district_id;

                            if (!$UpperLocalGovId) {
                                return UpperLocalGovernment::query()->pluck('name', 'id');
                            }

                            return UpperLocalGovernment::where('district_id', $UpperLocalGovId)->pluck('name', 'id');
                        })
                        ->searchable()
                        ->required()
                        ->preload()
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),
                ]),

            Grid::make(2)
                ->schema([
                    TextInput::make('name')
                        ->label(' Lower Local Government Name')
                        ->required()
                        ->maxLength(200)
                        ->placeholder('Enter  Lower Local Government name')
                        ->columnSpan(fn(string $context) => $context === 'create' ? 2 : 1)
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),

                    Select::make('status')
                        ->label('Status')
                        ->options([
                            'Active' => 'Active',
                            'Inactive' => 'Inactive',
                        ])
                        ->required()
                        ->columnSpan(fn(string $context) => $context === 'edit' ? 1 : 1)
                        ->extraAttributes([
                            'class' => '  p-1 w-full shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-red-400 ',
                        ])
                        ->visible(fn(string $context) => $context === 'edit'),
                ]),

            Grid::make(2)
                ->schema([
                    Select::make('local_gov_type_id')
                        ->label('Type')
                        ->options(fn() => SettingLowerLocalGovernmentType::pluck('name', 'id'))
                        ->searchable()
                        ->required()
                        ->preload()
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),

                    TextInput::make('vote_code')
                        ->label('Lower Local Government Code')
                        ->required()
                        ->maxLength(25)
                        ->unique(LowerLocalGovernment::class, 'vote_code', fn($record) => $record)
                        ->placeholder('Enter Lower Local Government e.g LL0007')
                        ->extraAttributes([
                            'class' => 'p-1 shadow-none w-[300px] -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                        ]),


                ]),
        ];
    }


    private function handleImportAction(Collection $records, array $data): void
    {
        $filePath = Storage::path($data['file']);

        $createdCount = 0;
        $skippedRows = [];
        $rowErrors = [];

        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            if (empty($rows) || !isset($rows[0])) {
                throw new \Exception('Excel file is empty or malformed');
            }

            $headers = array_map(fn($h) => Str::slug(trim($h), '_'), $rows[0]);
            $requiredHeaders = ['name', 'vote_code', 'upper_local_gov_id', 'local_gov_type_id'];
            $missingHeaders = array_diff($requiredHeaders, $headers);

            if (!empty($missingHeaders)) {
                throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
            }

            for ($i = 1; $i < count($rows); $i++) {
                try {
                    $rowData = array_values($rows[$i]);
                    $row = array_combine($headers, $rowData);

                    if (empty($row['name']) || empty($row['vote_code']) || empty($row['upper_local_gov_id']) || empty($row['local_gov_type_id'])) {
                        $skippedRows[] = "Row " . ($i + 1) . ": Required fields(columns) are missing.";
                        continue;
                    }

                    $UpperLocalGovernmentName = strtolower(trim($row['upper_local_gov_id']));
                    $UpperLocalGovernmentName = UpperLocalGovernment::whereRaw('LOWER(name) = ?', [$UpperLocalGovernmentName])->first();

                    if (!$UpperLocalGovernmentName) {
                        $skippedRows[] = "Row " . ($i + 1) . ": Upper Local Government not found! '{$row['upper_local_gov_id']}' not found.";
                        continue;
                    }

                    $Type = strtolower(trim($row['local_gov_type_id']));
                    $Type = SettingLowerLocalGovernmentType::whereRaw('LOWER(name) = ?', [$Type])->first();

                    if (!$Type) {
                        $skippedRows[] = "Row " . ($i + 1) . ": Lower Local Governmnet not found! '{$row['local_gov_type_id']}' not found.";
                        continue;
                    }


                    if (LowerLocalGovernment::where('vote_code', $row['vote_code'])->exists()) {
                        $skippedRows[] = "Row " . ($i + 1) . ": Lower Local Government code (vote_code) '{$row['vote_code']}' already exists.";
                        continue;
                    }


                    LowerLocalGovernment::create([
                        'name' => $row['name'],
                        'vote_code' => $row['vote_code'],
                        'upper_local_gov_id' => $UpperLocalGovernmentName->id,
                        'local_gov_type_id' => $Type->id,
                    ]);

                    $createdCount++;
                } catch (\Exception $rowException) {
                    $rowErrors[] = "Row " . ($i + 1) . ": " . $rowException->getMessage();
                }
            }

            Storage::delete($data['file']);

            // 🎉 Final success summary notification
            $message = "Import complete: {$createdCount}  Lower Local governments created.";
            if (!empty($skippedRows)) {
                $message .= " " . count($skippedRows) . " rows skipped.";
            }
            if (!empty($rowErrors)) {
                $message .= " " . count($rowErrors) . " errors encountered.";
            }

            $this->dispatch('notify', [
                'type' => $createdCount > 0 ? 'success' : 'warning',
                'message' => $message,
                'details' => array_merge($skippedRows, $rowErrors),
            ]);
        } catch (\Exception $e) {

            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to import  Lower Local governments: ' . $e->getMessage() . '. Please try again later!',
                'details' => array_merge($skippedRows, $rowErrors),
            ]);
        }
    }



    public function table(Table $table): Table
    {
        return $table
            ->query(LowerLocalGovernment::query())
            ->defaultSort('date_updated', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(['class' => 'bg-sky-500 pl-60 '])
                    ->label(' Lower Local Government Name')
                    ->limit(50),

                TextColumn::make('vote_code')
                    ->searchable()
                    ->sortable()
                    ->label(' Lower Local Government Code')
                    ->badge(),

                TextColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->badge()
                    ->color(fn($record) => $record->status == 'Active' ? 'success' : 'danger')
                    ->icon(fn($record) => $record->status == 'Active' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                    ->extraAttributes([
                        'class' => 'text-red-500 ',
                    ]),

                TextColumn::make('type.name')
                    ->searchable()
                    ->sortable()
                    ->label('Type'),

                TextColumn::make('upper_local_government.name')
                    ->searchable()
                    ->sortable()
                    ->label('Upper Local Government'),

                TextColumn::make('upper_local_government.district.name')
                    ->searchable()
                    ->sortable()
                    ->label('District'),
            ])
            ->headerActions([

                \Filament\Tables\Actions\CreateAction::make('import')
                    ->label('Import ')
                    ->form([
                        FileUpload::make('file')
                            ->label('Upload CSV or Excel File')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            ])
                            ->disk('local')
                            ->required()
                            ->directory('imports'),
                    ])
                    ->action(fn(Collection $records, array $data) => $this->handleImportAction($records, $data))
                    ->modalHeading('Import  Lower Local Governments')
                    ->modalDescription('Upload a CSV or Excel file to import Lower Local Governments.')
                    ->modalContent(fn() => AdminUnitHelpers::importTemplateNote('/lower-local-governments-sample'))

                    ->color('success')
                    ->createAnother(false)
                    ->modalWidth('xl')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->modalSubmitActionLabel('Import')
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS]),

                CreateAction::make()
                    ->label('Create')
                    ->icon('heroicon-o-plus')
                    ->modalHeading('Create  Lower Local Government')
                    ->modalWidth('2xl')
                    ->form(
                        $this->getLowerLocalGovernmentForm()
                    )
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])

                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => ' Lower Local Government created successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating  Lower Local Government. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    })
            ])


            ->actions([
                ViewAction::make()
                    ->modalWidth('3xl')
                    ->form([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('name')
                                    ->label('  Name')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                                    TextInput::make('vote_code')
                                    ->label(' Government Code')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),

                

                                TextInput::make('status')
                                    ->label('Status')
                                    ->disabled()
                                    ->extraAttributes([
                                        'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                    ]),
                              
                            ]),

                        Grid::make(3)
                            ->schema([

                                TextInput::make('type.name')
                                ->label('Lower Local Government Type')
                                ->formatStateUsing(fn($record) => $record->type->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                ]),

                                TextInput::make('upper_local_government.name')
                                ->label('Upper Local Government ')
                                ->formatStateUsing(fn($record) => $record->upper_local_government->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                ]),

                                TextInput::make('district_name')
                                ->label('District')
                                ->formatStateUsing(fn($state, $record) => $record->upper_local_government->district->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                ]),

                         


                            ]),

                        Grid::make(2)
                            ->schema([

                                TextInput::make('sub_region_name')
                                ->label('Sub Region')
                                ->formatStateUsing(fn($state, $record) => $record->upper_local_government->district->sub_region->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400',
                                ]),

                                TextInput::make('region_name')
                                ->label('Region ')
                                ->formatStateUsing(fn($record) => $record->upper_local_government->district->sub_region->region->name ?? '—')
                                ->disabled()
                                ->extraAttributes([
                                    'class' => '  p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400 ',
                                ]),
                            ]),


                    ]),

                EditAction::make()
                    ->label('Edit')
                    ->modalHeading('Edit Lower Local Government')
                    ->modalWidth('xl')
                    ->form($this->getLowerLocalGovernmentForm())

                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Lower Local Government updated successfully.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while updating  Lower Local Government. Please try again later! ' . $e->getMessage(),
                            ]);
                        }
                    }),



                EditAction::make('status')
                    ->label(fn($record) => $record->status == 'Active' ? 'Disable' : 'Enable')
                    ->requiresConfirmation()
                    ->modalDescription(fn($record) => $record->status == 'Active' ? 'Are you sure you want to disable this Lower Local Government?' : 'Are you sure you want to enable this Lower Local Government?')
                    ->color('primary')
                    ->icon('heroicon-o-information-circle')
                    ->modalAlignment('start')
                    ->using(fn($record) => $record->update([
                        'status' => $record->status == 'Active' ? 'Inactive' : 'Active',
                        'date_updated' => now(),
                    ]))

                    ->after(function ($record) {
                        try {
                            $statusMessage = $record->status === 'Active'
                                ? 'Lower Local Government enabled successfully.'
                                : 'Lower Local Government disabled successfully.';

                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => $statusMessage,
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong. Please try again later. ' . $e->getMessage(),

                            ]);
                        }
                    }),




            ]);
    }
    public function render()
    {
        return view('admin::livewire.admin-units-lower-local-governments');
    }
}
