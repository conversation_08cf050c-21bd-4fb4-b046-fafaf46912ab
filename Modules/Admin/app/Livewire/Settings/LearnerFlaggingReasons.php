<?php

namespace Modules\Admin\Livewire\Settings;

use Livewire\Component;
use Livewire\WithPagination;
use Modules\Core\Models\Settings\FlaggingReason;
use Modules\Core\Models\Settings\SchoolType;

class LearnerFlaggingReasons extends Component
{
    use WithPagination;

    public string $reasonName = '';
    public array  $school_type_ids = [];
    public ?int   $editing_id = null;

    public array $schoolTypes = [];

    public int $perPage = 12;
    public string $searchTerm = '';

    protected $rules = [
        'reasonName'      => 'required|string',
        'school_type_ids' => 'required|array|min:1',
    ];

    public bool   $searchActive = false;
    public function search(): void
    {
        $this->searchActive = trim($this->searchTerm) !== '';
        $this->resetPage();
    }

    public function resetSearch(): void
    {
        $this->searchTerm   = '';
        $this->searchActive = false;
        $this->resetPage();
    }

    public function toggleVisibility(int $id): void
    {
        $reason = FlaggingReason::findOrFail($id);
        $reason->is_visible = !$reason->is_visible;
        $reason->save();

        $this->dispatch('notify', [
            'status'  => 'success',
            'title'   => 'Success:',
            'message' => $reason->is_visible
                ? 'Reason enabled successfully.'
                : 'Reason disabled successfully.',
        ]);
    }

    public function mount(): void
    {
        $this->schoolTypes = SchoolType::query()
            ->select('id', 'name')
            ->orderBy('name')
            ->get()
            ->toArray();
    }

    public function save(): void
    {
        $this->validate();

        if ($this->editing_id) {
            $reason = FlaggingReason::findOrFail($this->editing_id);
            $reason->update([
                'name'           => $this->reasonName,
                'is_for_learner' => true,
            ]);
        } else {
            $reason = FlaggingReason::create([
                'name'           => $this->reasonName,
                'is_for_learner' => true,
            ]);
        }

        $reason->schoolTypes()->sync($this->school_type_ids);

        $this->resetForm();
        $this->dispatch('close-flagging-modal');
        $this->dispatch('notify', [
            'status'  => 'success',
            'title'   => 'Success:',
            'message' => $this->editing_id
                ? 'Reason updated successfully.'
                : 'Reason created successfully.',
        ]);
    }

    public function edit(int $id): void
    {
        $reason = FlaggingReason::findOrFail($id);

        $this->editing_id      = $reason->id;
        $this->reasonName      = $reason->name;
        $this->school_type_ids = $reason->schoolTypes->pluck('id')->toArray();
    }

    public function create(): void
    {
        $this->resetForm();
        $this->dispatchBrowserEvent('open-reason-modal');
    }

    public function resetForm(): void
    {
        $this->editing_id      = null;
        $this->reasonName      = '';
        $this->school_type_ids = [];
        $this->resetErrorBag();
        $this->resetValidation();
    }

    public function render()
    {
        $query = FlaggingReason::withoutGlobalScope('visible')
            ->where('is_for_learner', true);

        if ($this->searchTerm !== '') {
            $query->where('name', 'ilike', '%' . $this->searchTerm . '%');
        }

        return view('admin::livewire.settings.learner-flagging-reasons', [
            'flaggingReasons' => $query->paginate($this->perPage),
        ]);
    }
}
