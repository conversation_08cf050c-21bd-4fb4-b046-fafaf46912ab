<?php

namespace Modules\Admin\Livewire;
use Livewire\Component;
use Modules\Admin\Models\SubRegion;
use Modules\Core\Models\Settings\AdminUnits\Region;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DateTimePicker;
use Illuminate\Support\Collection;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\Action;
use Modules\Admin\Helpers\AdminUnitHelpers;

class AdminUnitSubRegions extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    // Tailwind Classes Constants
    private const INPUT_CLASS = 'p-1 shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const SELECT_CLASS = 'p-1 appearance-none shadow-none -mt-4 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';
    private const ACTION_BUTTON_CLASS = 'h-3 p-3 shadow-none -mt-3 focus:shadow-none focus:ring-0 focus:outline-none border border-gray-400';

    private function getFormSchema(bool $isDisabled = false, bool $showStatus = false): array
    {
        $fields = [
            TextInput::make('name')
                ->label('Sub Region Name')
                ->required(!$isDisabled)
                ->maxLength(200)
                ->placeholder('Enter sub region name')
                ->disabled($isDisabled)
                ->extraAttributes(['class' => self::INPUT_CLASS]),
            Select::make('region_id')
                ->label('Region')
                ->options(fn () => Region::pluck('name', 'id'))
                ->searchable()
                ->required(!$isDisabled)
                ->preload()
                ->disabled($isDisabled)
                ->extraAttributes(['class' => self::SELECT_CLASS]),
            TextInput::make('sub_region_code')
                ->label('Sub Region Code')
                ->required(!$isDisabled)
                ->maxLength(25)
                ->unique(SubRegion::class, 'sub_region_code', fn ($record) => $record)
                ->placeholder('Enter sub region code e.g SR0007')
                ->disabled($isDisabled)
                ->extraAttributes(['class' => self::INPUT_CLASS]),
        ];

        // Only add status field if showStatus is true
        if ($showStatus) {
            $fields[] = Select::make('status')
                ->label('Status')
                ->options(['Active' => 'Active', 'Inactive' => 'Inactive'])
                ->required(!$isDisabled)
                ->native(false)
                ->searchable()
                ->extraAttributes(['class' => self::SELECT_CLASS]);
        }

        return $fields;
    }

    private function getViewFormSchema(): array
    {
        $isDisabled = true; // Adjust as needed depending on view/edit context

        return [
            Grid::make(2)
                ->schema([
                    TextInput::make('name')
                        ->label('Sub Region Name')
                        ->required(!$isDisabled)
                        ->maxLength(200)
                        ->placeholder('Enter sub region name')
                        ->disabled($isDisabled)
                        ->extraAttributes(['class' => self::INPUT_CLASS]),

                    TextInput::make('sub_region_code')
                        ->label('Sub Region Code')
                        ->required(!$isDisabled)
                        ->maxLength(25)
                        ->unique(SubRegion::class, 'sub_region_code', fn ($record) => $record)
                        ->placeholder('Enter sub region code e.g. SR0007')
                        ->disabled($isDisabled)
                        ->extraAttributes(['class' => self::INPUT_CLASS]),
                ]),

            Grid::make(2)
                ->schema([
                    TextInput::make('region.name')
                    ->label('Region')
                    ->formatStateUsing(fn ($record) => $record->region->name ?? '—')
                    ->disabled()
                    ->extraAttributes(['class' => self::INPUT_CLASS]),

                      
                        TextInput::make('status')
                        ->label('Status')
                        ->disabled()
                        ->extraAttributes(['class' => self::INPUT_CLASS]),
                        ])
                

            
        ];
    }


    private function handleImportAction(Collection $records, array $data): void
    {
        $filePath = Storage::path($data['file']);

        $created = 0;
        $skipped = 0;
        $duplicates = 0;
        $rowErrors = [];
        $duplicateDetails = [];
        $inFileDuplicates = [];
        $processedCodes = []; // Track codes within this import file

        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            if (empty($rows) || !isset($rows[0])) {
                throw new \Exception('Excel file is empty or malformed.');
            }

            // Normalize headers
            $headers = array_map(fn($h) => Str::slug(trim($h), '_'), $rows[0]);
            $requiredHeaders = ['name', 'sub_region_code', 'region'];
            $missingHeaders = array_diff($requiredHeaders, $headers);

            if (!empty($missingHeaders)) {
                throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
            }

            // Pre-fetch all existing sub-region codes for efficient duplicate checking
            $existingCodes = SubRegion::pluck('sub_region_code')
                ->map(fn($code) => strtolower($code))
                ->toArray();

            for ($i = 1; $i < count($rows); $i++) {
                try {
                    $rowData = array_values($rows[$i]);
                    $rowNumber = $i + 1;

                    // Skip empty rows
                    if (empty(array_filter($rowData))) {
                        continue;
                    }

                    $row = array_combine($headers, $rowData);

                    // Check required fields first
                    if (empty($row['name']) || empty($row['sub_region_code']) || empty($row['region'])) {
                        $skipped++;
                        continue;
                    }

                    $subRegionCode = trim($row['sub_region_code']);
                    $subRegionCodeLower = strtolower($subRegionCode);

                    // Check for duplicates within the import file itself
                    if (in_array($subRegionCodeLower, $processedCodes)) {
                        $duplicates++;
                        $inFileDuplicates[] = [
                            'row' => $rowNumber,
                            'code' => $subRegionCode,
                            'name' => $row['name'],
                            'type' => 'in_file'
                        ];
                        continue;
                    }

                    // Check for duplicates against existing database records
                    if (in_array($subRegionCodeLower, $existingCodes)) {
                        $duplicates++;
                        $existingRecord = SubRegion::whereRaw('LOWER(sub_region_code) = ?', [$subRegionCodeLower])->first();
                        $duplicateDetails[] = [
                            'row' => $rowNumber,
                            'code' => $subRegionCode,
                            'name' => $row['name'],
                            'existing_name' => $existingRecord->name ?? 'Unknown',
                            'existing_id' => $existingRecord->id ?? null,
                            'type' => 'database'
                        ];
                        continue;
                    }

                    // Match region
                    $regionName = strtolower(trim($row['region']));
                    $region = \Modules\Core\Models\Settings\AdminUnits\Region::whereRaw('LOWER(name) = ?', [$regionName])->first();

                    if (!$region) {
                        $skipped++;
                        $rowErrors[] = "Row $rowNumber: Region '{$row['region']}' not found";
                        continue;
                    }

                    // Create the sub-region
                    SubRegion::create([
                        'name' => trim($row['name']),
                        'sub_region_code' => $subRegionCode,
                        'region_id' => $region->id,
                    ]);

                    // Track this code as processed
                    $processedCodes[] = $subRegionCodeLower;
                    $created++;

                } catch (\Exception $rowException) {
                    $rowErrors[] = "Row " . ($i + 1) . ": " . $rowException->getMessage();
                }
            }

            Storage::delete($data['file']);

            // Build detailed summary
            $summary = "Import completed: $created created, $duplicates duplicate(s), $skipped skipped.";
            $details = [];

            if (!empty($rowErrors)) {
                $summary .= " " . count($rowErrors) . " row(s) failed.";
                $details = array_merge($details, $rowErrors);
            }

            // Add duplicate details to the notification
            if (!empty($duplicateDetails)) {
                $details[] = "--- Database Duplicates ---";
                foreach ($duplicateDetails as $duplicate) {
                    $details[] = "Row {$duplicate['row']}: Code '{$duplicate['code']}' already exists (ID: {$duplicate['existing_id']}, Name: '{$duplicate['existing_name']}')";
                }
            }

            if (!empty($inFileDuplicates)) {
                $details[] = "--- In-File Duplicates ---";
                foreach ($inFileDuplicates as $duplicate) {
                    $details[] = "Row {$duplicate['row']}: Code '{$duplicate['code']}' appears multiple times in this file";
                }
            }

            $this->dispatch('notify', [
                'type' => $created > 0 ? 'success' : ($duplicates > 0 ? 'warning' : 'info'),
                'message' => $summary,
                'details' => $details,
            ]);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Import failed: ' . $e->getMessage(),
            ]);

        }
    }


    public function table(Table $table): Table
    {
        return $table
            ->query(SubRegion::query())
            ->defaultSort('date_updated', 'desc')
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(['class' => 'bg-sky-500 pl-60'])
                    ->label('Sub Region Name')
                    ->limit(50),
                TextColumn::make('sub_region_code')
                    ->searchable()
                    ->sortable()
                    ->label('Sub Region Code')
                    ->badge(),
                TextColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->badge()
                    ->colors([
                        'success' => 'Active',
                        'danger' => 'Inactive',
                    ])
                    ->icons([
                        'heroicon-o-check-circle' => 'Active',
                        'heroicon-o-x-circle' => 'Inactive',
                    ]),
                TextColumn::make('region.name')
                    ->searchable()
                    ->sortable()
                    ->label('Region'),
            ])
            ->headerActions([
                \Filament\Tables\Actions\CreateAction::make()
                    ->label('Sub Region')
                    ->modalHeading('Create New Sub Region')
                    ->modalWidth('xl')
                    ->icon('heroicon-o-plus')
                    ->form($this->getFormSchema())
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS])

                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Sub Region created.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while creating a sub region. Please try again ' . $e->getMessage(),
                            ]);
                        }
                    }),
                \Filament\Tables\Actions\CreateAction::make('import')
                    ->label('Import')
                    ->form([
                        FileUpload::make('file')
                            ->label('Upload an Excel File')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            ])
                            ->disk('local')
                            ->required()
                            ->directory('imports'),
                    ])
                    ->action(fn(Collection $records, array $data) => $this->handleImportAction($records, $data))
                    ->modalHeading('Import Sub Regions')
                    ->modalContent(fn () => AdminUnitHelpers::importTemplateNote('/subregions-sample'))

                    ->color('success')
                    ->modalWidth('lg')
                    ->createAnother(false)
                    ->icon('heroicon-o-arrow-down-tray')
                    ->extraAttributes(['class' => self::ACTION_BUTTON_CLASS]),

            ])
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->modalWidth('xl')
                    ->modalHeading('View Sub Region')
                    ->form($this->getViewFormSchema())

                   ,

                \Filament\Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->modalHeading('Edit Sub Region')
                    ->modalWidth('xl')
                    ->form($this->getFormSchema(false, true))
                    ->after(function () {
                        try {
                            $this->dispatch('notify', [
                                'type' => 'success',
                                'message' => 'Sub Region Edited.',
                            ]);
                        } catch (\Exception $e) {
                            $this->dispatch('notify', [
                                'type' => 'error',
                                'message' => 'Something went wrong while editing a sub region. Please try again ' . $e->getMessage(),
                            ]);
                        }
                    }),

                Action::make('toggle_status')
                ->label(fn ($record) => $record->status === 'Active' ? 'Disable' : 'Enable')
                ->icon(fn ($record) => $record->status === 'Active' ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                ->color(fn ($record) => $record->status === 'Active' ? 'danger' : 'success')
                ->requiresConfirmation()
                ->modalAlignment('start')
                ->modalHeading(fn ($record) => $record->status === 'Active' ? 'Disable Region' : 'Enable Region')
                ->modalDescription(fn ($record) => $record->status === 'Active'
                    ? 'Are you sure you want to disable this region? '
                    : 'Are you sure you want to enable this region?')
                ->modalSubmitActionLabel(fn ($record) => $record->status === 'Active' ? 'Disable' : 'Enable')
                ->action(function ($record) {
                    $newStatus = $record->status === 'Active' ? 'Inactive' : 'Active';
                    $record->update([
                        'status' => $newStatus,
                        'date_updated' => now(),
                    ]);

                    $actionWord = $newStatus === 'Active' ? 'enabled' : 'disabled';
                    $this->dispatch('notify', [
                        'type' => 'success',
                        'message' => "Sub Region {$record->name} has been {$actionWord}.",
                    ]);
                }),
            ]);
    }

    public function render()
    {
        return view('admin::livewire.admin-unit-sub-regions');
    }
}
