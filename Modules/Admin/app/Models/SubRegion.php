<?php
namespace Modules\Admin\Models;
use Modules\Core\Models\Settings\AdminUnits\Region;
use App\Models\Institutions\SchoolCampus;
use App\Models\Settings\Clusters\ClusterDistrict;
use Modules\Core\Traits\Initials;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Core\Models\Institutions\DiplomaAwardingSchool;
use Modules\Core\Models\Institutions\PrePrimarySchool;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Settings\AdminUnits\District;

class SubRegion extends Model
{
    use HasFactory, \Modules\Core\Traits\Initials;

    protected $guarded = [];
    protected $table = 'admin_unit_sub_regions';
    protected $hidden = ['date_created', 'date_updated'];
    protected $appends = ['initials'];

    const CREATED_AT = 'date_created';
    const UPDATED_AT = 'date_updated';

    public function wordForInitials() { return $this->name; }

    public function getNameAttribute($name) { return strtoupper($name); }

    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class, 'region_id');
    }

    public function districts(): HasMany
    {
        return $this->hasMany(District::class, 'sub_region_id');
    }

    public function pre_primary_schools(): HasMany
    {
        return $this->hasMany(PrePrimarySchool::class, 'region_id');
    }

    public function primary_schools(): HasMany
    {
        return $this->hasMany(DiplomaAwardingSchool::class, 'region_id');
    }

    public function secondary_schools(): HasMany
    {
        return $this->hasMany(DiplomaAwardingSchool::class, 'region_id');
    }

    public function schools(): HasMany
    {
        return $this->hasMany(School::class, 'district_id');
    }
}
