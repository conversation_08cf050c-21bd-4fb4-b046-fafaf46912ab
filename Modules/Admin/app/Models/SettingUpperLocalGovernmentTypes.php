<?php

namespace Modules\Admin\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Core\Traits\Initials;
use Modules\core\Models\settings\AdminUnits\UpperLocalGovernment;
// use Modules\Admin\Database\Factories\SettingUpperLocalGovernmentTypesFactory;

class SettingUpperLocalGovernmentTypes extends Model
{
    use HasFactory;

    use HasFactory, Initials;

    protected $guarded = [];

    protected $table = 'setting_upper_local_government_types';

    protected $appends = ['initials'];

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    public function wordForInitials()
    {
        return $this->name;
    }

    public function getNameAttribute($name)
    {
        return strtoupper($name);
    }
    
    public function upperLocalGovernments()
    {
    return $this->hasMany(UpperLocalGovernment::class, 'type_id');
    }


}
