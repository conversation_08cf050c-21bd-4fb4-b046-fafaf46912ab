<?php

namespace Modules\Admin\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Core\Models\Settings\AdminUnits\SubCounty;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cluster extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $table = 'clusters';

    protected $hidden = ['date_created', 'date_updated'];

    const CREATED_AT = 'date_created';

    const UPDATED_AT = 'date_updated';

    public function wordForInitials()
    {
        return $this->name;
    }

    public function getNameAttribute($name)
    {
        return strtoupper($name);
    }

    public function subcounty(): BelongsTo
    {
        return $this->belongsTo(SubCounty::class, 'sub_county_id');
    }
}
