<?php

namespace Modules\Admin\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Nwidart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Livewire\Livewire;
use Modules\Admin\Livewire\Settings\LearnerFlaggingReasons;

class AdminServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'Admin';

    protected string $nameLower = 'admin';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));
        Livewire::component('admin.admin-units-districts', \Modules\Admin\Livewire\AdminUnitsDistricts::class);
        Livewire::component('admin.region-admin-unit', \Modules\Admin\Livewire\RegionAdminUnit::class);
        Livewire::component('admin.admin-units-sub-counties', \Modules\Admin\Livewire\AdminUnitsSubCounties::class);
        Livewire::component('admin.admin-unit-sub-regions', \Modules\Admin\Livewire\AdminUnitSubRegions::class);
        Livewire::component('admin.notification', \Modules\Admin\Livewire\Notification::class);
        Livewire::component('admin.admin-unit-counties', \Modules\Admin\Livewire\AdminUnitsCounties::class);
        Livewire::component('admin.admin-unit-villages', \Modules\Admin\Livewire\AdminUnitVillages::class);
        Livewire::component('admin.admin-units-parishes', \Modules\Admin\Livewire\AdminUnitsParishes::class);
        Livewire::component('admin.clusters', \Modules\Admin\Livewire\Clusters::class);
        Livewire::component('admin.admin-units-upper-local-governments', \Modules\Admin\Livewire\AdminUnitsUpperLocalGovernments::class);
        Livewire::component('admin.admin-units-lower-local-governments', \Modules\Admin\Livewire\AdminUnitsLowerLocalGovernments::class);
        Livewire::component('admin.settings.learner-flagging-reasons', LearnerFlaggingReasons::class);
        Livewire::component('admin-learner-enrolment-applications-index', \Modules\Admin\Livewire\AdminLearnerPreRegistrationApplicationsIndex::class);
        Livewire::component('admin-learner-enrolment-application-show', \Modules\Admin\Livewire\AdminLearnerPreRegistrationApplicationsShow::class);
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/'.$this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $configPath = module_path($this->name, config('modules.paths.generator.config.path'));

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $config = str_replace($configPath.DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $config_key = str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $config);
                    $segments = explode('.', $this->nameLower.'.'.$config_key);

                    // Remove duplicated adjacent segments
                    $normalized = [];
                    foreach ($segments as $segment) {
                        if (end($normalized) !== $segment) {
                            $normalized[] = $segment;
                        }
                    }

                    $key = ($config === 'config.php') ? $this->nameLower : implode('.', $normalized);

                    $this->publishes([$file->getPathname() => config_path($config)], 'config');
                    $this->merge_config_from($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Merge config from the given path recursively.
     */
    protected function merge_config_from(string $path, string $key): void
    {
        $existing = config($key, []);
        $module_config = require $path;

        config([$key => array_replace_recursive($existing, $module_config)]);
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/'.$this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        Blade::componentNamespace(config('modules.namespace').'\\' . $this->name . '\\View\\Components', $this->nameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->nameLower)) {
                $paths[] = $path.'/modules/'.$this->nameLower;
            }
        }

        return $paths;
    }
}
