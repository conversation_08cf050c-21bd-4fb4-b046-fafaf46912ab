<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\Admin\Approvals\AdminManageFlaggedInstitutionController;
use Modules\Core\Http\Controllers\Api\V1\AcademicYearController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\AdminLearnerEnrolmentApplicationsApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\DataUpdate\AdminLearnersFlaggedForDeletingApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\DeveloperModeController;
use Modules\Core\Http\Controllers\Api\V1\Admin\DistrictUserManagerController;
use Modules\Core\Http\Controllers\Api\V1\Admin\InstitutionUserManagerController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Learners\AdminLearnerApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\MinistryUserManagerController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Projects\ProjectBeneficiariesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Projects\ProjectFundersController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Projects\ProjectsController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Reports\AdminReportsController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Reports\AdminReportsInfrastructureApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\RoleController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Schools\AdminSchoolParentsApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Schools\AdminSchoolsApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\AcademicYearSettingController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\AcmisApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\AdminApiClustersController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\AmendSchoolDetailsReasonApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\EmisReturnsApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\LearnerFlaggingReasonApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\LearnerTransferReasonController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\LicensingAndRegistrationRequirementController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\CategoriesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\CompletionStatusesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\ComponentsController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\ComponentTypesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\CurrenciesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Projects\FundersController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\ReligionController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\SchoolSuspensionReasonsController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\SchoolTypeRequirementController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\TeacherProfessionalQualificationController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\TeacherResponsibilityController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Tickets\TicketCategoriesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Settings\Tickets\TicketSubCategoriesController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\AdminNonTeachingStaffApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\AdminTeacherApiController;
use Modules\Core\Http\Controllers\Api\V1\Admin\SubCountyUserManagerController;
use Modules\Core\Http\Controllers\Api\V1\Admin\SurveyManagerController;
use Modules\Core\Http\Controllers\Api\V1\Cluster\Approvals\EmisNumberApplicationClusterController;
use Modules\Core\Http\Controllers\Api\V1\Cluster\DataUpdate\LearnerEsoFlaggedForDeletingApiController;
use Modules\Core\Http\Controllers\Api\V1\Cluster\Regional\AdminApiClustersRegionalController;
use Modules\Core\Http\Controllers\Api\V1\Cluster\Schools\ClusterUserSchoolParentsApiController;
use Modules\Core\Http\Controllers\Api\V1\Cluster\Schools\ClusterUserSchoolsApiController;
use Modules\Core\Http\Controllers\Api\V1\CourseCategoryController;
use Modules\Core\Http\Controllers\Api\V1\District\Approvals\DistrictLearnerEnrolmentApplicationsApiController;
use Modules\Core\Http\Controllers\Api\V1\District\Approvals\EmisNumberApplicationController;
use Modules\Core\Http\Controllers\Api\V1\District\DistrictSchoolParentsApiController;
use Modules\Core\Http\Controllers\Api\V1\HCM\HcmController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Approvals\LearnerEnrolmentApplicationsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\CertificateAwardingSchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\DegreeAwardingSchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\DiplomaAwardingSchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\InstitutionalInstructionalFacilitiesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\SchoolEnergySourceApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\SchoolGarbageDisposalMethodsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\SchoolOtherFacilitiesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\SchoolSanitationApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Facilities\SchoolWaterSourceApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Finance\SchoolBudgetApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Finance\SchoolExpensesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Finance\SchoolIncomeApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Governance\SchoolGovernanceApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\HealthMeals\SchoolHealthMealsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Ict\SchoolIctFacilitiesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Infrastructure\SchoolInfrastructureApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstitutionExaminedCourseApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolLabEquipmentApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolLabReagentsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolLearningAndPlayingMaterialsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolReferenceBooksApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolSneKitsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolTextBooksApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\InstructionalMaterials\SchoolWallChartsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\NonTeachingStaffApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\Parents\SchoolParentsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PeSports\SchoolPracticalSkillsApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PeSports\SchoolSportsActivitiesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PeSports\SchoolSportsEquipmentApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PeSports\SchoolSportsFacilitiesApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PrePrimarySchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\PrimarySchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SchoolContactApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SchoolCourseApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SchoolOwnersApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SecondarySchoolApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\SurveyController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\TeacherApiController;
use Modules\Core\Http\Controllers\Api\V1\Institutions\UploadedExcelsController;
use Modules\Core\Http\Controllers\Api\V1\Learners\CandidatesApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerClaimController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerEnrolmentsApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerFlaggedForDeletingApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerIndexNumberApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerLinApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerPromotionsController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerTransferController;
use Modules\Core\Http\Controllers\Api\V1\Learners\LearnerTransitionsApiController;
use Modules\Core\Http\Controllers\Api\V1\Learners\SearchLearnerLinApiController;
use Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController;
use Modules\Core\Http\Controllers\Api\V1\SubCounty\Approvals\EmisNumberApplicationSubCountyController;
use Modules\Core\Http\Controllers\Api\V1\SubCounty\Schools\SubCountySchoolParentsApiController;
use Modules\Core\Http\Controllers\Api\V1\SubCounty\Schools\SubCountySchoolsApiController;
use Modules\Core\Http\Controllers\Api\V1\TMIS\TmisApiController;
use Modules\Core\Http\Controllers\Api\V1\UNEB\UnebApiController;
use Modules\Core\Http\Controllers\Api\V1\UserController;
use Modules\Core\Http\Controllers\DownloadCentreController;
use Modules\Core\Http\Controllers\EmisNumberCertificatesController;
use Modules\Core\Http\Controllers\Institutions\EmisReturnController;
use Modules\Core\Http\Controllers\Institutions\FlagInstitutionController;
use Modules\Core\Http\Controllers\Institutions\InstitutionController;
use Modules\Core\Http\Controllers\Institutions\Notices\InstitutionEmisNoticesController;
use Modules\Core\Http\Controllers\V1\Lists\AdminUnitsController;
use Modules\Core\Http\Controllers\V1\Lists\GeneralListController;
use Modules\Core\Models\Settings\AdminUnits\County;
use Modules\Core\Models\Settings\AdminUnits\Parish;
use Modules\Core\Models\Settings\AdminUnits\SubCounty;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\NonTeachingStaffPostingController as AdminNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\NonTeachingStaffTransferController as AdminNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\TeacherPostingController as AdminTeacherPostingController;
use Modules\Core\Http\Controllers\Api\V1\Admin\Staff\TeacherTransferController as AdminTeacherTransferController;
use Modules\Core\Http\Controllers\Api\V1\District\Hr\NonTeachingStaffPostingController as DistrictNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\Api\V1\District\Hr\NonTeachingStaffTransferController as DistrictNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\Api\V1\District\Hr\TeacherPostingController as DistrictTeacherPostingController;
use Modules\Core\Http\Controllers\Api\V1\District\Hr\TeacherTransferController as DistrictTeacherTransferController;
use Modules\Core\Http\Controllers\Api\V1\District\SchoolsApiController as DistrictSchoolsApiController;
use Modules\Core\Http\Controllers\Api\V1\Staff\NonTeachingStaffPostingController as SchoolNonTeachingStaffPostingController;
use Modules\Core\Http\Controllers\Api\V1\Staff\NonTeachingStaffTransferController as SchoolNonTeachingStaffTransferController;
use Modules\Core\Http\Controllers\Api\V1\Staff\TeacherPostingController as SchoolTeacherPostingController;
use Modules\Core\Http\Controllers\Api\V1\Staff\TeacherTransferController as SchoolTeacherTransferController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */
//csp plugin - county, subcounty, parish
Route::prefix('csp')->group(function () {
    Route::get('get/counties/{district_id}', function ($district_id) {
        return County::query()->select('id', 'name')->where('district_id', $district_id)->get();
    });
    Route::get('get/subcounties/{county_id}', function ($county_id) {
        return SubCounty::query()->select('id', 'name')->where('county_id', $county_id)->get();
    });
    Route::get('get/parishes/{subcounty_id}', function ($subcounty_id) {
        return Parish::query()->select('id', 'name')->where('sub_county_id', $subcounty_id)->get();
    });
});

// NIRA Api
Route::prefix('nira')->group(function () {
    Route::post('user-info', [NiraApiController::class, 'userInfo'])->name('get-nira-data');
});

// TMIS Api
Route::prefix('tmis')->group(function () {
    Route::post('teacher-primary-info', [TmisApiController::class, 'teacherPrimaryInfo']);
    Route::post('teacher-secondary-info', [TmisApiController::class, 'teacherSecondaryInfo']);
    Route::post('teacher-health-info', [TmisApiController::class, 'teacherHealthInfo']);
    Route::post('teacher-rejected-info', [TmisApiController::class, 'teacherRejectedInfo']);
});

// External Endpoints
Route::get('course-categories', [CourseCategoryController::class, 'index']);
Route::post('learner-lin', [LearnerLinApiController::class, 'getLearnerDetails']);
//Route::post('africa-stalking-sms', [LearnerLinApiController::class, 'africaStalking']);
//Route::post('send-sms', [LearnerLinApiController::class, 'sendSMS']);

// UNEB Api
Route::prefix('uneb')->group(function () {
    Route::post('ple-learner-info', [UnebApiController::class, 'pleLearnerInfo']);
    Route::post('uce-learner-info', [UnebApiController::class, 'uceLearnerInfo']);
    Route::post('uace-learner-info', [UnebApiController::class, 'uaceLearnerInfo']);
    //Equated codes
    Route::post('equated-code', [UnebApiController::class, 'equatedData']);
    //Candidates
    // Route::post('ple-candidate-info', [UnebApiController::class, 'pleCandidatesInfo']);
    // Route::post('uce-candidate-info', [UnebApiController::class, 'uceCandidatesInfo']);
    // Route::post('uace-candidate-info', [UnebApiController::class, 'uaceCandidatesInfo']);
    Route::post('{examType}-candidate-info', [UnebApiController::class, 'getCandidatesInfo'])->where('examType', 'ple|uce|uace');
});

Route::middleware(['auth'])->group(function () {

    //Fetch permissions
    Route::get('/user-permissions', function (Request $request) {
        return response()->json([
            'permissions' => $request->user()->getAllPermissions()->pluck('name'),
        ]);
    });

    Route::post('change-my-password', [UserController::class, 'changeMyPassword']);

    Route::prefix('academic-years')->group(function () {
        Route::get('/', [AcademicYearController::class, 'all']);
    });

    Route::middleware(['admin'])->prefix('admin')->group(function () {
        //Administration
        Route::prefix('administration')->middleware('role:emis-super-admin')->group(function () {
            // utilities
            //reports
            Route::prefix('reports')->group(function () {
                Route::post('/', [AdminReportsController::class, 'index']);
            });
            Route::prefix('surveys')->group(function () {
                Route::post('/', [SurveyManagerController::class, 'all']);
                Route::post('create', [SurveyManagerController::class, 'create']);
                Route::put('update/{survey_id}', [SurveyManagerController::class, 'update']);
                Route::put('publish/{survey_id}', [SurveyManagerController::class, 'publish']);
                Route::put('un-publish/{survey_id}', [SurveyManagerController::class, 'unPublish']);
                Route::delete('delete/{survey_id}', [SurveyManagerController::class, 'delete']);
                Route::get('school-types/{id}/sections', [EmisReturnsApiController::class, 'getSections']);
            });
        });

        //EMIS Number Applications
        Route::prefix('emis-number-applications')->group(function () {
            Route::post('{type}', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'index']);
            Route::post('{schoolTypeId}/recommend/{referenceNumber}', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'recommend']);
            Route::post('{referenceNumber}/approve', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'approve']);
            Route::post('{referenceNumber}/endorse', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'endorse']);
            Route::post('{referenceNumber}/reject', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'reject']);
            Route::post('{type}/export-excel', [\Modules\Core\Http\Controllers\Api\V1\Admin\Approvals\EmisNumberApplicationController::class, 'exportExcel']);
        });

        //Schools
        Route::prefix('schools')->group(function () {
            //all
            Route::get('/{schoolTypeId}', [AdminSchoolsApiController::class, 'index']);
            Route::post('/{schoolTypeId}/filter', [AdminSchoolsApiController::class, 'filter']);
            Route::post('/{schoolId}/update-contacts', [AdminSchoolsApiController::class, 'updateContacts']);
            Route::post('/{schoolId}/update-location', [AdminSchoolsApiController::class, 'updateLocation']);
            Route::post('/{personId}/update-contact-person', [AdminSchoolsApiController::class, 'updateContactPerson']);
            Route::post('/{schoolTypeId}/update-operation-status', [AdminSchoolsApiController::class, 'updateOperationStatus']);
            Route::post('/{schoolTypeId}/export-excel', [AdminSchoolsApiController::class, 'exportExcel']);
            Route::post('/{schoolTypeId}/export-enrolment-excel', [AdminSchoolsApiController::class, 'exportEnrolmentStatsExcel']);
            Route::post('/{schoolTypeId}/export-promotions-excel', [AdminSchoolsApiController::class, 'exportPromotionStatsExcel']);
            Route::get('emis-number-certificates/{emisNumber}', [AdminSchoolsApiController::class, 'generatePdf']);

            Route::prefix('infrastructure')->group(function () {
                //all
                Route::get('/{schoolTypeId}', [AdminReportsInfrastructureApiController::class, 'index']);
                Route::post('/{schoolTypeId}/filter', [AdminReportsInfrastructureApiController::class, 'filter']);
                Route::post('/{schoolTypeId}/export-excel', [AdminReportsInfrastructureApiController::class, 'exportExcel']);
            });
        });

        //Parents
        Route::prefix('fetch-parents')->group(function () {
            Route::get('/', [AdminSchoolParentsApiController::class, 'getParents']);
            Route::post('/filter', [AdminSchoolParentsApiController::class, 'filter']);
        });

        //manage projects
        Route::prefix('projects')->group(function () {
            Route::post('/', [ProjectsController::class, 'index']);
            Route::post('store', [ProjectsController::class, 'store']);
            Route::put('{project_id}', [ProjectsController::class, 'update']);
            //Funders
            Route::prefix('funders')->group(function () {
                Route::post('/', [ProjectFundersController::class, 'index']);
                Route::post('store', [ProjectFundersController::class, 'store']);
                Route::put('{funder_id}', [ProjectFundersController::class, 'update']);
                Route::delete('remove/{id}{project_id}', [ProjectFundersController::class, 'destroy']);
            });
            //Beneficiaries
            Route::prefix('beneficiaries')->group(function () {
                Route::post('/', [ProjectBeneficiariesController::class, 'index']);
                Route::post('store', [ProjectBeneficiariesController::class, 'store']);
                Route::delete('remove/{id}{project_id}', [ProjectBeneficiariesController::class, 'destroy']);
            });
        });

        Route::prefix('user-manager')->middleware('role:emis-super-admin')->group(function () {
            // Ministry Users
            Route::prefix('ministry-users')->group(function () {
                Route::post('/', [MinistryUserManagerController::class, 'allUsers']);
                Route::post('/filter', [MinistryUserManagerController::class, 'allUsers']);
                Route::post('create', [MinistryUserManagerController::class, 'createUser']);
                Route::put('update/{person_id}', [MinistryUserManagerController::class, 'updateUser']);
                Route::delete('delete/{person_id}', [MinistryUserManagerController::class, 'deleteUser']);
                Route::put('suspend/{person_id}', [MinistryUserManagerController::class, 'suspendUser']);
                Route::put('re-activate/{person_id}', [MinistryUserManagerController::class, 'reActivateUser']);
            });

            // District Users
            Route::prefix('district-users')->group(function () {
                Route::post('/ ', [DistrictUserManagerController::class, 'allUsers']);
                Route::post('/filter', [DistrictUserManagerController::class, 'allUsers']);
                Route::post('create', [DistrictUserManagerController::class, 'createUser']);
                Route::put('update/{person_id}', [DistrictUserManagerController::class, 'updateUser']);
                Route::delete('delete/{person_id}', [DistrictUserManagerController::class, 'deleteUser']);
                Route::put('suspend/{person_id}', [DistrictUserManagerController::class, 'suspendUser']);
                Route::put('re-activate/{person_id}', [DistrictUserManagerController::class, 'restoreUser']);
            });

            // Sub-County Users
            Route::prefix('sub-county-users')->group(function () {
                Route::post('/ ', [SubCountyUserManagerController::class, 'allUsers']);
                Route::post('/filter', [SubCountyUserManagerController::class, 'allUsers']);
                Route::post('create', [SubCountyUserManagerController::class, 'createUser']);
                Route::put('update/{person_id}', [SubCountyUserManagerController::class, 'updateUser']);
                Route::delete('delete/{person_id}', [SubCountyUserManagerController::class, 'deleteUser']);
                Route::put('suspend/{person_id}', [SubCountyUserManagerController::class, 'suspendUser']);
                Route::put('re-activate/{person_id}', [SubCountyUserManagerController::class, 'restoreUser']);
            });

            // Institution Users
            Route::prefix('institution-users')->group(function () {
                Route::post('/filter', [InstitutionUserManagerController::class, 'allUsers']);
                Route::put('suspend/{person_id}', [InstitutionUserManagerController::class, 'suspendUser']);
                Route::put('re-activate/{person_id}', [InstitutionUserManagerController::class, 'restoreUser']);
            });

            //Route::post('tac', [DeveloperModeController::class, 'allTac'])->middleware('developer');
            Route::middleware(['developer'])->group(function ($user) {
                Route::post('tac', [DeveloperModeController::class, 'allTac']);
                Route::post('account-creation-dumps', [DeveloperModeController::class, 'allAccountCreationDumps']);
                Route::post('duplicate-approvals', [DeveloperModeController::class, 'allDuplicateApprovals']);
                Route::post('duplicate-approvals/bulk-delete', [DeveloperModeController::class, 'bulkDeleteDuplicateApprovals']);
                Route::delete('duplicate-approvals/delete/{approval_id}', [DeveloperModeController::class, 'deleteDuplicateApprovals']);
            });
            // Roles
            Route::prefix('roles')->group(function () {
                Route::post('/', [RoleController::class, 'index']);
                Route::post('create', [RoleController::class, 'store']);
                Route::post('update/{roleId}', [RoleController::class, 'update']);
                Route::post('permissions/update/{roleId}', [RoleController::class, 'updatePermissions']);
            });
        });

        //Uploaded Excels
        Route::post('uploaded-excels', [DeveloperModeController::class, 'allUploadedExcels']);

        Route::prefix('email-outbox')->middleware('developer')->group(function () {
            Route::post('/', [DeveloperModeController::class, 'emailOutbox']);
            Route::post('{emailId}/resend', [DeveloperModeController::class, 'resendEmail']);
        });

        //Settings
        Route::prefix('settings')->group(function () {

            //Academic Year
            Route::prefix('academic-years')->group(function () {
                Route::post('/', [AcademicYearSettingController::class, 'index']);
                Route::post('store', [AcademicYearSettingController::class, 'store']);
                Route::put('{academicYear}', [AcademicYearSettingController::class, 'update']);
                Route::put('visible/{academicYear}', [AcademicYearSettingController::class, 'updateVisible']);
                Route::post('/activate', [AcademicYearSettingController::class, 'activate']);
                Route::put('update-term-dates/{termId}', [AcademicYearSettingController::class, 'updateTermDates']);
            });

            //Learner Transfers Reasons
            Route::prefix('learner-transfers')->group(function () {
                Route::post('/', [LearnerTransferReasonController::class, 'index']);
                Route::post('store', [LearnerTransferReasonController::class, 'store']);
                Route::put('{learnerTransferReason}', [LearnerTransferReasonController::class, 'update']);
                Route::put('visible/{learnerTransferReason}', [LearnerTransferReasonController::class, 'updateVisible']);
            });

            //Learner Delete Reasons
            Route::prefix('learner-flagging-reason')->group(function () {
                Route::post('/', [LearnerFlaggingReasonApiController::class, 'index']);
                Route::post('store', [LearnerFlaggingReasonApiController::class, 'store']);
                Route::put('{record}', [LearnerFlaggingReasonApiController::class, 'update']);
                Route::put('visible/{record}', [LearnerFlaggingReasonApiController::class, 'updateVisible']);
            });

            Route::prefix('amend-school-details-reason')->group(function () {
                Route::post('/', [AmendSchoolDetailsReasonApiController::class, 'index']);
                Route::post('store', [AmendSchoolDetailsReasonApiController::class, 'store']);
                Route::put('{flaggingReason}', [AmendSchoolDetailsReasonApiController::class, 'update']);
                Route::put('visible/{flaggingReason}', [AmendSchoolDetailsReasonApiController::class, 'updateVisible']);
            });

            //School Suspension Reasons
            Route::prefix('school-suspension-reasons')->group(function () {
                Route::post('/', [SchoolSuspensionReasonsController::class, 'index']);
                Route::post('store', [SchoolSuspensionReasonsController::class, 'store']);
                Route::put('{record}', [SchoolSuspensionReasonsController::class, 'update']);
                Route::put('visible/{record}', [SchoolSuspensionReasonsController::class, 'updateVisible']);
            });

            //Teacher Professional Qualification
            Route::prefix('teacher-professional-qualifications')->group(function () {
                Route::post('/', [TeacherProfessionalQualificationController::class, 'index']);
                Route::post('store', [TeacherProfessionalQualificationController::class, 'store']);
                Route::put('{qualification}', [TeacherProfessionalQualificationController::class, 'update']);
            });

            //Teacher Responsibilities
            Route::prefix('teacher-responsibilities')->group(function () {
                Route::post('/', [TeacherResponsibilityController::class, 'index']);
                Route::post('store', [TeacherResponsibilityController::class, 'store']);
                Route::put('{responsibility}', [TeacherResponsibilityController::class, 'update']);
            });

            //Religions
            Route::prefix('religions')->group(function () {
                Route::post('/', [ReligionController::class, 'index']);
                Route::post('store', [ReligionController::class, 'store']);
                Route::put('{religion}', [ReligionController::class, 'update']);
            });

            //License and Registration Requirements
            Route::prefix('license-and-registration-requirements')->group(function () {
                Route::post('/', [LicensingAndRegistrationRequirementController::class, 'index']);
                Route::post('list', [LicensingAndRegistrationRequirementController::class, 'list']);
                Route::post('store', [LicensingAndRegistrationRequirementController::class, 'store']);
                Route::get('{requirement}', [LicensingAndRegistrationRequirementController::class, 'show']);
                Route::put('{requirement}', [LicensingAndRegistrationRequirementController::class, 'update']);
                Route::put('visible/{requirement}', [LicensingAndRegistrationRequirementController::class, 'updateVisible']);
            });

            //School Type Requirements
            Route::prefix('school-type-requirements')->group(function () {
                Route::post('/', [SchoolTypeRequirementController::class, 'index']);
                Route::post('update', [SchoolTypeRequirementController::class, 'store']);
            });

            //projects
            Route::prefix('projects')->group(function () {
                Route::prefix('currencies')->group(function () {
                    Route::post('/', [CurrenciesController::class, 'index']);
                    Route::post('store', [CurrenciesController::class, 'store']);
                    Route::put('{currency}', [CurrenciesController::class, 'update']);
                    Route::put('visible/{currency}', [CurrenciesController::class, 'updateVisible']);
                });

                Route::prefix('categories')->group(function () {
                    Route::post('/', [CategoriesController::class, 'index']);
                    Route::post('store', [CategoriesController::class, 'store']);
                    Route::put('{category}', [CategoriesController::class, 'update']);
                    Route::put('visible/{category}', [CategoriesController::class, 'updateVisible']);
                });

                Route::prefix('completion_statuses')->group(function () {
                    Route::post('/', [CompletionStatusesController::class, 'index']);
                    Route::post('store', [CompletionStatusesController::class, 'store']);
                    Route::put('{completion_status}', [CompletionStatusesController::class, 'update']);
                    Route::put('visible/{completion_status}', [CompletionStatusesController::class, 'updateVisible']);
                });

                Route::prefix('funders')->group(function () {
                    Route::post('/', [FundersController::class, 'index']);
                    Route::post('store', [FundersController::class, 'store']);
                    Route::put('{funder}', [FundersController::class, 'update']);
                    Route::put('visible/{funder}', [FundersController::class, 'updateVisible']);
                });

                Route::prefix('components')->group(function () {
                    Route::post('/', [ComponentsController::class, 'index']);
                    Route::post('store', [ComponentsController::class, 'store']);
                    Route::put('{component}', [ComponentsController::class, 'update']);
                    Route::put('visible/{component}', [ComponentsController::class, 'updateVisible']);
                });

                Route::prefix('component_types')->group(function () {
                    Route::post('/', [ComponentTypesController::class, 'index']);
                    Route::post('store', [ComponentTypesController::class, 'store']);
                    Route::put('{component_type}', [ComponentTypesController::class, 'update']);
                    Route::put('visible/{component_type}', [ComponentTypesController::class, 'updateVisible']);
                });
            });

            //Tickets
            Route::prefix('tickets')->group(function () {
                Route::prefix('categories')->group(function () {
                    Route::post('/', [TicketCategoriesController::class, 'index']);
                    Route::post('store', [TicketCategoriesController::class, 'store']);
                    Route::put('{record}', [TicketCategoriesController::class, 'update']);
                    Route::put('visible/{record}', [TicketCategoriesController::class, 'updateVisible']);
                });

                Route::prefix('sub_categories')->group(function () {
                    Route::post('/', [TicketSubCategoriesController::class, 'index']);
                    Route::post('store', [TicketSubCategoriesController::class, 'store']);
                    Route::put('{record}', [TicketSubCategoriesController::class, 'update']);
                    Route::put('visible/{record}', [TicketSubCategoriesController::class, 'updateVisible']);
                });
            });

            //Clusters
            Route::prefix('clusters')->group(function () {
                Route::post('/', [AdminApiClustersController::class, 'index']);
                Route::post('store', [AdminApiClustersController::class, 'store']);
                Route::put('{cluster}', [AdminApiClustersController::class, 'update']);
                Route::put('visible/{cluster}', [AdminApiClustersController::class, 'updateVisible']);
            });
        });

        //Data Update
        Route::prefix('learners')->group(function () {
            Route::prefix('flagged-for-delete')->group(function () {
                Route::get('/{schoolTypeId}', [AdminLearnersFlaggedForDeletingApiController::class, 'index']);
                Route::post('/{schoolTypeId}/filter', [AdminLearnersFlaggedForDeletingApiController::class, 'filter']);
                Route::get('{schoolTypeId}/duplicate-learners/{personId}', [AdminLearnersFlaggedForDeletingApiController::class, 'getDuplicateLearners']);
            });
        });
    });
//route to update ownership status
        Route::post('admin/schools/update-ownership-status', [AdminManageFlaggedInstitutionController::class, 'updateOwnershipStatus']);


    Route::prefix('admin')->group(function () {

        // School Change Requests Routes
        Route::prefix('/change-requests')->group(function () {
            Route::post('/approve/{id}', [AdminManageFlaggedInstitutionController::class, 'approveRequest'])->name('admin-approve-change-request');
            Route::post('/reject/{id}', [AdminManageFlaggedInstitutionController::class, 'rejectRequest'])->name('admin-reject-change-request');
        });

        //Learners
        Route::prefix('learners')->group(function () {
            //all learners
            Route::get('/{schoolTypeId}', [AdminLearnerApiController::class, 'indexAll']);
            Route::post('/{schoolTypeId}/filter', [AdminLearnerApiController::class, 'filterAll']);
            Route::post('/{schoolTypeId}/export-excel-all', [AdminLearnerApiController::class, 'exportExcelAll']);
            Route::post('transfer', [AdminLearnerApiController::class, 'transfer']);
            Route::post('claim', [AdminLearnerApiController::class, 'claim']);
            Route::post('transition', [AdminLearnerApiController::class, 'transition']);
            //school profile learners
            Route::get('/{emisNumber}/all', [AdminLearnerApiController::class, 'index']);
            Route::post('/{emisNumber}/filter-school', [AdminLearnerApiController::class, 'filter']);
            Route::post('/{emisNumber}/export-excel', [AdminLearnerApiController::class, 'exportExcel']);
        });

        //Teaching staff
        Route::prefix('teachers')->group(function () {
            Route::get('/{emisNumber}', [AdminTeacherApiController::class, 'all']);
            Route::post('/{emisNumber}/filter', [AdminTeacherApiController::class, 'filter']);

            //Postings
            Route::prefix('postings')->group(function () {
                Route::post('{level}', [AdminTeacherPostingController::class, 'index']);
                Route::post('{level}/transfers', [AdminTeacherPostingController::class, 'transfers']);
                Route::post('{level}/failures/{fileName}', [AdminTeacherPostingController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [AdminTeacherPostingController::class, 'process']);
                Route::get('{level}/download-template', [AdminTeacherPostingController::class, 'template']);
                Route::post('{level}/import-list', [AdminTeacherPostingController::class, 'import']);
            });

            //Transfers
            Route::prefix('transfers')->group(function () {
                Route::post('{level}', [AdminTeacherTransferController::class, 'teachers']);
                Route::post('{level}/transfers', [AdminTeacherTransferController::class, 'transfers']);
                Route::post('{level}/failures/{fileName}', [AdminTeacherTransferController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [AdminTeacherTransferController::class, 'process']);
                Route::post('{level}/download-list', [AdminTeacherTransferController::class, 'export']);
                Route::post('{level}/import-list', [AdminTeacherTransferController::class, 'import']);
                Route::post('{level}/export', [AdminTeacherTransferController::class, 'exportTeachers']);
            });
        });

        //Non-teaching staff
        Route::prefix('non-teaching-staff')->group(function () {
            Route::get('/{emisNumber}', [AdminNonTeachingStaffApiController::class, 'all']);
            Route::post('/{emisNumber}/filter', [AdminNonTeachingStaffApiController::class, 'filter']);

            //Postings
            Route::prefix('postings')->group(function () {
                Route::post('{level}', [AdminNonTeachingStaffPostingController::class, 'index']);
                Route::post('{level}/failures/{fileName}', [AdminNonTeachingStaffPostingController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [AdminNonTeachingStaffPostingController::class, 'process']);
                Route::get('{level}/download-template', [AdminNonTeachingStaffPostingController::class, 'template']);
                Route::post('{level}/import-list', [AdminNonTeachingStaffPostingController::class, 'import']);
            });

            //Transfers
            Route::prefix('transfers')->group(function () {
                Route::post('{level}', [AdminNonTeachingStaffTransferController::class, 'nonTeachingStaff']);
                Route::post('{level}/transfers', [AdminNonTeachingStaffTransferController::class, 'transfers']);
                Route::post('{level}/failures/{fileName}', [AdminNonTeachingStaffTransferController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [AdminNonTeachingStaffTransferController::class, 'process']);
                Route::post('{level}/download-list', [AdminNonTeachingStaffTransferController::class, 'export']);
                Route::post('{level}/import-list', [AdminNonTeachingStaffTransferController::class, 'import']);
            });
        });
    });

    Route::prefix('district')->group(function () {
        //Schools
        Route::prefix('schools')->group(function () {
            Route::get('/{schoolTypeId}', [DistrictSchoolsApiController::class, 'index']);
            Route::post('/{schoolTypeId}/filter', [DistrictSchoolsApiController::class, 'filter']);
            Route::post('/{schoolTypeId}/update-operation-status', [DistrictSchoolsApiController::class, 'updateOperationStatus']);
            Route::post('/{schoolTypeId}/export-excel', [DistrictSchoolsApiController::class, 'exportExcel']);
            Route::post('/{schoolTypeId}/export-enrolment-excel', [DistrictSchoolsApiController::class, 'exportEnrolmentStatsExcel']);
        });

        //Parents
        Route::prefix('fetch-parents')->group(function () {
            Route::get('/', [DistrictSchoolParentsApiController::class, 'getParents']);
            Route::post('/filter', [DistrictSchoolParentsApiController::class, 'filter']);
        });

        //EMIS Number Applications
        Route::prefix('emis-number-applications')->group(function () {
            Route::post('{schoolTypeId}', [EmisNumberApplicationController::class, 'index']);
            Route::post('{schoolTypeId}/recommend/{referenceNumber}', [EmisNumberApplicationController::class, 'recommend']);
            Route::post('{schoolTypeId}/reject/{referenceNumber}', [EmisNumberApplicationController::class, 'reject']);
        });

        //Learner Enrolment Applications
        Route::prefix('learner-enrolment-applications')->group(function () {
            Route::post('{type}', [DistrictLearnerEnrolmentApplicationsApiController::class, 'index']);
            Route::post('{applicationNumber}/approve', [DistrictLearnerEnrolmentApplicationsApiController::class, 'approve']);
            Route::post('{applicationNumber}/reject', [DistrictLearnerEnrolmentApplicationsApiController::class, 'reject']);
        });

        //HR Postings
        Route::prefix('postings')->group(function () {
            Route::prefix('teachers')->group(function () {
                Route::post('{level}', [DistrictTeacherPostingController::class, 'teachers']);
                Route::post('{level}/postings', [DistrictTeacherPostingController::class, 'postings']);
                Route::post('{level}/failures/{fileName}', [DistrictTeacherPostingController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [DistrictTeacherPostingController::class, 'process']);
                Route::get('{level}/download-template', [DistrictTeacherPostingController::class, 'template']);
                Route::post('{level}/import-list', [DistrictTeacherPostingController::class, 'import']);
            });

            Route::prefix('non-teaching-staff')->group(function () {
                Route::post('{level}', [DistrictNonTeachingStaffPostingController::class, 'index']);
            });
        });

        //HR Transfers
        Route::prefix('transfers')->group(function () {
            Route::prefix('teachers')->group(function () {
                Route::post('{level}', [DistrictTeacherTransferController::class, 'teachers']);
                Route::post('{level}/transfers', [DistrictTeacherTransferController::class, 'transfers']);
                Route::post('{level}/failures/{fileName}', [DistrictTeacherTransferController::class, 'failures']);
                Route::post('{level}/process/{fileName}', [DistrictTeacherTransferController::class, 'process']);
                Route::post('{level}/download-list', [DistrictTeacherTransferController::class, 'export']);
                Route::post('{level}/import-list', [DistrictTeacherTransferController::class, 'import']);
                Route::post('{level}/export', [DistrictTeacherTransferController::class, 'exportTeachers']);
            });

            Route::prefix('non-teaching-staff')->group(function () {
                Route::post('{level}', [DistrictNonTeachingStaffTransferController::class, 'nonTeachingStaff']);
                Route::post('{level}/transfers', [DistrictNonTeachingStaffTransferController::class, 'transfers']);
            });
        });
    });

    Route::prefix('sub-county')->group(function () {
        //EMIS Number Applications
        Route::prefix('emis-number-applications')->group(function () {
            Route::post('{schoolTypeId}', [EmisNumberApplicationSubCountyController::class, 'index']);
            Route::post('{schoolTypeId}/recommend/{referenceNumber}', [EmisNumberApplicationSubCountyController::class, 'recommend']);
            Route::post('{schoolTypeId}/reject/{referenceNumber}', [EmisNumberApplicationSubCountyController::class, 'reject']);
        });

        //Schools
        Route::prefix('schools')->group(function () {
            Route::get('/{schoolTypeId}', [SubCountySchoolsApiController::class, 'index']);
            Route::post('/{schoolTypeId}/filter', [SubCountySchoolsApiController::class, 'filter']);
            Route::post('/{schoolTypeId}/update-operation-status', [SubCountySchoolsApiController::class, 'updateOperationStatus']);
            Route::post('/{schoolTypeId}/export-excel', [SubCountySchoolsApiController::class, 'exportExcel']);
            Route::post('/{schoolTypeId}/export-enrolment-excel', [SubCountySchoolsApiController::class, 'exportEnrolmentStatsExcel']);
        });

        //Parents
        Route::prefix('fetch-parents')->group(function () {
            Route::get('/', [SubCountySchoolParentsApiController::class, 'getParents']);
            Route::post('/filter', [SubCountySchoolParentsApiController::class, 'filter']);
        });
    });

    Route::prefix('emis-support-officer')->group(function () {
        //EMIS Number Applications
        Route::prefix('emis-number-applications')->group(function () {
            Route::post('{schoolTypeId}', [EmisNumberApplicationClusterController::class, 'index']);
            //Route::post('{schoolTypeId}/recommend/{referenceNumber}', [EmisNumberApplicationClusterController::class, 'recommend']);
            //Route::post('{schoolTypeId}/reject/{referenceNumber}', [EmisNumberApplicationClusterController::class, 'reject']);
        });

        //Schools
        Route::prefix('schools')->group(function () {
            Route::get('/{schoolTypeId}', [ClusterUserSchoolsApiController::class, 'index']);
            Route::post('/{schoolTypeId}/filter', [ClusterUserSchoolsApiController::class, 'filter']);
            Route::post('/{schoolTypeId}/update-operation-status', [ClusterUserSchoolsApiController::class, 'updateOperationStatus']);
            Route::post('/{schoolTypeId}/export-excel', [ClusterUserSchoolsApiController::class, 'exportExcel']);
            Route::post('/{schoolTypeId}/export-enrolment-excel', [ClusterUserSchoolsApiController::class, 'exportEnrolmentStatsExcel']);
        });

        Route::prefix('learners')->group(function () {
            Route::prefix('flagged-for-delete')->group(function () {
                Route::get('/{schoolTypeId}', [LearnerEsoFlaggedForDeletingApiController::class, 'index']);
                Route::post('/{schoolTypeId}/filter', [LearnerEsoFlaggedForDeletingApiController::class, 'filter']);
                Route::post('approve-delete-learner/{learnerId}', [LearnerEsoFlaggedForDeletingApiController::class, 'approve']);
                Route::post('reject-delete-learner/{learnerId}', [LearnerEsoFlaggedForDeletingApiController::class, 'reject']);
                Route::get('{schoolTypeId}/duplicate-learners/{personId}', [LearnerEsoFlaggedForDeletingApiController::class, 'getDuplicateLearners']);
            });
        });

        //Parents
        Route::prefix('fetch-parents')->group(function () {
            Route::get('/', [ClusterUserSchoolParentsApiController::class, 'getParents']);
            Route::post('/filter', [ClusterUserSchoolParentsApiController::class, 'filter']);
        });

        //Regional
        Route::post('regional-clusters', [AdminApiClustersRegionalController::class, 'index']);
    });
});

//lists for dropdowns
Route::prefix('lists')->group(function () {
    //admin units
    Route::prefix('admin-units')->group(function () {
        Route::get('local-governments', [AdminUnitsController::class, 'localGovernments']);
    });
    Route::get('schools', [GeneralListController::class, 'schools']);

    //General Lists
    Route::get('teacher-responsibilities', [GeneralListController::class, 'teacherResponsibilities']);
    Route::get('religions', [GeneralListController::class, 'religions']);
    Route::get('operational-statuses', [GeneralListController::class, 'operationalStatuses']);
    Route::get('operational-suspension-reasons', [GeneralListController::class, 'operationalSuspensionReasons']);
    Route::get('promotion-statuses', [GeneralListController::class, 'promotionStatuses']);
    Route::get('reporting-statuses', [GeneralListController::class, 'reportingStatuses']);
    Route::get('tickets-sub-categories', [GeneralListController::class, 'subCategories']);
});

// Manage schools
Route::prefix('institutions')->group(function () {
    // School Master
    Route::post('verify-emis-number', [SchoolApiController::class, 'verifyEmisNumber']);
    Route::post('send-verify-code/{emisNumber}', [SchoolApiController::class, 'sendVerifyEmail']);
    Route::post('search-emis-number', [SchoolApiController::class, 'searchEmisNumber']);
    Route::post('information/store', [SchoolApiController::class, 'store']);
    Route::put('{emisNumber}/contact-info', [SchoolApiController::class, 'contactInfo']);
    Route::post('contact-person/{action}', [SchoolApiController::class, 'storeSchoolContact']);

    Route::middleware(['auth'])->group(function () {
        Route::post('save-learner-summary', [SchoolApiController::class, 'saveLearnerSummary']);

        //Parents
        Route::prefix('fetch-parents')->group(function () {
            Route::get('/', [SchoolParentsApiController::class, 'getParents']);
            Route::post('/filter', [SchoolParentsApiController::class, 'filter']);
        });

        // Owners
        Route::prefix('owners')->group(function () {
            Route::post('create', [SchoolOwnersApiController::class, 'createOwner']);
            Route::post('update/{owner_id}', [SchoolOwnersApiController::class, 'updateOwner']);
        });

        Route::prefix('pre-primary-schools')->group(function () {
            Route::post('update-logo', [PrePrimarySchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [PrePrimarySchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [PrePrimarySchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [PrePrimarySchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [PrePrimarySchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [PrePrimarySchoolApiController::class, 'updateProximities']);

            //Health Information
            Route::prefix('health-info')->group(function () {
                Route::post('update-sexuality-policy', [PrePrimarySchoolApiController::class, 'updateSexualityPolicy']);
                Route::post('update-hot-midday-meal', [PrePrimarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('update-source-of-food', [PrePrimarySchoolApiController::class, 'updateSourceOfFood']);
            });

            //SMC Committee
            Route::prefix('smc-committee')->group(function () {
                Route::post('create', [PrePrimarySchoolApiController::class, 'createSmcMember']);
                Route::post('update/{member_id}', [PrePrimarySchoolApiController::class, 'updateSmcMember']);
                Route::post('filter', [PrePrimarySchoolApiController::class, 'filterSmcMembers']);
                Route::get('/', [PrePrimarySchoolApiController::class, 'allMembers']);
            });
        });

        Route::prefix('primary-schools')->group(function () {
            Route::post('update-logo', [PrimarySchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [PrimarySchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [PrimarySchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [PrimarySchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [PrimarySchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [PrimarySchoolApiController::class, 'updateProximities']);

            //Health Information
            Route::prefix('health-info')->group(function () {
                Route::post('update-sexuality-policy', [PrimarySchoolApiController::class, 'updateSexualityPolicy']);
                Route::post('update-hot-midday-meal', [PrimarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('update-source-of-food', [PrimarySchoolApiController::class, 'updateSourceOfFood']);
            });
            //SMC Committee
            Route::prefix('smc-committee')->group(function () {
                Route::post('create', [PrimarySchoolApiController::class, 'createSmcMember']);
                Route::post('update/{member_id}', [PrimarySchoolApiController::class, 'updateSmcMember']);
                Route::post('filter', [PrimarySchoolApiController::class, 'filterSmcMembers']);
                Route::get('/', [PrimarySchoolApiController::class, 'allMembers']);
            });
        });

        Route::prefix('secondary-schools')->group(function () {
            Route::post('update-logo', [SecondarySchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [SecondarySchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [SecondarySchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [SecondarySchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [SecondarySchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [SecondarySchoolApiController::class, 'updateProximities']);

            Route::prefix('health-info')->group(function () {
                Route::post('update-sexuality-policy', [SecondarySchoolApiController::class, 'updateSexualityPolicy']);
                Route::post('update-hot-midday-meal', [SecondarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('update-source-of-food', [SecondarySchoolApiController::class, 'updateSourceOfFood']);
            });
            //SMC Committee
            Route::prefix('smc-committee')->group(function () {
                Route::post('create', [SecondarySchoolApiController::class, 'createSmcMember']);
                Route::post('update/{member_id}', [SecondarySchoolApiController::class, 'updateSmcMember']);
                Route::post('filter', [SecondarySchoolApiController::class, 'filterSmcMembers']);
                Route::get('/', [SecondarySchoolApiController::class, 'allMembers']);
            });
        });

        Route::prefix('certificate-schools')->group(function () {
            Route::post('update-logo', [CertificateAwardingSchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [CertificateAwardingSchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [CertificateAwardingSchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [CertificateAwardingSchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [CertificateAwardingSchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [CertificateAwardingSchoolApiController::class, 'updateProximities']);
        });

        Route::prefix('diploma-schools')->group(function () {
            Route::post('update-logo', [DiplomaAwardingSchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [DiplomaAwardingSchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [DiplomaAwardingSchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [DiplomaAwardingSchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [DiplomaAwardingSchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [DiplomaAwardingSchoolApiController::class, 'updateProximities']);
        });

        Route::prefix('degree-schools')->group(function () {
            Route::post('update-logo', [DegreeAwardingSchoolApiController::class, 'updateLogo']);
            Route::post('update-identity', [DegreeAwardingSchoolApiController::class, 'updateIdentity']);
            Route::post('update-ownership', [DegreeAwardingSchoolApiController::class, 'updateOwnership']);
            Route::post('update-location', [DegreeAwardingSchoolApiController::class, 'updateLocation']);
            Route::post('update-operations', [DegreeAwardingSchoolApiController::class, 'updateOperations']);
            Route::post('update-proximities', [DegreeAwardingSchoolApiController::class, 'updateProximities']);
        });

        Route::prefix('campuses')->group(function () {
            Route::post('update-campus', [DegreeAwardingSchoolApiController::class, 'updateCampus']);
            Route::post('delete-campus', [DegreeAwardingSchoolApiController::class, 'deleteCampus']);
        });

        Route::prefix('international-schools')->group(function () {
            Route::post('update-logo', [DegreeAwardingSchoolApiController::class, 'updateLogo']);
        });

        // Manage Learners
        Route::prefix('learners')->middleware('role:institution-admin|institution-user|emis-super-admin|emis-support-officer')->group(function () {
            Route::get('/', [LearnerApiController::class, 'index']);
            Route::post('filter', [LearnerApiController::class, 'filter']);
            Route::post('form-create', [LearnerApiController::class, 'formCreate']);
            Route::post('update-profile/{id}', [LearnerApiController::class, 'updateProfile']);
            Route::post('create-parent/{id}', [LearnerApiController::class, 'createParent']);
            Route::post('delete-parent', [LearnerApiController::class, 'deleteParent']);

            //Flagging learners
            Route::post('delete/{lin}', [LearnerApiController::class, 'delete']);
            Route::prefix('flagged-deleted')->group(function () {
                Route::get('/', [LearnerFlaggedForDeletingApiController::class, 'index']);
                Route::post('filter', [LearnerFlaggedForDeletingApiController::class, 'filter']);
            });

            if (! config('emis.excel.disableUploads') || ! config('emis.excel.disableAdminUploads')) {
                Route::get('import-ugandans', [LearnerApiController::class, 'importUgandansTemplate']);
                Route::get('import-ugandans/process/{file_upload_id}', [LearnerApiController::class, 'processImportUgandans']);
                Route::post('import-ugandans', [LearnerApiController::class, 'importUgandans']);

                Route::get('import-non-refugees', [LearnerApiController::class, 'importForeignersTemplate']);
                Route::get('import-non-refugees/process/{file_upload_id}', [LearnerApiController::class, 'processForeigners']);
                Route::post('import-non-refugees', [LearnerApiController::class, 'importForeigners']);

                Route::get('import-refugees', [LearnerApiController::class, 'importRefugeesTemplate']);
                Route::get('import-refugees/process/{file_upload_id}', [LearnerApiController::class, 'processImportRefugees']);
                Route::post('import-refugees', [LearnerApiController::class, 'importRefugees']);
            }

            Route::post('photo-bulk-uploads', [LearnerApiController::class, 'photoBulkUpload']);

            Route::post('extracurricular-template', [LearnerApiController::class, 'extracurricularExcel']);
            Route::post('extracurricular-template/process', [LearnerApiController::class, 'processExtracurricularExcel']);

            Route::prefix('transfers')->group(function () {
                Route::post('incoming', [LearnerTransferController::class, 'incoming']);
                Route::post('outgoing', [LearnerTransferController::class, 'outgoing']);
                Route::get('verify/{learner}', [LearnerTransferController::class, 'verify']);
                Route::post('{learner}', [LearnerTransferController::class, 'transfer']);
            });

            //Handle learner promotions
            Route::prefix('promotions')->group(function () {
                Route::post('filter', [LearnerPromotionsController::class, 'index']);
                //Route::post('filter-completed', [LearnerPromotionsController::class, 'completed']);
                Route::post('update-promotion-status', [LearnerPromotionsController::class, 'updatePromotionStatus']);
            });

            Route::prefix('enrolments')->group(function () {
                Route::post('filter', [LearnerEnrolmentsApiController::class, 'index']);
                Route::post('update-enrolment-status', [LearnerEnrolmentsApiController::class, 'updateEnrolmentStatus'])->name('enrolments.update-status');;
            });

            Route::prefix('claims')->group(function () {
                Route::post('incoming', [LearnerClaimController::class, 'incoming']);
                Route::post('outgoing', [LearnerClaimController::class, 'outgoing']);
                Route::post('verify', [LearnerClaimController::class, 'verify']);
                Route::post('{learner}', [LearnerClaimController::class, 'claim']);
            });

            Route::prefix('search-lin')->group(function () {
                Route::post('verify', [SearchLearnerLinApiController::class, 'getLearnerDetails']);
            });

            Route::post('save-classless-learners', [LearnerApiController::class, 'saveClasslessLearners']);
            Route::prefix('save-learners-stuck')->group(function () {
                Route::post('/update', [LearnerApiController::class, 'saveLearnersStuck2022']);
                Route::post('/filter', [LearnerApiController::class, 'filter']);
            });

            Route::prefix('transitions')->group(function () {
                Route::post('/', [LearnerTransitionsApiController::class, 'index']);
                Route::post('verify/{learner}', [LearnerTransitionsApiController::class, 'getLearnerDetails']);
                Route::post('transfer/{learner}', [LearnerTransitionsApiController::class, 'transition']);
            });
            Route::prefix('index-number')->group(function () {
                Route::post('all-candidates', [CandidatesApiController::class, 'allCandidates']);
                Route::get('verify/{learner}', [CandidatesApiController::class, 'verifyCandidate']);
                Route::post('register-candidate', [CandidatesApiController::class, 'registerCandidate']);
                Route::post('o_level', [LearnerIndexNumberApiController::class, 'o_level_index_numbers']);
                Route::post('a_level', [LearnerIndexNumberApiController::class, 'a_level_index_numbers']);
                Route::post('update', [LearnerIndexNumberApiController::class, 'updateIndexNumber']);
            });
        });

        //Learner Enrolment Applications
        Route::prefix('learner-enrolment-applications')->group(function () {
            Route::post('{type}', [LearnerEnrolmentApplicationsApiController::class, 'index']);
        });

        // Manage Teachers
        Route::prefix('teachers')->group(function () {
            Route::middleware('role:institution-admin|institution-user')->group(function () {
                Route::get('/', [TeacherApiController::class, 'index']);
                Route::post('filter', [TeacherApiController::class, 'filter']);
                Route::post('update-profile/{id}', [TeacherApiController::class, 'updateProfile']);
                Route::post('update-responsibilities/{teacherId}', [TeacherApiController::class, 'updateResponsibilities']);
                Route::delete('delete/{personId}', [TeacherApiController::class, 'delete']);

                Route::prefix('transfers')->group(function () {
                    Route::post('incoming', [SchoolTeacherTransferController::class, 'incoming']);
                    Route::post('incoming/reject/{transfer}', [SchoolTeacherTransferController::class, 'reject']);
                    Route::post('incoming/receive/{transfer}', [SchoolTeacherTransferController::class, 'receive']);
                    Route::post('outgoing', [SchoolTeacherTransferController::class, 'outgoing']);
                });

                Route::prefix('postings')->group(function () {
                    Route::post('/', [SchoolTeacherPostingController::class, 'index']);
                    Route::post('reject/{transfer}', [SchoolTeacherPostingController::class, 'reject']);
                    Route::post('accept/{transfer}', [SchoolTeacherPostingController::class, 'accept']);
                });

                Route::get('import-ugandans', [TeacherApiController::class, 'importUgandansTemplate']);
                Route::get('import-ugandans/process/{file_upload_id}', [TeacherApiController::class, 'processImportUgandans']);
                Route::post('import-ugandans', [TeacherApiController::class, 'importUgandans']);

                Route::get('import-foreigners', [TeacherApiController::class, 'importForeignersTemplate']);
                Route::get('import-foreigners/process/{file_upload_id}', [TeacherApiController::class, 'processImportForeigners']);
                Route::post('import-foreigners', [TeacherApiController::class, 'importForeigners']);

                Route::get('import-refugees', [TeacherApiController::class, 'downloadRefugeeTemplate']);
                Route::get('import-refugees/process/{file_upload_id}', [TeacherApiController::class, 'processImportRefugees']);
                Route::post('import-refugees', [TeacherApiController::class, 'importRefugees']);
            });
        });

        // Manage Non Teaching Staff
        Route::prefix('non-teaching-staff')->group(function () {
            Route::middleware('role:institution-admin|institution-user')->group(function () {
                Route::get('/', [NonTeachingStaffApiController::class, 'index']);
                Route::post('filter', [NonTeachingStaffApiController::class, 'filter']);
                Route::post('form-create', [NonTeachingStaffApiController::class, 'formCreate']);
                Route::post('update-profile/{id}', [NonTeachingStaffApiController::class, 'updateProfile']);

                Route::get('import-ugandans', [NonTeachingStaffApiController::class, 'importUgandansTemplate']);
                Route::get('import-ugandans/process/{file_upload_id}', [NonTeachingStaffApiController::class, 'processImportUgandans']);
                Route::post('import-ugandans', [NonTeachingStaffApiController::class, 'importUgandans']);

                Route::get('import-foreigners', [NonTeachingStaffApiController::class, 'importForeignersTemplate']);
                Route::get('import-foreigners/process/{file_upload_id}', [NonTeachingStaffApiController::class, 'processImportForeigners']);
                Route::post('import-foreigners', [NonTeachingStaffApiController::class, 'importForeigners']);

                Route::get('import-refugees', [NonTeachingStaffApiController::class, 'downloadRefugeeTemplate']);
                Route::get('import-refugees/process/{file_upload_id}', [NonTeachingStaffApiController::class, 'processImportRefugees']);
                Route::post('import-refugees', [NonTeachingStaffApiController::class, 'importRefugees']);

                Route::prefix('transfers')->group(function () {
                    Route::post('incoming', [SchoolNonTeachingStaffTransferController::class, 'incoming']);
                    Route::post('incoming/reject/{transfer}', [SchoolNonTeachingStaffTransferController::class, 'reject']);
                    Route::post('incoming/receive/{transfer}', [SchoolNonTeachingStaffTransferController::class, 'receive']);
                    Route::post('outgoing', [SchoolNonTeachingStaffTransferController::class, 'outgoing']);
                });

                Route::prefix('postings')->group(function () {
                    Route::post('/', [SchoolNonTeachingStaffPostingController::class, 'index']);
                    Route::post('reject/{transfer}', [SchoolNonTeachingStaffPostingController::class, 'reject']);
                    Route::post('accept/{transfer}', [SchoolNonTeachingStaffPostingController::class, 'accept']);
                });
            });
        });

        // Water Sources
        Route::prefix('water-sources')->group(function () {
            Route::post('update', [SchoolWaterSourceApiController::class, 'update']);
        });

        // School Courses for Degree
        Route::prefix('school-courses')->middleware('role:institution-admin|institution-user')->group(function () {
            Route::post('/', [SchoolCourseApiController::class, 'index']);
            Route::post('store', [SchoolCourseApiController::class, 'store']);
            Route::put('{schoolCourse}', [SchoolCourseApiController::class, 'update']);
            Route::delete('{schoolCourse}', [SchoolCourseApiController::class, 'delete']);
        });

        //manage courses
        Route::prefix('institution-examined-courses')->group(function () {
            Route::middleware('role:institution-admin|institution-user')->group(function () {
                Route::get('/', [InstitutionExaminedCourseApiController::class, 'allCourses']);
                Route::post('store', [InstitutionExaminedCourseApiController::class, 'store']);
                Route::put('/update/{course_id}', [InstitutionExaminedCourseApiController::class, 'update']);
            });
        });

        // Survey
        Route::prefix('surveys')->group(function () {
            Route::post('school/section-a/{survey_id}', [SurveyController::class, 'updateSchoolSectionA']);
            Route::post('school/section-b/{survey_id}', [SurveyController::class, 'updateSchoolSectionB']);
            Route::post('school/section-b/add-campuses/{survey_id}', [SurveyController::class, 'updateCampuses']);
            Route::post('learners/{survey_id}', [SurveyController::class, 'updateLearnersSection']);
            Route::post('teaching-staff/{survey_id}', [TeacherApiController::class, 'store']);
            Route::post('non-teaching-staff/{survey_id}', [NonTeachingStaffApiController::class, 'store']);

            Route::get('school/learners/{survey_id}', [SurveyController::class, 'getSchoolSectionLearners']);
            Route::get('school/teaching_staff/{survey_id}', [SurveyController::class, 'getSchoolSectionTeachingStaff']);
            Route::get('school/non_teaching_staff/{survey_id}', [SurveyController::class, 'getSchoolSectionNonTeachingStaff']);
            Route::get('school/infrastructure/{survey_id}', [SurveyController::class, 'getSchoolSectionInfrastructure']);
            Route::get('school/institutional_facilities/{survey_id}', [SurveyController::class, 'getSchoolSectionInstitutionalFacilities']);
            Route::get('school/other_facilities/{survey_id}', [SurveyController::class, 'getSchoolSectionOtherFacilities']);
            Route::get('school/water_sanitation/{survey_id}', [SurveyController::class, 'getSchoolSectionWaterSanitation']);
            Route::get('school/sources_of_energy/{survey_id}', [SurveyController::class, 'getSchoolSectionEnergySources']);
            Route::get('school/i_c_t/{survey_id}', [SurveyController::class, 'getSchoolSectionICT']);
            Route::get('school/instructional_materials/{survey_id}', [SurveyController::class, 'getSchoolSectionInstructionalMaterials']);
            Route::get('school/health_meals/{survey_id}', [SurveyController::class, 'getSchoolSectionHealthMeals']);
            Route::get('school/p_e_sports/{survey_id}', [SurveyController::class, 'getSchoolSectionPESports']);
            Route::get('school/extra_curricular_activities/{survey_id}', [SurveyController::class, 'getSchoolSectionPracticalSkills']);
            Route::get('school/governance/{survey_id}', [SurveyController::class, 'getSchoolSectionGovernance']);
            Route::get('school/gps_location/{survey_id}', [SurveyController::class, 'getSchoolSectionGPSLocation']);
            Route::get('school/finance/{survey_id}', [SurveyController::class, 'getSchoolSectionFinance']);
            Route::get('school/courses/{survey_id}', [SurveyController::class, 'getSchoolSectionCourses']);

            // instructional_materials
            // health&_meals
            // p._e.&_sports
            // practical_skills
            // governance
            // gps_location
            // finance

            Route::get('all-certificate-courses', [SurveyController::class, 'getCertificateCourses']);
            Route::get('all-diploma-courses', [SurveyController::class, 'getDiplomaCourses']);
            Route::get('all-certificate-institution-types', [SurveyController::class, 'getCertificateInstitutionTypes']);
            Route::get('all-diploma-institution-types', [SurveyController::class, 'getDiplomaInstitutionTypes']);
            Route::get('all-registration-statuses', [SurveyController::class, 'getRegistrationStatuses']);
            Route::get('all-course-durations', [SurveyController::class, 'getCourseDurations']);
            Route::get('all-course-categories', [SurveyController::class, 'getCourseCategories']);
            Route::get('all-course-award-types', [SurveyController::class, 'getCourseAwardTypes']);
            Route::post('update-certificate-courses', [SurveyController::class, 'updateCertificateCourses']);
            Route::post('update-diploma-courses', [SurveyController::class, 'updateDiplomaCourses']);
            Route::post('create-non-examinable-courses', [InstitutionExaminedCourseApiController::class, 'store']);
            Route::put('update-non-examinable-courses/{course_id}', [InstitutionExaminedCourseApiController::class, 'update']);
            Route::get('all-international-curriculums', [SurveyController::class, 'getInternationalCurriculums']);
            Route::post('update-international-curriculums', [SurveyController::class, 'updateInternationalCurriculums']);
            Route::get('all-international-sections', [SurveyController::class, 'getInternationalSections']);
            Route::post('update-international-sections', [SurveyController::class, 'updateInternationalSections']);
            Route::get('all-international-calendars', [SurveyController::class, 'getInternationalCalendars']);
            Route::post('update-international-calendars', [SurveyController::class, 'updateInternationalCalendars']);

            Route::post('water/{survey_id}', [SurveyController::class, 'updateWaterSection']);
            Route::post('sanitation/{survey_id}', [SchoolSanitationApiController::class, 'update']);
            Route::post('sources-of-energy/{survey_id}', [SchoolEnergySourceApiController::class, 'update']);
            Route::post('other-facility/{survey_id}', [SchoolOtherFacilitiesApiController::class, 'update']);
            Route::post('garbage-disposal-method/{survey_id}', [SchoolGarbageDisposalMethodsApiController::class, 'update']);
            Route::post('learning-playing-materials/{survey_id}', [SchoolLearningAndPlayingMaterialsApiController::class, 'update']);
            Route::post('sports-equipment/{survey_id}', [SchoolSportsEquipmentApiController::class, 'update']);
            Route::post('sports-facilities/{survey_id}', [SchoolSportsFacilitiesApiController::class, 'update']);
            Route::post('sports-activity/{survey_id}', [SchoolSportsActivitiesApiController::class, 'update']);
            Route::post('practical-skill/{survey_id}', [SchoolPracticalSkillsApiController::class, 'update']);

            Route::post('institutional-facilities/{survey_id}', [InstitutionalInstructionalFacilitiesApiController::class, 'update']);
            Route::post('finance/{survey_id}', [SurveyController::class, 'updateFinanceSection']);

            Route::prefix('ict-facilities')->group(function () {
                Route::post('update/{survey_id}', [SchoolIctFacilitiesApiController::class, 'updateIctFacilities']);
                Route::post('update-internet-source/{survey_id}', [SchoolIctFacilitiesApiController::class, 'updateInternetSource']);
            });

            Route::prefix('infrastructure')->group(function () {
                Route::post('create/{survey_id}', [SchoolInfrastructureApiController::class, 'create']);
                Route::post('update/{survey_id}', [SchoolInfrastructureApiController::class, 'update']);
            });

            Route::prefix('instructional-materials')->group(function () {
                Route::post('textbooks-create/{survey_id}', [SchoolTextBooksApiController::class, 'create']);
                Route::post('textbooks-update/{survey_id}', [SchoolTextBooksApiController::class, 'update']);
                Route::post('reference-books-create/{survey_id}', [SchoolReferenceBooksApiController::class, 'create']);
                Route::post('reference-books-update/{survey_id}', [SchoolReferenceBooksApiController::class, 'update']);
                Route::post('charts-create/{survey_id}', [SchoolWallChartsApiController::class, 'create']);
                Route::post('charts-update/{survey_id}', [SchoolWallChartsApiController::class, 'update']);
                Route::post('sne-kits-create/{survey_id}', [SchoolSneKitsApiController::class, 'create']);
                Route::post('sne-kits-update/{survey_id}', [SchoolSneKitsApiController::class, 'update']);
                Route::post('lab-equipment-create/{survey_id}', [SchoolLabEquipmentApiController::class, 'create']);
                Route::post('lab-equipment-update/{survey_id}', [SchoolLabEquipmentApiController::class, 'update']);
                Route::post('lab-reagents-create/{survey_id}', [SchoolLabReagentsApiController::class, 'create']);
                Route::post('lab-reagents-update/{survey_id}', [SchoolLabReagentsApiController::class, 'update']);
            });

            Route::prefix('smc-committee')->group(function () {
                Route::post('update-governance/{survey_id}', [SchoolGovernanceApiController::class, 'updateGovernance']);
                Route::post('add-members/{survey_id}', [SchoolGovernanceApiController::class, 'addMembers']);
            });

            //Health Information
            Route::prefix('pre-primary')->group(function () {
                Route::post('hot-midday-meal', [PrePrimarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('source-of-food', [PrePrimarySchoolApiController::class, 'updateSourceOfFood']);
                Route::post('health-services/{survey_id}', [SchoolHealthMealsApiController::class, 'update']);
            });
            Route::prefix('primary')->group(function () {
                Route::post('sexuality-policy', [PrimarySchoolApiController::class, 'updateSexualityPolicy']);
                Route::post('hot-midday-meal', [PrimarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('source-of-food', [PrimarySchoolApiController::class, 'updateSourceOfFood']);
                Route::post('health-services/{survey_id}', [SchoolHealthMealsApiController::class, 'update']);
            });
            Route::prefix('secondary')->group(function () {
                Route::post('sexuality-policy', [SecondarySchoolApiController::class, 'updateSexualityPolicy']);
                Route::post('hot-midday-meal', [SecondarySchoolApiController::class, 'updateHotMiddayMeal']);
                Route::post('source-of-food', [SecondarySchoolApiController::class, 'updateSourceOfFood']);
                Route::post('health-services/{survey_id}', [SchoolHealthMealsApiController::class, 'update']);
            });
        });

        // Human Resource
        Route::prefix('hr')->group(function () {
            // Teachers
            Route::prefix('teachers')->group(function () {
                Route::get('/', [TeacherApiController::class, 'all']);
                Route::post('filter', [TeacherApiController::class, 'filter']);
                Route::post('export-excel', [TeacherApiController::class, 'exportExcel']);
            });

            //Non Teaching Staff
            Route::prefix('non-teaching-staff')->group(function () {
                Route::get('/', [NonTeachingStaffApiController::class, 'all']);
                Route::post('/filter', [NonTeachingStaffApiController::class, 'filter']);
                Route::post('export-excel', [NonTeachingStaffApiController::class, 'exportExcel']);
            });
        });

        // Finance
        Route::prefix('finance')->group(function () {
            // Income
            Route::prefix('incomes')->group(function () {
                Route::get('/', [SchoolIncomeApiController::class, 'index']);
                Route::post('filter', [SchoolIncomeApiController::class, 'filter']);
                Route::post('store', [SchoolIncomeApiController::class, 'store']);
                Route::put('{income_id}', [SchoolIncomeApiController::class, 'update']);
            });

            // Budget
            Route::prefix('budgets')->group(function () {
                Route::get('/', [SchoolBudgetApiController::class, 'index']);
                Route::post('filter', [SchoolBudgetApiController::class, 'filter']);
                Route::post('store', [SchoolBudgetApiController::class, 'store']);
                Route::put('{budget_id}', [SchoolBudgetApiController::class, 'update']);
            });

            // Expenses
            Route::prefix('expenses')->group(function () {
                Route::get('/', [SchoolExpensesApiController::class, 'index']);
                Route::post('filter', [SchoolExpensesApiController::class, 'filter']);
                Route::post('store', [SchoolExpensesApiController::class, 'store']);
                Route::put('{expense_id}', [SchoolExpensesApiController::class, 'update']);
            });
        });

        //User manager
        Route::prefix('users')->group(function () {
            Route::get('/', [SchoolContactApiController::class, 'all']);
            Route::post('/filter', [SchoolContactApiController::class, 'filter']);
            Route::post('store', [SchoolContactApiController::class, 'addUser']);
            Route::put('/update/{contact_id}', [SchoolContactApiController::class, 'update']);
            Route::post('bulk-action/{action}', [SchoolContactApiController::class, 'bulkAction']);
            Route::put('suspend/{contact_id}', [SchoolContactApiController::class, 'suspend']);
            Route::put('restore/{contact_id}', [SchoolContactApiController::class, 'restore']);
        });

        // Notices
        Route::prefix('emis-notices')->group(function () {
            Route::get('view-latest', [InstitutionEmisNoticesController::class, 'latestNotice']);
        });

        //Uploaded Excels
        Route::post('uploaded-excels', [UploadedExcelsController::class, 'allUploadedExcels']);
        Route::post('file-uploads', [EmisReturnController::class, 'fileUploads']); //->name('institution-emis-return-file-uploads');
        Route::get('file-uploads/details/{file_upload_id}', [SurveyController::class, 'fileUploadDetails'])->middleware('throttle:api');

        Route::prefix('download-centre')->group(function () {
            Route::post('export-request', [DownloadCentreController::class, 'storeExportRequest']);
            Route::get('export-requests', [DownloadCentreController::class, 'getExportRequests']);
            Route::get('export-requests/{export_request_uuid}/download', [DownloadCentreController::class, 'download']);
        });
    });

    Route::middleware(['auth'])->group(function () {

        Route::get('change-reasons', [FlagInstitutionController::class, 'getChangeReasonsForSchool']);
        Route::post('update-name', [FlagInstitutionController::class, 'addUpdateRequest']);
        Route::post('update-ownership-request', [FlagInstitutionController::class, 'addOwnershipUpdateRequest']);
        Route::get('{school}/change-requests', [FlagInstitutionController::class, 'getChangeRequests']);
        Route::get('{school}/change-requests-ownership', [FlagInstitutionController::class, 'getChangeRequestsOwnership']);

        Route::post('{school}/change-requests/filter', [FlagInstitutionController::class, 'filterChangeRequests']);
        Route::get('ownership-types', [FlagInstitutionController::class, 'getOwnershipTypes']);


        // School Name Change Routes
        Route::prefix('name-changes')->group(function () {
            Route::get('/pending', [FlagInstitutionController::class, 'getPendingNameChanges']);
            Route::get('/approved', [FlagInstitutionController::class, 'getApprovedNameChanges']);
            Route::get('/rejected', [FlagInstitutionController::class, 'getRejectedNameChanges']);
            Route::post('/approve/{requestId}', [FlagInstitutionController::class, 'approveNameChange']);
            Route::post('/reject/{requestId}', [FlagInstitutionController::class, 'rejectNameChange']);
        });

        // School Ownership Change Routes
        Route::prefix('ownership-changes')->group(function () {
            Route::get('/pending', [FlagInstitutionController::class, 'getPendingOwnershipChanges']);
            Route::get('/approved', [FlagInstitutionController::class, 'getApprovedOwnershipChanges']);
            Route::get('/rejected', [FlagInstitutionController::class, 'getRejectedOwnershipChanges']);
            Route::post('/approve/{requestId}', [FlagInstitutionController::class, 'approveOwnershipChange']);
            Route::post('/reject/{requestId}', [FlagInstitutionController::class, 'rejectOwnershipChange']);
        });
    });
});

// Download EMIS certificates
Route::prefix('institution')->group(function () {
    Route::middleware(['auth', 'school'])->group(function () {
        Route::get('emis-number-certificates/{emisNumber}', [InstitutionController::class, 'generatePdf']);
    });
});

Route::middleware('auth')->group(function () {
    Route::get('emis-number-certificates/{emisNumber}', [EmisNumberCertificatesController::class, 'generatePdfCertificate']);
});

Route::middleware('auth')->group(function () {
    // Manage Learners
    Route::prefix('learners')->group(function () {
        Route::get('/', [LearnerApiController::class, 'index']);
        Route::post('filter', [LearnerApiController::class, 'filter']);
        Route::post('export-excel', [LearnerApiController::class, 'exportExcel']);
    });
});

Route::post('entry-scheme', [AcmisApiController::class, 'entryScheme']);
Route::post('school-course', [AcmisApiController::class, 'schoolCourse']);
Route::post('sponsor', [AcmisApiController::class, 'sponsor']);
Route::post('school-campus', [AcmisApiController::class, 'schoolCampus']);

Route::get('test-nira/{nin?}', [NiraApiController::class, 'getPerson'])->middleware('nira.auth');

Route::get('test-hcm/{employee_no?}', [HcmController::class, 'verifyEmployee']);
