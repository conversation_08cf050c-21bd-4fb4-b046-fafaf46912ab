// export paths to be collected by the main vite.config.mjs file
export const paths = [
    'Modules/Core/resources/assets/sass/app.scss',
    'Modules/Core/resources/assets/css/app.css',
    'Modules/Core/resources/assets/js/app.js',
    'Modules/Core/resources/assets/js/admin-charts.js',
    'Modules/Core/resources/assets/js/admin-components.js',
    'Modules/Core/resources/assets/js/clusters-charts.js',
    'Modules/Core/resources/assets/js/district-charts.js',
    'Modules/Core/resources/assets/js/district-components.js',
    'Modules/Core/resources/assets/js/institution-charts.js',
    'Modules/Core/resources/assets/js/school-components.js',
    'Modules/Core/resources/assets/js/subcounty-charts.js',
];
// Scen all resources for assets file. Return array
//function getFilePaths(dir) {
//    const filePaths = [];
//
//    function walkDirectory(currentPath) {
//        const files = readdirSync(currentPath);
//        for (const file of files) {
//            const filePath = join(currentPath, file);
//            const stats = statSync(filePath);
//            if (stats.isFile() && !file.startsWith('.')) {
//                const relativePath = 'Modules/Core/'+relative(__dirname, filePath);
//                filePaths.push(relativePath);
//            } else if (stats.isDirectory()) {
//                walkDirectory(filePath);
//            }
//        }
//    }
//
//    walkDirectory(dir);
//    return filePaths;
//}

//const __filename = fileURLToPath(import.meta.url);
//const __dirname = dirname(__filename);

//const assetsDir = join(__dirname, 'resources/assets');
//export const paths = getFilePaths(assetsDir);


//export const paths = [
//    'Modules/Core/resources/assets/sass/app.scss',
//    'Modules/Core/resources/assets/js/app.js',
//];
