<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="js">

<head>
	<meta charset="utf-8">
	<meta name="csrf-token" content="{{ csrf_token() }}">
	<meta name="author" content="Demis">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content="District User Portal">
	<!-- Fav Icon  -->
	<link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">
	<!-- Page Title  -->
	<title>@yield('title') - District Admin DEMIS</title>
	<!-- StyleSheets  -->
    @include('core::includes.assets.dash-lite');
	<link id="skin-default" rel="stylesheet" href="{{ asset('assets/css/skins/theme-egyptian.css?ver=3.3.0') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css?ver=2') }}">
	@yield('styles')
</head>

<body class="nk-body bg-lighter npc-general has-sidebar no-touch nk-nio-theme">
	<div id="demis-app" class="nk-app-root">
		<!-- main @s -->
		<div class="nk-main ">
			<!-- sidebar @s -->
			@include('core::district.layouts.sidebar')
			<!-- sidebar @e -->
			<!-- wrap @s -->
			<div class="nk-wrap" style="background-color: #F5F6FA">
				<!-- main header @s -->
				@include('core::district.layouts.header')
				<!-- main header @e -->
				<!-- content @s -->
				@yield('content')
				<!-- content @e -->
				<!-- footer @s -->
				@include('core::district.layouts.footer')
				<!-- footer @e -->
			</div>

			<!-- wrap @e -->
		</div>
		<!-- main @e -->
        @if(auth()->user()->is_temporary_password_yn)
            <change-temporary-password temporary-password="{{ auth()->user()->is_temporary_password_yn }}"></change-temporary-password>
        @endif
	</div>
	<!-- app-root @e -->
	<!-- JavaScript -->
	<script src="{{ asset('assets/js/bundle.js?ver=3.3.0') }}"></script>
	<script src="{{ asset('assets/js/scripts.js?ver=3.3.0') }}"></script>
	<script src="{{ asset('assets/js/libs/jquery.blockUI.js') }}"></script>
    <script>
        $(document).ready(function(){
            var url = document.URL;
            if(url.indexOf('#') > 0)
            {
                var hsh = url.split('#');
                hash_url = hsh[1];
                if(hash_url.length > 0)
                {
                    $(".nav-link").each(function(){
                        $(this).removeClass('active');
                    });
                    $(".tab-pane").each(function(){
                        $(this).removeClass('active');
                    });
                    $(".nav-link[href='#"+hash_url+"']").addClass('active');
                    $("#"+hash_url).addClass('active');
                }
            }
        });
        $(".recommend_radio").click(function(){
            let v = $(this).val();
            if ($(".recommend_fields").length) {
                if (v === 1 || v === '1') {
                    if ($(".recommend_fields").hasClass('d-none')) {
                        $(".recommend_fields").removeClass('d-none');
                    }
                } else {
                    if ($(".recommend_fields").hasClass('d-none')) {

                    } else {
                        $(".recommend_fields").addClass('d-none');
                    }
                }
            }
        });
    </script>
    @yield('summer-note-script')
	@vite('Modules/Core/resources/assets/js/app.js')
    @yield('scripts')
</body>

</html>
