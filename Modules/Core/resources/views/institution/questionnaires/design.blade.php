<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="js">

<head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-Q0CRXX3H2R"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-Q0CRXX3H2R');
</script>
    <meta charset="utf-8">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="author" content="Softnio">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="A powerful and conceptual apps base dashboard template that especially build for developers and programmers.">
    <!-- Fav Icon  -->
    <link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">

    <!-- Page Title  -->
    <title>DEMIS - @yield('title')</title>
    <!-- StyleSheets  -->
    @include('core::includes.assets.dash-lite')
    <link id="skin-default" rel="stylesheet" href="{{ asset('assets/instructions/css/skins/theme-landing.css?ver=3.3.0') }}">
    <link rel="stylesheet" href="{{ asset('assets/fileupload/css/fileupload.min.css') }}">

    <style>
        .logo-img {
            max-height: 50px !important;
        }

        #regForm {
            background-color: #ffffff;
            margin: 50px auto;
            padding: 40px;
            border-radius: 10px;
        }

        input {
            padding: 10px;
            width: 100%;
            font-size: 17px;
            border: 1px solid #aaaaaa;
            border-radius: 10px;
            -webkit-appearance: none
        }

        .tab input:focus {
            border: 1px solid #1B708C !important;
            outline: none
        }

        input.invalid {
            border: 1px solid #e03a0666
        }

        .tab {
            display: none
        }

        button {
            background-color: #1266f1;
            color: #ffffff;
            border: none;
            border-radius: 5%;
            padding: 10px 20px;
            font-size: 17px;
            cursor: pointer;
            /*margin-top: 20%;*/
        }

        button:hover {
            opacity: 0.8
        }

        button:focus {
            outline: none !important
        }

        #prevBtn {
            background-color: #364a63
        }

        .all-steps {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 30px;
            width: 100%;
            display: inline-flex;
            justify-content: center
        }

        .step {
            height: 60px;
            width: 60px;
            margin: 0 10px;
            background-color: #bbbbbb;
            border: none;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #1B708C;
            opacity: 0.5
        }

        .step.active {
            opacity: 1
        }

        .step.finish {
            color: #fff;
            background: #1B708C;
            opacity: 1
        }

        .all-steps {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 30px
        }

        .thanks-message {
            display: none
        }
    </style>
</head>

<body class="nk-body bg-lighter npc-default has-no-sidebar">
<div id="demis-app" class="nk-app-root">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- wrap @s -->
        <div class="nk-wrap">
            <!-- main header @s -->
        @include('core::institution.survey.header')
        <!-- main header @e -->
            <!-- content @s -->
        @yield('content')
        <!-- content @e -->
            <!-- footer @s -->
        @include('core::institution.survey.footer')
        <!-- footer @e -->
        </div>
        <!-- wrap @e -->
    </div>
    <!-- main @e -->
</div>
<!-- app-root @e -->
<!-- JavaScript -->
<script src="{{ asset('assets/js/bundle.js?ver=3.3.0') }}"></script>
<script src="{{ asset('assets/js/scripts.js?ver=3.3.0') }}"></script>
<script src="{{ asset('assets/fileupload/js/fileupload.min.js') }}"></script>
@vite('Modules/Core/resources/assets/js/app.js')

<script>
    var currentTab = 0;
    document.addEventListener("DOMContentLoaded", function(event) {
        showTab(currentTab);
    });

    function showTab(n) {
        var x = document.getElementsByClassName("tab");
        x[n].style.display = "block";
        if (n === 0) {
            document.getElementById("prevBtn").style.display = "none";
        } else {
            document.getElementById("prevBtn").style.display = "inline";
        }
        if (n === (x.length - 1)) {
            document.getElementById("nextBtn").innerHTML = '<i class="ni ni-arrow-right-circle-fill mr-1"></i>Save & Continue';
        } else {
            document.getElementById("nextBtn").innerHTML = '<i class="ni ni-arrow-right-circle-fill mr-1"></i>Save & Continue';
        }
        fixStepIndicator(n)
    }

    function nextPrev(n) {
        var x = document.getElementsByClassName("tab");

        if (n === 1 && !validateForm()) return false;
        x[currentTab].style.display = "none";
        currentTab = currentTab + n;
        if (currentTab >= x.length) {

            document.getElementById("nextprevious").style.display = "none";
            document.getElementById("all-steps").style.display = "none";
            document.getElementById("register").style.display = "none";
            document.getElementById("text-message").style.display = "block";
        }
        showTab(currentTab);
    }

    function validateForm() {
        var x, y, i, valid = true;
        x = document.getElementsByClassName("tab");
        y = x[currentTab].getElementsByTagName("input");
        for (i = 0; i < y.length; i++) { if (y[i].value === "" ) { y[i].className +=" invalid" ; valid=false; } }
        if (valid) { document.getElementsByClassName("step")[currentTab].className +=" finish" ; } return valid; }

        function fixStepIndicator(n) {
            var i, x=document.getElementsByClassName("step");
            for (i=0; i < x.length; i++) {
                x[i].className=x[i].className.replace(" active", "" );
            }
            x[n].className +=" active" ;
        }
</script>

</body>
</html>
