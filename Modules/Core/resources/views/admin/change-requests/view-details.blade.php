@extends('core::admin.layouts.admin_design')
@section('title', $request->dataUpdate->column_name === 'name' ? 'Name Amendment Request Details' : 'Ownership Amendment Request Details')
@section('stylesheets')
    <style>
        .style-head {
            line-height: inherit;
        }
    </style>
@endsection
@section('content')
    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="content-page m-auto">
                        <div class="nk-block-head-sub">
                            @if($request->dataUpdate->column_name === 'school_ownership_status_id')
                                @if($request->approval_status === 'PENDING')
                                    <a class="back-to" href="{{ route('admin-new-ownership-changes') }}">
                                @elseif($request->approval_status === 'APPROVED')
                                    <a class="back-to" href="{{ route('admin-approved-ownership-changes') }}">
                                @elseif($request->approval_status === 'REJECTED')
                                    <a class="back-to" href="{{ route('admin-rejected-ownership-changes') }}">
                                @endif
                            @else
                                @if($request->approval_status === 'PENDING')
                                    <a class="back-to" href="{{ route('admin-new-name-changes') }}">
                                @elseif($request->approval_status === 'APPROVED')
                                    <a class="back-to" href="{{ route('admin-approved-name-changes') }}">
                                @elseif($request->approval_status === 'REJECTED')
                                    <a class="back-to" href="{{ route('admin-rejected-name-changes') }}">
                                @endif
                            @endif
                                <em class="icon ni ni-arrow-left"></em><span>Back</span>
                            </a>
                        </div>
                        <nav class="nk-block-des mb-1">
                            <ul class="breadcrumb breadcrumb-arrow">
                                @role('emis-super-admin')
                                    <li class="breadcrumb-item">
                                        <a href="{{ route('admin-dashboard') }}" class="text-primary">Dashboard</a>
                                    </li>
                                @endrole
                                @if($request->dataUpdate->column_name === 'name')
                                    <li class="breadcrumb-item">
                                        <a href="javascript:history.back()" class="text-primary">Name Amendment Requests</a>
                                    </li>
                                @elseif($request->dataUpdate->column_name === 'school_ownership_status_id')
                                    <li class="breadcrumb-item">
                                        <a href="javascript:history.back()" class="text-primary">Ownership Amendment Requests</a>
                                    </li>
                                @endif
                                <li class="breadcrumb-item active" aria-current="page">View Details</li>
                            </ul>
                        </nav>
                        @include('core::admin.alerts.messages')
                        <div class="nk-block">
                            <div class="card card-stretch">
                                <div class="card-inner-group">
                                    <div class="card-body card-bordered border-dark-teal">
                                        <div class="card-inner card-inner-xl" style="padding: 3%">
                                            <div class="entry">
                                                <h4>{{ $request->dataUpdate->column_name === 'name' ? 'Name Amendment Request Details' : 'Ownership Amendment Request Details' }}</h4>
                                                <p>Below are the school and contact person details</p>
                                                <h6 class="style-head"><span class="text-gray mr-2">Request ID:</span>#{{ $request->id ?? 'Not set' }}</h6>
                                                <h6 class="style-head"><span class="text-gray mr-2">Date Submitted:</span>
                                                    @if($request->date_created)
                                                        {{ \Carbon\Carbon::parse($request->date_created)->toDayDateTimeString() }}
                                                    @else
                                                        'Not set'
                                                    @endif
                                                </h6>
                                                <h6 class="style-head"><span class="text-gray mr-2">Status:</span>
                                                    @if($request->approval_status === 'PENDING')
                                                        <span class="text-uppercase badge badge-primary">Pending</span>
                                                    @elseif($request->approval_status === 'APPROVED')
                                                        <span class="text-uppercase badge bg-dark-teal">Approved</span>
                                                        <h6 class="style-head"><span class="text-gray mr-2">Date Approved:</span>
                                                            @if($request->date_approved)
                                                                {{ \Carbon\Carbon::parse($request->date_approved)->toDayDateTimeString() }}
                                                            @else
                                                                <span class="text-muted font-italic">Not set</span>
                                                            @endif
                                                        </h6>
                                                    @elseif($request->approval_status === 'REJECTED')
                                                        <span class="text-uppercase badge badge-danger">Rejected</span>
                                                                 <h6 class="style-head"><span class="text-gray mr-2">Date Rejected:</span>
                                                            @if($request->date_approved)
                                                                {{ \Carbon\Carbon::parse($request->date_approved)->toDayDateTimeString() }}
                                                            @else
                                                                <span class="text-muted font-italic">Not set</span>
                                                            @endif
                                                        </h6>
                                                    @endif
                                                </h6>
                                                <h6 class="style-head">
                                                <span class="text-gray mr-2">
                                                    @if($request->approval_status === 'APPROVED')
                                                        Approved By:
                                                    @elseif($request->approval_status === 'REJECTED')
                                                        Rejected By:
                                                    @else
                                                        Status By:
                                                    @endif
                                                </span>
                                                    @if($request->approved_by !== null)
                                                        {{ $request->approvedBy->first_name ?? 'Not set' }} {{ $request->approvedBy->surname ?? '' }} {{ $request->approvedBy->other_names ?? '' }}
                                                    @else
                                                        <span class="text-muted font-italic">Not set</span>
                                                    @endif
                                                </h6>

                                                @if($request->dataUpdate->column_name === 'name')
                                                    <h6 class="style-head"><span class="text-gray mr-2">Old Name:</span>{{ $request->dataUpdate->old_value ?? 'Not set' }}</h6>
                                                    <h6 class="style-head"><span class="text-gray mr-2">New Name:</span>{{ $request->dataUpdate->new_value ?? 'Not set' }}</h6>
                                                @elseif($request->dataUpdate->column_name === 'school_ownership_status_id')
                                                    <h6 class="style-head"><span class="text-gray mr-2">Old Ownership:</span>{{ $request->dataUpdate->old_value ?? 'Not set' }}</h6>
                                                    <h6 class="style-head"><span class="text-gray mr-2">New Ownership:</span>{{ $request->dataUpdate->new_value ?? 'Not set' }}</h6>
                                                @endif
                                                <h6 class="style-head"><span class="text-gray mr-2">Reason:</span>{{ $request->reason->name ?? 'Not set' }}</h6>

                                                <h6 class="style-head"><span class="text-gray mr-2">Description:</span>{{ $request->description ?? 'Not set' }}</h6>

                                                <div class="nk-divider divider md"></div>
                                                <div class="preview-block">
                                                    <div class="row gy-4">
                                                        <div class="col-sm-6">
                                                            <span class="preview-title-lg overline-title">Institution Details</span>
                                                            <div class="row">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Name:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">EMIS No:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->emis_number ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Old EMIS No:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->old_emis_number ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Institution Type:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->school_type->display_name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Ownership:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->ownership_status->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-4">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Email:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->email ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Phone:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->phone ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-sm-6">
                                                            <span class="preview-title-lg overline-title">Location Details</span>
                                                            <div class="row mt-2">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Region:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->region->name ?? 'Not set' }} REGION</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">District:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->district->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">County:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->county->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Sub County:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->sub_county->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>

                                                            <div class="row mt-1">
                                                                <div class="col-lg-4">
                                                                    <h6 class="style-head"><span class="text-gray">Parish:</span></h6>
                                                                </div>
                                                                <div class="col-lg-8">
                                                                    <h6 class="style-head">{{ $request->dataUpdate->requestSchool->parish->name ?? 'Not set' }}</h6>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @role('emis-master-deo')
                                                @if($request->approval_status === 'PENDING')
                                                    <div class="form-group mt-5">
                                                        <p class="lead">By Clicking Approve, I hereby confirm that I have verified the institution details and I take full responsibility of this action.</p>
                                                    </div>
                                                @endif
                                                @endrole
                                            </div>
                                            @if($request->approval_status === 'REJECTED')
                                                <div class="nk-divider divider md"></div>
                                                <div class="nk-block">
                                                    <span class="preview-title-lg overline-title">Why Was The Application Rejected?</span>
                                                    <div class="nk-block-head nk-block-head-sm nk-block-between">
                                                    </div><!-- .nk-block-head -->
                                                    <div class="bq-note">
                                                        <div class="bq-note-item">
                                                            <div class="bq-note-text">
                                                                <p>{{ $request->reject_reason }}</p>
                                                            </div>
                                                        </div><!-- .bq-note-item -->
                                                    </div><!-- .bq-note -->
                                                </div>
                                            @endif

                                        </div><!-- .card-inner -->
                                                                              <!-- Buttons for Approve, Reject, and Cancel -->
                        @if($request->approval_status === 'PENDING')
                            <div class="nk-block mt-3">
                                <div class="d-flex justify-content-left">
                                    <button class="btn btn-primary mr-2" onclick="approveRequest({{ $request->id }})">
                                        <em class="icon ni ni-check-circle"></em> Approve
                                    </button>
                                    <button class="btn btn-danger mr-2" onclick="rejectRequest({{ $request->id }})">
                                        <em class="icon ni ni-cross-circle"></em> Reject
                                    </button>
                                    <a href="{{ route('admin-new-name-changes') }}" class="btn btn-secondary">
                                        <em class="icon ni ni-arrow-left"></em> Back
                                    </a>
                                </div>
                            </div>
                        @endif
                                    </div>
                                </div><!-- .card-inner-group -->
                            </div><!-- .card -->
                        </div><!-- .nk-block -->


                    </div><!-- .content-page -->
                </div>
            </div>
        </div>
    </div>
@endsection
@push('approvereject')
<script>
    function approveRequest(id) {
        Swal.fire({
            title: 'Approve Request',
            text: 'Are you sure you want to approve this request?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<em class="icon ni ni-check-circle"></em> Yes, approve it!',
            cancelButtonText: '<em class="icon ni ni-cross-circle"></em> Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                axios.post(`/admin/change-requests/approve/${id}`)
                    .then(response => {
                        Swal.fire('Approved!', 'Request has been approved.', 'success')
                            .then(() => window.location.reload());
                    })
                    .catch(error => {
                        Swal.fire('Error!', 'Could not approve request.', 'error');
                    });
            }
        });
    }

    function rejectRequest(id) {
        Swal.fire({
            title: 'Reject Request',
            text: 'Please provide a reason for rejection:',
            input: 'textarea',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<em class="icon ni ni-cross-circle"></em> Yes, reject it!',
            cancelButtonText: '<em class="icon ni ni-arrow-left"></em> Cancel',
            inputValidator: (value) => {
                if (!value ) {
                    return 'You need to provide a reason!';
                }
                if (value.length > 200 ) {
                    return 'Please use less than 200 characters'+ value.length;
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                axios.post(`/admin/change-requests/reject/${id}`, {
                    reject_reason: result.value
                })
                .then(response => {
                    Swal.fire('Rejected!', 'Request has been rejected.', 'success')
                        .then(() => window.location.reload());
                })
                .catch(error => {
                    Swal.fire('Error!', 'Could not reject request.', 'error');
                });
            }
        });
    }
</script>
