<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="js">

<head>
	<meta charset="utf-8">
	<meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="refresh" content="{{ config('session.lifetime') * 60 }}">
	<meta name="author" content="DEMIS">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content="Education Information Management System">
	<!-- Fav Icon  -->
	<link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">
	<!-- Page Title  -->
	<title>@yield('title') - Admin : EMIS</title>
	<!-- StyleSheets  -->
    @include('core::includes.assets.dash-lite');
	<link id="skin-default" rel="stylesheet" href="{{ asset('assets/css/skins/theme-egyptian.css?ver=3.3.0') }}">
{{--    <link rel="stylesheet" href="{{ asset('assets/css/custom.css?ver=2') }}">--}}
	@yield('stylesheets')
</head>
<body class="nk-body bg-lighter npc-default has-sidebar no-touch nk-nio-theme">
	<div id="demis-app" class="nk-app-root">
		<!-- main @s -->
		<div class="nk-main">
			<!-- sidebar @s -->
			@include('core::admin.layouts.sidebar')
			<!-- sidebar @e -->
			<!-- wrap @s -->
			<div class="nk-wrap" style="background-color: #F5F6FA">
				<!-- main header @s -->
				@include('core::admin.layouts.header')
				<!-- main header @e -->
				<!-- content @s -->
				@yield('content')
				<!-- content @e -->
				<!-- footer @s -->
				@include('core::admin.layouts.footer')
				<!-- footer @e -->
			</div>

			<!-- wrap @e -->
		</div>
		<!-- main @e -->
        @if(auth()->user()->is_temporary_password_yn)
            <change-temporary-password temporary-password="{{ auth()->user()->is_temporary_password_yn }}"></change-temporary-password>
        @endif
	</div>
	<!-- app-root @e -->
	<!-- JavaScript -->
    @yield('ticket-scripts')
	<script src="{{ asset('assets/js/bundle.js?ver=3.3.0') }}"></script>
	<script src="{{ asset('assets/js/scripts.js?ver=3.3.0') }}"></script>
    <script src="{{ asset('assets/js/libs/jquery.blockUI.js') }}"></script>
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $(document).ready(function(){
            var url = document.URL;
            if(url.indexOf('#') > 0)
            {
                var hsh = url.split('#');
                hash_url = hsh[1];
                if(hash_url.length > 0)
                {
                    $(".nav-link").each(function(){
                        $(this).removeClass('active');
                    });
                    $(".tab-pane").each(function(){
                        $(this).removeClass('active');
                    });
                    $(".nav-link[href='#"+hash_url+"']").addClass('active');
                    $("#"+hash_url).addClass('active');
                }
            }
            $(".recommend_radio").click(function(){
                let v = $(this).val();
                const sexComposition = document.querySelector("[name='sex_composition']");
                const recommendField = document.querySelector('.recommend_fields');
                if (recommendField) {
                    if (v === 1 || v === '1') {
                        recommendField.classList.remove('d-none');
                        if(sexComposition && sexComposition.hasAttribute('required') === false){
                            sexComposition.setAttribute('required','required');
                        }
                    } else {
                        recommendField.classList.add('d-none');

                        if(sexComposition && sexComposition.hasAttribute('required') === true){
                            sexComposition.removeAttribute('required');
                        }
                    }
                }
            });
            $(".lr_settings").click(function(e){
                e.preventDefault();
                var id = $(this).data('id'),
                    act = $(this).data('act'),
                    cat = $(this).data('cat');
                $.ajax({
                    url: '/admin/licence-applications-settings',
                    data: 'lr_settings=1&id='+id+'&cat='+cat+'&act='+act+'&csrf_token={{csrf_token()}}',
                    type:'POST',
                    beforeSend:function(){
                        $("#LRMdForm .modal-body").html('<span class="icon ni ni-loader"></span> Loading ...');
                    },
                    success: function(data){
                        if(act === 'delete'){
                            $(".modal_action_button").val('Yes, Remove Item');
                            $(".modal_action_button").removeClass('btn-primary').addClass('btn-danger');
                        }
                        $("#LRMdForm .modal-body").html(data);
                    },
                    error:function(jqXHR, textStatus, errorThrown){
                        $("#LRMdForm .modal-body").html('<span class="text-danger">'+textStatus+':=>'+errorThrown+'</span>');
                    }
                });
                $("#LRMdForm").modal('show');
            });
        });

        function fef_field_change(sv){
            $(".num_ranges,.select_options").attr('style','display:none !important');
            if (sv === 'number') {
                $(".num_ranges").attr('style','');
                $(".select_options").attr('style','display:none !important');
            }
            if (sv === 'checkbox' || sv === 'select') {
                $(".num_ranges").attr('style','display:none !important');
                $(".select_options").attr('style','');
            }
        }

    </script>
    @yield('lr-scripts')
    @yield('project-scripts')
	@vite('Modules/Core/resources/assets/js/app.js')
	@yield('scripts')
	@yield('dashboard-scripts')
    @yield('text-editor-scripts')

</body>
@stack('scripts')
@stack('approvereject')
</html>
