@extends('core::admin.layouts.admin_design')
@section('title', $applicationNumber.' Learner Pre-Registration Application')
@section('content')
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block-head-sub mb-2">
                        <a class="back-to"
                           href="{{ url('/admin/learner-pre-registration-applications/' . strtolower($status ?? 'pending')) }}">
                            <em class="icon ni ni-arrow-left"></em><span>Back</span>
                        </a>
                    </div>
                    <nav class="nk-block-des mb-1">
                        <ul class="breadcrumb breadcrumb-arrow">
                            <li class="breadcrumb-item">
                                <a href="/admin/dashboard" class="text-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url('/admin/learner-pre-registration-applications/' . strtolower($status ?? 'pending')) }}"
                                   class="text-primary">
                                    {{ ucfirst($status ?? 'Pending') }} Learner Pre-registration Applications
                                </a>
                            </li>
                            <li class="breadcrumb-item active text-soft">Application Details</li>
                        </ul>
                    </nav>
                    @livewire('admin-learner-enrolment-application-show', ['status' => $status, 'applicationNumber' => $applicationNumber])
                </div>
            </div>
        </div>
    </div>
@endsection
