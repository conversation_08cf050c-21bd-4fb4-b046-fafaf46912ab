<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-Q0CRXX3H2R"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-Q0CRXX3H2R');
</script>
	<meta charset="utf-8">
	<meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="refresh" content="{{ config('session.lifetime') * 60 }}">
	<meta name="author" content="Softnio">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<meta name="description" content=".">
	<!-- Fav Icon  -->
	<link rel="shortcut icon" href="{{ asset('images/favicon.png') }}">
	<!-- Page Title  -->
	<title>@yield('title') - EMIS</title>
	<!-- StyleSheets  -->
    @include('core::includes.assets.instructions.dash-lite')
	<link id="skin-default" rel="stylesheet" href="{{ asset('assets/instructions/css/skins/theme-landing.css?ver=3.3.0') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom-other.css') }}">
	<style>
		.logo-img {
			max-height: 50px !important;
		}
	</style>
</head>

<body class="nk-body bg-lighter npc-default has-no-sidebar ">
<div id="demis-app" class="nk-app-root">
	<!-- main @s -->
	<div class="nk-main ">
		<!-- wrap @s -->
		<div class="nk-wrap ">
			<!-- main header @s -->
		@include('core::layouts.header')
		<!-- main header @e -->
			<!-- content @s -->
		@yield('content')
		@if(request()->is('teacheraccreditation'))
		@livewire('teacheraccreditation.accreditation')
		@endif

		<!-- content @e -->
			<!-- footer @s -->
		@include('core::layouts.footer')
		<!-- footer @e -->
		</div>
		<!-- wrap @e -->
	</div>
	<!-- main @e -->
</div>
<!-- app-root @e -->
<!-- JavaScript -->
<script src="{{ asset('assets/instructions/js/bundle.js?ver=3.3.0') }}"></script>
<script src="{{ asset('assets/instructions/js/scripts.js?ver=3.3.0') }}"></script>
@yield('validation-script')
@vite('Modules/Core/resources/assets/js/app.js')
@yield('scripts')
</body>
</html>
