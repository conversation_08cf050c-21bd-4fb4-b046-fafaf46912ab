import Vue from "vue";

Vue.component('admin-manage-all-schools', () => import(/* webpackChunkName: "AdminManageAllSchools" */ './components/Admin/Schools/Index.vue'));
Vue.component('admin-manage-all-schools-enrolments', () => import(/* webpackChunkName: "AdminManageAllSchoolsEnrolments" */ './components/Admin/Schools/Enrolments/Index.vue'));
Vue.component('admin-manage-reports-promotions', () => import(/* webpackChunkName: "AdminManageReportsPromotions" */ './components/Admin/Reports/Promotions/Index.vue'));
Vue.component('admin-manage-reports-promotions-local-governments', () => import(/* webpackChunkName: "AdminManageReportsPromotionsLocalGovernments" */ './components/Admin/Reports/Promotions/LocalGovernment.vue'));
Vue.component('admin-units-filter', () => import(/* webpackChunkName: "AdminUnitsFilter" */ './components/Admin/AdminUnitsFilter.vue'));
Vue.component('admin-institution-identification', () => import(/* webpackChunkName: "AdminInstitutionIdentification" */ './components/Admin/Schools/Identification.vue'));
Vue.component('admin-units-filter-district-user', () => import(/* webpackChunkName: "AdminUnitsFilterDistrictUser" */ './components/Admin/AdminUnitsFilterDistrictUser.vue'));
Vue.component('admin-units-filter-sub-county-user', () => import(/* webpackChunkName: "AdminUnitsFilterSubCountyUser" */ './components/Admin/AdminUnitsFilterSubCountyUser.vue'));
Vue.component('latest-notice', () => import(/* webpackChunkName: "AdminLatestNotice" */ './components/Admin/LatestNotice.vue'));
Vue.component('school-location-view', () => import(/* webpackChunkName: "AdminSchoolLocationView" */ './components/Admin/Schools/Location.vue'));
Vue.component('school-users-view', () => import(/* webpackChunkName: "AdminSchoolUsersView" */ './components/Admin/Schools/Users.vue'));

Vue.component('emis-calendar', () => import(/* webpackChunkName: "emisCalendar" */ './components/Admin/Administration/EmisCalendar.vue'));
Vue.component('emis-returns', () => import(/* webpackChunkName: "emisReturns" */ './components/Admin/Administration/EmisReturns.vue'));
Vue.component('update-emis-return', () => import(/* webpackChunkName: "updateEmisReturns" */ './components/Admin/Administration/UpdateEmisReturn.vue'));
Vue.component('admin-reports', () => import(/* webpackChunkName: "adminReports" */ './components/Admin/Administration/Reports.vue'));
Vue.component('admin-infrastructure-reports', () => import(/* webpackChunkName: "adminInfrastructureReports" */ './components/Admin/Facilities/Infrastructure.vue'));

Vue.component('admin-emis-number-applications-index', () => import(/* webpackChunkName: "AdminEmisNumberApplicationsIndex" */ './components/Admin/Emis Applications/Index.vue'));
Vue.component('admin-emis-number-applications-show', () => import(/* webpackChunkName: "AdminEmisNumberApplicationShow" */ './components/Admin/Emis Applications/Show.vue'));

Vue.component('admin-learner-enrolment-applications-index', () => import(/* webpackChunkName: "AdminLearnerPreRegistrationApplicationsIndex" */ './components/Admin/LearnerEnrolmentApplications/Index.vue'));
Vue.component('admin-learner-enrolment-applications-show', () => import(/* webpackChunkName: "AdminLearnerEnrolmentApplicationShow" */ './components/Admin/LearnerEnrolmentApplications/Show.vue'));

Vue.component('user-manager-ministry-users', () => import(/* webpackChunkName: "UserManagerMinistryUsers" */ './components/Admin/UserManager/MinistryUsers.vue'));
Vue.component('user-manager-district-users', () => import(/* webpackChunkName: "UserManagerDistrictUsers" */ './components/Admin/UserManager/DistrictUsers.vue'));
Vue.component('user-manager-sub-county-users', () => import(/* webpackChunkName: "UserManagerSubCountyUsers" */ './components/Admin/UserManager/SubCountyUsers.vue'));
Vue.component('user-manager-institution-users', () => import(/* webpackChunkName: "UserManagerInstitutionUsers" */ './components/Admin/UserManager/InstitutionUsers.vue'));
Vue.component('user-manager-roles', () => import(/* webpackChunkName: "UserManagerRoles" */ './components/Admin/UserManager/Roles.vue'));

//Learners
Vue.component('admin-learners-enrolment', () => import(/* webpackChunkName: "AdminEnrolment" */ './components/Admin/Learners/Enrolment.vue'));
Vue.component('particular-school-learners', () => import(/* webpackChunkName: "ParticularSchoolLearnersIndex" */ './components/Admin/Learners/SchoolLearners.vue'));
Vue.component('particular-school-learners-degree', () => import(/* webpackChunkName: "ParticularSchoolLearnersDegree" */ './components/Admin/Learners/SchoolDegreeLearners.vue'));
Vue.component('admin-manage-all-learners', () => import(/* webpackChunkName: "AdminManageAllLearners" */ './components/Admin/Learners/All.vue'));
Vue.component('admin-manage-all-learners-degree', () => import(/* webpackChunkName: "AdminManageAllLearnersDegree" */ './components/Admin/Learners/DegreeLearners.vue'));
//Parents
Vue.component('admin-school-parents', () => import(/* webpackChunkName: "AdminSchoolParents" */ './components/Admin/Schools/Parents.vue'));

//Staff
Vue.component('particular-school-teachers', () => import(/* webpackChunkName: "ParticularSchoolTeachers" */ './components/Admin/Staff/Teachers.vue'));
Vue.component('particular-school-non-teaching-staff', () => import(/* webpackChunkName: "ParticularSchoolNonTeachingStaff" */ './components/Admin/Staff/NonTeachingStaff.vue'));

Vue.component('admin-teacher-postings-index', () => import(/* webpackChunkName: "AdminTeacherPostings" */ './components/Admin/Staff/Postings/Teachers/Index.vue'));
Vue.component('admin-non-teaching-staff-postings-index', () => import(/* webpackChunkName: "AdminNonTeachingStaffPostings" */ './components/Admin/Staff/Postings/Non Teaching Staff/Index.vue'));

Vue.component('admin-teacher-transfers-index', () => import(/* webpackChunkName: "AdminTeacherTransfers" */ './components/Admin/Staff/Transfers/Teachers/Index.vue'));
Vue.component('admin-teacher-transfers-create', () => import(/* webpackChunkName: "AdminNewTeacherTransfer" */ './components/Admin/Staff/Transfers/Teachers/Create.vue'));

Vue.component('admin-non-teaching-staff-transfers-index', () => import(/* webpackChunkName: "AdminNonTeachingStaffTransfers" */ './components/Admin/Staff/Transfers/Non Teaching Staff/Index.vue'));
Vue.component('admin-non-teaching-staff-transfers-create', () => import(/* webpackChunkName: "AdminNewNonTeachingStaffTransfer" */ './components/Admin/Staff/Transfers/Non Teaching Staff/Create.vue'));

//Developers
Vue.component('email-outbox', () => import(/* webpackChunkName: "EmailOutbox" */ './components/Admin/UserManager/EmailOutbox.vue'));
Vue.component('tac', () => import(/* webpackChunkName: "UserTac" */ './components/Admin/UserManager/Tac.vue'));
Vue.component('uploaded-excels', () => import(/* webpackChunkName: "UploadedExcels" */ './components/Admin/UserManager/UploadedExcels.vue'));
Vue.component('user-account-creation-dumps', () => import(/* webpackChunkName: "UserAccountCreationDumps" */ './components/Admin/Developer/AccountCreationDumps.vue'));
Vue.component('duplicate-approvals', () => import(/* webpackChunkName: "DuplicateApprovals" */ './components/Admin/Developer/DuplicateApprovals.vue'));

//Settings
Vue.component('admin-settings-academic-years', () => import(/* webpackChunkName: "AdminSettingsAcademicYears" */ './components/Admin/Settings/AcademicYears.vue'));
Vue.component('admin-settings-religions', () => import(/* webpackChunkName: "AdminSettingsReligions" */ './components/Admin/Settings/Religions.vue'));
Vue.component('admin-settings-learner-transfer-reasons', () => import(/* webpackChunkName: "AdminSettingsLearnerTransferReasons" */ './components/Admin/Settings/LearnerTransferReasons.vue'));
Vue.component('admin-settings-learner-flagging-reasons', () => import(/* webpackChunkName: "AdminSettingsLearnerFlaggingReasons" */ './components/Admin/Settings/LearnerFlaggingReasons.vue'));
Vue.component('admin-settings-amend-school-details-reasons', () => import(/* webpackChunkName: "AdminSettingsAmendSchoolDetailsReasons" */ './components/Admin/Settings/AmendSchoolDetailsReasons.vue'));
Vue.component('admin-settings-school-suspension-reasons', () => import(/* webpackChunkName: "AdminSettingsSchoolSuspensionReasons" */ './components/Admin/Settings/SchoolSuspensionReasons.vue'));
Vue.component('admin-settings-teacher-professional-qualifications', () => import(/* webpackChunkName: "AdminSettingsTeacherProfessionalQualifications" */ './components/Admin/Settings/TeacherProfessionalQualifications.vue'));
Vue.component('admin-settings-teacher-responsibilities', () => import(/* webpackChunkName: "AdminSettingsTeacherResponsibilities" */ './components/Admin/Settings/TeacherResponsibilities.vue'));
Vue.component('admin-settings-licensing-and-registration-requirements', () => import(/* webpackChunkName: "AdminSettingsLicencingAndRegistrationRequirements" */ './components/Admin/Settings/LicencingAndRegistrationRequirements.vue'));
Vue.component('admin-settings-school-type-requirements', () => import(/* webpackChunkName: "AdminSettingsSchoolTypeRequirements" */ './components/Admin/Settings/SchoolTypeRequirements.vue'));
Vue.component('admin-settings-project-currencies', () => import(/* webpackChunkName: "AdminSettingsProjectCurrencies" */ './components/Admin/Settings/Projects/Currencies.vue'));
Vue.component('admin-settings-project-categories', () => import(/* webpackChunkName: "AdminSettingsProjectCategories" */ './components/Admin/Settings/Projects/Categories.vue'));
Vue.component('admin-settings-project-completion-statuses', () => import(/* webpackChunkName: "AdminSettingsProjectCompletionStatuses" */ './components/Admin/Settings/Projects/CompletionStatuses.vue'));
Vue.component('admin-settings-project-funders', () => import(/* webpackChunkName: "AdminSettingsProjectFunders" */ './components/Admin/Settings/Projects/Funders.vue'));
Vue.component('admin-settings-project-components', () => import(/* webpackChunkName: "AdminSettingsProjectComponents" */ './components/Admin/Settings/Projects/Components.vue'));
Vue.component('admin-settings-project-component-types', () => import(/* webpackChunkName: "AdminSettingsProjectComponentTypes" */ './components/Admin/Settings/Projects/ComponentTypes.vue'));
Vue.component('admin-settings-clusters', () => import(/* webpackChunkName: "AdminSettingsClusters" */ './components/Admin/Settings/Clusters.vue'));
Vue.component('admin-settings-clusters-upper-lgs', () => import(/* webpackChunkName: "AdminSettingsClustersUpperLgs" */ './components/Admin/Settings/ClusterUpperLgs.vue'));
Vue.component('admin-settings-clusters-lower-lgs', () => import(/* webpackChunkName: "AdminSettingsClustersLowerLgs" */ './components/Admin/Settings/ClusterLowerLgs.vue'));
Vue.component('admin-settings-cluster-view-details', () => import(/* webpackChunkName: "AdminSettingsClusterViewDetails" */ './components/Admin/Settings/ClusterViewDetails.vue'));
Vue.component('clusters-manage-regional', () => import(/* webpackChunkName: "ClustersManagerRegional" */ './components/ClusterUser/Regional/Index.vue'));
Vue.component('admin-settings-ticket-categories', () => import(/* webpackChunkName: "AdminSettingsTicketCategories" */ './components/Admin/Settings/Tickets/Categories.vue'));
Vue.component('admin-settings-ticket-sub-categories', () => import(/* webpackChunkName: "AdminSettingsTicketSubCategories" */ './components/Admin/Settings/Tickets/SubCategories.vue'));


//Utilities
Vue.component('learner-export', () => import(/* webpackChunkName: "AdminSettingsReligions" */ './components/Admin/Utilities/LearnerExport.vue'));

//Projects
Vue.component('admin-view-projects', () => import(/* webpackChunkName: "AdminViewProjects" */ './components/Admin/Projects/ViewProjects.vue'));
Vue.component('admin-create-projects', () => import(/* webpackChunkName: "AdminCreateProjects" */ './components/Admin/Projects/CreateProject.vue'));
Vue.component('admin-edit-project', () => import(/* webpackChunkName: "AdminEditProject" */ './components/Admin/Projects/EditProject.vue'));
Vue.component('admin-view-project-details', () => import(/* webpackChunkName: "AdminViewProjectDetails" */ './components/Admin/Projects/viewProjectDetails.vue'));
Vue.component('admin-view-project-info', () => import(/* webpackChunkName: "AdminViewProjectInfo" */ './components/Admin/Projects/viewProjectInfo.vue'));
Vue.component('admin-view-project-funders', () => import(/* webpackChunkName: "AdminViewProjectFunders" */ './components/Admin/Projects/viewProjectFunders.vue'));
Vue.component('admin-view-project-beneficiaries', () => import(/* webpackChunkName: "AdminViewProjectBeneficiaries" */ './components/Admin/Projects/viewBeneficiarySchools.vue'));

//Learner transfers and claims
Vue.component('admin-school-learners-transfers', () => import(/* webpackChunkName: "AdminLearnersTransfers" */ './components/Admin/Utilities/LearnerTransfers.vue'));
Vue.component('admin-incoming-learner-transfers', () => import(/* webpackChunkName: "AdminIncomingLearnerTransfers" */ './components/Admin/Utilities/IncomingLearnerTransfers.vue'));
Vue.component('admin-outgoing-learner-transfers', () => import(/* webpackChunkName: "AdminOutgoingLearnerTransfers" */ './components/Admin/Utilities/OutgoingLearnerTransfers.vue'));

Vue.component('admin-school-learners-claims', () => import(/* webpackChunkName: "AdminLearnersClaims" */ './components/Admin/Utilities/LearnerClaims.vue'));
Vue.component('admin-incoming-learner-claims', () => import(/* webpackChunkName: "AdminIncomingLearnerClaims" */ './components/Admin/Utilities/IncomingLearnerClaims.vue'));
Vue.component('admin-outgoing-learner-claims', () => import(/* webpackChunkName: "AdminOutgoingLearnerClaims" */ './components/Admin/Utilities/OutgoingLearnerClaims.vue'));

//Learner admission
Vue.component('admin-school-learners-transitions', () => import(/* webpackChunkName: "AdminLearnersTransitions" */ './components/Admin/Utilities/LearnerTransitions.vue'));
Vue.component('admin-incoming-learner-transitions', () => import(/* webpackChunkName: "AdminIncomingLearnerTransitions" */ './components/Admin/Utilities/IncomingLearnerTransitions.vue'));
Vue.component('admin-outgoing-learner-transitions', () => import(/* webpackChunkName: "AdminOutgoingLearnerTransitions" */ './components/Admin/Utilities/OutgoingLearnerTransitions.vue'));

//Data update
Vue.component('admin-learners-for-deleting', () => import(/* webpackChunkName: "AdminLearnersDeleted" */ './components/Admin/DataUpdate/LearnersDeleted.vue'));
Vue.component('admin-learner-flagged-view-application', () => import(/* webpackChunkName: "AdminLearnerDeletedViewApplication" */ './components/Admin/DataUpdate/LearnerDeletedViewApplication.vue'));
