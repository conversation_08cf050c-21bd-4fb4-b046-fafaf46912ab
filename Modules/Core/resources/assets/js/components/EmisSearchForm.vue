<template>
    <div class="nk-content nk-content-lg nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="kyc-app wide-md m-auto">
                        <div class="nk-block-head nk-block-head-lg wide-xs mx-auto">
                            <div class="nk-block-head-content text-center">
                                <h3 class="nk-block-title fw-normal">EMIS NUMBER SEARCH FORM</h3>
                                <div class="nk-block-des">
                                    <p>
                                        Please use the search form below to retrieve the EMIS Number for your institution. For those whose administrative units changed e.g County or Sub-country, try to search with previous County or Sub-county
                                    </p>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                        <div class="nk-block">
                            <div class="card">
                                <div class="card-body">
                                    <div class="tab-content card-inner">
                                        <form ref="searchForm" @submit.prevent="searchEmisNumber()">
                                            <div class="row">
                                                <div class="col-md-6 pb-3">

                                                    <div class="form-group">
                                                        <label class="form-label" for="school_type_id">Institution Type  <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select your Institution type.</span>

                                                        <div class="form-control-wrap">
                                                            <select id="school_type_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="school_type in institution_types" :value="school_type.id">{{ school_type.display_name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="form-label" for="district_id">District <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the District of your institution.school.</span>

                                                        <div class="form-control-wrap">
                                                            <select id="district_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>

                                                    </div>
                                                    <div class="form-group">
                                                        <label class="form-label" for="county_id">County <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the County of your institution.school.</span>

                                                        <div class="form-control-wrap">
                                                            <select id="county_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 pb-3">
                                                    <div class="form-group">
                                                        <label class="form-label" for="sub_county_id">Sub County <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the Sub County of your institution.school.</span>

                                                        <div class="form-control-wrap">
                                                            <select id="sub_county_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                            </select>
                                                        </div>
                                                    </div>


                                                    <div class="form-group">
                                                        <label class="form-label" for="parish_id">Parish <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the Parish of your institution.school.</span>

                                                        <div class="form-control-wrap">
                                                            <select id="parish_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="form-label" for="school_ownership_status_id">Ownership <span class="text-danger">*</span></label>
                                                        <div class="form-control-wrap">
                                                            <select id="school_ownership_status_id" class="form-control" required>
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="ownership in ownership_statuses" :value="ownership.id">{{ ownership.name.toUpperCase() }}</option>

                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='row'>
                                                <div class='col-lg-6 col-6'>
                                                    <div class="form-control-wrap pt-4">
                                                        <button style="width:100%; margin-top:5px; text-align:center"  :disabled="loading" type="submit" class="btn btn-primary">
                                                            <em v-if="!loading" class="ni ni-search mr-2"></em>
                                                            <em v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></em>
                                                            <span v-if="loading" class="align-self-center">Searching...</span>
                                                            <span v-if="loading" class="sr-only">Searching...</span>
                                                            <span v-if="!loading" class="align-self-center text-center">Search</span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class='col-lg-6 col-6'>
                                                    <div class="form-control-wrap pt-4">
                                                        <button @click="resetSearchForm" style="width:100%; margin-top:5px; text-align:center"  type="reset" class="btn btn-outline-primary"><span class="align-self-center text-center">Reset</span></button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!--div class="row g-4 mb-3">
                                                <div class="col-lg-5 mt-lg-3">
                                                    <div class="form-group">
                                                        <label class="form-label" for="school-name">Institution Name <span class="text-danger">*</span></label>
                                                        <span class="form-note">Enter the registered name of your school.</span>
                                                    </div>
                                                </div>
                                                <div class="col-lg-7 mt-lg-3">
                                                    <div class="form-group">
                                                        <div class="form-control-wrap">
                                                            <input v-model.trim="institution.school.name" type="text" placeholder="Enter Institution Name" class="text-uppercase form-control bg-primary-dim" required id="school-name">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div-->

                                        </form>
                                    </div>

                                    <div class="text-center pt-3 pb-3" v-if="loading">loading... please wait</div>
                                    <div class="text-center pt-3 pb-3 text-grey" style="color:#666" v-if="!schools.length && !loading">No school records</div>
                                    <table class="table table-sm table-striped mt-5 mb-5" v-if="schools.length">
                                        <thead>
                                        <tr>
                                            <th>Schools</th>
                                            <th>District</th>
                                            <th>EMIS Number</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="school in schools">
                                                <td class='ma-4'>{{ school.name }}</td>
                                                <td class='ma-4'>{{ school.district.name }}</td>
                                                <td class='ma-4'>{{ school.emis_number }}</td>
<!--                                                <td class='ma-4'>{{ school.emis_number.substring(2, 8) }}</td>-->
<!--                                                <td v-if="school.old_emis_number === null" class='ma-4'>{{ school.emis_number.substring(2, 8) }}</td>-->
<!--                                                <td v-else class='ma-4'>{{ school.old_emis_number }}</td>-->
                                            </tr>
                                        </tbody>
                                    </table>
                                    <p>
                                        If you cannot find your school in the list above, please contact your respective District/Municipal/City Education Officer for assistance or send email request to <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </p>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                    </div><!-- .kyc-app -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Notifications from "./Notifications.vue";
import OtpTimer from "./Institution/OtpTimer.vue";
import CSPMixin from "../mixins/CSPMixin";

export default {
    name: "EmisSearchForm",
    props: [
        'districtsObj',
        'institutionTypesObj',
        'ownershipStatusesObj'
    ],
    mixins:[CSPMixin],
    components: {
        Notifications
    },
    mounted() {
        // alert(9999);
        this.initCSPMixin(null,'#district_id','#county_id','#sub_county_id','#parish_id', this.institution.school);
        this.initPlugins();
    },
    data: function () {
        return {
            selectedDistrict:{name:''},
            schools:[],
            loading: false,
            institution_types: [],
            ownership_statuses: [],
            districts: [],
            counties: [],
            sub_counties: [],
            parishes: [],
            villages: [],
            institution: {
                school: {
                    school_type_id: '', // School type
                    district_id: '',
                    county_id: '',
                    sub_county_id: '',
                    parish_id: '',
                    school_ownership_status_id: '', //Ownership status
                },
            },
        }
    },
    methods: {
        resetSearchForm: function(){
            //console.log(this.$refs.searchForm.reset);
            this.$refs.searchForm.reset();

            $(function() {
                $('select').val('').trigger('change');
            })
        },

        initPlugins: function () {
            console.clear();
            let self = this;

            this.districts = JSON.parse(this.districtsObj);
            // console.log(this.districts);
            this.institution_types = JSON.parse(this.institutionTypesObj);
            this.ownership_statuses = JSON.parse(this.ownershipStatusesObj);

            $('#school_type_id').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.institution.school.school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            // $('#district_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.institution.school.district_id = data.id.length > 0 ? Number(data.id) : "";
            //         self.loadCounties();
            //         // self.selectedDistrict = data;
            //         // console.log(self.districts);
            //         return data.text;
            //     },
            // });

            // $('#county_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.institution.school.county_id = data.id.length > 0 ? Number(data.id) : "";
            //         self.loadSubCounties();
            //         return data.text;
            //     },
            // });

            // $('#sub_county_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.institution.school.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
            //         self.loadParishes();
            //         return data.text;
            //     },
            // });

            // $('#parish_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.institution.school.parish_id = data.id.length > 0 ? Number(data.id) : "";
            //         return data.text;
            //     },
            // });

            $('#school_ownership_status_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.institution.school.school_ownership_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
        },

        searchEmisNumber: function() {
            this.loading = true;
            this.schools = [];
            axios.post('/institutions/search-emis-number', this.institution.school)
                .then(({data})=>{
                    // console.log(data);
                    this.schools = data;
                })
                .finally(()=>{
                    this.loading = false;
                })

        },
    },
    computed: {
        institutionType: function () {
            let type = null;

            if (this.school_type_id !== '') {
                type = this.institution_types.find(type=>{
                    return type.id === this.school_type_id
                });
            }

            return type;
        },
    }
}
</script>

<style scoped>
span.form-note{
    display: none;
}
button span{
    width:100%;
}
</style>
