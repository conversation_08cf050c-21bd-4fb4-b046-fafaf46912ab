<template>
    <div class="nk-content nk-content-lg nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="kyc-app wide-md m-auto">
                        <div class="nk-block-head nk-block-head-lg wide-xs mx-auto">
                            <div class="nk-block-head-content text-center">
                                <h3 class="nk-block-title fw-normal">EMIS NUMBER STATUS</h3>
                                <div class="nk-block-des"><p></p></div>
                            </div>
                        </div><!-- nk-block -->
                        <div class="nk-block">
                            <div class="card">
                                <div class="card-body">
                                    <div class="p-3">
                                        <div class="entry">
                                            <h6 class="style-head"><span class="text-gray mr-2">Application ID:</span>#{{ application.reference_number }}</h6>
                                            <h6 class="style-head"><span class="text-gray mr-2">Submitted Date:</span>{{ formatDate(application) }}</h6>
                                            <h6 class="style-head"><span class="text-gray mr-2">Status:</span>
                                                <span v-if="application.status === 0 && application.deo_date_approved === null" class="text-uppercase badge badge-secondary">Pending</span>
                                                <span v-if="application.status === 0 && application.deo_date_approved !== null" class="text-uppercase badge badge-primary">Recommended</span>
                                                <span v-if="application.status === 1" class="text-uppercase badge badge-dark-teal">Approved</span>
                                                <span v-if="application.status === 2" class="text-uppercase badge badge-red">Rejected</span>
                                                <span v-if="application.status === 3" class="text-uppercase badge badge-amaranth">Endorsed</span>
                                            </h6>
                                            <h6 v-if="application.status !== 2 && application.status === 1" class="style-head mt-4">
                                                <span class="text-gray mr-2">EMIS Number:</span>{{ application.emis_number }} |
                                                <span @click="downloadLetter()" class="cursor btn btn-sm btn-outline-dark-teal">
                                                <em class="icon ni ni-download mr-1"></em>DOWNLOAD LETTER
                                            </span>
                                            </h6>
                                            <div class="nk-divider divider md"></div>
                                            <div class="preview-block">
                                                <div class="row gy-4">
                                                    <div class="col-lg-7">
                                                        <span class="preview-title-lg  overline-title text-dark">Institution Details</span>
                                                        <div class="row">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Name:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.school_name }}</h6>
                                                            </div>
                                                        </div>

                                                        <div v-if="application.institution_level.name === 'diploma'" class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Institution Type:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.diploma_awarding_school_type.name }}</h6>
                                                            </div>
                                                        </div>

                                                        <div v-if="application.institution_level.name === 'certificate'" class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Institution Type:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.certificate_awarding_school_type.name }}</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Institution Level:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.institution_level.display_name }}</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-4">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Region:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.region.name }} REGION</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">District:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.district.name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">County:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.county.name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Sub-County:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.sub_county.name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Parish:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.parish.name }}</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-4">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Ownership Status:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.school_ownership_status.name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Year Founded:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.year_founded }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Founding Body:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.founding_body.name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Sex Composition:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 v-if="application.has_male_students && application.has_female_students" class="style-head">MIXED</h6>
                                                                <h6 v-if="!application.has_male_students && application.has_female_students" class="style-head">FEMALE ONLY</h6>
                                                                <h6 v-if="application.has_male_students && !application.has_female_students" class="style-head">MALE ONLY</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Boarding Status:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 v-if="application.admits_day_scholars_yn && application.admits_boarders_yn" class="style-head">PARTLY BOARDING</h6>
                                                                <h6 v-if="!application.admits_day_scholars_yn && application.admits_boarders_yn" class="style-head">FULLY BOARDING</h6>
                                                                <h6 v-if="application.admits_day_scholars_yn && !application.admits_boarders_yn" class="style-head">DAY SCHOOL</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-4">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Email:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">{{ application.email }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-4">
                                                                <h6 class="style-head"><span class="text-gray">Phone:</span></h6>
                                                            </div>
                                                            <div class="col-lg-8">
                                                                <h6 class="style-head">+{{ application.phone }}</h6>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-5">
                                                        <span class="preview-title-lg  overline-title text-dark">Contact Person Details</span>
                                                        <div v-if="application.contact_person !== null" class="row mt-1">
                                                            <div class="col-lg-5"></div>
                                                            <div class="col-lg-7">
                                                                <div class="d-flex justify-content-start">
                                                                    <div class="user-avatar lg sq bg-white justify-content-start">
                                                                        <img class="py-3 rounded-0" :src="application.contact_person.photo_url" :alt="application.contact_person.full_name">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-4">
                                                            <div class="col-lg-5">
                                                                <h6 class="style-head"><span class="text-gray">Name:</span></h6>
                                                            </div>
                                                            <div class="col-lg-7">
                                                                <h6 v-if="application.contact_person !== null" class="style-head ucap">{{ application.contact_person.full_name }}</h6>
                                                                <h6 v-else class="style-head ucap">{{ application.full_name }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-5">
                                                                <h6 v-if="application.school_type_id <= 6" class="style-head"><span class="text-gray">National ID No:</span></h6>
                                                                <h6 v-else class="style-head"><span class="text-gray">Passport No:</span></h6>
                                                            </div>
                                                            <div class="col-lg-7">
                                                                <h6 class="style-head ucap">{{ application.masked_contact_person_nin }}</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-1">
                                                            <div class="col-lg-5">
                                                                <h6 class="style-head"><span class="text-gray">Gender:</span></h6>
                                                            </div>
                                                            <div v-if="application.contact_person !== null" class="col-lg-7">
                                                                <h6 v-if="application.contact_person.gender === 'M'">MALE</h6>
                                                                <h6 v-if="application.contact_person.gender === 'F'">FEMALE</h6>
                                                            </div>
                                                            <div v-if="application.contact_person === null" class="col-lg-8">
                                                                <h6 v-if="application.gender === 'M'">MALE</h6>
                                                                <h6 v-if="application.gender === 'F'">FEMALE</h6>
                                                            </div>
                                                        </div>

                                                        <div class="row mt-4">
                                                            <div class="col-lg-5">
                                                                <h6 class="style-head"><span class="text-gray">Email:</span></h6>
                                                            </div>
                                                            <div class="col-lg-7">
                                                                <h6>{{ application.contact_person_email }}</h6>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-1">
                                                            <div class="col-lg-5">
                                                                <h6 class="style-head"><span class="text-gray">Phone Number:</span></h6>
                                                            </div>
                                                            <div class="col-lg-7">
                                                                <h6>+{{ application.contact_person_phone }}</h6>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="application.status === 2" class="nk-divider divider md"></div>

                                        <div v-if="application.deo !== null && application.deo_reject_reason !== null" class="nk-block">
                                            <div class="nk-block-head nk-block-head-sm nk-block-between">
                                                <h5 class="title">{{ application.deo.full_name }} | {{ deoTitle }} {{ application.district.name }}</h5>
                                            </div><!-- .nk-block-head -->
                                            <div class="bq-note">
                                                <div class="bq-note-item">
                                                    <div class="bq-note-text">
                                                        <p>{{ application.deo_reject_reason }}</p>
                                                    </div>
                                                </div><!-- .bq-note-item -->
                                            </div><!-- .bq-note -->
                                        </div>

                                        <div v-if="application.moes_desk_officer !== null && application.moes_desk_officer_reject_reason !== null" class="nk-block">
                                            <div class="nk-block-head nk-block-head-sm nk-block-between">
                                                <h5 class="title">{{ application.moes_desk_officer.full_name }} | Desk Officer - EMIS Number Applications</h5>
                                            </div><!-- .nk-block-head -->
                                            <div class="bq-note">
                                                <div class="bq-note-item">
                                                    <div class="bq-note-text">
                                                        <p>{{ application.moes_desk_officer_reject_reason }}</p>
                                                    </div>
                                                </div><!-- .bq-note-item -->
                                            </div><!-- .bq-note -->
                                        </div>

                                        <div v-if="application.moes_staff !== null && application.moes_staff_reject_reason !== null" class="nk-block">
                                            <div class="nk-block-head nk-block-head-sm nk-block-between">
                                                <h5 class="title">{{ application.moes_staff.full_name }} | MOES Staff</h5>
                                            </div><!-- .nk-block-head -->
                                            <div class="bq-note">
                                                <div class="bq-note-item">
                                                    <div class="bq-note-text">
                                                        <p>{{ application.moes_staff_reject_reason }}</p>
                                                    </div>
                                                </div><!-- .bq-note-item -->
                                            </div><!-- .bq-note -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                    </div><!-- .kyc-app -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import moment from "moment";

export default {
    name: "EmisNumberStatus",
    props: ['applicationObj'],
    mounted() {
        this.application = this.applicationObj
    },
    data: function () {
        return {
            application: {
                reference_number: '',
                school_name: '',
                emis_number: '',
                phone: '',
                email: '',
                masked_contact_person_nin: '',
                contact_person_phone: '',
                contact_person_email: '',
                year_founded: '',
                status: 0,
                deo_date_approved: null,
                has_male_students: false,
                has_female_students: false,
                admits_day_scholars_yn: false,
                admits_boarders_yn: false,
                institution_level: {name: '',display_name: ''},
                diploma_awarding_school_type: {name: ''},
                certificate_awarding_school_type: {name: ''},
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                local_government: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                school_ownership_status: {name: ''},
                founding_body: {name: ''},
                contact_person: {
                    gender: 'M'
                },
                gender: 'M',
                deo: {
                    full_name: '',
                    photo_url: '',
                },
                deo_reject_reason: '',
                moes_staff: {
                    full_name: '',
                    photo_url: '',
                },
                moes_staff_reject_reason: '',
                moes_staff_date_approved: null,
                moes_desk_officer: {
                    full_name: '',
                    photo_url: '',
                },
                moes_desk_officer_reject_reason: '',
                moes_desk_officer_date_approved: null,
            },
        }
    },
    methods: {
        formatDate: function (application) {
            if (application.date_submitted === null) {
                return moment(application.date_updated).format("D MMMM, YYYY hh:mma");
            } else {
                return moment(application.date_submitted).format("D MMMM, YYYY hh:mma");
            }
        },
        downloadLetter: function () {
            axios({
                method: 'get',
                url: '/emis-number-certificates/'+this.application.emis_number.toLowerCase()+'-emis-number-certificate.pdf?x='+moment().unix(),
                responseType: 'arraybuffer'
            })
                .then(response=>{
                    let blob = new Blob([response.data], { type: 'application/pdf' });
                    let a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(blob, {
                        type: 'data:application/pdf'
                    })
                    a.download = this.application.emis_number.toLowerCase()+'-emis-number-certificate.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                    document.body.removeChild(a);
                })
                .catch(error=>{
                    // this.renderError(error)
                });
        },
    },
    computed: {
        deoTitle: function () {
            if (this.application.local_government === null) {
                return null
            }

            if (this.application.local_government.name.toUpperCase().endsWith(" CITY")) {
                return "CEO";
            }

            if (this.application.local_government.name.toUpperCase().endsWith(" MUNICIPALITY")) {
                return "MEO";
            }

            return "DEO";
        },
        applicationStatus: function () {
            if (this.application.status === 2) {
                return 'Rejected';
            }

            if (this.application.status === 3) {
                return 'Endorsed';
            }

            if (this.application.status === 1) {
                return 'Approved';
            }

            return 'Pending';
        }
    }
}
</script>

<style scoped>
</style>
