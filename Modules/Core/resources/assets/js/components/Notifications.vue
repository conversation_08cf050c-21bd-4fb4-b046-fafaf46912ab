<template>
    <div>
        <div v-for="(message, index) in messages" :class="[message.status === 'error' ? 'alert-danger':'alert-dark-teal','mb-4 pl-3 alert alert-icon alert-dismissible d-flex']">
            <div v-if="message.status === 'error'" class="preview-icon-wrap p-0 d-inline mr-2 my-auto">
                <em class="ni ni-alert-fill"></em>
            </div>
            <div v-else class="preview-icon-wrap p-0 d-inline mr-2 my-auto">
                <em class="ni ni-check-circle-fill"></em>
            </div>
            <span class="my-auto">
                <strong>{{ message.title }}</strong>
                <span v-html="message.message"></span>
            </span>
            <button @click="clearMessages(index)" class="close" data-dismiss="alert"></button>
        </div>
    </div>
</template>

<script>
export default {
    name: "Notifications",
    mounted() {},
    data: function () {
        return {
            messages: [],
        }
    },
    methods: {
        clearMessages: function (index) {
            this.messages.splice(index,1);
        }
    },
    computed: {},
}
</script>

<style scoped>

</style>
