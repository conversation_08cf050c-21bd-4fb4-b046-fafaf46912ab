<template>
    <div>
        <div class="modal fade zoom" tabindex="-1" id="changePasswordModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Change Password</h5>
                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>
                        <div class="example-alert">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> Password must contain upper case and lower case letter, special character and number.
                            </div>
                        </div>
                        <form @submit.prevent="updatePassword()">
                            <div class="form-group mt-3">
                                <label class="form-label" for="current_password">Current Password</label>
                                <div class="form-control-wrap">
                                    <a tabindex="-1" href="#" class="form-icon form-icon-right passcode-switch" @click="switchVisibilityCurrentPassword">
                                        <em class="passcode-icon icon-show icon ni ni-eye"></em>
                                        <em class="passcode-icon icon-hide icon ni ni-eye-off"></em>
                                    </a>
                                    <input v-model.trim="user.current_password" :type="passwordFieldTypeCurrent" placeholder="Enter Current Password" class="form-control" id="current_password" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="password">New Password</label>
                                <div class="form-control-wrap">
                                    <a tabindex="-1" href="#" class="form-icon form-icon-right passcode-switch" @click="switchVisibilityNewPassword">
                                        <em class="passcode-icon icon-show icon ni ni-eye"></em>
                                        <em class="passcode-icon icon-hide icon ni ni-eye-off"></em>
                                    </a>
                                    <input v-model.trim="user.password" :type="passwordFieldTypeNew" placeholder="Enter Your New Password" class="form-control" id="password" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="password_confirmation">Confirm New Password</label>
                                <div class="form-control-wrap">
                                    <a tabindex="-1" href="#" class="form-icon form-icon-right passcode-switch" @click="switchVisibilityConfirmPassword">
                                        <em class="passcode-icon icon-show icon ni ni-eye"></em>
                                        <em class="passcode-icon icon-hide icon ni ni-eye-off"></em>
                                    </a>
                                    <input v-model.trim="user.password_confirmation" :type="passwordFieldTypeConfirm" placeholder="Re-Enter Your New Password" class="form-control" id="password_confirmation" required>
                                </div>
                            </div>
                            <div class="form-group text-primary">
                                <button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status_id" aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Saving...</span>
                                    <span v-if="loading" class="sr-only">Loading...</span>
                                    <span v-if="!loading" class="align-self-center">Save Password</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Notifications from "./Notifications.vue";
export default {
    name: "ChangePassword",
    props: ['temporaryPassword'],
    components: {
        Notifications
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            updated: false,
            user: {
                current_password: '',
                password: '',
                password_confirmation: ''
            },
            passwordFieldTypeCurrent: "password",
            passwordFieldTypeNew: "password",
            passwordFieldTypeConfirm: "password",
        }
    },
    methods: {
        initPlugins: function () {
            if (Number(this.temporaryPassword) === 1) {
                $('#changePasswordModal').modal({backdrop:"static"});
            }
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        switchVisibilityCurrentPassword() {
            this.passwordFieldTypeCurrent = this.passwordFieldTypeCurrent === "password" ? "text" : "password";
        },
        switchVisibilityNewPassword() {
            this.passwordFieldTypeNew = this.passwordFieldTypeNew === "password" ? "text" : "password";
        },
        switchVisibilityConfirmPassword() {
            this.passwordFieldTypeConfirm = this.passwordFieldTypeConfirm === "password" ? "text" : "password";
        },
        updatePassword: function () {
            let self = this;
            if (this.user.password.length === 0 || this.user.password_confirmation.length === 0) {
                self.$refs.notify.messages.push({status: 'error', title: 'Error:', message:"Type and repeat your new password to proceed"});
            } else if (this.user.password !== this.user.password_confirmation) {
                self.$refs.notify.messages.push({status: 'error', title: 'Error:', message:"Your new passwords do not match"});
            } else if (this.user.password.length < 6) {
                self.$refs.notify.messages.push({status: 'error', title: 'Error:', message:"Password must be at least 6 characters long"});
            } else if (/^(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&])[A-Za-z\d@#$!%*?&]{6,}$/g.test(this.user.password) === false) {
                self.$refs.notify.messages.push({status: 'error', title: 'Error:', message:"Your password must include at least 1 capital letter, 1 number and 1 special character"});
            } else {
                this.loading = true;
                axios.post('/change-my-password', this.user)
                    .then(response=>{
                        this.loading = true;
                        this.updated = true;
                        self.$refs.notify.messages.push({status: 'success', title: 'Success:', message:response.data});
                        document.location.reload(true);
                    })
                    .catch(error=>{
                        this.loading = false;
                        self.renderError(error)
                    })
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
