    <template>
        <div class="nk-block">
            <error-notifications ref="notifyError"></error-notifications>
            <success-notifications ref="notify"></success-notifications>
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-aside-wrap">
                    <div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg"
                        data-content="userAside" data-toggle-screen="lg" data-toggle-overlay="true">
                        <div class="card-inner-group" data-simplebar>
                            <div class="card-inner">
                                <div class="user-card">
                                    <div class="user-avatar w-100px" style="height: 1%">
                                        <img :src="school.logo_url" :alt="school.name + ' Logo'">
                                    </div>
                                    <div class="user-info">
                                        <span class="lead-text">{{ school.name }}</span>
                                        <span class="sub-text">{{ school.email }}</span>
                                    </div>
                                    <div class="user-action">
                                        <a data-toggle="modal" data-target="#updateLogoModal" data-backdrop="static"
                                            class="btn btn-round btn-icon btn-sm bg-dark-teal">
                                            <em class="icon ni ni-camera-fill"></em>
                                        </a>
                                    </div>
                                </div><!-- .user-card -->
                            </div><!-- .card-inner -->
                            <div class="card-inner p-0">
                                <ul class="link-list-menu nav nav-tabs">
                                    <li>
                                        <a data-toggle="tab" href="#basicInfo" class="active">
                                            <em class="icon ni ni-building-fill"></em><span>Institution
                                                Identification</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a data-toggle="tab" href="#ownership">
                                            <em class="icon ni ni-downward-alt-fill"></em><span>Ownership</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a data-toggle="tab" href="#locationDetails">
                                            <em class="icon ni ni-location"></em><span>Location Details</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a data-toggle="tab" href="#operationDetails">
                                            <em class="icon ni ni-opt-alt-fill"></em><span>Operational Details</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a data-toggle="tab" href="#healthInfo">
                                            <em class="icon ni ni-activity-round-fill"></em><span>Health
                                                information</span>
                                        </a>
                                    </li>
                                </ul>
                            </div><!-- .card-inner -->
                        </div><!-- .card-inner-group -->
                    </div><!-- card-aside -->
                    <div class="card-inner card-inner-lg">
                        <div class="tab-content">
                            <div class="tab-pane active" id="basicInfo">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">Institution Identification</h4>
                                            <div class="nk-block-des">
                                                <p>Basic school information.</p>
                                            </div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                                data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div><!-- .nk-block-head -->
                                <div class="nk-block">
                                    <div class="nk-data data-list">
                                        <!-- Display Form with Update Button -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Name Of Institution</span>
                                                <div class="d-flex align-items-center flex-wrap">
                                                    <div class="d-flex align-items-center mr-3">
                                                        <span class="data-value ">
                                                            {{ school.name }}
                                                            <span v-if="hasPendingNameChange"
                                                                class="badge badge-gray text-white">
                                                                Under Review

                                                            </span>
                                                        </span>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <button v-if="!hasPendingNameChange"
                                                            @click="showUpdateNameForm = true" type="button"
                                                            class="btn btn-sm btn-primary">
                                                            <em class="ni ni-edit-fill text-white mr-1"></em>Update
                                                        </button>
                                                        <span v-else class="text-muted small">

                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div>

                                        <update-school-name v-else :school="school" @success="handleNameUpdateSuccess"
                                            @error="handleNameUpdateError" @cancel="showUpdateNameForm = false"
                                            @request-submitted="handleRequestSubmitted" />

                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">EMIS Number</span>
                                                <span class="data-value text-dark">{{ school.emis_number }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div v-show="school.school_ownership_status_id === '2'" class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">License Number</span>
                                                <span v-if="school.licence_number === null"
                                                    class="data-value text-muted font-italic">Not Yet Licensed</span>
                                                <span v-else class="data-value text-dark">{{ school.licence_number
                                                    }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div v-show="school.school_ownership_status_id === '2'" class="data-item py-1"
                                            v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Registration Status</span>
                                                <span v-if="school.registration_status === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{
                                                    school.registration_status.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div v-show="school.school_ownership_status_id === '2'" class="data-item py-1"
                                            v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Registration Number</span>
                                                <span v-if="school.registration_number === null"
                                                    class="data-value text-muted font-italic">Not Yet Registered</span>
                                                <span v-else class="data-value text-dark">{{ school.registration_number
                                                    }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->

                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Postal Address</span>
                                                <span
                                                    v-if="school.postal_address === null || school.postal_address === undefined"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark ucap">{{
                                                    school.postal_address.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Institution Email Address</span>
                                                <span v-if="school.email === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.email }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Institution Telephone Contact</span>
                                                <span v-if="school.phone === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">+{{ school.phone }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Website</span>
                                                <span v-if="school.website === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.website }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm">
                                            <div class="data-col">
                                                <span class="data-label">Land Area</span>
                                                <span v-if="school.school_land_area === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark ucap">{{
                                                    school.school_land_area }} Acres</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div>

                                        <!-- Dowclass="data-item py-1"nload Letter Button (after phone contact) -->
                                        <div class="data-item py-1" v-if="!showUpdateNameForm && !hasPendingNameChange">
                                            <div class="data-col">
                                                <button @click="downloadLetter()" :disabled="loading" type="submit"
                                                    class="btn btn-xs btn-dark-teal d-flex">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm"
                                                        role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Downloading...</span>
                                                    <span v-if="loading" class="sr-only">Downloading...</span>
                                                    <span v-if="!loading" class="align-self-center">
                                                        <em class="icon ni ni-download text-white mr-1"></em>Download
                                                        Letter
                                                    </span>
                                                </button>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div>

                                        <!-- Name Change History Section -->
                                        <div class="mt-5">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Name Amendment History</h5>
                                            </div>
                                            <!-- Add the NameChangeRequestsTable component -->
                                            <name-change-requests-table v-if="school.id" ref="nameChangeRequestsTable"
                                                :school-id="school.id" :refresh-trigger="refreshTrigger" />
                                        </div>
                                    </div><!-- data-list -->
                                </div>
                                <!-- .nk-block -->
                            </div><!-- .tab-pane -->
                            <div class="tab-pane" id="licenseStatus">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">License Status</h4>
                                            <div class="nk-block-des">
                                                <p>Institution License Status.</p>
                                            </div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                                data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div><!-- .nk-block-head -->
                            </div><!-- .tab-pane -->
                            <div class="tab-pane" id="ownership">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">Ownership</h4>
                                            <div class="nk-block-des">
                                                <p>Institution ownership information.</p>
                                            </div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                                data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div><!-- .nk-block-head -->
                                <div class="nk-block">
                                    <div class="nk-data data-list">

                                        <!-- Only show label when not updating -->
                                        <div class="data-item py-1" v-if="!showUpdateOwnershipForm">
                                            <div class="data-col">
                                                <span class="data-label">Institution Type</span>
                                                <span v-if="school.certificate_school.school_type === null"
                                                    class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{
                                                    school.certificate_school.school_type.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div>

                                        <div class="data-item py-1" v-if="!showUpdateOwnershipForm">
                                            <div class="data-col">
                                                <span class="data-label">Ownership Status</span>
                                                <div class="d-flex align-items-center flex-wrap">
                                                    <div class="d-flex align-items-center mr-3">
                                                        <span class="data-value">
                                                            <span v-if="school.ownership_status === null"
                                                                class="data-value text-muted font-italic">Not Set</span>
                                                            <span v-else class="data-value text-dark">{{
                                                                school.ownership_status.name.toUpperCase() }}</span>

                                                            <span v-if="hasPendingOwnershipChange"
                                                                class="badge badge-gray text-white ml-2">
                                                                Under Review
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div>
                                        <!-- Update form -->
                                        <update-school-ownership v-if="showUpdateOwnershipForm" :school="school"
                                            @success="handleOwnershipUpdateSuccess" @error="handleOwnershipUpdateError"
                                            @request-submitted="handleOwnershipRequestSubmitted"
                                            @cancel="showUpdateOwnershipForm = false" />

                                        <div class="data-col w-15 data-col-end"></div>
                                        <!-- data-item -->
                                        <!-- Only show these items when not updating -->
                                        <template v-if="!showUpdateOwnershipForm">
                                            <div class="data-item py-1">
                                                <div class="data-col">
                                                    <span class="data-label">Founder</span>
                                                    <span v-if="school.founding_body === null"
                                                        class="data-value text-muted font-italic">Not Set</span>
                                                    <span v-else class="data-value text-dark">{{
                                                        school.founding_body.name.toUpperCase() }}</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->
                                            <div class="data-item py-1">
                                                <div class="data-col">
                                                    <span class="data-label">Main Funding Source</span>
                                                    <span v-if="school.funding_source === null"
                                                        class="data-value text-muted font-italic">Not Set</span>
                                                    <span v-else class="data-value text-dark">{{
                                                        school.funding_source.name.toUpperCase() }}</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->
                                            <div class="data-item py-1">
                                                <div class="data-col">
                                                    <span class="data-label">Institution Owner</span>
                                                    <span v-if="school.founding_body === null"
                                                        class="data-value text-muted font-italic">Not Set</span>
                                                    <span v-else class="data-value text-dark">{{
                                                        school.founding_body.name.toUpperCase() }}</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->
                                            <div class="data-item py-1">
                                                <div class="data-col">
                                                    <span class="data-label">Legal Ownership Status</span>
                                                    <span class="data-value text-dark ucap"
                                                        v-if="school.school_ownership_status_id === 1">GOVERNMENT</span>
                                                    <span class="data-value text-dark ucap"
                                                        v-else-if="school.legal_ownership_status !== null && school.school_ownership_status_id === 2">{{
                                                            school.legal_ownership_status.name.toUpperCase() }}</span>
                                                    <span v-else class="text-muted font-italic">Not Set</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->
                                            <div class="data-item py-1"
                                                v-if="school.certificate_school.certificate_awarding_school_type_id !== null">
                                                <div class="data-col">
                                                    <span class="data-label">Registering Authority</span>
                                                    <span
                                                        v-if="school.certificate_school.certificate_awarding_school_type_id === null"
                                                        class="data-value text-muted font-italic">Not Set</span>
                                                    <span v-else class="data-value text-dark ucap">{{
                                                        school.certificate_school.school_type.institution_category.registering_body.name
                                                        }}</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->
                                            <div class="data-item py-1">
                                                <div class="data-col">
                                                    <span class="data-label">Year Institution Founded</span>
                                                    <span v-if="school.year_founded === null"
                                                        class="data-value text-muted font-italic">Not Set</span>
                                                    <span v-else class="data-value text-dark">{{ school.year_founded
                                                        }}</span>
                                                </div>
                                                <div class="data-col w-15 data-col-end"></div>
                                            </div><!-- data-item -->



                                        </template>
                                    </div><!-- data-list -->
                                </div> <!-- Ownership Change History Section -->
                                
                            </div><!-- .tab-pane -->
                            <div class="tab-pane" id="locationDetails">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">GPS Details</h4>
                                            <div class="nk-block-des">
                                                <p>Institution GPS Details.</p>
                                            </div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div><!-- .nk-block-head -->
                                <div class="nk-block">
                                    <div class="nk-data data-list">
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">District</span>
                                                <span v-if="school.district === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.district.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">County/Municipality</span>
                                                <span v-if="school.county === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.county.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Sub-County/Division</span>
                                                <span v-if="school.sub_county === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.sub_county.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Parish/Ward</span>
                                                <span v-if="school.parish === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.parish.name.toUpperCase() }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Physical Address</span>
                                                <span v-if="school.physical_address === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.physical_address }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Latitude</span>
                                                <span v-if="school.latitude === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.latitude }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Longitude</span>
                                                <span v-if="school.longitude === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.longitude }}</span>
                                            </div>
                                            <div class="data-col data-col-end"></div>
                                        </div><!-- data-item -->
                                    </div>
                                    <div v-if="googleMapsApi.length" class="card-inner">
                                        <div id="map"></div>
                                    </div>
                                </div>
                            </div><!-- .tab-pane -->
                            <!-- <div class="tab-pane" id="contactDetails"></div> --><!-- .tab-pane -->
                            <div class="tab-pane" id="operationDetails">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">Operational Details</h4>
                                            <div class="nk-block-des">
                                                <p>Institution operational information.</p>
                                            </div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                                data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div><!-- .nk-block-head -->
                                <div class="nk-block">
                                    <div class="nk-data data-list">
                                        <div class="data-item py-1" v-if="school.school_ownership_status_id === 2">
                                            <div class="data-col">
                                                <span class="data-label">Registration Status</span>
                                                <span class="data-value text-dark" v-if="school.registration_status_id === 1">REGISTERED</span>
                                                <span class="data-value text-dark" v-if="school.registration_status_id === 2">LICENSED</span>
                                                <span class="data-value text-dark" v-if="school.registration_status_id === 0">NOT LICENSED</span>
                                                <span v-if="school.registration_status_id === null" class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1"
                                            v-if="school.registration_status_id === 1 && school.school_ownership_status_id === 2">
                                            <div class="data-col">
                                                <span class="data-label">Registration Number</span>
                                                <span class="data-value text-dark" v-if="school.registration_number !== null">{{ school.registration_number }}</span>
                                                <span v-else class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1"
                                            v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                                            <div class="data-col">
                                                <span class="data-label">License Number</span>
                                                <span class="data-value text-dark" v-if="school.license_number !== null">{{ school.license_number }}</span>
                                                <span v-else class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1"
                                            v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                                            <div class="data-col">
                                                <span class="data-label">License No. Expiry Date</span>
                                                <span class="data-value text-dark" v-if="school.license_number !== null">{{ formatDate(school.licence_certificate_expiry_date) }}</span>
                                                <span v-else class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->

                                        <div v-show="school.school_ownership_status_id === 1" class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Supply Number</span>
                                                <span v-if="school.certificate_school.supply_number === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">{{ school.certificate_school.supply_number }}</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Center Number</span>
                                                <span v-if="school.center_number === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark ucap">{{ school.center_number }}</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Sex Composition</span>
                                                <span class="data-value text-dark ucap" v-if="school.has_male_students && school.has_female_students">Mixed</span>
                                                <span class="data-value text-dark ucap" v-if="school.has_male_students && !school.has_female_students">Males Only</span>
                                                <span class="data-value text-dark ucap" v-if="!school.has_male_students && school.has_female_students">Females Only</span>
                                                <span v-if="!school.has_male_students && !school.has_female_students" class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Residential or Non-Residential</span>
                                                <span class="data-value text-dark ucap" v-if="school.certificate_school.admits_day_scholars_yn && school.certificate_school.admits_boarders_yn">RESIDENTIAL & NON-RESIDENTIAL</span>
                                                <span class="data-value text-dark ucap" v-if="!school.certificate_school.admits_day_scholars_yn && school.certificate_school.admits_boarders_yn">RESIDENTIAL ONLY</span>
                                                <span class="data-value text-dark ucap" v-if="school.certificate_school.admits_day_scholars_yn && !school.certificate_school.admits_boarders_yn">NON RESIDENTIAL ONLY</span>
                                                <span v-if="!school.certificate_school.admits_day_scholars_yn && !school.certificate_school.admits_boarders_yn" class="data-value text-muted font-italic">Not Set</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1">
                                            <div class="data-col">
                                                <span class="data-label">Highest Award</span>
                                                <span class="data-value text-dark ucap">CERTIFICATE</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div><!-- data-item -->
                                        <div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
                                            <div class="data-col">
                                                <span class="data-label">Capital For Establishment</span>
                                                <span v-if="school.capital_for_establishment === null" class="data-value text-muted font-italic">Not Set</span>
                                                <span v-else class="data-value text-dark">UGX {{ formatMoney(school.capital_for_establishment) }}</span>
                                            </div>
                                            <div class="data-col w-15 data-col-end"></div>
                                        </div>
                                        <!-- data-item -->

                                    </div><!-- data-list -->
                                </div><!-- .nk-block -->
                            </div><!-- .tab-pane -->

                            <!-- health-info -->
                            <div class="tab-pane" id="healthInfo">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content d-flex justify-content-between w-100">
                                        <div class="nk-block-head-content">
                                            <h4 class="nk-block-title">Health Information</h4>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none">
                                            <a class="toggle cursor btn btn-icon btn-trigger mt-n1"
                                                data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                        </div>
                                    </div>
                                </div>
                                <!-- sexuality education policy -->
                                <div class="nk-data data-list">
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Does this institution have/own a Health Facility within?</span>
                                            <span v-if="school.health_facility_distance !== null" class="pl-lg-5 ml-lg-5 data-value text-dark">Yes</span>
                                            <span v-else class="pl-lg-5 ml-lg-5 data-value text-dark">No</span>
                                        </div>
                                        
                                    </div><!-- data-item -->
                                    <div class="data-item py-1" v-show="school.health_facility_distance !== null">
                                        <div class="data-col">
                                            <span class="data-label">Distance to the nearest Health facility</span>
                                            <span v-if="school.health_facility_distance === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark ucap">{{ school.health_facility_distance.name }}</span>
                                        </div>
                                        
                                    </div><!-- data-item -->
                                </div>

                                
                            </div>

                        </div><!-- .tab-content -->
                    </div>
                </div><!-- .card-aside-wrap -->
            </div><!-- .card -->

            <!-- Logo Update Modal -->
            <div class="modal fade zoom" tabindex="-1" id="updateLogoModal">
                <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                    <form @submit.prevent="updateLogo()">
                        <div class="modal-content">
                            <a @click="resetLogo()" class="cursor close" data-dismiss="modal" aria-label="Close">
                                <em class="icon ni ni-cross"></em>
                            </a>
                            <div class="modal-header">
                                <h5 class="modal-title">Update Logo</h5>
                            </div>
                            <div class="modal-body">
                                <div class="row g-4">
                                    <div class="col-12">
                                        <div class="align-self-center form-group">
                                            <input id="schoolLogo" ref="logo" @change="selectFile"
                                                accept="image/png,image/jpeg" data-max-file-size="2M" type="file"
                                                class="dropify" data-height="180"
                                                data-allowed-file-extensions="jpeg jpg png"
                                                data-default-file="/images/school-logos/logo.png" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <span @click="school.logo === '' ? seletLogo() : resetLogo()"
                                            class="btn btn-block bg-dark-teal">
                                            <em class="icon ni ni-camera-fill mr-1"></em>
                                            <span v-if="school.logo === ''">Select Logo</span>
                                            <span v-else>Remove Logo</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer d-flex justify-content-center">
                                <button @click="resetLogo()" :disabled="loading" type="button" data-dismiss="modal"
                                    class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status"
                                        aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Updating...</span>
                                    <span v-if="loading" class="sr-only">Updating...</span>
                                    <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading"
                                        class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- Logo Update Modal -->

            <!-- Loading Modal -->
            <div style="display:none;" id="schoolLoadingMessage" class="card card-preview">
                <div class="card-inner">
                    <div class="d-flex align-items-center">
                        <strong>Deleting...</strong>
                        <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
                    </div>
                </div>
            </div>
            <!-- /Loading Modal -->

        </div><!-- .nk-block -->
    </template>

<script>
import { Loader } from "@googlemaps/js-api-loader";
import ErrorNotifications from "../Notifications.vue";
import SuccessNotifications from "../Notifications.vue";
import UpdateSchoolName from './UpdateSchoolName.vue';
import UpdateSchoolOwnership from './UpdateSchoolOwnership.vue';
import NameChangeRequestsTable from './NameChangeRequestsTable.vue';
import OwnershipChangeRequestsTable from './OwnershipChangeRequestsTable.vue';

export default {
    name: "CertificateSchoolProfile",
    props: [
        'schoolObj',
        'googleMapsApi',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
        UpdateSchoolName,
        UpdateSchoolOwnership,
        NameChangeRequestsTable,
        OwnershipChangeRequestsTable,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            apiUrl: '/institutions/certificate-schools',
            loading: false,
            edit: false,
            valid_file: false,
            logoDropify: null,
            map: null,
            marker: null,
            school: {
                logo: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
                examining_body_id: '',
                registration_status_id: '',
                registration_body_id: '',
                award_type_id: '',
                school_ownership_status_id: '',
                tertiary_institution_type_id: '',
                certificate_school: {
                    certificate_awarding_school_type_id: null,
                    authority: { name: '' },
                    school_type: {
                        name: '',
                        institution_category: {
                            name: '',
                            examining_body: { acronym: '' },
                            registering_body: {
                                name: ''
                            }
                        },
                    },
                    registering_body_id: '',
                    supply_number: '',
                    admits_day_scholars_yn: '',
                    admits_boarders_yn: '',
                    school_boarding_status: '',
                },
                district: {
                    name: '',
                },
                parish: {
                    name: '',
                },
                sub_county: {
                    name: '',
                },
                county: {
                    name: '',
                },
                founding_body: {
                    name: '',
                },
                funding_source: {
                    name: '',
                },
                examining_bodies: [],
                examining_body: {
                    name: '',
                },
                ownership_status: {
                    name: '',
                },
                institution_type: {
                    name: '',
                },
                land_owner_type: {
                    name: '',
                },
                legal_ownership_status: {
                    name: '',
                },
                authority: {
                    name: '',
                },
                operational_status: {
                    name: '',
                },
                registration_status: {
                    name: '',
                },
                health_facility_distance: {
                    name: '',
                },
                registration_body: {
                    name: '',
                },
                head_teacher: {
                    teacher: {
                        full_name: '',
                        contacts: [{ contact: '' }],
                    },
                },
            },
            showUpdateNameForm: false,
            updateNameForm: {
                schoolName: '',
                reason: '',
                description: '',
            },
            changeReasons: [], // Array to hold the reasons
            showUpdateOwnershipForm: false,
            nameChangeRequests: [], // Add this to store the requests
            ownershipChangeRequests: [], // Initialize as an empty array
            refreshTrigger: 0,
            ownershipRefreshCounter: 0
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;

            this.logoDropify = $('#schoolLogo').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Ooops, something wrong appended.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max).'
                }
            });

            this.logoDropify.on('dropify.afterClear', function (event, element) {
                self.school.logo = '';
                self.$refs.logo.value = null;
            });

            $('.dropify-clear').click(() => {
                this.resetLogo();
            });
            this.resetLogo();
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        formatMoney: function (raw_money = '') {
            if (typeof raw_money === "string" && raw_money.length) {
                return moneyFormat.to(moneyFormat.from(raw_money.replace(/[^0-9]/g, "")));
            } else if (typeof raw_money === "number") {
                return moneyFormat.to(moneyFormat.from(raw_money.toString().replace(/[^0-9]/g, "")));
            }
            return null
        },
        selectFile() {
            this.school.logo = this.$refs.logo.files[0];
        },
        seletLogo() {
            $('.dropify').click();
        },
        updateLogo: function () {
            let formData = new FormData();
            this.loading = true;
            formData.append('logo', this.school.logo);
            axios.post(this.apiUrl + '/update-logo', formData, { headers: { 'Content-Type': 'multipart/form-data' } })
                .then(response => {
                    this.school.logo_url = response.data.logo_url;
                    this.school.logo = response.data.logo;
                    $('#updateLogoModal').modal('hide');
                    this.resetLogo();
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:" Logo updated successfully"});
                })
                .catch(error => {
                    console.log(error)
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetLogo: function () {
            let filedropper = this.logoDropify.data('dropify');
            filedropper.resetPreview();
            filedropper.clearElement();
            filedropper.settings['defaultFile'] = this.school.logo_url;
            filedropper.destroy();
            filedropper.init();
            this.loading = false;
        },
        downloadLetter: function () {
            this.loading = true;
            try {
                axios({
                    method: 'get',
                    url: '/institution/emis-number-certificates/' + this.school.emis_number,
                    responseType: 'arraybuffer'
                })
                    .then(response => {
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        console.log(blob);
                        let a = window.document.createElement('a');
                        a.href = window.URL.createObjectURL(blob, {
                            type: 'data:application/pdf'
                        })
                        a.download = this.school.emis_number + '-emis-number-certificate.pdf';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(a.href);
                        document.body.removeChild(a);
                    })
                    .catch(error => {
                        // Handle error
                        this.renderError(error)
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            } catch (error) {
                // Handle error
                this.renderError(error)
            }
        },
        startLoading: function () {
            $.blockUI({
                message: $('#schoolLoadingMessage'),
                css: {
                    padding: 0,
                    margin: 0,
                    width: '30%',
                    top: '40%',
                    left: '35%',
                    textAlign: 'center',
                    color: '#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor: 'wait'
                },
            });
        },
        submitNameUpdate: function () {
            let self = this;
            // Validate required fields
            if (!self.updateNameForm.schoolName || !self.updateNameForm.reason) {
                self.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Validation Error',
                    message: 'School name and reason for change are required.'
                });
                return;
            }

            Swal.fire({
                title: 'Are you sure?',
                html: '<div style="margin-bottom: 10px;">' +
                    'You are about to change the school name from <strong>' + self.school.name + '</strong> to <strong>' + self.updateNameForm.schoolName + '</strong>.</div>' +
                    '<div class="text-info">NOTE: Once approved by admin, this name change will be reflected in the system.</div>',
                icon: 'warning',
                confirmButtonText: 'Yes, Proceed',
                showCancelButton: true,
                cancelButtonColor: '#29384A',
                reverseButtons: true,
            }).then(function (result) {
                if (result.value) {
                    self.loading = true;
                    axios.post('/institutions/secondary-schools/update-name', {
                        new_name: self.updateNameForm.schoolName,
                        reason_id: self.updateNameForm.reason, // Send reason_id instead of reason text
                        description: self.updateNameForm.description || '',
                        old_value: self.school.name,
                        school_id: self.school.id  // Add the school_id from the school object
                    })
                        .then(response => {
                            self.loading = false;
                            self.showUpdateNameForm = false;

                            // Reset the form
                            self.updateNameForm.schoolName = '';
                            self.updateNameForm.reason = '';
                            self.updateNameForm.description = '';

                            Swal.fire({
                                title: 'Success',
                                html: '<div style="margin-bottom: 10px;">' +
                                    'The school name change request has been submitted successfully.</div>' +
                                    '<div>The new name will be reflected once approved by admin.</div>',
                                icon: 'success',
                                confirmButtonText: 'Close',
                                showCancelButton: false,
                            });
                        })
                        .catch(error => {
                            self.loading = false;
                            self.renderError(error);
                        });
                }
            });
        },
        cancelNameUpdate: function () {
            this.showUpdateNameForm = false;
            this.updateNameForm.schoolName = '';
            this.updateNameForm.reason = '';
            this.updateNameForm.description = '';
        },
        fetchChangeReasons() {
            axios.get('/institutions/change-reasons')
                .then(response => {
                    this.changeReasons = response.data;
                })
                .catch(error => {
                    console.error("There was an error fetching the change reasons:", error);
                });
        },
        handleNameUpdateSuccess(response) {
            this.showUpdateNameForm = false;
            // Switch to basicInfo tab
            $('a[href="#basicInfo"]').tab('show');

            Swal.fire({
                title: 'Success',
                html: '<div style="margin-bottom: 10px;">' +
                    'The school name change request has been submitted successfully.</div>' +
                    '<div>The new name will be reflected once approved by admin.</div>',
                icon: 'success',
                confirmButtonText: 'Close',
                showCancelButton: false,
            }).then(() => {
                // Refresh the change requests
                this.fetchNameChangeRequests();
            });
        },
        handleNameUpdateError(error) {
            this.renderError(error);
        },
        editOwnershipStatus() {
            // Implement the logic to edit ownership status
            console.log('Edit ownership status');
        },
        handleOwnershipUpdateSuccess(response) {
            // Hide the update form
            this.showUpdateOwnershipForm = false;

            // Refresh the school data if needed
            if (response.school) {
                this.school = response.school;
            }
        },
        handleOwnershipRequestSubmitted() {
            // Refresh the table
            this.ownershipRefreshCounter++;

            // Fetch latest requests to update badge status
            this.fetchOwnershipChangeRequests();
        },

        handleOwnershipUpdateError(error) {
            Swal.fire({
                title: 'Error',
                text: error.response?.data?.message || 'Error updating ownership status',
                icon: 'error',
                confirmButtonText: 'Ok'
            });
        },
        handleNameChangeRequestError(error) {
            console.error('Error in name change requests:', error);
            this.$refs.notify.error(error.response?.data?.message || 'Error fetching name change requests');
        },

        fetchOwnershipChangeRequests() {

            axios.get(`/institutions/${this.schoolObj.id}/change-requests-ownership`)
                .then(response => {
                    this.ownershipChangeRequests = response.data;
                })
                .catch(error => {
                    console.error('Error fetching ownership change requests:', error);
                });
        },
        fetchNameChangeRequests() {
            axios.get(`/institutions/${this.schoolObj.id}/change-requests`)
                .then(response => {
                    this.nameChangeRequests = response.data;
                })
                .catch(error => {
                    console.error('Error fetching name change requests:', error);

                });
        },
        handleRequestSubmitted() {
            // Increment the trigger to force a refresh
            this.refreshTrigger++;
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },

    created() {
        this.fetchChangeReasons();
        this.fetchNameChangeRequests(); // Add this to fetch requests on creation
        this.fetchOwnershipChangeRequests();
    },
    computed: {
        hasPendingNameChange() {
            return this.nameChangeRequests.some(request => request.approval_status === 'PENDING');
        },
        hasPendingOwnershipChange() {
            return this.ownershipChangeRequests.some(request => request.approval_status === 'PENDING');
        }
    },
    watch: {
        'school.id': {
            handler: 'fetchNameChangeRequests',
            immediate: true
        },
        'school.id': {
            handler: 'fetchOwnershipChangeRequests',
            immediate: true
        }
    }
}
</script>

<style scoped>
#map {
    height: 400px;
}
</style>
