<template>
    <div class="components-preview mx-auto">
        <div class="nk-block nk-block-lg">
            <div class="nk-block-head">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h4 class="title nk-block-title">Learning &amp; Playing Materials</h4>
                        <div class="nk-block-des">
                            <p>Information about your school's learning and playing materials</p>
                        </div>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div>
            <div class="nk-block">
                <div class="card card-inner card-inner-lg">
                    <div class="table-responsive">
                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                            <tr>
                                <th class="text-white align-middle text-uppercase w-45" rowspan="2">Learning Materials</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white py-2" :colspan="grades.length + 1">Class</th>
                            </tr>
                            <tr>
                                <th class="py-2 text-center text-white text-uppercase border-left border-white" v-for="grade in grades">{{ grade.name.toUpperCase() }}</th>
                            </tr>
                            </thead>
                            <tbody class="border-top-0 border-dark-teal" v-for="category in categories">
                                <tr>
                                    <th class="text-dark-teal py-2 border-bottom border-top border-dark-teal text-center" :colspan="grades.length + 2">{{ category.name.toUpperCase() }}</th>
                                </tr>
                                <tr v-for="material in category.materials">
                                    <td class="align-middle">{{ material.name.toUpperCase() }}</td>
                                    <td class="align-middle border-left text-center" v-for="(grade, index) in grades">
                                        <div v-if="editMaterial === material.id" class="form-group mx-auto">
                                            <div class="form-control-wrap">
                                                <input v-model.number="form_report[index].quantity" type="number" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+grade.id">
                                            </div>
                                        </div>
                                        <span v-else>{{ getMaterialQuantity(material.id, grade.id) }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "LearningAndPlayingMaterials",
    props: ['gradesObj', 'categoriesObj', 'reportObj'],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            report: [],
            grades: [],
            categories: [],
            editMaterial: '',
        }
    },
    methods: {
        initPlugins: function () {
            this.report = JSON.parse(this.reportObj);
            this.grades = JSON.parse(this.gradesObj);
            this.categories = JSON.parse(this.categoriesObj);
        },
        getMaterialQuantity: function (materialId, gradeId) {
            let qtyReport = this.report.find(materialReport=>{
                return materialReport.education_grade_id === gradeId && materialReport.learning_material_id === materialId;
            })

            if (qtyReport === undefined) {
                return 0;
            } else {
                return qtyReport.quantity;
            }
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
