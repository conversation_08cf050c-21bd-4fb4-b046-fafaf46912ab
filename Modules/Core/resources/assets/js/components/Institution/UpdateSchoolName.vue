<template>
    <div class="">
        <div class="">
            <!-- Update Form -->
            <form class="w-100" v-if="!showChangeRequests">
                <div v-if="hasPendingRequest" class="alert alert-warning mb-3">
                    <em class="icon ni ni-alert-circle"></em>
                    There is already a pending name amendment request. Please wait for it to be processed before submitting a new request.
                </div>

                <template v-if="!hasPendingRequest">
                    <div class="form-group row align-items-center">
                        <label for="newSchoolName" class="col-lg-4 col-form-label">New School Name</label>
                        <div class="col-lg-8">
                            <input v-model="form.schoolName"
                                   type="text"
                                   class="form-control"
                                   placeholder="Enter new school name"
                                   required>
                        </div>
                    </div>
                    <div class="form-group row align-items-center">
                        <label for="reason" class="col-lg-4 col-form-label">Reason for Amendment</label>
                        <div class="col-lg-8">
                            <div class="form-control-wrap">
                                <select id="reason_id" class="form-control" required>
                                    <option value="">--SELECT REASON--</option>
                                    <option v-for="reason in changeReasons" :value="reason.id">
                                        {{ reason.name.toUpperCase() }}
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row align-items-center">
                        <label for="description" class="col-lg-4 col-form-label">Description</label>
                        <div class="col-lg-8">
                            <textarea v-model="form.description"
                                      class="form-control"
                                      placeholder="Enter detailed description"
                                      rows="2"
                                      required></textarea>
                        </div>
                    </div>
                </template>

                <div class="mt-2 d-flex justify-content-between">

                    <div v-if="!hasPendingRequest">
                        <button @click="submitNameUpdate"
                                :disabled="loading"
                                type="button"
                                class="btn btn-primary btn-sm mr-2">
                            <em class="ni ni-save mr-1"></em>
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading">Submitting...</span>
                            <span v-if="!loading">Submit</span>
                        </button>
                        <button @click="cancel"
                                :disabled="loading"
                                type="button"
                                class="btn btn-light btn-sm">
                            <em class="ni ni-cross-circle mr-1"></em>
                            Cancel
                        </button>
                    </div>
                </div>
            </form>

            <!-- Change Requests List -->
            <div v-else>
                <div class="nk-block">
                    <!-- Back Button -->
                    <div class="mb-3 d-flex justify-content-end">
                        <button type="button"
                                class="btn btn-light btn-sm"
                                @click="showChangeRequests = false">
                            <em class="icon ni ni-arrow-left mr-1"></em>
                            Back to Form
                        </button>style="border-radius: 20px;"
                    </div>

                    <div class="card card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner p-0">
                                <div class="nk-tb-list nk-tb-ulist">
                                    <!-- Table Headers -->
                                    <div class="nk-tb-item nk-tb-head bg-secondary">
                                        <div class="nk-tb-col"><span class="sub-text text-white">OLD NAME</span></div>
                                        <div class="nk-tb-col"><span class="sub-text text-white">NEW NAME</span></div>
                                        <div class="nk-tb-col"><span class="sub-text text-white">REASON</span></div>
                                        <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span></div>
                                        <div class="nk-tb-col"><span class="sub-text text-white">DATE REQUESTED</span></div>
                                    </div>

                                    <!-- Table Rows -->
                                    <div v-for="request in changeRequests" :key="request.id" class="nk-tb-item">
                                        <div class="nk-tb-col">style="border-radius: 20px;"
                                            <span>{{ request.data_update?.old_value }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ request.data_update?.new_value }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ request.reason?.name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span :class="getStatusBadgeClass(request.approval_status)">
                                                {{ request.approval_status }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ formatDate(request.date_created) }}</span>
                                        </div>
                                    </div>

                                    <!-- No Data Message -->
                                    <div v-if="!changeRequests.length" class="nk-tb-item">
                                        <div class="nk-tb-col text-center" colspan="5">
                                            <em class="icon ni ni-alert-circle"></em> No amendment requests found
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'UpdateSchoolName',
    props: {
        school: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            showChangeRequests: false,
            form: {
                schoolName: '',
                reason: '',
                description: ''
            },
            changeReasons: [],
            changeRequests: [],
            showUpdateOwnershipForm: false
        }
    },
    computed: {
        hasPendingRequest() {
            return this.changeRequests.some(request => request.approval_status === 'PENDING');
        }
    },
    methods: {
        submitNameUpdate() {
            if (this.hasPendingRequest) {
                Swal.fire({
                    title: 'Error',
                    text: 'There is already a pending name amendment request.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }

            if (!this.form.schoolName) {
                Swal.fire({
                    title: 'Error',
                    text: 'School name  required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }

            if (!this.form.reason) {
                Swal.fire({
                    title: 'Error',
                    text: 'Reason for amendment is required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }
                 if (!this.form.description) {
                Swal.fire({
                    title: 'Error',
                    text: 'Description is required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }

            const self = this;
            Swal.fire({
                title: 'Are you sure?',
                html: `
                    <div style="margin-bottom: 10px;">
                        You are about to change the school name from
                        <strong>${this.school.name}</strong> to <strong>${this.form.schoolName}</strong>.
                    </div>
                    <div class="text-info">
                        NOTE: Once approved by admin, this name amendment will be reflected in the system.
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Proceed',
                reverseButtons: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    self.loading = true;
                    axios.post('/institutions/update-name', {
                        new_name: self.form.schoolName.toUpperCase(), // Capitalize the new name
                        reason_id: self.form.reason,
                        description: self.form.description || '',
                        old_value: self.school.name,
                        school_id: self.school.id
                    })
                    .then(response => {
                        self.loading = false;

                        // Emit success event to parent
                        self.$emit('success', response.data);
                        self.$emit('request-submitted');

                        // Reset form
                        self.resetForm();

                        // Fetch updated requests immediately
                        self.fetchChangeRequests();
                    })
                    .catch(error => {
                        self.loading = false;
                        self.$emit('error', error);
                    });
                }
            });
        },
        cancel() {
            this.resetForm();
            this.$emit('cancel');
        },
        resetForm() {
            this.form = {
                schoolName: '',
                reason: '',
                description: ''
            };
        },
        fetchChangeReasons() {
            axios.get('/institutions/change-reasons')
                .then(response => {
                    this.changeReasons = response.data;
                })
                .catch(error => {
                    this.$emit('error', error);
                });
        },
        fetchChangeRequests() {
            axios.get(`/institutions/${this.school.id}/change-requests`)
                .then(response => {
                    this.changeRequests = response.data;
                    // Emit the pending status to parent
                    this.$emit('pending-status-change', this.hasPendingRequest);
                })
                .catch(error => {
                    this.$emit('error', error);
                });
        },
        getStatusBadgeClass(status) {
            const classes = {
                'PENDING': 'badge badge-gray',
                'APPROVED': 'badge badge-dark-teal',
                'REJECTED': 'badge badge-danger'
            };
            return classes[status] || 'badge badge-secondary';
        },
        formatDate(date) {
            return moment(date).format('D MMMM, YYYY'); // Will output like "5 December, 2024"
        },
        handleOwnershipUpdateSuccess() {
            this.showUpdateOwnershipForm = false;
            // Optionally, refresh school data or show a success notification
            this.$refs.notify.success('Ownership status updated successfully');
        },
        handleOwnershipUpdateError(error) {
            // Handle error, show notification etc.
            this.$refs.notify.error(error.response?.data?.message || 'Error updating ownership status');
        },
        initPlugins() {
            let self = this;

            $('#reason_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form.reason = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
        }
    },
    created() {
        this.fetchChangeReasons();
        this.fetchChangeRequests();
    },
    watch: {
        showChangeRequests(newVal) {
            if (newVal) {
                this.fetchChangeRequests();
            }
        }
    },
    mounted() {
        this.initPlugins();
    }
}
</script>


