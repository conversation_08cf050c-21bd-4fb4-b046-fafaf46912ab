<template>
    <div>
        <div class="nk-block-head nk-block-head-lg">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h4 class="nk-block-title">School Management Committee</h4>
                    <div class="nk-block-des">
                        <p>You have a total of {{ smc_committee.length }} SMC members.</p>
                    </div>
                </div>
            </div>
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <notifications ref="notify"></notifications>
            <div v-if="smc_committee.length < 6" class="example-alert mb-3">
                <div class="alert alert-danger alert-icon">
                    <em class="icon ni ni-alert-circle"></em> Missing information!</div>
            </div>
            <div class="card card-stretch">
                <div class="card-inner-group">
                    <div class="card-inner position-relative border-bottom-0 card-tools-toggle">
<!--                        <div class="card-title-group">-->
                            <div class="card-tools row">
                                <div class="col-lg-12">
                                    <div class="row">
                                        <div class="col-lg-2">
                                            <div class="form-wrap">
                                                <select id="filter_gender" class="form-select-sm">
                                                    <option value="">SELECT GENDER</option>
                                                    <option value="M">MALE</option>
                                                    <option value="F">FEMALE</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-lg-2">
                                            <div class="form-wrap">
                                                <select id="filter_status" class="form-select-sm">
                                                    <option value="">SELECT STATUS</option>
                                                    <option value="active">ACTIVE</option>
                                                    <option value="inactive">INACTIVE</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-3">
                                            <div class="form-wrap">
                                                <select v-model="filter.country_id" id="filter_country_id" class="form-select-sm">
                                                    <option value="">SELECT COUNTRY</option>
                                                    <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-lg-5">
                                            <div class="form-wrap">
                                                <div class="input-group">
                                                    <input v-model.trim="filter.search_term" type="text" class="form-control text-uppercase" placeholder="Search By Name">
                                                    <div class="input-group-append">
                                                        <button :disabled="loading" @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                            <em class="icon ni ni-cross"></em>
                                                        </button>
                                                        <button :disabled="loading" @click.prevent="filterMembers()" class="btn rounded-right text-white bg-dark-teal" type="button">
                                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

<!--                              <span data-toggle="modal" data-target="#schoolSmcCommitteeMemberModal" data-backdrop="static" class="btn bg-dark-teal">-->
<!--                                <em class="icon ni ni-plus-circle-fill text-white"></em>-->
<!--                                <span class="">Add SMC Member</span>-->
<!--                              </span>-->
<!--                            </div>&lt;!&ndash; .card-tools &ndash;&gt;-->
                            <!--                            <div class="card-tools">-->
                            <!--                                <div class="form-inline flex-nowrap gx-3">-->
                            <!--                                    <span><a href="#" data-toggle="modal" data-target="#uploadSmcCommitteeMemberModal" data-backdrop="static" class="btn btn-white btn-outline-dark-teal"><em class="icon ni ni-upload-cloud"></em><span>Import</span></a></span>-->
                            <!--                                </div>&lt;!&ndash; .form-inline &ndash;&gt;-->
                                                        </div><!-- .card-tools -->
<!--                        </div>&lt;!&ndash; .card-title-group &ndash;&gt;-->
                    </div><!-- .card-inner -->
                    <div class="card-inner pt-0 pb-3">
                        <div v-if="loading" class="text-center">
                            <div class="py-5"></div>
                            <div class="py-3"></div>
                            <div class="spinner-border" style="width: 2rem; height: 2rem;" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <div class="py-3"></div>
                            <div class="py-5"></div>
                        </div>
                        <div v-if="!loading" class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleAllOwner()" v-model="select_all_smc_committee"  type="checkbox" class="custom-control-input" id="smc_committeeSelect">
                                        <label class="custom-control-label" for="smc_committeeSelect"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col"><span class="sub-text text-white">NAMES</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-white">GENDER</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">NATIONALITY</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">APPOINTMENT DATE</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span></div>
                                <div class="nk-tb-col"><span class="text-white">ACTIONS</span></div>
                            </div><!-- .nk-tb-item -->
                            <div v-for="member in smc_committee" class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleOneOwner()" v-model="selected_smc_committee" :value="member.id" type="checkbox" class="custom-control-input" :id="'smc_committeeSelect'+member.id">
                                        <label class="custom-control-label" :for="'smc_committeeSelect'+member.id"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col">
                                    <div @click="viewSchoolSmcCommitteeMember(member)" class="user-card cursor">
                                        <div class="user-avatar bg-primary">
                                            <span>{{ member.person.initials }}</span>
                                        </div>
                                        <div class="user-name text-uppercase">
                                            <span class="d-block text-dark-teal">{{ member.person.full_name }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-dark text-center">
                                    <span class="text-uppercase">{{ member.person.gender === 'M' ? 'Male' : 'Female' }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span class="text-dark">UGANDAN</span>
<!--                                    <span v-if="member.person.country_id === null" class="text-muted font-italic">Not Yet Set</span>-->
<!--                                    <span v-else class="text-dark">{{ member.person.country.name.toUpperCase() }}</span>-->
                                </div>
                                <div class="nk-tb-col">
                                    <span v-if="member.appointment_date === null" class="text-muted font-italic">Not Yet Set</span>
                                    <span v-else class="text-dark">{{ formatDate(member.appointment_date) }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span v-if="member.end_date === null" class="badge badge-dark-teal">Active</span>
                                    <span v-else class="badge badge-danger">Inactive</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                  <span @click="viewSchoolSmcCommitteeMember(member)" data-toggle="tooltip" data-placement="top" title="View Member" class="cursor lead text-dark-teal">
                                    <em class="icon ni ni-eye-fill"></em>
                                  </span>
                                    <span @click="editSchoolSmcCommitteeMember(member)" data-toggle="tooltip" data-placement="top" title="Edit Member" class="cursor lead text-primary">
                                    <em class="icon ni ni-edit-fill"></em>
                                  </span>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                        <div v-if="!smc_committee.length && !loading" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No members to display at the moment...
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
        <!-- SMC Member Modal -->
        <div class="modal fade zoom" tabindex="-1" id="schoolSmcCommitteeMemberModal">
            <form @submit.prevent="edit ? updateSchoolSmcCommitteeMember() : createSchoolSmcCommitteeMember()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetSchoolSmcCommitteeMember()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">{{ edit ? 'Edit ':'Add ' }} SMC Member</h5>
                        </div>
                        <div class="modal-body">
                            <notifications ref="notifyError"></notifications>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSmcCommitteeMemberFirstName">First Name</label>
                                        <span class="form-note">Specify the school SMC member's first name.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.first_name" id="schoolSmcCommitteeMemberFirstName" type="text" class="form-control bg-primary-dim" autocomplete="off" required placeholder="eg. Clinton" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSmcCommitteeMemberLastName">Surname</label>
                                        <span class="form-note">Specify the school SMC member's surname.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.surname" id="schoolSmcCommitteeMemberLastName" type="text" class="form-control bg-primary-dim" autocomplete="off" required placeholder="eg. Nkesiga" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSmcCommitteeMemberOtherNames">Other Names</label>
                                        <span class="form-note">Specify the school SMC member's other names.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.other_names" id="schoolSmcCommitteeMemberOtherNames" type="text" class="form-control bg-primary-dim" autocomplete="off" placeholder="eg. Bill" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSmcCommitteeMemberNIN">ID Number</label>
                                        <span class="form-note">Specify the school SMC member's ID Number.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.id_number" id="schoolSmcCommitteeMemberNIN" type="text" maxlength="14" minlength="14" class="text-uppercase form-control bg-primary-dim w-100" autocomplete="off" required placeholder="eg. CM3755****WLML" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="member_phone_1">Primary Contact</label>
                                        <span class="form-note">Specify the school SMC member's Primary Contact.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.phone_1" id="member_phone_1" type="text" class="form-control bg-primary-dim w-100" maxlength="10" autocomplete="off" required placeholder="eg. 7382838283">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label">Gender</label>
                                        <span class="form-note">Specify the school SMC member's gender.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <ul class="custom-control-group g-3 align-center flex-wrap">
                                                <li>
                                                    <div class="custom-control custom-radio">
                                                        <input v-model.number="form_member.gender" type="radio" class="custom-control-input" value="M" id="member_gender_male" disabled>
                                                        <label class="custom-control-label text-uppercase" for="member_gender_male">Male</label>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="custom-control custom-radio">
                                                        <input v-model.number="form_member.gender" type="radio" class="custom-control-input" value="F" id="member_gender_female" disabled>
                                                        <label class="custom-control-label text-uppercase" for="member_gender_female">Female</label>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="member_country_id">Nationality</label>
                                        <span class="form-note">Specify the school SMC member's nationality.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <select disabled v-model="form_member.country_id" id="member_country_id" class="">
                                                <option value="">--Select--</option>
                                                <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="member_appointment_date">Appointment Date</label>
                                        <span class="form-note">Specify the school SMC member's appointment date.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.appointment_date" id="member_appointment_date" type="text" class="form-control bg-primary-dim w-100" autocomplete="off" required placeholder="eg. ">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="member_end_date">End Date</label>
                                        <span class="form-note">Specify the school SMC member's end date.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <input v-model.trim="form_member.end_date" id="member_end_date" type="text" class="form-control bg-primary-dim w-100" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetSchoolSmcCommitteeMember()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Saving...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="viewSchoolSmcCommitteeMemberModal">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetSchoolSmcCommitteeMember()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">View SMC Member Details</h5>
                    </div>
                    <div class="modal-body">
                        <div class="nk-block">
                            <div class="nk-data data-list">
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">First Name</span>
                                        <span v-if="member.person.first_name === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">{{ member.person.first_name }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Surname</span>
                                        <span v-if="member.person.surname === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">{{ member.person.surname }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Other Names</span>
                                        <span v-if="member.person.other_names === null" class="data-value text-muted font-italic"></span>
                                        <span v-else class="data-value text-dark">{{ member.person.other_names }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">ID Number</span>
                                        <span v-if="member.person.id_number === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">{{ member.person.masked_id_number.toUpperCase() }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Primary Contact</span>
                                        <span v-if="member.person.phone_1 === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">+256{{ member.person.phone_1 }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Gender</span>
                                        <span v-if="member.person.gender === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark text-uppercase">{{ member.person.gender === 'M' ? 'Male' : 'Female' }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Nationality</span>
                                        <span v-if="member.person.country_id === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">{{ member.person.country.name.toUpperCase() }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Appointment Date</span>
                                        <span v-if="member.appointment_date === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark">{{ formatDate(member.appointment_date) }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">End Appointment</span>
                                        <span v-if="member.end_date === null || member.end_date === ''" class="data-value text-muted font-italic">Still A Member</span>
                                        <span v-else class="data-value text-dark">{{ formatDate(member.end_date) }}</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Status</span>
                                        <span v-if="member.end_date === null || member.end_date === ''" class="data-value badge badge-dark-teal">Active</span>
                                        <span v-else class="data-value text-white badge badge-danger">Inactive</span>
                                    </div>
                                    <div class="data-col data-col-end"></div>
                                </div><!-- data-item -->

                            </div><!-- data-list -->
                        </div><!-- .nk-block -->

                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button @click="resetSchoolSmcCommitteeMember()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                            <em class="icon ni ni-cross"></em><span>Close</span>
                        </button>

                    </div>
                </div>
            </div>
        </div>
        <!-- import SMC members -->
        <div class="modal fade zoom" tabindex="-1" id="uploadSmcCommitteeMemberModal">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetSchoolSmcCommitteeMember()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Import SMC Members</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row align-center">
                            <pre-primary-smc-members-upload
                                ref="smcCommittee"
                                @fresh-data="reloadCommittee"
                            ></pre-primary-smc-members-upload>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /SMC Member Modal -->

    </div>
</template>

<script>
import Notifications from "../Notifications.vue";

export default {
    name: "PrimarySchoolSmcCommittee",
    props: ['countriesObj'],
    mounted() {
        this.initPlugins();
    },
    components: {
        Notifications
    },
    data: function () {
        return {
            valid_id_number: false,
            loading: false,
            filtering: false,
            edit: false,
            api_url: '/institutions/primary-schools/',
            bulk_action: '',
            select_all_smc_committee: false,
            selected_smc_committee: [],
            smc_committee: [],
            countries: [],
            form_member: {
                id: '',
                first_name: '',
                surname: '',
                other_names: '',
                id_number: '',
                masked_id_number: '',
                phone_1: '',
                gender: 'M',
                country_id: '',
                country: {
                    name: '',
                },
                appointment_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                end_date: '',
            },
            member: {
                person : {
                    id: '',
                    first_name: '',
                    surname: '',
                    other_names: '',
                    id_number: '',
                    masked_id_number: '',
                    phone_1: '',
                    gender: 'M',
                    country_id: '',
                    country: {
                        name: '',
                    },
                    appointment_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                    end_date: '',
                },
            },
            country: 'UGANDA',
            filter: {
                gender: '',
                status: '',
                country_id: '',
                search_term: '',
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        setCountryDefault: function (country_id) {
            $('#member_country_id').val(country_id).change();
        },
        initPlugins: function () {
            let self = this;

            this.countries = this.countriesObj;

            $('#filter_country_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.country_id = data.id;
                    return data.text;
                },
            });

            $('#filter_gender').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#filter_status').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.status = data.id;
                    return data.text;
                },
            });

            $('#member_country_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_member.country_id = data.id.length > 0 ? Number(data.id) : "";
                    self.country = data.text;
                    return data.text;
                },
            });

            let member_phone_1 = document.querySelector('#member_phone_1');
            let iti_member_phone_1 = intlTelInput(member_phone_1, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            member_phone_1.addEventListener('blur', ()=>{
                self.form_member.phone_1 = iti_member_phone_1.getNumber().slice(-9);
                member_phone_1.value = iti_member_phone_1.getNumber().slice(-9);
            });
            member_phone_1.addEventListener('change', ()=>{
                self.form_member.phone_1 = iti_member_phone_1.getNumber().slice(-9);
                member_phone_1.value = iti_member_phone_1.getNumber().slice(-9);
            });

            $('#member_appointment_date').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.form_member.appointment_date = moment(e.date).format("D MMMM, YYYY");
            });

            $('#member_end_date').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.form_member.end_date = e.date === undefined ? '' : moment(e.date).format("D MMMM, YYYY");
            });

            $('#memberBulkAction').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });
        },
        createSchoolSmcCommitteeMember: function () {
            if (!this.valid_id_number && this.country === "UGANDA") {
                this.validateNIN(this.form_member.id_number);
            } else {
                this.loading = true;
                axios.post(this.api_url+'smc-committee/create', this.form_member)
                    .then(response => {
                        this.smc_committee = response.data;
                        this.resetSchoolSmcCommitteeMember();
                        this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"School CMC Member Created Successfully"});
                    })
                    .catch(error => {
                        //console.log(error)
                        this.loading = false;
                        if (error.response.data.errors !== undefined) {
                            for (let field in error.response.data.errors) {
                                this.showError(error.response.data.errors[field]);
                            }
                        } else {
                            this.renderError(error)
                        }
                    });
            }
        },
        editSchoolSmcCommitteeMember: function (member) {
            $('#member_country_id').val(member.person.country_id).change();
            this.edit = true;
            this.form_member.id = member.person.id;
            this.form_member.first_name = member.person.first_name;
            this.form_member.surname = member.person.surname;
            this.form_member.other_names = member.person.other_names;
            this.form_member.id_number = member.person.id_number;
            this.form_member.phone_1 = member.person.phone_1;
            this.form_member.gender = member.person.gender;
            this.form_member.country_id = member.person.country_id;
            this.form_member.appointment_date = moment(member.appointment_date).format("D MMMM, YYYY");
            this.form_member.end_date = member.end_date === '' || member.end_date === null ? '' : moment(member.end_date).format("D MMMM, YYYY");
            $('#schoolSmcCommitteeMemberModal').modal({backdrop:'static'});
        },
        viewSchoolSmcCommitteeMember: function (member) {
            this.edit = true;
            this.member.person.first_name = member.person.first_name;
            this.member.person.surname = member.person.surname;
            this.member.person.other_names = member.person.other_names;
            this.member.person.id_number = member.person.id_number;
            this.member.person.masked_id_number = member.person.masked_id_number;
            this.member.person.phone_1 = member.person.phone_1;
            this.member.person.gender = member.person.gender;
            this.member.person.country = member.person.country;
            this.member.appointment_date = moment(member.appointment_date).format("D MMMM, YYYY");
            this.member.end_date = member.end_date === '' || member.end_date === null ? '' : moment(member.end_date).format("D MMMM, YYYY");
            $('#viewSchoolSmcCommitteeMemberModal').modal({backdrop:'static'});
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        updateSchoolSmcCommitteeMember: function () {
            this.loading = true;
            axios.post(this.api_url+'smc-committee/update/'+this.form_member.id, this.form_member)
                .then(response=>{
                    this.smc_committee = response.data;
                    this.resetSchoolSmcCommitteeMember();
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"CMC Member Updated Successfully"});
                })
                .catch(error=>{
                    //console.log(error)
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        reloadCommittee: function (data) {
            this.smc_committee = data;
        },
        filterMembers: function () {
            this.loading = true;
            axios.post(this.api_url+'smc-committee/filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.smc_committee = response.data;
                    this.loading = false;
                    this.filtering = true;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetFilter: function () {
            this.loading = true;
            axios.get(this.api_url+'smc-committee/?year='+this.selectedYear)
                .then(response=>{
                    this.smc_committee = response.data;
                    this.loading = false;
                    this.filter.search_term = '';
                    $('#filter_gender').val('').change();
                    $('#filter_country_id').val('').change();
                    $('#filter_status').val('').change();
                    this.filtering = false;
                    // $('#filterPerPage').val(15).change();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error)
                });
        },

        toggleAllOwner: function () {
            this.selected_smc_committee =[];

            if (this.select_all_smc_committee) {
                this.smc_committee.forEach(member=>{
                    this.selected_smc_committee.push(member.id)
                });
            }
        },
        toggleOneOwner: function () {
            this.select_all_smc_committee = this.selected_smc_committee.length === this.smc_committee.length
        },

        startLoading: function() {
            $.blockUI({
                message: $('#schoolSmcCommitteeLoadingMessage'),
                css: {
                    padding:0,
                    margin:0,
                    width:'30%',
                    top:'40%',
                    left:'35%',
                    textAlign:'center',
                    color:'#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor:'wait'
                },
            });
        },
        resetSchoolSmcCommitteeMember: function () {
            $('#schoolSmcCommitteeMemberModal').modal('hide');
            this.valid_id_number = false;
            this.loading = false;
            this.edit = false;
            this.form_member = {
                id: '',
                first_name: '',
                surname: '',
                other_names: '',
                id_number: '',
                masked_id_number: '',
                phone_1: '',
                gender: 'M',
                country_id: '',
                country: {
                    name: '',
                },
                appointment_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                end_date: '',
            };
            $('#member_country_id').val(this.form_member.country_id).change();
        },
        validateNIN: function (id_number) {
            this.loading = true;
            console.log(this.form_member.first_name);
            console.log(this.form_member.surname);
            console.log(this.form_member.gender);
            axios.post('/nira/user-info', {id_number: id_number})
                .then(response=>{
                    if (response.data.first_name === this.form_member.first_name
                        && response.data.surname === this.form_member.surname
                        && Number(response.data.gender) === this.form_member.gender) {
                        this.valid_id_number = true;
                        this.createSchoolSmcCommitteeMember();
                    } else {
                        this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:"NIN Data Mismatch, Try Again..."});
                        this.loading = false;
                    }
                })
                .catch(error=>{
                    this.valid_id_number = false;
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedYear: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                return urlParams.get('year');
            } else {
                return moment().format("YYYY");
            }
        },
        ugandaId: function () {
            return this.countries.find(country=>{
                return country.name.toUpperCase()  === 'UGANDA';
            }).country_id;
        },
    }
}
</script>

<style scoped>

</style>
