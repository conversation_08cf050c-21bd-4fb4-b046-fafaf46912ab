<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">SECTION D: TEACHING STAFF INFORMATION</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th rowspan="2" class="align-middle text-uppercase border-secondary text-dark">Category</th>
                    <th rowspan="2" class="align-middle text-uppercase border-secondary text-dark">Type of qualification, training and post level</th>
                    <th colspan="2" class="text-uppercase border-secondary text-dark text-center">Sex of Staff</th>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark text-center">M</th>
                    <th class="text-uppercase border-secondary text-dark text-center">F</th>
                </tr>
                </thead>
                <tbody class="border-secondary border-top">
                <tr>
                    <th :rowspan="trainedQualifications.length+1" class="align-middle text-uppercase border-secondary text-dark">Trained Tutors & Instructors</th>
                </tr>
                <tr v-for="qualification in trainedQualifications">
                    <td class="align-middle text-uppercase border-secondary text-dark">{{ qualification.name }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ countMaleTeachers(qualification.teachers) }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ countFemaleTeachers(qualification.teachers) }}</td>
                </tr>
                <tr>
                    <th :rowspan="teacherEducationLevels.length+1" class="align-middle text-uppercase border-secondary text-dark">Qualified Tutors & Instructors</th>
                </tr>
                <tr v-for="educationLevel in teacherEducationLevels">
                    <td class="align-middle text-uppercase border-secondary text-dark">{{ educationLevel.name }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ educationLevel.male_count }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ educationLevel.female_count }}</td>
                </tr>
                </tbody>
                <tfoot>
                <tr class="bg-secondary-dim">
                    <th colspan="2" class="align-middle text-uppercase border-secondary text-dark">
                        Total
                    </th>
                    <th class="align-middle text-uppercase border-secondary text-dark text-center">{{ countTotalMaleTeachers() }}</th>
                    <th class="align-middle text-uppercase border-secondary text-dark text-center">{{ countTotalFemaleTeachers() }}</th>
                </tr>
                </tfoot>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#updateSectionDModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE TEACHING STAFF INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="updateSectionDModal">
            <div class="modal-xl modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">UPDATE TEACHING STAFF INFORMATION</h6>
                    </div>
                    <div class="modal-body pt-0">
                        <error-notifications ref="notifyError"></error-notifications>
                        <ul class="nav nav-tabs">
                            <li @click="loadSection('form-create')" class="nav-item">
                                <span :class="[active_section === 'form-create' ? 'active' : '', 'nav-link cursor']" href="#addTeacherFormTab">Add Single Tutor / Instructor</span>
                            </li>
<!--                            <li @click="loadSection('import-ugandans')" class="nav-item">-->
<!--                                <span :class="[active_section === 'import-ugandans' ? 'active' : '', 'nav-link cursor']" href="#addTeacherExcelUgandan">Upload Ugandan Tutors & Instructors</span>-->
<!--                            </li>-->
<!--                            <li @click="loadSection('import-non-ugandans')" class="nav-item">-->
<!--                                <span :class="[active_section === 'import-non-ugandans' ? 'active' : '', 'nav-link cursor']" href="#addTeacherExcelForeigners">Upload Foreigner Tutors & Instructors</span>-->
<!--                            </li>-->
                        </ul>
                        <div class="tab-content">
                            <div :class="[active_section === 'form-create' ? 'active' : '', 'tab-pane']" id="addTeacherFormTab">
                                <survey-certificate-teacher-form-create
                                    ref="primaryTeacher"
                                    :section-id="sectionId"
                                    :school-obj="schoolObj"
                                    :survey-obj="surveyObj"
                                    :countries-obj="countriesObj"
                                    :teacher-types-obj="teacherTypesObj"
                                    :marital-statuses-obj="maritalStatusesObj"
                                    :education-levels-obj="educationLevelsObj"
                                    :teacher-designations-obj="teacherDesignationsObj"
                                    :employment-statuses-obj="employmentStatusesObj"
                                    :teacher-qualifications-obj="teacherQualificationsObj"
                                ></survey-certificate-teacher-form-create>
                            </div>
<!--                            <div :class="[active_section === 'import-ugandans' ? 'active' : '', 'tab-pane']" id="addTeacherExcelUgandan">-->
<!--                                <survey-certificate-tutors-import-ugandans-->
<!--                                    :section-id="sectionId"-->
<!--                                    :school-obj="schoolObj"-->
<!--                                    :survey-obj="surveyObj"-->
<!--                                ></survey-certificate-tutors-import-ugandans>-->
<!--                            </div>-->
<!--                            <div :class="[active_section === 'import-non-ugandans' ? 'active' : '', 'tab-pane']" id="addTeacherExcelForeigners">-->
<!--                                <survey-certificate-tutors-import-foreigners-->
<!--                                    :section-id="sectionId"-->
<!--                                    :school-obj="schoolObj"-->
<!--                                    :survey-obj="surveyObj"-->
<!--                                ></survey-certificate-tutors-import-foreigners>-->
<!--                            </div>-->
                        </div>
                    </div>
                    <div v-if="active_section === 'form-create'" class="modal-footer d-flex justify-content-center">
                        <button @click="resetTeacher()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button v-if="verify && active_section === 'form-create'" @click="saveTeacherInformation()" :disabled="loading" type="submit" class="btn btn-primary d-flex">
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading" class="align-self-center">Verifying...</span>
                            <span v-if="loading" class="sr-only">Verifying...</span>
                            <span v-if="!loading && uganda" class="align-self-center">Verify Teacher NIN</span>
                            <span v-if="!loading && !uganda" class="align-self-center">Verify Teacher Work Permit</span>
                            <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                        </button>
                        <button v-else @click="saveTeacherInformation()" :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading" class="align-self-center">Saving...</span>
                            <span v-if="loading" class="sr-only">Saving...</span>
                            <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "SectionD",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'teacherQualificationsObj',
        'countriesObj',
        'teacherTypesObj',
        'educationLevelsObj',
        'maritalStatusesObj',
        'teacherDesignationsObj',
        'employmentStatusesObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            verify: true,
            uganda: true,
            active_section: 'form-create',
            api_url: '/institutions/surveys/school/section-d',
            school: {},
            survey: {},
            teacher_qualifications: [],
            education_levels: [],
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.teacher_qualifications = this.teacherQualificationsObj;
            this.education_levels = this.educationLevelsObj;
        },
        loadSection: function (section) {
            this.active_section = section;
        },
        countMaleTeachers: function (persons) {
            return persons.filter(teacher=>{
                return teacher.person.gender === "M";
            }).length;
        },
        countFemaleTeachers: function (persons) {
            return persons.filter(teacher=>{
                return teacher.person.gender === "F";
            }).length;
        },
        countTotalMaleTeachers: function () {
            let count = 0;

            this.trainedQualifications.forEach(qualification=>{
                count += qualification.teachers.filter(teacher=>{
                    return teacher.person.gender === "M";
                }).length;
            });

            this.teacherEducationLevels.forEach(level=>{
                count += level.male_count;
            })

            return count;
        },
        countTotalFemaleTeachers: function () {
            let count = 0;

            this.trainedQualifications.forEach(qualification=>{
                count += qualification.teachers.filter(teacher=>{
                    return teacher.person.gender === "F";
                }).length;
            });

            this.teacherEducationLevels.forEach(level=>{
                count += level.female_count;
            });

            return count;
        },
        saveTeacherInformation: function () {
            if (this.active_section === 'form-create') {
                $('#teacherFormCreateFormSubmit').click();
            }
        },
        resetTeacher: function () {
            if (this.active_section === 'form-create') {
                this.$refs.primaryTeacher.resetTeacher();
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        qualifiedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return qualification.pivot.is_qualified;
            });
        },
        trainedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return !qualification.pivot.is_qualified;
            });
        },
        teacherEducationLevels: function () {
            return this.education_levels.filter(level => {
                return level.level_rank >= 2;
            });
        },
    },
}
</script>

<style scoped>

</style>
