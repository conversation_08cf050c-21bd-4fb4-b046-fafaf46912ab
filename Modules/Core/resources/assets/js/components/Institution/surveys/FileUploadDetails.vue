<template>
    <div class="w-100">
        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <tbody>
                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        File Name:
                    </th>
                    <td class="rounded-0 text-dark align-middle border-secondary border-top border-1">
                        {{ file_upload.file_name }}
                    </td>
                </tr>
                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        File Type:
                    </th>
                    <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                        {{ getFileType() }}
                    </td>
                </tr>
                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        Uploaded By:
                    </th>
                    <td class="rounded-0 text-uppercase text-dark align-middle border-secondary border-top border-1">
                        {{ getUploader() }}
                    </td>
                </tr>
                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        Date Uploaded:
                    </th>
                    <td class="rounded-0 text-uppercase text-dark align-middle border-secondary border-top border-1">
                        {{ formatDateUploaded() }}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div v-if="file_upload.errors > 0 && learners" class="table-responsive mt-3">
            <table class="table table-sm table-bordered table-hover">
                <tbody>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-white text-uppercase border-right-0 border-secondary align-middle border-1">FLAGGED LEARNERS</td>
                </tr>
                </tbody>
                <tbody>
                <tr class="bg-secondary">
                    <td class="text-white text-uppercase border-right-0 border-secondary align-middle border-1">NAME</td>
                    <td class="text-white text-uppercase border-white align-middle border-1">CLASS</td>
                    <td class="text-white text-uppercase border-secondary align-middle border-1">ERROR MESSAGE</td>
                </tr>
                </tbody>
                <tbody v-if="uganda && file_upload.failed_ugandan_learners !== undefined && file_upload.failed_ugandan_learners.length">
                <tr v-for="entry in file_upload.failed_ugandan_learners" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.learner_full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.class }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">
                        <span v-for="error in formatErrors(entry.validation_errors)" class="block">{{ error }}</span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="!uganda && file_upload.failed_foreign_learners !== undefined && file_upload.failed_foreign_learners.length">
                <tr v-for="entry in file_upload.failed_foreign_learners" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.learner_full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.class }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">
                        <span v-for="error in formatErrors(entry.validation_errors)" class="block">{{ error }}</span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="!uganda && file_upload.failed_refugee_learners !== undefined && file_upload.failed_refugee_learners.length">
                <tr v-for="entry in file_upload.failed_refugee_learners" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.learner_full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.class }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">
                        <span v-for="error in formatErrors(entry.validation_errors)" class="block">{{ error }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div v-if="file_upload.errors > 0 && teachers" class="table-responsive mt-3">
            <table class="table table-sm table-bordered table-hover">
                <tbody>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-white text-uppercase border-right-0 border-secondary align-middle border-1">FLAGGED {{ getTeacherTitle(file_upload.user.school_type_id) }}</td>
                </tr>
                </tbody>
                <tbody>
                <tr class="bg-secondary">
                    <td class="text-white text-uppercase border-right-0 border-secondary align-middle border-1">NAME</td>
                    <td class="text-white text-uppercase border-secondary align-middle border-1">ERROR MESSAGE</td>
                </tr>
                </tbody>
                <tbody v-if="uganda && file_upload.failed_ugandan_teachers.length">
                <tr v-for="entry in file_upload.failed_ugandan_teachers" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">
                        <span v-for="error in formatErrors(entry.validation_errors)" class="block">{{ error }}</span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="!uganda && file_upload.failed_foreign_teachers.length">
                <tr v-for="entry in file_upload.failed_foreign_teachers" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ entry.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">
                        <span v-for="error in formatErrors(entry.validation_errors)" class="block">{{ error }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div v-if="uganda" class="table-responsive mt-5">
            <table class="table table-sm table-hover table-bordered">
                <tbody>
                <tr class="">
                    <th colspan="4" class="rounded-0 bg-secondary text-white text-uppercase align-middle border-secondary border-top text-center border-1">
                        NIN Summaries
                    </th>
                </tr>
                <tr class="">
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        NINs Processed
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Pending
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Successful
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Failed
                    </th>
                </tr>
                <tr class="">
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsSuccess + getTotalNINsFailed }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsPending }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsSuccess }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsFailed }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div v-if="uganda && getTotalNINsFailed > 0" class="table-responsive mt-3">
            <table class="table table-sm table-bordered table-hover">
                <tbody v-if="file_upload.ugandan_non_teaching_staff_nins.length || file_upload.ugandan_teacher_nins.length || file_upload.ugandan_learner_nins.length || file_upload.ugandan_learner_parent_nins.length">
                <tr class="bg-secondary">
                    <td class="text-white text-uppercase border-right-0 border-secondary align-middle border-1">NAME</td>
                    <td class="text-white text-uppercase border-white align-middle border-1">DATE OF BIRTH</td>
                    <td class="text-white text-uppercase border-white align-middle border-1">NIN</td>
                    <td class="text-white text-uppercase border-secondary align-middle border-1">ERROR MESSAGE</td>
                </tr>
                </tbody>
                <tbody v-if="file_upload.ugandan_learner_nins.length">
                <tr v-for="nin in file_upload.ugandan_learner_nins" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.learner_upload.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ formatCarbonDate(nin.learner_upload.birth_date) }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.document_id }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.verification_error }}</td>
                </tr>
                </tbody>
                <tbody v-if="file_upload.ugandan_learner_parent_nins.length">
                <tr v-for="nin in file_upload.ugandan_learner_parent_nins" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.learner_parent_upload.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ formatCarbonDate(nin.learner_parent_upload.birth_date) }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.document_id }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.verification_error }}</td>
                </tr>
                </tbody>
                <tbody v-if="file_upload.ugandan_teacher_nins.length">
                <tr v-for="nin in file_upload.ugandan_teacher_nins" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.teacher_upload.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ formatCarbonDate(nin.teacher_upload.birth_date) }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.document_id }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.verification_error }}</td>
                </tr>
                </tbody>
                <tbody v-if="file_upload.ugandan_non_teaching_staff_nins.length">
                <tr v-for="nin in file_upload.ugandan_non_teaching_staff_nins" class="">
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.non_teaching_staff_upload.full_name }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ formatCarbonDate(nin.non_teaching_staff_upload.birth_date) }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.document_id }}</td>
                    <td class="text-dark text-uppercase border-secondary align-middle border-1">{{ nin.verification_error }}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    name: "FileUploadDetails",
    props: ["fileUploadObj"],
    components: {},
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            file_upload: {
                id: "",
                file_name: "",
                file_type: "",
                total_records: 0,
                processed_records: 0,
                user: {
                    school_type: {
                        name: "",
                    },
                    school: null,
                    person: null,
                },
                ugandan_teacher_nins: [],
                ugandan_learner_nins: [],
                ugandan_learner_parent_nins: [],
                ugandan_non_teaching_staff_nins: [],
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.file_upload = this.fileUploadObj;
            window.setInterval(()=>{
                this.loadFileUpload();
            }, 2000)
        },
        loadFileUpload: function () {
            if (this.file_upload !== "") {
                axios.get("/institutions/file-uploads/details/"+this.file_upload.id)
                    .then(response=>{
                        this.file_upload = response.data;
                    })
                    .catch(error=>{
                        console.log(error)
                    });
            }
        },
        getUploader: function () {
            if (this.file_upload.user.school !== null) {
                return this.file_upload.user.school.name;
            }
            if (this.file_upload.user.person !== null) {
                return this.file_upload.user.person.full_name;
            }
            return null;
        },
        getFileType: function () {
            switch (this.file_upload.file_type) {
                case "ugandan_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Ugandan Students" : "Ugandan Learners";

                case "foreigner_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Foreign Students" : "Foreign Learners";

                case "refugee_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Refugee Students" : "Refugee Learners";

                case "ugandan_teachers":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Ugandan Caregivers" : "Ugandan Teachers";

                case "foreigner_teachers":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Foreign Caregivers" : "Foreign Teachers";

                case "ugandan_support_staff":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Ugandan Support/Administrative Staff" : "Ugandan Non Teaching Staff";

                case "foreigner_support_staff":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Foreign Support/Administrative Staff" : "Foreign Non Teaching Staff";
            }
        },
        formatDateUploaded: function () {
            return moment(this.file_upload.date_created).format("DD MMMM, YYYY hh:mm:ssa")
        },
        formatCarbonDate: function (row) {
            return moment(row).format("DD MMMM, YYYY");
        },
        formatErrors: function (validation_errors) {
            let errors = JSON.parse(validation_errors);
            let formatted = [];
            for (let field in errors) {
                for (let i = 0; i < errors[field].length; i++) {
                    formatted.push(errors[field][i]);
                }
            }
            return formatted;
        },
        getTeacherTitle: function(schoolTypeId) {
            switch (schoolTypeId) {
                case 1:
                    return "Caregiver";

                case 2:
                    return "Teacher";

                case 3:
                    return "Teacher";
            }
        },
        getNonTeachingStaffTitle: function(schoolTypeId) {
            switch (schoolTypeId) {
                case 1:
                    return "Support Staff";

                case 2:
                    return "Non Teaching Staff";

                case 3:
                    return "Non Teaching Staff";
            }
        },
    },
    computed: {
        getTotalFailed: function () {
            let total = 0

            if (this.file_upload.errors !== undefined && this.file_upload.errors !== null) {
                total += this.file_upload.errors;
            }

            return total;
        },
        getTotalNINsPending: function () {
            let total = 0

            if (this.file_upload.parent_pending_nins !== undefined && this.file_upload.parent_pending_nins !== null) {
                total += this.file_upload.parent_pending_nins;
            }

            if (this.file_upload.pending_nins !== undefined && this.file_upload.pending_nins !== null) {
                total += this.file_upload.pending_nins;
            }

            return total;
        },
        getTotalNINsFailed: function () {
            let total = 0

            if (this.file_upload.parent_error_nins !== undefined && this.file_upload.parent_error_nins !== null) {
                total += this.file_upload.parent_error_nins;
            }

            if (this.file_upload.error_nins !== undefined && this.file_upload.error_nins !== null) {
                total += this.file_upload.error_nins;
            }

            return total;
        },
        getTotalNINsSuccess: function () {
            let total = 0

            if (this.file_upload.parent_success_nins !== undefined && this.file_upload.parent_success_nins !== null) {
                total += this.file_upload.parent_success_nins;
            }

            //Changed this from success_nins to success
            if (this.file_upload.success !== undefined && this.file_upload.success !== null) {
                total += this.file_upload.success;
            }

            return total;
        },
        uganda: function () {
            return this.file_upload.file_type.startsWith('ugandan_')
        },
        learners: function () {
            return this.file_upload.file_type.endsWith('_learners')
        },
        teachers: function () {
            return this.file_upload.file_type.endsWith('_teachers')
        },
        support_staff: function () {
            return this.file_upload.file_type.endsWith('_support_staff')
        },
    }
}
</script>

<style scoped>

</style>
