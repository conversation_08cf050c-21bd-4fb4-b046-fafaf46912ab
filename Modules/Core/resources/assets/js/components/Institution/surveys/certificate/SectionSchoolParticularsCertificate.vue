<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                <tr class="bg-secondary">
                    <th colspan="2" class="align-middle text-uppercase text-white border-secondary border">Section B: Institution PARTICULARS & PROGRAMS</th>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="align-middle text-uppercase text-dark border-secondary border w-35">Identifier</th>
                    <th class="align-middle text-uppercase text-dark border-secondary border">Name/Detail</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">INSTITUTION TYPE</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.certificate_school.school_type !== null">{{ school.certificate_school.school_type.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">OWNERSHIP STATUS</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.ownership_status !== null">{{ school.ownership_status.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.school_ownership_status_id === 1">GOVERNMENT</span>
                        <span v-else-if="school.legal_ownership_status !== null && school.school_ownership_status_id === 2">{{ school.legal_ownership_status.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.founding_body !== null">{{ school.founding_body.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Main Funding Source</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.funding_source !== null">{{ school.funding_source.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.certificate_school.certificate_awarding_school_type_id !== null">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Registering Authority</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.certificate_school.certificate_awarding_school_type_id !== null">
                            {{ school.certificate_school.school_type.institution_category.registering_body.name }}
                        </span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.year_founded !== null">{{ school.year_founded }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.registration_status_id === 1">REGISTERED</span>
                        <span v-if="school.registration_status_id === 2">LICENSED</span>
                        <span v-if="school.registration_status_id === 0">NOT LICENSED</span>
                        <span v-if="school.registration_status_id === null" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.registration_status_id === 1 && school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.registration_number !== null">{{ school.registration_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.license_number !== null">{{ school.license_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.license_number !== null">{{ formatDate(school.licence_certificate_expiry_date) }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-show="school.school_ownership_status_id === 1">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Supply Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.certificate_school.supply_number !== null">{{ school.certificate_school.supply_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="examiningBody.acronym.length">
                    <th class="align-middle text-uppercase text-dark border border-secondary">{{ examiningBody.acronym }} Center Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.center_number !== null">{{ school.center_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.has_male_students && school.has_female_students">Mixed</span>
                        <span v-if="school.has_male_students && !school.has_female_students">Males Only</span>
                        <span v-if="!school.has_male_students && school.has_female_students">Females Only</span>
                        <span v-if="!school.has_male_students && !school.has_female_students" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">RESIDENTIAL or NON-RESIDENTIAL</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.certificate_school.admits_day_scholars_yn && school.certificate_school.admits_boarders_yn">RESIDENTIAL & NON-RESIDENTIAL</span>
                        <span v-if="!school.certificate_school.admits_day_scholars_yn && school.certificate_school.admits_boarders_yn">RESIDENTIAL ONLY</span>
                        <span v-if="school.certificate_school.admits_day_scholars_yn && !school.certificate_school.admits_boarders_yn">NON RESIDENTIAL ONLY</span>
                        <span v-if="!school.certificate_school.admits_day_scholars_yn && !school.certificate_school.admits_boarders_yn" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Highest Award</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span>CERTIFICATE</span>
                    </td>
                </tr>
                <tr v-show="school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.capital_for_establishment !== null">UGX {{ formatMoney(school.capital_for_establishment) }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>

                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health facility</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.health_facility_distance !== null">{{ school.health_facility_distance.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="2" class="border-0 text-center">
                        <button data-toggle="modal" data-target="#updateSectionBModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                            <span class="text-uppercase">UPDATE INSTITUTION PARTICULARS</span>
                        </button>
                    </td>
                </tr>
                </tfoot>
            </table>

            <div class="mt-5">
                <!-- Has Campuses -->
                <div class="row w-100">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-label">Does this institution have branches/campuses?</label>
                            <span class="form-note">Specify whether your institution has branches/campuses.</span>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" v-model="has_campuses" value="yes" id="institutionHasCampuses">
                                <label class="custom-control-label text-uppercase" for="institutionHasCampuses">YES</label>
                            </div>
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" v-model="has_campuses" value="no" id="institutionWithoutCampuses">
                                <label class="custom-control-label text-uppercase" for="institutionWithoutCampuses">NO</label>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /Has Campuses -->
            </div>

            <div v-if="has_campuses === 'yes'" class="mt-5">
                <success-notifications ref="notifySuccessCampuses"></success-notifications>
                <button data-toggle="modal" data-target="#updateCampusesModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                    <span class="text-uppercase">ADD BRANCHES / CAMPUSES</span>
                </button>
                <table class="table border border-dark mt-2">
                    <thead class="bg-secondary">
                    <tr>
                        <th class="text-white align-middle text-uppercase">Name</th>
                        <th class="py-2 text-center text-white text-uppercase border-left border-white">Phone No.</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white">District</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white">County</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white">Sub County</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white">Parish</th>
                    </tr>
                    </thead>
                    <tbody class="border-top-0 border-secondary">
                    <tr v-for="campus in school.certificate_school.campuses">
                        <td class="align-middle border-left border-secondary ucap text-dark">{{ campus.name }}</td>
                        <td class="align-middle border-left border-secondary text-center">
                            <span class="text-dark text-uppercase">+256{{ campus.phone }}</span>
                        </td>
                        <td class="align-middle border-left border-secondary text-center">
                            <span  class="text-dark text-uppercase">{{ campus.district.name }}</span>
                        </td>
                        <td class="align-middle border-left border-secondary text-center">
                            <span  class="text-dark text-uppercase">{{ campus.county.name }}</span>
                        </td>
                        <td class="align-middle border-left border-secondary text-center">
                            <span  class="text-dark text-uppercase">{{ campus.sub_county.name }}</span>
                        </td>
                        <td class="align-middle border-left border-secondary text-center">
                            <span  class="text-dark text-uppercase">{{ campus.parish.name }}</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div v-if="!school.certificate_school.campuses.length" class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There is no campus/branch information to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Update Section B Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateSectionBModal">
            <form @submit.prevent="updateSectionB()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetSectionB()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Institution Particulars</h5>
                        </div>
                        <div class="modal-body overflow-auto h-500px">
                            <error-notifications ref="notifyError"></error-notifications>
                            <!-- Institution Type -->
                            <div v-show="school.certificate_school.certificate_awarding_school_type_id === null" class="row g-3 align-center">
                                <div v-show="school.certificate_school.certificate_awarding_school_type_id === null" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="certificateAwardingSchoolTypeId">Institution Type</label>
                                        <span class="form-note">Select the type of your institution.</span>
                                    </div>
                                </div>
                                <div v-show="school.certificate_school.certificate_awarding_school_type_id === null" class="col-lg-6">
                                    <div class="form-group">
                                        <select :required="school.certificate_school.certificate_awarding_school_type_id === null" id="certificateAwardingSchoolTypeId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="institution_type in all_institution_types" :value="institution_type.id">{{ institution_type.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Institution Type -->

                            <!-- Legal Ownership Status -->
                            <div class="row g-3 align-center">
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                        <span class="form-note">Specify Institution's legal ownership status.</span>
                                    </div>
                                </div>
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <select :required="school.school_ownership_status_id === 2" id="schoolLegalOwnershipStatusId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option :disabled="(school.school_ownership_status_id === '2' || school.school_ownership_status_id === 2) && legal_ownership_status.name.startsWith('GOVERNMENT')" v-for="legal_ownership_status in legal_ownership_statuses" :value="legal_ownership_status.id">{{ legal_ownership_status.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Legal Ownership Status -->

                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                        <span class="form-note">Specify Institution's founding body.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolFoundingBodyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option :disabled="(school.school_ownership_status_id === '2' || school.school_ownership_status_id === 2) && founding_body.name.startsWith('GOVERNMENT')" v-for="founding_body in founding_bodies" :value="founding_body.id">{{ founding_body.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Founding Body -->

                            <!-- Funding Source -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFundingSourceId">Main Funding Source</label>
                                        <span class="form-note">Specify Institution's main funding source.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolFundingSourceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="funding_source in funding_sources" :value="funding_source.id">{{ funding_source.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Funding Source -->

                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                        <span class="form-note">Specify the year this Institution was founded.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolYearFounded" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="year in founderYears" :value="year">{{ year }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Year Founded -->

                            <!-- Registration Status -->
                            <div class="row g-3 align-center">
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationStatus">Registration Status</label>
                                        <span class="form-note">Specify if this Institution is registered or licensed.</span>
                                    </div>
                                </div>
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <select :required="school.school_ownership_status_id === 2" id="schoolRegistrationStatus" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="1">REGISTERED</option>
                                            <option value="2">LICENCED</option>
                                            <option value="0">NOT LICENSED</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Registration Status -->

                            <!-- Registration Number -->
                            <div v-show="school_form.registration_status_id === 1" :class="school_form.registration_status_id === 1 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 1 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationNumber">Registration Number</label>
                                        <span class="form-note">Specify this Institution's registration number</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 1 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 1" v-model.trim="school_form.registration_number" id="schoolRegistrationNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="Enter Registration Number" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Registration Number -->

                            <!-- License Number -->
                            <div v-show="school_form.registration_status_id === 2" :class="school_form.registration_status_id === 2 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLicenseNumber">License Number</label>
                                        <span class="form-note">Specify this Institution's license number</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 2" v-model.trim="school_form.license_number" id="schoolLicenseNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="Enter License Number" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-show="school_form.registration_status_id === 2" :class="school_form.registration_status_id === 2 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="licenseNumberDateExpiry">License Number Expiry Date</label>
                                        <span class="form-note">Specify this Institution's license expiry date</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 2" v-model.trim="school_form.licence_certificate_expiry_date" id="licenseNumberDateExpiry" placeholder="Enter License Number Expiry Date." type="text" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /License Number -->

                            <!-- Supply Number -->
                            <div class="row g-3 align-center">
                                <div v-show="school.school_ownership_status_id === 1" class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <label class="form-label" for="supplyNumber">Supply Number</label>
                                        <span class="form-note">Specify Institution's Supply Number</span>
                                    </div>
                                </div>
                                <div v-show="school.school_ownership_status_id === 1" class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="school_form.supply_number" placeholder="Enter Supply Number" id="supplyNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Supply Number -->

                            <!-- Center Number -->
                            <div v-if="examiningBody.acronym.length" class="row g-3 align-center">
                                <div class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCenterNumber">{{ examiningBody.acronym }} Center Number</label>
                                        <span class="form-note">Specify this Institution's {{ examiningBody.acronym }} Center number</span>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="school_form.center_number" id="schoolCenterNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" :placeholder="'Enter '+examiningBody.acronym+' Number'" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Center Number -->

                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSexComposition">Sex Composition</label>
                                        <span class="form-note">Specify this Institution's sex composition.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-2">
                                    <div class="form-group">
                                        <select required id="schoolSexComposition" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="male">MALES ONLY</option>
                                            <option value="female">FEMALES ONLY</option>
                                            <option value="mixed">MIXED</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Sex Composition -->

                            <!-- DAY OR BOARDING -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDayOrBoarding">Residential Or Non-Residential</label>
                                        <span class="form-note">Specify whether this Institution is Residential, Non-Residential or both.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolDayOrBoarding" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="boarding">RESIDENTIAL ONLY</option>
                                            <option value="day">NON-RESIDENTIAL ONLY</option>
                                            <option value="both">RESIDENTIAL & NON-RESIDENTIAL</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /DAY OR BOARDING -->

                            <!-- Capital For Establishment -->
                            <div v-show="school.school_ownership_status_id === 2" class="row g-3 align-center">
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                        <span class="form-note">Specify this Institution's Capital For Establishment.</span>
                                    </div>
                                </div>
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input @keyup="cleanUpEstablishmentCapital()" v-model.trim="school_form.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="E.g 5,000,000" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Capital For Establishment -->

                            <!-- Health Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                        <span class="form-note">Distance from this Institution to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolHealthDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="distance in health_distances" :value="distance.id">{{ distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Health Distance -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetSectionB()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Section B Modal -->

        <!-- Update Campuses Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateCampusesModal">
            <form @submit.prevent="updateCampuses()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetCampuses()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Add Branches/Campuses</h5>
                        </div>
                        <div class="modal-body overflow-auto scrollbar-dark-teal" data-simplebar data-simplebar-auto-hide="false">
                            <error-notifications ref="notifyError"></error-notifications>
                            <!-- Campus Name -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusName">Campus Name</label>
                                        <span class="form-note">Specify Branch Name</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="form_campus.name" placeholder="Enter Campus Name" id="campusName" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 align-center">
                                <div class="col-lg-6 mt-1">
                                    <div class="form-group">
                                        <label class="form-label" for="campusPhone">Campus Telephone Contact</label>
                                        <span class="form-note">Specify Branch Phone Number</span>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-1">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="form_campus.phone" id="campusPhone" type="text" maxlength="10" class="form-control bg-primary-dim" autocomplete="off" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Campus Name -->

                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusDistrictId">District</label>
                                        <span class="form-note">Specify Institution's district.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusDistrictId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusCountyId">County/Municipality</label>
                                        <span class="form-note">Specify Institution's county/municipality.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusCountyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusSubCountyId">Sub County/Division</label>
                                        <span class="form-note">Specify Institution's sub county/division.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusSubCountyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusParishId">Parish/Ward</label>
                                        <span class="form-note">Specify Institution's parish/town council.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusParishId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetCampuses()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Saving...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Campuses Modal -->
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import CSPBecMixin from "../../../../mixins/CSPBecMixin";
import SurveyFooter from "../SurveyFooter.vue";
export default {
    name: "SectionSchoolParticularsCertificate",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'districtsObj',
        'legalOwnershipStatusesObj',
        'foundingBodiesObj',
        'fundingSourcesObj',
        'schoolAuthoritiesObj',
        'healthDistancesObj',
    ],
    mixins:[CSPBecMixin],
    components: {
        SurveyFooter,
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            edit: false,
            loading: false,
            has_campuses: 'no',
            api_url: '/institutions/surveys/school/section-b',
            districts: [],
            counties: [],
            sub_counties: [],
            parishes: [],
            legal_ownership_statuses: [],
            founding_bodies: [],
            authorities: [],
            funding_sources: [],
            health_distances: [],
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                ownership_status: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                funding_source: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                certificate_institution_courses: [],
                institution_examined_courses: [],
                certificate_school: {
                    certificate_awarding_school_type_id: null,
                    authority: {name: ''},
                    school_type: {
                        name: '',
                        institution_category: {
                            name: '',
                            examining_body: {acronym: ''},
                        },
                    },
                    supply_number: '',
                    registering_body_id: '',
                    admits_day_scholars_yn: '',
                    admits_boarders_yn: '',
                    campuses: [],
                },
            },
            school_form: {
                legal_ownership_status_id: '',
                founding_body_id: '',
                funding_source_id: '',
                registering_body_id: '',
                year_founded: '',
                registration_status_id: '',
                registration_number: '',
                license_number: '',
                licence_certificate_expiry_date: '',
                certificate_awarding_school_type_id: '',
                supply_number: '',
                center_number: '',
                sex_composition: '',
                day_or_boarding: '',
                capital_for_establishment: '',
                health_facility_distance_range_id: '',
                section_id: '',
            },
            form_campus: {
                name: '',
                phone: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
            },
            all_institution_types: [],
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school_form.section_id = this.sectionId;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.districts = this.districtsObj;
            this.legal_ownership_statuses = this.legalOwnershipStatusesObj;
            this.founding_bodies = this.foundingBodiesObj;
            this.funding_sources = this.fundingSourcesObj;
            this.authorities = this.schoolAuthoritiesObj;
            this.health_distances = this.healthDistancesObj;

            this.allInstitutionTypes();

            $('#schoolLegalOwnershipStatusId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.legal_ownership_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolHealthDistanceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.health_facility_distance_range_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolFoundingBodyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.founding_body_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolFundingSourceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.funding_source_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolAuthorityId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.registering_body_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolYearFounded').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.year_founded = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolSexComposition').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.sex_composition = data.id;
                    return data.text;
                },
            });

            $('#schoolDayOrBoarding').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.day_or_boarding = data.id;
                    return data.text;
                },
            });

            $('#schoolRegistrationStatus').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.registration_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#certificateAwardingSchoolTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.school_form.certificate_awarding_school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#campusDistrictId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.district_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadCounties();
                    return data.text;
                },
            });

            $('#campusCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadSubCounties();
                    return data.text;
                },
            });

            $('#campusSubCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadParishes();
                    return data.text;
                },
            });

            $('#campusParishId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.parish_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#licenseNumberDateExpiry').datepicker({
                format: 'd MM, yyyy',
                // endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.school_form.licence_certificate_expiry_date = moment(e.date).format("D MMMM, YYYY");
            });

            let school_phone = document.querySelector('#campusPhone');
            let iti_school_phone = intlTelInput(school_phone, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });

            school_phone.addEventListener('blur', ()=>{
                self.school_form.phone = iti_school_phone.getNumber();
                school_phone.value = iti_school_phone.getNumber();
            });
            school_phone.addEventListener('change', ()=>{
                self.school_form.phone = iti_school_phone.getNumber();
                school_phone.value = iti_school_phone.getNumber();
            });

            this.resetSectionB();
            this.resetCampuses();
        },
        allInstitutionTypes: function () {
            axios.get('/institutions/surveys/all-certificate-institution-types')
                .then(response=>{
                    this.all_institution_types = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        cleanUpEstablishmentCapital: function () {
            if (this.school_form.capital_for_establishment.length) {
                this.school_form.capital_for_establishment = this.school_form.capital_for_establishment.replace(/[^0-9]/g,"");
            }

            if (this.school_form.capital_for_establishment.length) {
                this.school_form.capital_for_establishment = moneyFormat.to(moneyFormat.from(this.school_form.capital_for_establishment));
            }
        },
        formatMoney: function (raw_money = '') {
            if (typeof raw_money === "string" && raw_money.length) {
                return moneyFormat.to(moneyFormat.from(raw_money.replace(/[^0-9]/g,"")));
            } else if(typeof raw_money === "number") {
                return moneyFormat.to(moneyFormat.from(raw_money.toString().replace(/[^0-9]/g,"")));
            }
            return null
        },
        updateSectionB: function () {
            this.loading = true;
            axios.post(this.api_url+"/"+this.survey.id, this.school_form)
                .then(response=>{
                    this.school = response.data.school;
                    this.$parent.school = response.data.school;
                    this.$parent.survey = response.data.survey;
                    this.resetSectionB();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Institution Particulars Updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetSectionB: function() {
            $('#updateSectionBModal').modal('hide');
            this.loading = false;

            let gender = '';
            if (this.school.has_male_students && this.school.has_female_students) {
                gender = 'mixed';
            } else if (this.school.has_male_students && !this.school.has_female_students) {
                gender = 'male';
            } else if (!this.school.has_male_students && this.school.has_female_students) {
                gender = 'female';
            }

            let boarding = '';
            if (this.school.certificate_school.admits_day_scholars_yn && this.school.certificate_school.admits_boarders_yn) {
                boarding = 'both';
            } else if (this.school.certificate_school.admits_day_scholars_yn && !this.school.certificate_school.admits_boarders_yn) {
                boarding = 'day';
            } else if (!this.school.certificate_school.admits_day_scholars_yn && this.school.certificate_school.admits_boarders_yn) {
                boarding = 'boarding';
            }

            this.school_form = {
                certificate_awarding_school_type_id: this.school.certificate_school.certificate_awarding_school_type_id === null ? '' : this.school.certificate_school.certificate_awarding_school_type_id,
                legal_ownership_status_id: this.school.legal_ownership_status_id === null ? '' : this.school.legal_ownership_status_id,
                founding_body_id: this.school.founding_body_id === null ? '' : this.school.founding_body_id,
                funding_source_id: this.school.funding_source_id === null ? '' : this.school.funding_source_id,
                registering_body_id: this.school.certificate_school.registering_body_id === null ? '' : this.school.certificate_school.registering_body_id,
                year_founded: this.school.year_founded,
                registration_status_id: this.school.registration_status_id === null ? '' : this.school.registration_status_id,
                registration_number: this.school.registration_number,
                license_number: this.school.license_number,
                licence_certificate_expiry_date: this.school.licence_certificate_expiry_date,
                supply_number: this.school.certificate_school.supply_number,
                center_number: this.school.center_number,
                capital_for_establishment: this.school.capital_for_establishment,
                sex_composition: gender,
                day_or_boarding: boarding,
                health_facility_distance_range_id: this.school.health_facility_distance_range_id === null ? '' : this.school.health_facility_distance_range_id,
                section_id: '',
            }
            this.school_form.section_id = this.sectionId;

            window.setTimeout(()=>{
                $('#schoolLegalOwnershipStatusId').val(this.school_form.legal_ownership_status_id).change();
                $('#schoolFoundingBodyId').val(this.school_form.founding_body_id).change();
                $('#schoolFundingSourceId').val(this.school_form.funding_source_id).change();
                $('#schoolAuthorityId').val(this.school_form.registering_body_id).change();
                $('#schoolYearFounded').val(this.school.year_founded).change();
                $('#schoolRegistrationStatus').val(this.school_form.registration_status_id).change();
                $('#schoolSexComposition').val(this.school_form.sex_composition).change();
                $('#schoolDayOrBoarding').val(this.school_form.day_or_boarding).change();
                $('#schoolHealthDistanceId').val(this.school_form.health_facility_distance_range_id).change();
            }, 50)
        },
        updateCampuses: function () {
            this.loading = true;
            axios.post(this.api_url+"/add-campuses/"+this.survey.id, this.form_campus)
                .then(response=>{
                    this.school.certificate_school.campuses = response.data.certificate_school.campuses;
                    this.resetCampuses();
                    this.$refs.notifySuccessCampuses.messages.push({status: 'success', title: 'Success:', message:"Campus added successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetCampuses: function() {
            $('#updateCampusesModal').modal('hide');
            this.loading = false;

            this.has_campuses = this.school.certificate_school.campuses.length ? 'yes' : 'no';

            this.form_campus = {
                district_id: this.school.certificate_school.campuses.district_id,
                county_id: this.school.certificate_school.campuses.county_id,
                sub_county_id: this.school.certificate_school.campuses.sub_county_id,
                parish_id: this.school.certificate_school.campuses.parish_id,
                name: this.school.certificate_school.campuses.name,
                phone: this.school.certificate_school.campuses.phone,
            }
            this.form_campus.section_id = this.sectionId;

            window.setTimeout(()=>{
                $('#campusDistrictId').val(this.school.certificate_school.campuses.district_id).trigger('change');
                window.setTimeout(()=>{
                    $('#campusCountyId').val(this.school.certificate_school.campuses.county_id).trigger('change');
                    window.setTimeout(()=>{
                        $('#campusSubCountyId').val(this.school.certificate_school.campuses.sub_county_id).trigger('change');
                        window.setTimeout(()=>{
                            $('#campusParishId').val(this.school.certificate_school.campuses.parish_id).trigger('change');
                        }, 50);
                    }, 50);
                }, 50);
            }, 50);
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        founderYears: function () {
            let years = [];

            for (let i = Number(moment().format('YYYY')); i >= 1870; i--) {
                years.push(i);
            }

            return years;
        },
        examiningBody: function () {
            let body = {
                acronym: "",
                name: "",
            }

            if (this.school.certificate_school.certificate_awarding_school_type_id !== null) {
                return this.school.certificate_school.school_type.institution_category.examining_body;
            }

            if (this.school_form.certificate_awarding_school_type_id !== '') {
                let find = this.all_institution_types.find(type=>{
                    return type.id === this.school_form.certificate_awarding_school_type_id;
                });

                if (find !== undefined) {
                    body = find.institution_category.examining_body;
                }
            }

            return body;
        },
    },
}
</script>

<style scoped></style>
