<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <error-notifications ref="notifyError"></error-notifications>
        <div class="row">
            <div class="col-12 d-flex flex-row">
                <div class="d-flex flex-row">
                    <div class="d-flex flex-row align-self-center">
                        <div class="preview-icon-wrap mr-1 pb-0">
                            <em :class="[school.internet_source === null || school.internet_source.internet_source_id === '' || school.internet_source.internet_source_id === 1 ? 'text-muted':'text-dark-teal','ni ni-signal']"></em>
                        </div>
                        <h5 class="nice-title align-self-center">School Internet Source:</h5>
                    </div>
                    <span v-if="school.internet_source === null || school.internet_source.internet_source_id === ''" class="fs-15px ml-lg-3 align-self-center text-muted font-italic">Not Set</span>
                    <span v-else class="fs-15px ml-lg-3 align-self-center text-uppercase">{{ school.internet_source.source.name }}</span>
                </div>
                <div class="d-flex ml-3">
                    <button @click="editInternetSource()" type="button" class="btn btn-sm bg-dark-teal align-self-center">Update</button>
                </div>
            </div>
            <div class="col-12 d-flex mt-3 flex-row">
                <div class="d-flex flex-row">
                    <div class="d-flex flex-row">
                        <div class="preview-icon-wrap align-self-center mr-1 pb-0">
                            <em v-if="school.internet_source !== null && school.internet_source.connectivity_status === 1" class="ni ni-wifi text-dark-teal"></em>
                            <em v-if="school.internet_source === null || school.internet_source.connectivity_status === 0" class="ni ni-wifi-off text-muted"></em>
                        </div>
                        <h5 class="nice-title align-self-center">Internet Connectivity Status:</h5>
                    </div>
                    <div class="d-flex flex-row">
                        <label class="mr-2 ml-lg-3 mb-0 align-self-center" for="internetConnectivityStatus">In-Active</label>
                        <div class="custom-control custom-switch custom-control-inline align-self-center">
                            <input :disabled="school.internet_source === null || school.internet_source.internet_source_id === '' || school.internet_source.internet_source_id === 1" @change="updateInternetConnectivityStatus()" v-model="toggle" type="checkbox" class="custom-control-input custom-control-input-dark-teal" id="internetConnectivityStatus">
                            <label class="custom-control-label custom-control-label-dark-teal" for="internetConnectivityStatus">Active</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade zoom" tabindex="-1" id="internetSourcesModal">
                <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetInternetSources()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <form @submit.prevent="updateInternetSources()">
                            <div class="modal-header">
                                <h6 class="modal-title">Update Internet Source</h6>
                            </div>
                            <div class="modal-body">
                                <div class="row g-4">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">Internet Source</label>
                                            <select required id="internetSourceId" data-placeholder="--Select--" class="form-select-sm">
                                                <option disabled>--Select--</option>
                                                <option v-for="source in internet_sources" :value="source.id">{{ source.name.toUpperCase() }}</option>
                                            </select>
                                        </div>
                                    </div><!-- .col -->
                                </div><!-- .row -->
                            </div>
                            <div class="modal-footer d-flex justify-content-center">
                                <button @click="resetInternetSources()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Saving...</span>
                                    <span v-if="loading" class="sr-only">Loading...</span>
                                    <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Loading Modal -->
            <div style="display:none;" id="internetConnectivityStatusLoadingMessage" class="card card-preview">
                <div class="card-inner">
                    <div class="d-flex align-items-center">
                        <strong>Updating...</strong>
                        <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
                    </div>
                </div>
            </div>
            <!-- /Loading Modal -->
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "InternetSources",
    props: [
        'schoolObj',
        'surveyObj',
        'sectionId',
        'allInternetSourcesObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            toggle: false,
            api_url: '/institutions/surveys/ict-facilities/',
            internet_sources: [],
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            current_section: {name:'', logical:''},
            school: {
                internet_source: {
                    internet_source_id: '',
                    connectivity_status: 0,
                    source: {
                        name: '',
                    },
                },
            },
            form_internet_source: {
                internet_source_id: '',
                connectivity_status: 0,
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_internet_source.section_id = this.sectionId;
            this.form_internet_source.survey_id = this.survey.id;

            if (this.allInternetSourcesObj !== '') {
                this.internet_sources = this.allInternetSourcesObj;
                this.toggle = this.school.internet_source !== null && this.school.internet_source.connectivity_status === 1;
            }

            $('#internetSourceId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_internet_source.internet_source_id = data.id;
                    return data.text;
                },
            });
        },
        editInternetSource: function () {
            if (this.school.internet_source === null) {
              $('#internetSourceId').val('').change();
            } else {
              $('#internetSourceId').val(this.school.internet_source.internet_source_id).change();
            }

            $('#internetSourcesModal').modal({backdrop: "static"});
        },
        updateInternetConnectivityStatus: function () {
            // this.startLoading()
            this.form_internet_source.connectivity_status = this.toggle ? 1 : 0;
            axios.post(this.api_url+'update-internet-source/'+this.survey.id, this.form_internet_source)
                .then(response => {
                    this.school.internet_source = response.data.internet_source;
                    this.resetInternetSources();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Internet Connectivity Status Updated Successfully"});
                })
                .catch(error=>{
                    console.log(error);
                    this.renderError(error);
                })
        },
        updateInternetSources: function () {
            this.loading = true;
            axios.post(this.api_url+'update-internet-source/'+this.survey.id, this.form_internet_source)
                .then(response => {
                    this.school.internet_source = response.data.internet_source;
                    this.school.ict_facilities = response.data.ict_facilities;
                    this.$emit('updated-internet-source', response.data.ict_facilities);
                    this.resetInternetSources();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Internet Source Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetInternetSources: function () {
            this.loading = false;
            this.form_internet_source.internet_source_id = '';
            this.form_internet_source.connectivity_status = 0;
            this.form_internet_source.section_id = this.sectionId;
            this.toggle = this.school.internet_source !== null && this.school.internet_source.connectivity_status === 1;
            $('#internetSourcesModal').modal("hide");
            if (this.school.internet_source === null) {
              $('#internetSourceId').val('').change();
            } else {
              $('#internetSourceId').val(this.school.internet_source.internet_source_id).change();
            }
        },
        startLoading: function() {
            $.blockUI({
                message: $('#internetConnectivityStatusLoadingMessage'),
                css: {
                    padding:0,
                    margin:0,
                    width:'30%',
                    top:'40%',
                    left:'35%',
                    textAlign:'center',
                    color:'#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor:'wait'
                },
            });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>
.nice-title {
    font-size: 1.15rem;
    letter-spacing: -0.01rem;
    font-family: "Roboto", sans-serif;
    font-weight: 500;
}
</style>
