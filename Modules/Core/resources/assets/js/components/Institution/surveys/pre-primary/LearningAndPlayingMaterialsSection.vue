<template>
    <div>
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <error-notifications ref="notifyError"></error-notifications>
        <div class="table-responsive">
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase" rowspan="2">Learning Materials</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2" :colspan="education_grades.length + 1">Class</th>
                </tr>
                <tr>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white" v-for="grade in education_grades">{{ grade.name.toUpperCase() }}</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
                </thead>
                <tbody class="border-top-0 border-dark-teal" v-for="category in categories">
                <tr>
                    <th class="text-dark-teal py-2 border-bottom border-top border-dark-teal text-center" :colspan="education_grades.length + 2">{{ category.name.toUpperCase() }}</th>
                </tr>
                <tr v-for="material in category.materials">
                    <td class="align-middle">{{ material.name.toUpperCase() }}</td>
                    <td class="align-middle border-left text-center" v-for="(grade, index) in education_grades">
                        <div v-if="editMaterial === material.id" class="form-group mx-auto">
                            <div class="form-control-wrap">
                                <input v-model.number="form_report[index].quantity" type="number" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+grade.id">
                            </div>
                        </div>
                        <span v-else>{{ getMaterialQuantity(material.id, grade.id) }}</span>
                    </td>
                    <td class="align-middle border-left text-center">
                        <span v-if="editMaterial !== material.id" @click="toggleUpdate(material.id)" class="cursor btn-sm btn-primary">
                            <em class="icon ni ni-edit-fill"></em><span>Update</span>
                        </span>
                        <div v-if="editMaterial === material.id" @click="saveUpdates()" class="cursor btn-sm bg-dark-teal">
                            <em class="icon ni ni-check"></em><span>Save</span>
                        </div>
                        <div v-if="editMaterial === material.id" @click="toggleUpdate(material.id)" class="cursor btn-sm text-white bg-secondary mt-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>

</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";

export default {
    name: "LearningAndPlayingMaterialsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'educationGradesObj',
        'learningAndPlayingMaterialsObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/learning-playing-materials',
            school: {
                learning_materials: []
            },
            education_grades: [],
            categories: [],
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            form_report: [],
            editMaterial: '',
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.education_grades = this.educationGradesObj;
            this.categories = this.learningAndPlayingMaterialsObj;

            this.education_grades.forEach((grade, index)=>{
                this.form_report[index] = {
                    learning_material_id: '',
                    education_grade_id: grade.id,
                    quantity: 0,
                }
            });
        },

        getMaterialQuantity: function (materialId, gradeId) {
            let qtyReport = this.school.learning_materials.find(materialReport=>{
                return materialReport.education_grade_id === gradeId && materialReport.learning_material_id === materialId;
            })

            if (qtyReport === undefined) {
                return 0;
            } else {
                return qtyReport.quantity;
            }
        },
        toggleUpdate: function (materialId) {
            if (this.editMaterial === "" || this.editMaterial !== materialId) {
                this.editMaterial = materialId;

                this.education_grades.forEach((grade, index)=>{
                    let exists = this.school.learning_materials.find(entry=>{
                        return entry.education_grade_id === grade.id && entry.learning_material_id === materialId;
                    });

                    if (exists !== undefined) {
                        this.form_report[index].quantity = exists.quantity;
                        this.form_report[index].learning_material_id = materialId;
                        this.form_report[index].section_id = this.sectionId;
                        this.form_report[index].survey_id = this.survey.id;
                    } else {
                        this.form_report[index].quantity = 0;
                        this.form_report[index].learning_material_id = materialId;
                        this.form_report[index].section_id = this.sectionId;
                        this.form_report[index].survey_id = this.survey.id;
                    }
                });
            }
            else {
                this.editMaterial = "";
                this.form_report.quantity = 0
                this.form_report.section_id = this.sectionId;
                this.form_report.survey_id = this.survey.id;
            }
        },

        saveUpdates: function () {
            axios.post(this.api_url+'/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.learning_materials = response.data.learning_materials
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success', message:"Learning and playing updated successfully"});

                    this.editMaterial = "";
                    this.form_report.learning_material_id = ''
                    this.quantity = 0
                    this.form_report.section_id = this.sectionId;
                    this.form_report.survey_id = this.survey.id;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
            });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
