<template>
    <div class="w-100">
        <div class="nk-block-head">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h6 class="nk-block-title text-uppercase">Sports Equipment</h6>
                    <div class="nk-block-des">
                        <p>How many Sports equipment does this institution have?</p>
                    </div>
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div>
        <success-notifications ref="notifySuccess"></success-notifications>
        <error-notifications ref="notifyError"></error-notifications>

        <div class="table-responsive">
            <table class="table border border-secondary">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase">Category</th>
                    <th class="text-white align-middle text-uppercase border-left border-white">Sports Equipment</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
                </thead>
                <tbody class="border-top-0 border-left border-secondary" v-for="category in categories">

                    <tr v-for="item in category.equipments" class="border-bottom border-secondary">
                        <td class="text-left pl-3 border-left border-secondary">{{ category.name.toUpperCase() }}</td>
                        <td class="text-left pl-3 border-left border-secondary">{{ item.name.toUpperCase() }}</td>
                        <td class="text-center pl-3 border-left border-secondary">
                            <div v-if="editEquipment === item.id" class="form-group mx-auto">
                                <div class="form-control-wrap">
                                    <input v-model.number="form_report.quantity" type="number" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+item.id">
                                </div>
                            </div>
                            <span v-else>{{ getEquipmentQuantity(item.id) }}</span>
                        </td>
                        <td class="align-middle border-left border-secondary">
                            <span v-if="editEquipment !== item.id" @click="toggleUpdate(item.id)" class="cursor btn-sm btn-primary">
                                <em class="icon ni ni-edit-fill"></em><span>Update</span>
                            </span>
                            <span v-if="editEquipment === item.id" @click="saveUpdates()" class="cursor btn-sm bg-dark-teal">
                                <em class="icon ni ni-check"></em><span>Save</span>
                            </span>
                            <span v-if="editEquipment === item.id" @click="toggleUpdate(item.id)" class="cursor btn-sm text-white bg-secondary">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "SportsEquipmentSection",
    props: [
        'sportsEquipmentCategoriesObj',
        'sportsEquipmentObj',
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/sports-equipment',
            school: {
                sports_equipment: []
            },
            categories: [],
            equipment: [],
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            form_report: {
                section_id: '',
                survey_id: '',
                equipment_id: '',
                quantity: 0,
            },
            editEquipment: '',
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.categories = this.sportsEquipmentCategoriesObj;
            this.equipment = this.sportsEquipmentObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
        },

        getEquipmentQuantity: function (materialId, gradeId) {
            let qtyReport = this.school.sports_equipment.find(materialReport=>{
                return materialReport.education_grade_id === gradeId && materialReport.equipment_id === materialId;
            })

            if (qtyReport === undefined) {
                return 0;
            } else {
                return qtyReport.quantity;
            }
        },
        toggleUpdate: function (materialId) {
            if (this.editEquipment === "" || this.editEquipment !== materialId) {
                this.editEquipment = materialId;
                this.form_report.equipment_id = materialId;

                let exists = this.school.sports_equipment.find(entry=>{
                    return entry.equipment_id === materialId;
                });

                if (exists !== undefined) {
                    this.form_report.quantity = exists.quantity;
                } else {
                    this.form_report.quantity = 0;
                }
            } else {
                this.editEquipment = "";
                this.form_report.equipment_id = ''
                this.quantity = 0
                this.form_report.section_id = this.sectionId;
                this.form_report.survey_id = this.survey.id;
            }
        },
        saveUpdates: function () {
            axios.post(this.api_url+'/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.sports_equipment = response.data.sports_equipment
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Sports Equipment Updated Successfully"});

                    this.editEquipment = "";

                    this.form_report.equipment_id = ''
                    this.quantity = 0
                    this.form_report.section_id = this.sectionId;
                    this.form_report.survey_id = this.survey.id;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
