export default (await import('vue')).defineComponent({
name: "FileUploads",
props: ['fileUploadsObj'],
components: {
ErrorNotifications,
SuccessNotifications,
},
mounted() {
this.initPlugins();
},
data: function () {
return {
loading: false,
file_uploads: { current_page: -1, data: [], links: [], total: 0 },
api_url: '/institutions/file-uploads',
};
},
methods: {
initPlugins: function () {
let self = this;
// console.log(this.fileUploadsObj);
this.file_uploads = this.fileUploadsObj;
},
processUgandanLearnersUploads: function (fileUpload) {
this.loading = true;
//TODO
axios.get("/institutions/learners/import-ugandans/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processForeignLearnersUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/learners/import-non-refugees/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processRefugeeLearnersUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/learners/import-refugees/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processUgandanTeachersUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/teachers/import-ugandans/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processForeignTeachersUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/teachers/import-foreigners/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processUgandanNonTeachingStaffUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/non-teaching-staff/import-ugandans/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
processForeignNonTeachingUploads: function (fileUpload) {
this.loading = true;
axios.get("/institutions/non-teaching-staff/import-foreigners/process/" + fileUpload.id)
.then(() => {
this.preview = false;
this.loading = false;
this.$refs.notifySuccess.messages.push({
status: 'success',
title: 'Success!',
message: fileUpload.file_name + " is being processed and verified."
});
window.setTimeout(() => location.reload(), 1000);
})
.catch(error => {
this.loading = false;
this.renderError(error);
});
},
getFileType: function (fileUpload) {
switch (fileUpload.file_type) {
case "ugandan_learners":
return fileUpload.user.school_type_id > 3 ? "Ugandan Students" : "Ugandan Learners";

case "foreigner_learners":
return fileUpload.user.school_type_id > 3 ? "Foreign Students" : "Foreign Learners";

case "refugee_learners":
return fileUpload.user.school_type_id > 3 ? "Refugee Students" : "Refugee Learners";

case "ugandan_teachers":
return fileUpload.user.school_type.name === "preprimary" ? "Ugandan Caregivers" : "Ugandan Teachers";

case "foreigner_teachers":
return fileUpload.user.school_type.name === "preprimary" ? "Foreign Caregivers" : "Foreign Teachers";

case "ugandan_support_staff":
return fileUpload.user.school_type.name === "preprimary" ? "Ugandan Support Staff" : "Ugandan Non Teaching Staff";

case "foreigner_support_staff":
return fileUpload.user.school_type.name === "preprimary" ? "Foreign Support Staff" : "Foreign Non Teaching Staff";

default:
return "";
}
},
downloadExcel: function (excel) {
axios({
method: 'get',
url: excel.file_url + '?x=' + moment().unix(),
responseType: 'arraybuffer'
})
.then(response => {
let blob = new Blob([response.data], { type: 'application/octet-stream' });
let a = window.document.createElement('a');
a.href = window.URL.createObjectURL(blob, {
type: 'data:attachment/xlsx'
});
a.download = excel.file_name;
document.body.appendChild(a);
a.click();
window.URL.revokeObjectURL(a.href);
document.body.removeChild(a);
})
.catch(error => {
this.renderError(error);
});
},
renderError: function (error) {
if (error.response && (error.response.status === 500 || error.response.status === 405)) {
this.$refs.notifyError.messages.push({ status: 'error', title: 'System Error:', message: error.response.data.message });
} else if (error.response && error.response.status === 401) {
this.$refs.notifyError.messages.push({ status: 'error', title: 'Permission Error:', message: 'You are not authorised to perform this action' });
} else if (error.response && error.response.status === 404) {
this.$refs.notifyError.messages.push({ status: 'error', title: 'Resource Not Found:', message: 'You are trying to reach a url that does not exist' });
} else if (error.response && error.response.status === 422) {
let text = '';
for (let field in error.response.data.errors) {
for (let i = 0; i < error.response.data.errors[field].length; i++) {
text += '<br>' + error.response.data.errors[field][i];
}
}
this.$refs.notifyError.messages.push({ status: 'error', title: 'Data Error!', message: text });
} else {
this.$refs.notifyError.messages.push({ status: 'error', title: 'Other Error:', message: error.message });
}
$("html, body").animate({ scrollTop: 0 }, "slow");
},
fileUploadErrors: function (fileUpload) {
let errors = 0;
errors += fileUpload.ugandan_learners;
errors += fileUpload.foreign_learners;
errors += fileUpload.refugee_learners;
errors += fileUpload.ugandan_teachers;
errors += fileUpload.foreign_teachers;
errors += fileUpload.ugandan_non_teaching_staff;
errors += fileUpload.foreign_non_teaching_staff;

return errors;
},
getLinkClasses: function (link = null) {
let css = "page-item";

if (link !== null) {
if (link.active) {
css += " active-dark-teal disabled";
}

if (link.url === null) {
css += " disabled";
}
}

return css;
},
loadFileUploads: function (page, filtering = false) {
if (!isNaN(Number(page))) {
this.loading = true;
axios.post(this.api_url + '?page=' + page, this.filter)
.then(response => {
this.loading = false;
this.file_uploads = response.data;
if (filtering) {
this.filtering = true;
}
})
.catch(error => {
this.loading = false;
console.log(error);
this.renderError(error);
});
}
},
},
computed: {
getPaginationLinks: function () {
let arr = this.file_uploads.links;
arr.pop();
arr.shift();
return arr;
},
}
});
