<template>
    <div class="w-100 vertical-scrollable">
        <survey-certificate-sports-equipment
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :sports-equipment-categories-obj="sportsEquipmentCategoriesObj"
            :sports-equipment-obj="sportsEquipmentObj"
        ></survey-certificate-sports-equipment>
        <survey-certificate-sports-activities
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :sports-activities-obj="sportsActivitiesObj"
        ></survey-certificate-sports-activities>
        <survey-certificate-sports-facilities
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :sports-facilities-obj="sportsFacilitiesObj"
        ></survey-certificate-sports-facilities>
    </div>
</template>

<script>

export default {
    name: "SportsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'sportsEquipmentCategoriesObj',
        'sportsEquipmentObj',
        'sportsFacilitiesObj',
        'sportsActivitiesObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
