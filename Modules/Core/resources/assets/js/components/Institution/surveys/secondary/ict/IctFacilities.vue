<template>
	<div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <error-notifications ref="notifyError"></error-notifications>
        <div class="nk-block">
            <div class="card">
                <div class="card-inner-group">
                    <div class="card-inner p-0">
                        <survey-internet-sources class="mb-5"
                             @updated-internet-source="updateIctFacilitiesAgain"
                             ref="internetSources"
                             :section-id="current_section.id"
                             :school-obj="schoolObj"
                             :survey-obj="surveyObj"
                             :all-internet-sources-obj="allInternetSourcesObj"
                        ></survey-internet-sources>

                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item bg-secondary nk-tb-head">
                                <div class="nk-tb-col"><span class="sub-text text-uppercase text-white">Facility User</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Functional Computers</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Non Functional Computers</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Computers with Internet Access</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Total</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Actions</span></div>
                            </div><!-- .nk-tb-item -->
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-dark text-uppercase">Learners</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_non_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_with_internet }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_functional + learnerFacilities.total_computers_non_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <button @click="editIctFacilities(0)" class="btn btn-sm bg-dark-teal" type="button">Update</button>
                                </div>
                            </div><!-- .nk-tb-item -->
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-dark text-uppercase">Teachers</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_non_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_with_internet }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                    <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_functional + teacherFacilities.total_computers_non_functional }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <button @click="editIctFacilities(1)" class="btn btn-sm bg-dark-teal" type="button">Update</button>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                    </div><!-- .card-inner -->
                </div>
            </div>
        </div>
		<div class="modal fade zoom" tabindex="-1" id="ictFacilitiesModal">
			<div class="modal-dialog modal-sm modal-dialog-centered" role="document">
				<div class="modal-content">
					<a @click="resetIctFacilities()" class="cursor close" data-dismiss="modal" aria-label="Close">
						<em class="icon ni ni-cross"></em>
					</a>
					<form @submit.prevent="updateIctFacilities()">
						<div class="modal-header">
							<h6 class="modal-title">Edit {{ form_ict_facility.is_for_teachers === 1 ? "Teacher ICT Facilities" : "Learner ICT Facilities" }}</h6>
						</div>
						<div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
							<div class="row g-4">
								<div class="col-12">
									<div class="form-group">
										<div class="form-label-group">
											<label for="ictFacilityTotalComputersFunctional" class="form-label">Total Functional Computers <span class="text-danger">*</span></label>
										</div>
										<div class="form-control-group">
											<input min="0" v-model.number="form_ict_facility.total_computers_functional" id="ictFacilityTotalComputersFunctional" type="number" class="form-control text-center bg-primary-dim" required>
										</div>
									</div>
								</div><!-- .col -->
							</div><!-- .row -->
							<div class="row g-4">
								<div class="col-12">
									<div class="form-group">
										<div class="form-label-group">
											<label for="ictFacilityTotalComputersNonFunctional" class="form-label">Total Non Functional Computers <span class="text-danger">*</span></label>
										</div>
										<div class="form-control-group">
											<input min="0" v-model.number="form_ict_facility.total_computers_non_functional" id="ictFacilityTotalComputersNonFunctional" type="number" class="form-control text-center bg-primary-dim" required>
										</div>
									</div>
								</div><!-- .col -->
							</div><!-- .row -->
							<div class="row g-4">
								<div class="col-12">
									<div class="form-group">
										<div class="form-label-group">
											<label for="ictFacilityTotalComputersWithInternetAccess" class="form-label">Total Computers With Internet Access <span class="text-danger">*</span></label>
										</div>
										<div class="form-control-group">
											<input min="0" v-model.number="form_ict_facility.total_computers_with_internet" id="ictFacilityTotalComputersWithInternetAccess" type="number" class="form-control text-center bg-primary-dim" required>
										</div>
									</div>
								</div><!-- .col -->
							</div><!-- .row -->
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button @click="resetIctFacilities()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button :disabled="loading" type="submit" class="btn btn-primary d-flex">
								<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="loading" class="align-self-center">Saving...</span>
								<span v-if="loading" class="sr-only">Loading...</span>
								<span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
    import ErrorNotifications from "../../../../Notifications.vue";
    import SuccessNotifications from "../../../../Notifications.vue";
	export default {
		name: "IctFacilities",
		props: [
            'sectionId',
            'schoolObj',
            'surveyObj',
            'allInternetSourcesObj',
        ],
		components: {
            ErrorNotifications,
            SuccessNotifications
        },
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
				edit: false,
                api_url: '/institutions/surveys/ict-facilities/',
				ict_facilities: [],
                survey: {
                    current_section_id: '',
                    sections: [{name:'', is_complete_yn:false}],
                    section_items: [{name:'', section_id:'', is_complete_yn:false}],
                    survey: {name: ''},
                },
                current_section: {name:'', logical:''},
                school: {
                    ict_facilities: []
                },
				form_ict_facility: {
                    id: '',
                    section_id: '',
                    survey_id: '',
					total_computers_functional: 0,
					total_computers_non_functional: 0,
					total_computers_with_internet: 0,
					is_for_teachers: 0,
				},
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;

                this.school = this.schoolObj;
                this.survey = this.surveyObj;
                this.form_ict_facility.section_id = this.sectionId;
                this.form_ict_facility.survey_id = this.survey.id;
			},
			updateIctFacilitiesAgain: function (facilities) {
				this.school.ict_facilities = facilities;
			},
			editIctFacilities: function (is_for_teachers) {
				if (is_for_teachers === 1) {
					this.form_ict_facility.id = this.teacherFacilities === undefined ? '' : this.teacherFacilities.id;
					this.form_ict_facility.total_computers_functional = this.teacherFacilities === undefined ? 0 : this.teacherFacilities.total_computers_functional;
					this.form_ict_facility.total_computers_non_functional = this.teacherFacilities === undefined ? 0 : this.teacherFacilities.total_computers_non_functional;
					this.form_ict_facility.total_computers_with_internet = this.teacherFacilities === undefined ? 0 : this.teacherFacilities.total_computers_with_internet;
					this.form_ict_facility.is_for_teachers = is_for_teachers;
				} else if (is_for_teachers === 0) {
					this.form_ict_facility.id = this.learnerFacilities === undefined ? '' : this.learnerFacilities.id;
					this.form_ict_facility.total_computers_functional = this.learnerFacilities === undefined ? 0 : this.learnerFacilities.total_computers_functional;
					this.form_ict_facility.total_computers_non_functional = this.learnerFacilities === undefined ? 0 : this.learnerFacilities.total_computers_non_functional;
					this.form_ict_facility.total_computers_with_internet = this.learnerFacilities === undefined ? 0 : this.learnerFacilities.total_computers_with_internet;
					this.form_ict_facility.is_for_teachers = is_for_teachers;
				}
				$('#ictFacilitiesModal').modal({backdrop: "static"});
			},
			updateIctFacilities: function () {
				this.loading = true;
				// if (this.internetSourceObj === "") {
				// 	this.form_ict_facility.total_computers_with_internet = 0;
				// }
				axios.post(this.api_url+'update/'+this.survey.id, this.form_ict_facility)
				.then(response=>{
					this.school.ict_facilities = response.data.ict_facilities;
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"ICT Facilities Updated Successfully"});
					this.resetIctFacilities();
				})
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
			},
			resetIctFacilities: function () {
				this.loading = false;
				this.form_ict_facility.total_computers_functional = 0;
				this.form_ict_facility.total_computers_non_functional = 0;
				this.form_ict_facility.total_computers_with_internet = 0;
				this.form_ict_facility.is_for_teachers = 0;
                this.form_ict_facility.section_id = this.sectionId;
				$('#ictFacilitiesModal').modal("hide");
			},
            renderError: function (error) {
                if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                } else if (error.response && error.response.status === 401) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                } else if (error.response && error.response.status === 404) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                } else if (error.response && error.response.status === 422) {
                    let text = '';
                    for (let field in error.response.data.errors) {
                        for (let i = 0; i < error.response.data.errors[field].length; i++) {
                            text += '<br>'+ error.response.data.errors[field][i];
                        }
                    }
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
                } else {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                }
                $("html, body").animate({ scrollTop: 0 }, "slow");
            },
		},
		computed: {
			teacherFacilities: function () {
				return this.school.ict_facilities.find(facility=>{
					return facility.is_for_teachers === 1;
				});
			},
			learnerFacilities: function () {
				return this.school.ict_facilities.find(facility=>{
					return facility.is_for_teachers === 0;
				});
			},
		}
	}
</script>

<style scoped>

</style>
