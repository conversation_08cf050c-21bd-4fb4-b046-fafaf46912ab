<template>
    <div class="w-100 mt-5">
        <success-notifications ref="notifySuccess"></success-notifications>
        <h4>Garbage Disposal Method</h4>
        <div class="data-item py-1">
            <div class="data-col">
                <span class="" style="font-size: initial">Main Garbage Disposal Method</span>
                <span v-if="school.garbage_disposal_method === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.garbage_disposal_method.garbage.name }}</span>
            </div>
            <div class="data-col data-col-end">
                <button @click="editGarbageDisposal()" type="button" class="btn btn-sm bg-dark-teal">
                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                </button>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>


        <div class="modal fade zoom" tabindex="-1" id="garbageDisposalModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetGarbageDisposal()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateGarbageDisposal()">
                        <div class="modal-header">
                            <h6 class="modal-title">Update Garbage Disposal Method</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="garbageDisposalId">How is garbage finally disposed off ?</label>
                                    <span class="form-note">Specify the method used.</span>
                                </div>
                                <div class="form-group">
                                    <select v-model="form_report.garbage_disposal_method_id" required id="garbageDisposalId" class="form-select-sm">
                                        <option value="">--Select--</option>
                                        <option v-for="garbage in garbage_disposal_methods" :value="garbage.id">{{ garbage.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetGarbageDisposal()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";

export default {
    name: "GarbageDisposalMethodsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'garbageDisposalMethodsObj',
        'schoolGarbageDisposalMethodsObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/garbage-disposal-method',
            school: {
                garbage_disposal_method: {
                    garbage: {
                        name: ''
                    }
                },
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            garbage_disposal_methods: [],
            form_report: {
                section_id: '',
                survey_id: '',
                garbage_disposal_method_id: '',
            },

        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.garbage_disposal_methods = this.garbageDisposalMethodsObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;

            $('#garbageDisposalId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#garbageDisposalModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.garbage_disposal_method_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
        },

        editGarbageDisposal: function () {
            this.resetGarbageDisposal();

            $('#garbageDisposalModal').modal({backdrop: "static"});
        },
        updateGarbageDisposal: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.garbage_disposal_method = response.data.garbage_disposal_method;
                    this.resetGarbageDisposal();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Garbage Disposal Methods Updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetGarbageDisposal: function () {
            $('#garbageDisposalModal').modal("hide");
            this.loading = false;

            window.setTimeout(()=>{
                if (this.school.garbage_disposal_method === null) {
                    $('#garbageDisposalId').val('').change();
                } else {
                    $('#garbageDisposalId').val(this.school.garbage_disposal_method.garbage_disposal_method_id).change();
                }

            }, 50);

            this.form_report.section_id = this.sectionId;
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },

    },
    computed: {

    }
}
</script>

<style scoped>

</style>
