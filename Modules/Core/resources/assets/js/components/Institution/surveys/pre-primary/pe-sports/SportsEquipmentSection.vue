<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <error-notifications ref="notifyError"></error-notifications>
        <div class="data-item py-1">
            <div class="">
                <span class="data-label" style="font-size: initial">How many play grounds does the ECCE centre have/own?</span>
                <span v-if="school.sports_play_ground === null" class="ml-lg-5 data-value text-dark">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.sports_play_ground.no_of_play_grounds }}</span>
            </div>
            <div class="data-col data-col-end">
                <button @click="editPlayGroundsNumber()" data-backdrop="static" type="button" class="btn btn-sm bg-dark-teal">
                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                </button>
            </div>
        </div><!-- data-item -->
        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label" style="font-size: initial">What is the size of the play grounds in Square Meters?</span>
                    <span v-if="school.sports_play_ground === null" class="ml-lg-5 data-value text-dark">NOT SET</span>
                    <span v-else class="ml-lg-5 data-value text-dark" style="padding-left: 23px">{{ school.sports_play_ground.size_of_play_grounds }} SQM</span>
                </div>

            </div><!-- data-item -->
            <div class="data-col-end"></div>
        </div>
        <div class="table-responsive mt-5">
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Sports Equipment</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
                </thead>
                <tbody class="border-top-0 border-dark-teal">
                <tr v-for="item in equipment">
                    <td class="align-middle">{{ item.name.toUpperCase() }}</td>
                    <td class="align-middle border-left text-center" >
                        <div v-if="editEquipment === item.id" class="form-group mx-auto">
                            <div class="form-control-wrap">
                                <input v-model.number="form_report.quantity" type="number" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+item.id">
                            </div>
                        </div>
                        <span v-else>{{ getEquipmentQuantity(item.id) }}</span>
                    </td>
                    <td class="align-middle border-left text-center">
                        <span v-if="editEquipment !== item.id" @click="toggleUpdate(item.id)" class="cursor btn-sm btn-primary">
                            <em class="icon ni ni-edit-fill"></em><span>Update</span>
                        </span>
                        <span v-if="editEquipment === item.id" @click="saveUpdates()" class="cursor btn-sm bg-dark-teal">
                            <em class="icon ni ni-check"></em><span>Save</span>
                        </span>
                        <span v-if="editEquipment === item.id" @click="toggleUpdate(item.id)" class="cursor btn-sm text-white bg-secondary">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="playGroundModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="saveUpdates()">
                        <div class="modal-header">
                            <h5 class="modal-title">UPDATE ECCE CENTRE PLAY GROUNDS</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">How many play grounds does the ECCE centre have/own?</label>
                                        <input  v-model.number="form_report.no_of_play_grounds" type="number" placeholder="Enter Number" class="form-control">
                                    </div>
                                </div>

                            </div>
                            <div class="row px-4">
                                <div class="col-12 mt-3">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">What is the size of the play grounds in Square Meters?</label>
                                        <input  v-model.number="form_report.size_of_play_grounds" type="number" placeholder="Enter Number" class="form-control">
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "SportsEquipmentSection",
    props: [
        'prePrimaryEquipmentObj',
        'schoolSportsEquipmentUpdatesObj',
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/sports-equipment',
            school: {
                sports_equipment: [],
                sports_play_ground: {
                    no_of_play_grounds: '',
                    size_of_play_grounds: ''
                },
            },
            equipment: [],
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            form_report: {
                section_id: '',
                survey_id: '',
                equipment_id: '',
                quantity: 0,
                no_of_play_grounds: '',
                size_of_play_grounds: ''
            },
            editEquipment: '',
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.equipment = this.prePrimaryEquipmentObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
        },

        editPlayGroundsNumber: function () {
            this.edit = true;
            if (this.school.sports_play_ground !== null) {
                this.form_report.no_of_play_grounds = this.school.sports_play_ground.no_of_play_grounds === null ? 0 : this.school.sports_play_ground.no_of_play_grounds;
                this.form_report.size_of_play_grounds = this.school.sports_play_ground.size_of_play_grounds === null ? 0 : this.school.sports_play_ground.size_of_play_grounds;
            }
            $('#playGroundModal').modal({backdrop:'static'});
        },

        getEquipmentQuantity: function (materialId, gradeId) {
            let qtyReport = this.school.sports_equipment.find(materialReport=>{
                return materialReport.education_grade_id === gradeId && materialReport.equipment_id === materialId;
            })

            if (qtyReport === undefined) {
                return 0;
            } else {
                return qtyReport.quantity;
            }
        },
        toggleUpdate: function (materialId) {
            if (this.editEquipment === "" || this.editEquipment !== materialId) {
                this.editEquipment = materialId;
                this.form_report.equipment_id = materialId;

                let exists = this.school.sports_equipment.find(entry=>{
                    return entry.equipment_id === materialId;
                });

                if (exists !== undefined) {
                    this.form_report.quantity = exists.quantity;
                } else {
                    this.form_report.quantity = 0;
                }
            } else {
                this.editEquipment = "";
                this.form_report.equipment_id = ''
                this.quantity = 0
                this.form_report.section_id = this.sectionId;
                this.form_report.survey_id = this.survey.id;
            }
        },
        saveUpdates: function () {
            axios.post(this.api_url+'/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.sports_equipment = response.data.sports_equipment
                    this.school.sports_play_ground = response.data.sports_play_ground
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Sports Updated Successfully"});

                    this.editEquipment = "";

                    this.form_report.equipment_id = ''
                    this.quantity = 0
                    this.form_report.section_id = this.sectionId;
                    this.form_report.survey_id = this.survey.id;
                    $('#playGroundModal').modal('hide');
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
