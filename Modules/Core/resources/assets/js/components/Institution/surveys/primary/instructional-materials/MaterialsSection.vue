<template>
    <div class="w-100 vertical-scrollable">
        <survey-primary-textbooks
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :education-grades-obj="educationGradesObj"
            :textbooks-obj="textbooksObj"
        ></survey-primary-textbooks>
        <survey-primary-reference-books
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :education-grades-obj="educationGradesObj"
            :primary-subjects-obj="primarySubjectsObj"
            :reference-books-obj="referenceBooksObj"
        ></survey-primary-reference-books>
        <survey-primary-sne-kits
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :education-grades-obj="educationGradesObj"
            :sne-kits-obj="sneKitsObj"
        ></survey-primary-sne-kits>
        <survey-primary-wall-charts
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :education-grades-obj="educationGradesObj"
            :wall-charts-obj="wallChartsObj"
        ></survey-primary-wall-charts>
    </div>
</template>

<script>

export default {
    name: "MaterialsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'educationGradesObj',
        'textbooksObj',
        'primarySubjectsObj',
        'referenceBooksObj',
        'sneKitsObj',
        'wallChartsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
