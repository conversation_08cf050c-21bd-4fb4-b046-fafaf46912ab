<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <form @submit.prevent="verify ? createTeacher() : (uganda ? verifyNIN() : verifyWorkPermit())">
            <div class="row">
                <div class="col-lg-4 col-md-12">
                    <div class="form-group d-flex flex-column justify-content-center">
                        <label class="align-self-center form-label">Add Photo</label>
                        <input
                            ref="photo" @change="selectFile"
                            accept="image/x-png,image/jpeg"
                            data-max-file-size="2M"
                            id="teacherWithPhoto"
                            :disabled="verify"
                            type="file"
                            class="dropify"
                            data-height="190"
                            data-allowed-file-extensions="jpeg jpg png"
                            :data-default-file="teacher.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                    </div>
                    <div class="d-flex flex-column justify-content-center">
                        <button :disabled="verify" @click="teacher.photo === null || teacher.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                            <em class="icon ni ni-camera-fill"></em>
                            <span v-if="teacher.photo === null || teacher.photo === ''">Choose Photo</span>
                            <span v-else>Remove Photo</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 overflow-auto scrollbar-dark-teal h-425px">
                    <h6 class="overline-title title text-dark-teal">TEACHER DETAILS</h6>
                    <div class="row mt-3">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is this staff member a refugee?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="yes" id="staffIsRefuge">
                                    <label class="custom-control-label text-uppercase" for="staffIsRefuge">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="no" id="staffIsNotRefugee">
                                    <label class="custom-control-label text-uppercase" for="staffIsNotRefugee">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                    <select :disabled="verify" required id="teacherCountryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-if="uganda" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="teacherNIN" class="form-label">National ID (NIN) <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :disabled="verify" :required="uganda" v-model.trim="teacher.nin" id="teacherNIN" maxlength="14" type="text" placeholder="eg. CM74838348F83" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="teacherWorkPermit" class="form-label">Work Permit <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :disabled="verify" pattern="^EP[0-9]{7}$" title="Work Permit Format EP0011223" :required="!uganda" v-model.trim="teacher.work_permit" id="teacherWorkPermit" minlength="9" maxlength="9" type="text" placeholder="eg. EP0011223" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="staffRefugeeNumber" class="form-label">Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && staff_refugee_no === 'yes'" v-model.trim="teacher.refugee_number" id="staffRefugeeNumber" minlength="12" type="text" title="Staff Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherFirstName" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :disabled="uganda" :required="!uganda" v-model.trim="teacher.first_name" id="teacherFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherSurname" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" :disabled="uganda" v-model.trim="teacher.surname" id="teacherSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input :disabled="uganda" v-model.trim="teacher.other_names" id="teacherOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="teacherBirthDate">Date Of Birth <span v-if="!uganda" class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :disabled="uganda" :required="!uganda" v-model.trim="teacher.birth_date" placeholder="eg. 23/05/2001" id="teacherBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Gender</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input :disabled="uganda" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="teacher.gender" value="M" id="teacherMale">
                                        <label class="custom-control-label text-uppercase" for="teacherMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input :disabled="uganda" @change="updateDefaultPhoto()" type="radio" class="custom-control-input" v-model="teacher.gender" value="F" id="teacherFemale">
                                        <label class="custom-control-label text-uppercase" for="teacherFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Teacher Type <span class="text-danger">*</span></label>
                                    <select required id="teacherTeacherTypeId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="teacher_type in teacher_types" :value="teacher_type.id">{{ teacher_type.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Education Level <span class="text-danger">*</span></label>
                                    <select required id="teacherHighestEducationLevelsId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_level in teacherEducationLevels" :value="education_level.id">{{ education_level.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Employment Status <span class="text-danger">*</span></label>
                                    <select required id="teacherEmploymentStatusId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="employment_status in employment_statuses" :value="employment_status.id">{{ employment_status.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Religion <span class="text-danger">*</span></label>
                                    <select required id="religionId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="religion in religions" :key="religion.id" :value="religion.id">{{ religion.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="teacher_type === 'TRAINED'" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Teaching Qualification <span class="text-danger">*</span></label>
                                    <select :required="teacher_type === 'TRAINED'" id="teacherHighestTeachingQualificationId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="qualification in teacher_qualifications" :value="qualification.id">{{ qualification.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Designation <span class="text-danger">*</span></label>
                                    <select :required="teacher_type === 'TRAINED'" id="teacherDesignation" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="designation in designations" :value="designation.id">{{ designation.name }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Marital Status <span class="text-danger">*</span></label>
                                    <select required id="teacherMaritalStatusesId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_grade in marital_statuses" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input required="" v-model="teacher.phone_1" id="staffPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="teacher.phone_2" id="staffPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="teacher.email" id="staffEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>



                </div>
            </div>
            <button type="submit" id="teacherFormCreateFormSubmit" hidden="hidden"></button>
        </form>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import moment from "moment";
export default {
    name: "TeacherFormCreate",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'teacherTypesObj',
        'maritalStatusesObj',
        'countriesObj',
        'teacherTypesObj',
        'educationLevelsObj',
        'teacherDesignationsObj',
        'employmentStatusesObj',
        'teacherQualificationsObj',
    ],
    mounted() {
        this.loadReligions();
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            teacher_type: "",
            verify: false,
            uganda: true,
            staff_refugee_no: 'no',
            api_url: '/institutions/surveys/teaching-staff',
            photoDropify: null,
            school: {
                school_ownership_status_id: '',
            },
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            teacher: {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                qualification_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            countries: [],
            marital_statuses: [],
            teacher_types: [],
            education_levels: [],
            designations: [],
            employment_statuses: [],
            teacher_qualifications: [],
            religions: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.marital_statuses = this.maritalStatusesObj;
            this.teacher_types = this.teacherTypesObj;
            this.education_levels = this.educationLevelsObj;
            this.designations = this.teacherDesignationsObj;
            this.employment_statuses = this.employmentStatusesObj;
            this.teacher_qualifications = this.teacherQualificationsObj;

            $('#teacherBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(18, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.teacher.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#teacherWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.teacher.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#teacherCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.teacher.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherDesignation').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.designation_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherMaritalStatusesId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.marital_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherTeacherTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.teacher_type_id = data.id !== "" ? Number(data.id) : data.id;
                    self.teacher_type = data.text;
                    if (self.teacher_type !== "TRAINED") {
                        $('#teacherDesignation').val("").change();
                        $('#teacherHighestTeachingQualificationId').val("").change();
                    }
                    return data.text;
                },
            });
            $('#teacherHighestEducationLevelsId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.highest_education_level_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherHighestTeachingQualificationId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.qualification_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherEmploymentStatusId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.employment_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                // Initialize both phone inputs
                this.setupPhoneInput('#staffPhoneOne', 'phone_1');
                this.setupPhoneInput('#staffPhoneTwo', 'phone_2');

                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#teacherCountryId').val(ug.id).change();
                $('#teacherTeacherTypeId').val(trained.id).change();
            }, 50);
        },
        setupPhoneInput(inputId, modelKey) {
            const inputElement = document.querySelector(inputId);
            const iti = intlTelInput(inputElement, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: (selectedCountryPlaceholder) => `eg. ${selectedCountryPlaceholder}`,
            });

            const updatePhone = () => {
                const number = iti.getNumber();
                this.teacher[modelKey] = number;
                inputElement.value = number;
            };

            inputElement.addEventListener('blur', updatePhone);
            inputElement.addEventListener('change', updatePhone);
        },
        loadReligions () {
            let self = this;
            $('#religionId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.religion_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            axios
                .get('/lists/religions')
                .then(response=>{
                    this.religions = response.data
                })
                .catch(error=>{
                    console.log(error)
                })
        },
        verifyNIN: function () {
            if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The teacher must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                axios.post('/nira/user-info', {id_number: this.teacher.nin.toUpperCase()})
                    .then(response => {
                        this.$parent.loading = false;
                        this.nira_person = response.data;
                        this.teacher.first_name = this.nira_person.given_names;
                        this.teacher.surname = this.nira_person.surname;
                        this.teacher.birth_date = moment(this.nira_person.birth_date).format("D MMMM, YYYY");
                        this.teacher.gender = this.nira_person.gender;
                        this.$parent.verify = false;
                        this.verify = true;
                        this.updateDefaultPhoto();
                        this.$refs.notifySuccess.messages.push({
                            status: 'success',
                            title: 'Success: ',
                            message: 'Teacher NIN was verified successfully'
                        });
                    })
                    .catch(error => {
                        this.$parent.loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyWorkPermit: function () {
            if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The teacher must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                window.setTimeout(() => {
                    this.$parent.loading = false;
                    this.$parent.verify = false;
                    this.verify = true;
                    this.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success: ',
                        message: 'Teacher ID Number was verified successfully'
                    });
                }, 1000);
            }
        },
        createTeacher: function () {
            this.$parent.loading = true;
            let formData = new FormData();
            let self = this;
            if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The staff member must be at least 18 years or older!'});
            } else {
                self.$parent.loading = true;
                formData.append('nin', (self.uganda ? self.teacher.nin : ''));
                formData.append('work_permit', (!self.uganda ? self.teacher.work_permit : ''));
                formData.append('refugee_number', (!self.uganda ? self.teacher.refugee_number : ''));
                formData.append('first_name', self.teacher.first_name);
                formData.append('surname', self.teacher.surname);
                formData.append('other_names', self.teacher.other_names);
                formData.append('birth_date', self.teacher.birth_date);
                formData.append('gender', self.teacher.gender);
                formData.append('photo', self.teacher.photo);
                formData.append('marital_status_id', self.teacher.marital_status_id);
                formData.append('religion_id', self.teacher.religion_id);
                formData.append('teacher_type_id', self.teacher.teacher_type_id);
                formData.append('designation_id', self.teacher.designation_id);
                formData.append('highest_education_level_id', self.teacher.highest_education_level_id);
                formData.append('employment_status_id', self.teacher.employment_status_id);
                formData.append('qualification_id', self.teacher.qualification_id);
                formData.append('country_id', self.teacher.country_id);
                formData.append('phone_1', self.teacher.phone_1);
                formData.append('phone_2', self.teacher.phone_2);
                formData.append('email', self.teacher.email);
                formData.append('section_id', self.sectionId);
                formData.append('survey_id', self.survey.id);

                axios.post(self.api_url+'/'+self.survey.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                    .then(response => {
                        self.$parent.education_levels = response.data.education_levels;
                        self.$parent.$refs.notifySuccess.messages.push({status: 'success', title: 'Success: ', message:'Teacher saved successfully'});
                        self.resetTeacher();
                    })
                    .catch(error=>{
                        self.$parent.loading = false;
                        self.renderError(error);
                    });
            }
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        resetTeacher: function () {
            $('#updateSectionDModal').modal('hide');
            this.$parent.loading = false;
            this.$parent.verify = true;
            this.$parent.uganda = true;
            this.$parent.active_section = 'form-create';
            this.loading = false;
            this.verify = false;
            this.uganda = true;
            this.staff_refugee_no = 'no';
            this.teacher= {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            };
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#teacherCountryId').val(ug.id).change();
                $('#teacherTeacherTypeId').val(trained.id).change();
                $('#teacherDesignation').val('').change();
                $('#teacherMaritalStatusesId').val('').change();
                $('#teacherHighestEducationLevelsId').val('').change();
                $('#teacherHighestTeachingQualificationId').val('').change();
                $('#teacherEmploymentStatusId').val('').change();
                $('#religionId').val('').change();
                this.clearPhoto();
            }, 50);
        },
        selectFile() {
            this.teacher.photo = this.$refs.photo.files[0];
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.teacher.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.teacher.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.nira_person.photo !== null) {
                if (this.nira_person.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_person.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_person.photo);
                }
            } else {
                if (this.teacher.photo === null) {
                    this.clearPhoto();
                }
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        teacherEducationLevels: function () {
            return this.education_levels.filter(level => {
                return level.level_rank >= 2;
            });
        },
        trainedCheck: function () {
            return this.teacher_type === 'TRAINED';
        },
    },
}
</script>

<style scoped>

</style>
