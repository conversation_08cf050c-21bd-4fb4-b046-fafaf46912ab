<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <form @submit.prevent="verify ? createSupportStaff() : (uganda ? verifyNIN() : verifyWorkPermit())">
            <div class="row">
                <div class="col-lg-4 col-md-12">
                    <div class="form-group d-flex flex-column justify-content-center">
                        <label class="align-self-center form-label">Add Photo</label>
                        <input
                            ref="photo" @change="selectFile"
                            accept="image/x-png,image/jpeg"
                            data-max-file-size="2M"
                            id="supportStaffWithPhoto"
                            type="file"
                            class="dropify"
                            data-height="190"
                            data-allowed-file-extensions="jpeg jpg png"
                            :data-default-file="support_staff.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                    </div>
                    <div class="d-flex flex-column justify-content-center">
                        <button @click="support_staff.photo === null || support_staff.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                            <em class="icon ni ni-camera-fill"></em>
                            <span v-if="support_staff.photo === null || support_staff.photo === ''">Choose Photo</span>
                            <span v-else>Remove Photo</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 overflow-auto scrollbar-dark-teal h-425px">
                    <h6 class="overline-title title text-dark-teal">STAFF DETAILS</h6>
                    <div class="row mt-3">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is this staff member a refugee?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="yes" id="staffIsRefuge">
                                    <label class="custom-control-label text-uppercase" for="staffIsRefuge">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="no" id="staffIsNotRefugee">
                                    <label class="custom-control-label text-uppercase" for="staffIsNotRefugee">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                    <select required :disabled="verify" id="supportStaffCountryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-if="uganda" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="supportStaffNIN" class="form-label">National ID (NIN) <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="uganda" :disabled="verify" v-model.trim="support_staff.nin" id="supportStaffNIN" maxlength="14" type="text" placeholder="eg. CM74838348F83" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="supportStaffWorkPermit" class="form-label">Work Permit <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input pattern="^EP[0-9]{7}$" title="Work Permit Format EP0011223" :required="!uganda" v-model.trim="support_staff.work_permit" id="supportStaffWorkPermit" minlength="9" maxlength="9" type="text" placeholder="eg. EP0011223" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="staffRefugeeNumber" class="form-label">Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && staff_refugee_no === 'yes'" v-model.trim="support_staff.refugee_number" id="staffRefugeeNumber" minlength="12" type="text" title="Staff Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="supportStaffFirstName" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" :disabled="uganda" v-model.trim="support_staff.first_name" id="supportStaffFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="supportStaffSurname" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" :disabled="uganda" v-model.trim="support_staff.surname" id="supportStaffSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="supportStaffOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model.trim="support_staff.other_names" :disabled="uganda" id="supportStaffOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="supportStaffBirthDate">Date Of Birth <span v-if="!uganda" class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="!uganda" :disabled="uganda" v-model.trim="support_staff.birth_date" placeholder="eg. 23/05/2001" id="supportStaffBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Gender</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" :disabled="uganda" type="radio" class="custom-control-input" v-model="support_staff.gender" value="M" id="supportStaffMale">
                                        <label class="custom-control-label text-uppercase" for="supportStaffMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" :disabled="uganda" type="radio" class="custom-control-input" v-model="support_staff.gender" value="F" id="supportStaffFemale">
                                        <label class="custom-control-label text-uppercase" for="supportStaffFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Marital Status <span class="text-danger">*</span></label>
                                    <select required id="supportStaffMaritalStatusesId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_grade in marital_statuses" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Staff Category <span class="text-danger">*</span></label>
                                    <select required id="supportStaffCategoryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="category in staff_categories" :value="category.id">{{ category.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Job Title <span class="text-danger">*</span></label>
                                    <select required id="supportStaffJobTitle" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="designation in non_teaching_staff_roles" :value="designation.id">{{ designation.name }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label">Religion <span class="text-danger">*</span></label>
                                <select required id="religionId" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option v-for="religion in religions" :key="religion.id" :value="religion.id">{{ religion.name.toUpperCase() }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Employment Status <span class="text-danger">*</span></label>
                                    <select required id="supportStaffEmploymentStatusId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="employment_status in employment_statuses" :value="employment_status.id">{{ employment_status.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Education Level <span class="text-danger">*</span></label>
                                    <select required id="supportStaffHighestEducationLevelsId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_level in education_levels" :value="education_level.id">{{ education_level.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input required="" v-model="support_staff.phone_1" id="staffPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="support_staff.phone_2" id="staffPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="support_staff.email" id="staffEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="submit" id="supportStaffFormCreateFormSubmit" hidden="hidden"></button>
        </form>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "SupportStaffFormCreate",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'maritalStatusesObj',
        'countriesObj',
        'employmentStatusesObj',
        'educationLevelsObj',
        'nonTeachingStaffRolesObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications
    },
    mounted() {
        this.loadReligions();
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            uganda: true,
            staff_refugee_no: 'no',
            verify: false,
            school: {},
            survey: {},
            api_url: '/institutions/surveys/non-teaching-staff',
            support_staff: {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                non_teaching_staff_category_id: '',
                non_teaching_staff_role_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            countries: [],
            marital_statuses: [],
            education_levels: [],
            employment_statuses: [],
            non_teaching_staff_roles: [],
            staff_categories: [],
            religions: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.marital_statuses = this.maritalStatusesObj;
            this.education_levels = this.educationLevelsObj;
            this.employment_statuses = this.employmentStatusesObj;
            this.non_teaching_staff_roles = this.nonTeachingStaffRolesObj;

            this.non_teaching_staff_roles.forEach(role => {
                if (!this.staff_categories.length || this.staff_categories.find(category=>{return category.id === role.non_teaching_staff_category_id}) === undefined) {
                    this.staff_categories.push(role.non_teaching_staff_category);
                }
            })

            $('#supportStaffBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(18, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.support_staff.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#supportStaffWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.support_staff.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#supportStaffCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.support_staff.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#supportStaffJobTitle').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.non_teaching_staff_role_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#supportStaffMaritalStatusesId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.marital_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#supportStaffCategoryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.non_teaching_staff_category_id = data.id !== "" ? Number(data.id) : data.id;
                    self.loadJobTitles(data.text);
                    return data.text;
                },
            });
            $('#supportStaffHighestEducationLevelsId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.highest_education_level_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#supportStaffEmploymentStatusId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.employment_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                // Initialize both phone inputs
                this.setupPhoneInput('#staffPhoneOne', 'phone_1');
                this.setupPhoneInput('#staffPhoneTwo', 'phone_2');

                // Set the default country to Uganda
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#supportStaffCountryId').val(ug.id).change();
            }, 50);
        },
        setupPhoneInput(inputId, modelKey) {
            const inputElement = document.querySelector(inputId);
            const iti = intlTelInput(inputElement, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: (selectedCountryPlaceholder) => `eg. ${selectedCountryPlaceholder}`,
            });

            const updatePhone = () => {
                const number = iti.getNumber();
                this.support_staff[modelKey] = number;
                inputElement.value = number;
            };

            inputElement.addEventListener('blur', updatePhone);
            inputElement.addEventListener('change', updatePhone);
        },
        loadReligions () {
            let self = this;
            $('#religionId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateNonTeachingStaffModal'),
                templateSelection: function (data, container) {
                    self.support_staff.religion_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            axios
                .get('/lists/religions')
                .then(response=>{
                    this.religions = response.data
                })
                .catch(error=>{
                    console.log(error)
                })
        },
        loadJobTitles: function (category) {
            let self = this;
            let select = $("#supportStaffJobTitle");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.support_staff.non_teaching_staff_role_id = "";

            //load new options
            if (category === "SUPPORT") {
                self.supportRoles.forEach(jobTitle=>{
                    let jobTitleOption = new Option(jobTitle.name, jobTitle.id, false, false);
                    select.append(jobTitleOption).trigger('change');
                });
            } else if (category === "ADMINISTRATIVE") {
                self.administrativeRoles.forEach(jobTitle=>{
                    let jobTitleOption = new Option(jobTitle.name, jobTitle.id, false, false);
                    select.append(jobTitleOption).trigger('change');
                });
            }
        },
        verifyNIN: function () {
            if (moment(this.support_staff.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The staff member must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                axios.post('/nira/user-info', {id_number: this.support_staff.nin.toUpperCase()})
                    .then(response => {
                        this.$parent.loading = false;
                        this.nira_person = response.data;
                        this.support_staff.first_name = this.nira_person.given_names;
                        this.support_staff.surname = this.nira_person.surname;
                        this.support_staff.birth_date = moment(this.nira_person.birth_date).format("D MMMM, YYYY");
                        this.support_staff.gender = this.nira_person.gender;
                        this.$parent.verify = false;
                        this.verify = true;
                        this.updateDefaultPhoto();
                        this.$refs.notifySuccess.messages.push({
                            status: 'success',
                            title: 'Success: ',
                            message: 'Support Staff NIN was verified successfully'
                        });
                    })
                    .catch(error => {
                        this.$parent.loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyWorkPermit: function () {
            if (moment(this.support_staff.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The staff member must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                window.setTimeout(() => {
                    this.$parent.loading = false;
                    this.$parent.verify = false;
                    this.verify = true;
                    this.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success: ',
                        message: 'Staff ID Number is verified successfully'
                    });
                }, 1000);
            }
        },
        createSupportStaff: function () {
            this.$parent.loading = true;
            let formData = new FormData();
            let self = this;
            if (moment(this.support_staff.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The staff member must be at least 18 years or older!'});
            } else {
                self.$parent.loading = true;
                formData.append('nin', (self.uganda ? self.support_staff.nin : ''));
                formData.append('work_permit', (!self.uganda ? self.support_staff.work_permit : ''));
                formData.append('refugee_number', (!self.uganda ? self.support_staff.refugee_number : ''));
                formData.append('first_name', self.support_staff.first_name);
                formData.append('surname', self.support_staff.surname);
                formData.append('other_names', self.support_staff.other_names);
                formData.append('birth_date', self.support_staff.birth_date);
                formData.append('gender', self.support_staff.gender);
                formData.append('photo', self.support_staff.photo);
                formData.append('country_id', self.support_staff.country_id);
                formData.append('religion_id', self.support_staff.religion_id);
                formData.append('marital_status_id', self.support_staff.marital_status_id);
                formData.append('highest_education_level_id', self.support_staff.highest_education_level_id);
                formData.append('employment_status_id', self.support_staff.employment_status_id);
                formData.append('non_teaching_staff_category_id', self.support_staff.non_teaching_staff_category_id);
                formData.append('non_teaching_staff_role_id', self.support_staff.non_teaching_staff_role_id);
                formData.append('phone_1', self.support_staff.phone_1);
                formData.append('phone_2', self.support_staff.phone_2);
                formData.append('email', self.support_staff.email);

                formData.append('section_id', self.sectionId);
                formData.append('survey_id', self.survey.id);

                axios.post(self.api_url+'/'+self.survey.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                    .then(response => {
                        self.$parent.non_teaching_staff_roles = response.data;
                        self.$parent.$refs.notifySuccess.messages.push({status: 'success', title: 'Success: ', message:'Support Staff saved successfully'});
                        self.resetSupportStaff();
                    })
                    .catch(error=>{
                        self.$parent.loading = false;
                        self.renderError(error);
                    });
            }
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        resetSupportStaff: function () {
            $('#updateNonTeachingStaffModal').modal('hide');
            this.$parent.loading = false;
            this.$parent.verify = true;
            this.$parent.uganda = true;
            this.$parent.active_section = 'form-create';
            this.loading = false;
            this.verify = false;
            this.uganda = true;
            this.staff_refugee_no = 'no';
            this.support_staff= {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                non_teaching_staff_category_id: '',
                non_teaching_staff_role_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            };
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                $('#supportStaffBirthDate').datepicker('setDate', moment().subtract(18, 'years').toDate());
                $('#supportStaffCountryId').val(ug.id).change();
                $('#supportStaffJobTitle').val('').change();
                $('#supportStaffMaritalStatusesId').val('').change();
                $('#supportStaffCategoryId').val('').change();
                $('#supportStaffHighestEducationLevelsId').val('').change();
                $('#supportStaffEmploymentStatusId').val('').change();
                $('#religionId').val('').change();
                this.clearPhoto();
            }, 50);
        },
        selectFile() {
            this.support_staff.photo = this.$refs.photo.files[0];
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.support_staff.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.support_staff.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.nira_person.photo !== null) {
                if (this.nira_person.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_person.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_person.photo);
                }
            } else {
                if (this.support_staff.photo === null) {
                    this.clearPhoto();
                }
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        supportRoles: function () {
            return this.non_teaching_staff_roles.filter(role => {
                return role.non_teaching_staff_category.name === "SUPPORT";
            });
        },
        administrativeRoles: function () {
            return this.non_teaching_staff_roles.filter(role => {
                return role.non_teaching_staff_category.name === "ADMINISTRATIVE";
            });
        },
    },
}
</script>

<style scoped>

</style>
