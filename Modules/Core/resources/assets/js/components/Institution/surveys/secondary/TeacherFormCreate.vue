<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <form @submit.prevent="handleFormSubmit()">
            <div class="row">
                <div class="col-lg-4 col-md-12">
                    <div class="form-group d-flex flex-column justify-content-center">
                        <label class="align-self-center form-label">Add Photo</label>
                        <input
                            ref="photo" @change="selectFile"
                            accept="image/x-png,image/jpeg"
                            data-max-file-size="2M"
                            id="teacherWithPhoto"
                            type="file"
                            class="dropify"
                            data-height="190"
                            data-allowed-file-extensions="jpeg jpg png"
                            :data-default-file="teacher.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                    </div>
                    <div class="d-flex flex-column justify-content-center">
                        <button @click="teacher.photo === null || teacher.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                            <em class="icon ni ni-camera-fill"></em>
                            <span v-if="teacher.photo === null || teacher.photo === ''">Choose Photo</span>
                            <span v-else>Remove Photo</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 overflow-auto scrollbar-dark-teal h-425px">
                    <h6 class="overline-title title text-dark-teal">TEACHER DETAILS</h6>
                    <div class="row mt-3">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is this staff member a refugee?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="yes" id="staffIsRefuge">
                                    <label class="custom-control-label text-uppercase" for="staffIsRefuge">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="no" id="staffIsNotRefugee">
                                    <label class="custom-control-label text-uppercase" for="staffIsNotRefugee">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                    <select required id="teacherCountryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-if="uganda" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="teacherNIN" class="form-label">National ID (NIN) <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="uganda" v-model.trim="teacher.nin" id="teacherNIN" maxlength="14" type="text" placeholder="eg. CM74838348F83" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="teacherWorkPermit" class="form-label">Work Permit <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input pattern="^EP[0-9]{7}$" title="Work Permit Format EP0011223" :required="!uganda" v-model.trim="teacher.work_permit" id="teacherWorkPermit" minlength="9" maxlength="9" type="text" placeholder="eg. EP0011223" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="staffRefugeeNumber" class="form-label">Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && staff_refugee_no === 'yes'" v-model.trim="teacher.refugee_number" id="staffRefugeeNumber" minlength="12" type="text" title="Staff Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherFirstName" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" v-model.trim="teacher.first_name" :disabled="uganda" id="teacherFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherSurname" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" v-model.trim="teacher.surname" :disabled="uganda" id="teacherSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacherOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model.trim="teacher.other_names" id="teacherOtherNames" :disabled="uganda" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="teacherBirthDate">Date Of Birth <span v-if="!uganda" class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="!uganda" v-model.trim="teacher.birth_date" :disabled="uganda" placeholder="eg. 23/05/2001" id="teacherBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Gender</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" type="radio" :disabled="uganda" class="custom-control-input" v-model="teacher.gender" value="M" id="teacherMale">
                                        <label class="custom-control-label text-uppercase" for="teacherMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" type="radio" :disabled="uganda" class="custom-control-input" v-model="teacher.gender" value="F" id="teacherFemale">
                                        <label class="custom-control-label text-uppercase" for="teacherFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Religion <span class="text-danger">*</span></label>
                                    <select required id="religionId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="religion in religions" :key="religion.id" :value="religion.id">{{ religion.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Marital Status <span class="text-danger">*</span></label>
                                    <select required id="teacherMaritalStatusesId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_grade in marital_statuses" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Teacher Type <span class="text-danger">*</span></label>
                                    <select required id="teacherTeacherTypeId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="teacher_type in teacher_types" :value="teacher_type.id">{{ teacher_type.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="verify" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="weeklyTeachingPeriods" class="form-label">Weekly Teaching Periods <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify" v-model.number="teacher.weekly_teaching_periods" id="weeklyTeachingPeriods" type="number" min="1" placeholder="eg. 20" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div v-if="teacher_type === 'TRAINED'" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="trackingNumber" class="form-label">TMIS Tracking Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <div class="input-group">
                                        <input :disabled="verify && tmis_verify" :required="teacher_type === 'TRAINED' && verify" v-model.number="teacher.tracking_number" id="tracking Number" type="text" placeholder="eg. 673288238AD37RT" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                        <div class="input-group-append">
                                            <button :disabled="teacher.tracking_number === '' || tmis_api_loading || tmis_verify || !verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                <span v-if="tmis_api_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="tmis_api_loading" class="align-self-center">Verifying...</span>
                                                <span v-if="tmis_api_loading" class="sr-only">Verifying...</span>
                                                <span v-if="!tmis_api_loading" class="">Verify</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div v-if="tmis_verify" class="row mt-3 mb-3">
                        <div class="col-lg-12 mt-lg-0 mt-3">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                    <tr>
                                        <td class="px-2">
                                            <span class="overline-title mb-0">TMIS Details</span>
                                        </td>
                                    </tr>
                                    </thead>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">NAMES</h6>
                                            <span class="">{{ tmis_person.first_name }} {{ tmis_person.last_name }}</span>
                                        </td>
                                        <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                            <span class="">{{ tmis_person.date_of_birth }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                            <span class="">{{ tmis_person.gender }}</span>
                                        </td>
                                        <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">TRACKING NUMBER</h6>
                                            <span class="">{{ tmis_person.tracking_number }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">PHONE NUMBER</h6>
                                            <span class="">{{ tmis_person.phone_number }}</span>
                                        </td>
                                        <td class="px-2 w-75 align-middle text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal text-uppercase">EMAIL</h6>
                                            <span class="">{{ tmis_person.email_address.toLowerCase() }}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div v-if="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is Teacher on Government Payroll?</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" v-model.number="teacher.is_on_government_payroll" value="1" id="IsGovtTeacher">
                                        <label class="custom-control-label" for="IsGovtTeacher">Yes</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" v-model.number="teacher.is_on_government_payroll" value="0" id="IsPrivateTeacher">
                                        <label class="custom-control-label" for="IsPrivateTeacher">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="teacher.is_on_government_payroll === 1" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacher_ipps_number" class="form-label">IPPS Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="teacher.is_on_government_payroll === 1 && school.school_ownership_status_id === 1" v-model.trim="teacher.ipps_number" id="teacher_ipps_number" type="text" placeholder="Enter IPPS Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--                <div v-if="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1" class="row mt-3">-->
                    <!--                    <div class="col-lg-6 mt-lg-0 mt-3">-->
                    <!--                        <div class="form-group">-->
                    <!--                            <div class="form-label-group">-->
                    <!--                                <label for="teacher_tmis_number" class="form-label">TMIS Number</label>-->
                    <!--                            </div>-->
                    <!--                            <div class="form-control-group">-->
                    <!--                                <input v-model.trim="teacher.tmis_number" id="teacher_tmis_number" type="text" placeholder="Enter TMIS Number" class="form-control bg-primary-dim" autocomplete="off">-->
                    <!--                            </div>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <!--                    <div class="col-lg-6 mt-lg-0 mt-3">-->
                    <!--                        <div class="form-group">-->
                    <!--                            <div class="form-label-group">-->
                    <!--                                <label for="teacher_registration_number" class="form-label">Old Registration Number</label>-->
                    <!--                            </div>-->
                    <!--                            <div class="form-control-group">-->
                    <!--                                <input v-model.trim="teacher.registration_number" id="teacher_registration_number" type="text" placeholder="Enter Old Reg Number" class="form-control bg-primary-dim" autocomplete="off">-->
                    <!--                            </div>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <!--                </div>-->

                    <div v-if="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" class="row mt-3">
                        <div  class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="dateOfFirstAppointment">Date of First Appointment <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" v-model.trim="teacher.first_appointment_date" id="dateOfFirstAppointment" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div  class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacher_appointment_minute_number" class="form-label">First Appointment Minute Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify &&  teacher_type === 'TRAINED' && teacher.is_on_government_payroll === 1 && school.school_ownership_status_id === 1" v-model.trim="teacher.appointment_minute_number" id="teacher_appointment_minute_number" type="text" class="form-control bg-primary-dim" placeholder="Enter Number" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="dateOfCurrentAppointment">Date of Current Appointment <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="verify &&  teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" v-model.trim="teacher.current_appointment_date" id="dateOfCurrentAppointment" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="teacher_current_appointment_minute_number" class="form-label">Current Appointment Minute Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify &&  teacher_type === 'TRAINED' && teacher.is_on_government_payroll === 1 && school.school_ownership_status_id === 1" v-model.trim="teacher.current_appointment_minute_number" id="teacher_current_appointment_minute_number" type="text" class="form-control bg-primary-dim" placeholder="Enter Number" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="dateOfPosting">Posting Date <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="verify &&  teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1" v-model.trim="teacher.posting_date" id="dateOfPosting" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="escMinuteNumber" class="form-label">Minute Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify &&  school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1 && teacher_type === 'TRAINED'" v-model.trim="teacher.esc_minute_number" id="escMinuteNumber" type="text" placeholder="Enter Minute Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-show="verify && teacher_type === 'TRAINED'" class="row mt-3">
                        <div v-show="verify && teacher_type === 'TRAINED' && school.school_ownership_status_id === 1 &&teacher.is_on_government_payroll === 1 " class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="utsFileNumber" class="form-label">File Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify && school.school_ownership_status_id === 1 && teacher.is_on_government_payroll === 1 && teacher_type === 'TRAINED'" v-model.trim="teacher.uts_file_number" id="utsFileNumber" type="text" placeholder="Enter File Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div v-show="verify && teacher_type === 'TRAINED'" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label">Teacher Category <span class="text-danger">*</span></label>
                                <select :required="verify && teacher_type === 'TRAINED'" id="teacherSubjectCategory" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option value="arts">ARTS</option>
                                    <option value="sciences">SCIENCES</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div v-show="verify" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Education Level <span class="text-danger">*</span></label>
                                    <select :required="verify" id="teacherHighestEducationLevelsId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_level in teacherEducationLevels" :value="education_level.id">{{ education_level.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Employment Status <span class="text-danger">*</span></label>
                                    <select :required="verify" id="teacherEmploymentStatusId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="employment_status in employment_statuses" :value="employment_status.id">{{ employment_status.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-show="verify && teacher_type === 'TRAINED'" class="row mt-3">
                        <div v-show="teacher_type === 'TRAINED'" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Teaching Qualification <span class="text-danger">*</span></label>
                                    <select :required="verify && teacher_type === 'TRAINED'" id="teacherHighestTeachingQualificationId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="qualification in trainedQualifications" :value="qualification.id">{{ qualification.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-show="teacher_type === 'TRAINED'" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Designation <span class="text-danger">*</span></label>
                                    <select :required="verify && teacher_type === 'TRAINED'" id="teacherDesignation" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="designation in designations" :value="designation.id">{{ designation.name }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="verify" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label v-if="teacher_type === 'TRAINED'" class="form-label">Trained Subject 1 <span class="text-danger">*</span></label>
                                    <label v-else class="form-label">Subject 1 <span class="text-danger">*</span></label>
                                    <select :required="verify" id="teacherSubject1Id" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="subject in principalSubjects" :value="subject.id">{{ subject.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label v-if="teacher_type === 'TRAINED'" class="form-label">Trained Subject 2 <span class="text-danger">*</span></label>
                                    <label v-else class="form-label">Subject 2 <span class="text-danger">*</span></label>
                                    <select :required="verify" id="teacherSubject2Id" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="subject in principalSubjects" :value="subject.id">{{ subject.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="verify" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="verify" v-model="teacher.phone_1" id="staffPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="teacher.phone_2" id="staffPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="verify" class="row mt-3">
                        <div class="col-lg-12 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="teacher.email" id="staffEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="submit" id="teacherFormCreateFormSubmit" hidden="hidden"></button>
        </form>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import moment from "moment";

export default {
    name: "TeacherFormCreate",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'teacherTypesObj',
        'maritalStatusesObj',
        'countriesObj',
        'teacherTypesObj',
        'educationLevelsObj',
        'teacherDesignationsObj',
        'employmentStatusesObj',
        'teacherQualificationsObj',
        'subjectsObj',
    ],
    mounted() {
        this.loadReligions();
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            tmis_api_loading: false,
            staff_refugee_no: 'no',
            teacher_type: "",
            verify: false,
            tmis_verify: false,
            uganda: true,
            api_url: '/institutions/surveys/teaching-staff',
            photoDropify: null,
            school: {
                school_ownership_status_id: '',
            },
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            teacher: {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                posting_date: moment().format("D MMMM, YYYY"),
                first_appointment_date: moment().format("D MMMM, YYYY"),
                current_appointment_date: moment().format("D MMMM, YYYY"),
                uts_file_number: '',
                subject_category: '',
                esc_minute_number: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                is_on_government_payroll: 0,
                weekly_teaching_periods: 1,
                ipps_number: '',
                tmis_number: '',
                registration_number: '',
                tracking_number: '',
                tmis_nin: '',
                tmis_first_name: '',
                tmis_last_name: '',
                tmis_gender: '',
                tmis_date_of_birth: '',
                tmis_phone_number: '',
                tmis_email_address: '',
                appointment_minute_number: '',
                current_appointment_minute_number: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                qualification_id: '',
                subject_1_id: '',
                subject_2_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            tmis_person: {
                nin: '',
                first_name: '',
                last_name: '',
                date_of_birth: '',
                gender: '',
                tracking_number: '',
                phone_number: '',
                email_address: '',
            },
            countries: [],
            religions: [],
            marital_statuses: [],
            teacher_types: [],
            education_levels: [],
            designations: [],
            employment_statuses: [],
            teacher_qualifications: [],
            subjects: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.marital_statuses = this.maritalStatusesObj;
            this.teacher_types = this.teacherTypesObj;
            this.education_levels = this.educationLevelsObj;
            this.designations = this.teacherDesignationsObj;
            this.employment_statuses = this.employmentStatusesObj;
            this.teacher_qualifications = this.teacherQualificationsObj;
            this.subjects = this.subjectsObj;

            $('#teacherBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(18, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.teacher.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#dateOfPosting').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.teacher.posting_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#dateOfFirstAppointment').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.teacher.first_appointment_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#dateOfCurrentAppointment').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.teacher.current_appointment_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#teacherWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.teacher.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#teacherCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.teacher.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherDesignation').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.designation_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherSubjectCategory').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.subject_category = data.id;
                    return data.text;
                },
            });
            $('#teacherMaritalStatusesId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.marital_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherTeacherTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.teacher_type_id = data.id !== "" ? Number(data.id) : data.id;
                    self.teacher_type = data.text;
                    if (self.teacher_type !== "TRAINED") {
                        $('#teacherDesignation').val("").change();
                        $('#teacherHighestTeachingQualificationId').val("").change();
                    }
                    self.loadQualifications(data.text);
                    return data.text;
                },
            });
            $('#teacherHighestEducationLevelsId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.highest_education_level_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherHighestTeachingQualificationId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.qualification_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherEmploymentStatusId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.employment_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherSubject1Id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.subject_1_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#teacherSubject2Id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.subject_2_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                // Initialize both phone inputs
                this.setupPhoneInput('#staffPhoneOne', 'phone_1');
                this.setupPhoneInput('#staffPhoneTwo', 'phone_2');

                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#teacherCountryId').val(ug.id).change();
                $('#teacherTeacherTypeId').val(trained.id).change();
            }, 50);
        },
        setupPhoneInput(inputId, modelKey) {
            const inputElement = document.querySelector(inputId);
            const iti = intlTelInput(inputElement, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: (selectedCountryPlaceholder) => `eg. ${selectedCountryPlaceholder}`,
            });

            const updatePhone = () => {
                const number = iti.getNumber();
                this.teacher[modelKey] = number;
                inputElement.value = number;
            };

            inputElement.addEventListener('blur', updatePhone);
            inputElement.addEventListener('change', updatePhone);
        },
        loadQualifications: function (teacherType) {
            let self = this;
            let select = $("#teacherHighestTeachingQualificationId");
            select.empty().trigger('change');
            let newOption = new Option("--SELECT--", "", false, false);
            select.append(newOption).trigger('change');
            self.teacher.qualification_id = "";

            //load new options
            if (teacherType === "TRAINED") {
                self.trainedQualifications.forEach(qualification=>{
                    let qualificationOption = new Option(qualification.name, qualification.id, false, false);
                    select.append(qualificationOption).trigger('change');
                });
            } else if (teacherType === "QUALIFIED") {
                self.qualifiedQualifications.forEach(qualification=>{
                    let qualificationOption = new Option(qualification.name, qualification.id, false, false);
                    select.append(qualificationOption).trigger('change');
                });
            }
        },
        loadReligions () {
            let self = this;
            $('#religionId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.teacher.religion_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            axios
                .get('/lists/religions')
                .then(response=>{
                    this.religions = response.data
                })
                .catch(error=>{
                    console.log(error)
                })
        },
        handleFormSubmit: function() {
            switch (true) {
                case (this.verify && this.teacher_type !== 'TRAINED'):
                    this.createTeacher(); // If teacher is not trained, skip tracking number verification and create the teacher directly
                    break;
                case (this.verify && this.teacher_type === 'TRAINED' && this.tmis_verify):
                    this.createTeacher(); // If teacher is trained and verification is done, proceed to create teacher
                    break;
                case (this.verify && this.teacher_type === 'TRAINED' && !this.tmis_verify):
                    this.verifyTmisTeacher(); // If teacher is trained and verification is not done, initiate tracking number verification
                    break;
                case (!this.verify && this.uganda):
                    this.verifyNIN(); // If verify flag is false and Ugandan, perform NIN verification
                    break;
                case (!this.verify && !this.uganda):
                    this.verifyWorkPermit(); // If verify flag is false and not Ugandan, perform work permit verification
                    break;
                default:
                    break;
            }
        },
        verifyNIN: function () {
            if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error!', message:'The teacher must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                axios.post('/nira/user-info', {id_number: this.teacher.nin.toUpperCase()})
                    .then(response => {
                        this.$parent.loading = false;
                        this.nira_person = response.data;
                        this.teacher.first_name = this.nira_person.given_names;
                        this.teacher.surname = this.nira_person.surname;
                        this.teacher.birth_date = moment(this.nira_person.birth_date).format("D MMMM, YYYY");
                        this.teacher.gender = this.nira_person.gender;
                        this.$parent.verify = false;
                        this.verify = true;
                        this.updateDefaultPhoto();
                        this.$refs.notifySuccess.messages.push({
                            status: 'success',
                            title: 'Success: ',
                            message: 'Teacher NIN was verified successfully, proceed to verify tracking number'
                        });
                    })
                    .catch(error => {
                        this.$parent.loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyWorkPermit: function () {
            if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The teacher must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                window.setTimeout(() => {
                    this.$parent.loading = false;
                    this.$parent.verify = false;
                    this.verify = true;
                    this.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success: ',
                        message: 'Teacher ID Number was verified successfully, proceed to verify tracking number'
                    });
                }, 1000);
            }
        },
        verifyTmisTeacher: function () {
            this.tmis_api_loading = true;
            axios.post('/tmis/teacher-secondary-info', {tracking_number: this.teacher.tracking_number})
                .then(response => {
                    this.tmis_api_loading = false;
                    this.tmis_person = response.data;
                    this.teacher.tmis_nin = this.tmis_person.nin;
                    this.teacher.tmis_first_name = this.tmis_person.first_name;
                    this.teacher.tmis_last_name = this.nira_person.surname;
                    this.teacher.tmis_date_of_birth = moment(this.tmis_person.date_of_birth).format("D MMMM, YYYY");
                    this.teacher.tmis_gender = this.tmis_person.gender;
                    this.teacher.tmis_phone_number = this.tmis_person.phone_number;
                    this.teacher.tmis_email_address = this.tmis_person.email_address;
                    this.$parent.tmis_verify = true;
                    this.tmis_verify = true;
                    this.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success: ',
                        message: 'TMIS Tracking Number verified successfully, proceed to Save Information'
                    });
                })
                .catch(error => {
                    this.tmis_api_loading = false;
                    this.renderError(error);
                });
        },
        createTeacher: function () {
            this.$parent.loading = true;
            let formData = new FormData();
            let self = this;
            if (this.isTeacherTrained) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({
                    status: 'error',
                    title: 'Error: ',
                    message:'The NIN and the date of birth in TMIS do not match those in NIRA. Please try again!'
                });
            } else {
                if (moment(this.teacher.birth_date).isAfter(moment().subtract(18, 'years'))) {
                    this.$parent.loading = false;
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error: ',
                        message: 'The staff member must be at least 18 years or older!'
                    });
                } else {
                    self.$parent.loading = true;
                    formData.append('nin', (self.uganda ? self.teacher.nin : ''));
                    formData.append('work_permit', (!self.uganda ? self.teacher.work_permit : ''));
                    formData.append('refugee_number', (!self.uganda ? self.teacher.refugee_number : ''));
                    formData.append('first_name', self.teacher.first_name);
                    formData.append('surname', self.teacher.surname);
                    formData.append('other_names', self.teacher.other_names);
                    formData.append('birth_date', self.teacher.birth_date);
                    formData.append('gender', self.teacher.gender);
                    formData.append('photo', self.teacher.photo);
                    formData.append('marital_status_id', self.teacher.marital_status_id);
                    formData.append('religion_id', self.teacher.religion_id);
                    formData.append('posting_date', self.teacher.posting_date);
                    formData.append('first_appointment_date', self.teacher.first_appointment_date);
                    formData.append('current_appointment_date', self.teacher.current_appointment_date);
                    formData.append('uts_file_number', self.teacher.uts_file_number);
                    formData.append('subject_category', self.teacher.subject_category);
                    formData.append('esc_minute_number', self.teacher.esc_minute_number);
                    formData.append('teacher_type_id', self.teacher.teacher_type_id);
                    formData.append('is_on_government_payroll', self.teacher.is_on_government_payroll);
                    formData.append('weekly_teaching_periods', self.teacher.weekly_teaching_periods);
                    formData.append('ipps_number', self.teacher.ipps_number);
                    formData.append('tmis_number', self.teacher.tracking_number);
                    // formData.append('registration_number', self.teacher.registration_number);
                    formData.append('appointment_minute_number', self.teacher.appointment_minute_number);
                    formData.append('current_appointment_minute_number', self.teacher.current_appointment_minute_number);
                    formData.append('designation_id', self.teacher.designation_id);
                    formData.append('highest_education_level_id', self.teacher.highest_education_level_id);
                    formData.append('employment_status_id', self.teacher.employment_status_id);
                    formData.append('qualification_id', self.teacher.qualification_id);
                    formData.append('country_id', self.teacher.country_id);
                    formData.append('subject_1_id', self.teacher.subject_1_id);
                    formData.append('subject_2_id', self.teacher.subject_2_id);
                    formData.append('phone_1', self.teacher.phone_1);
                    formData.append('phone_2', self.teacher.phone_2);
                    formData.append('email', self.teacher.email);
                    formData.append('section_id', self.sectionId);
                    formData.append('survey_id', self.survey.id);

                    axios.post(self.api_url + '/' + self.survey.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                        .then(response => {
                            self.$parent.teacher_qualifications = response.data.qualifications;
                            self.$parent.education_levels = response.data.education_levels;
                            self.$parent.$refs.notifySuccess.messages.push({
                                status: 'success',
                                title: 'Success: ',
                                message: 'Teacher saved successfully'
                            });
                            self.resetTeacher();
                        })
                        .catch(error => {
                            self.$parent.loading = false;
                            self.renderError(error);
                        });
                }
            }
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        resetTeacher: function () {
            $('#updateSectionDModal').modal('hide');
            this.$parent.loading = false;
            this.$parent.tmis_api_loading = false;
            this.$parent.verify = true;
            this.$parent.tmis_verify = true;
            this.$parent.uganda = true;
            this.$parent.active_section = 'form-create';
            this.loading = false;
            this.tmis_api_loading = false;
            this.verify = false;
            this.tmis_verify = false;
            this.uganda = true;
            this.staff_refugee_no = 'no';
            this.teacher= {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                posting_date: moment().format("D MMMM, YYYY"),
                first_appointment_date: moment().format("D MMMM, YYYY"),
                current_appointment_date: moment().format("D MMMM, YYYY"),
                uts_file_number: '',
                subject_category: '',
                esc_minute_number: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                is_on_government_payroll: 0,
                weekly_teaching_periods: 1,
                ipps_number: '',
                tmis_number: '',
                registration_number: '',
                appointment_minute_number: '',
                current_appointment_minute_number: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                subject_1_id: '',
                subject_2_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            };
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.tmis_person = {
                nin: '',
                first_name: '',
                last_name: '',
                date_of_birth: '',
                gender: '',
                tracking_number: '',
                phone_number: '',
                email_address: '',
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#teacherCountryId').val(ug.id).change();
                $('#teacherTeacherTypeId').val(trained.id).change();
                $('#teacherDesignation').val('').change();
                $('#teacherMaritalStatusesId').val('').change();
                $('#teacherHighestEducationLevelsId').val('').change();
                $('#teacherHighestTeachingQualificationId').val('').change();
                $('#teacherEmploymentStatusId').val('').change();
                $('#teacherSubject1Id').val('').change();
                $('#teacherSubject2Id').val('').change();
                $('#religionId').val('').change();
                this.clearPhoto();
            }, 50);
        },
        selectFile() {
            this.teacher.photo = this.$refs.photo.files[0];
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.teacher.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.teacher.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.nira_person.photo !== null) {
                if (this.nira_person.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_person.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_person.photo);
                }
            } else {
                if (this.teacher.photo === null) {
                    this.clearPhoto();
                }
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        qualifiedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return qualification.pivot.is_qualified;
            });
        },
        trainedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return !qualification.pivot.is_qualified;
            });
        },
        teacherEducationLevels: function () {
            return this.education_levels.filter(level => {
                return level.level_rank >= 2;
            });
        },
        principalSubjects: function () {
            return this.subjects.filter(subject=>{
                return subject.is_principal_subject;
            });
        },
        isTeacherTrained: function () {
            return (
              this.teacher.nin.toUpperCase() !== this.tmis_person.nin.toUpperCase() &&
              this.teacher.birth_date !== this.formatDate(this.tmis_person.date_of_birth) &&
              this.teacher_type === 'TRAINED'
            );
        }
    },
}
</script>

<style scoped>

</style>
