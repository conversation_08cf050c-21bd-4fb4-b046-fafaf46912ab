<template>
	<div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <div class="table-responsive">
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Extra-curricular Activity</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
                </thead>
                <tbody class="border-top-0 border-secondary">

                <tr v-for="facility in practical_skills_types">
                    <td class="align-middle border-left border-secondary">{{ facility.name.toUpperCase() }}</td>
                    <td class="align-middle border-left border-secondary text-center" >
                        <div class="preview-icon-wrap">
                            <span v-if="facilityExists(facility)" class="text-dark-teal text-uppercase">Yes</span>
                            <span v-else class="text-danger text-uppercase">No</span>
                        </div>
                    </td>
                    <td class="align-middle border-left border-secondary text-center">
                        <button @click="editPracticalSkill(facility)" class="btn bg-dark-teal btn-xs align-self-center">
                            <em class="icon ni ni-edit-fill"></em>
                            <span class="">Update</span>
                        </button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div><!-- .nk-tb-list -->


		<div class="modal fade zoom" tabindex="-1" id="practicalSkillModal">
			<div class="modal-dialog modal-dialog-centered" role="document">
				<div class="modal-content">
					<a @click="resetPracticalSkill()" class="cursor close" data-dismiss="modal" aria-label="Close">
						<em class="icon ni ni-cross"></em>
					</a>
					<form @submit.prevent="updatePracticalSkill()">
						<div class="modal-header">
							<h6 class="modal-title">Update Extra-curricular Activity</h6>
						</div>
						<div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
							<div class="row py-5">
								<div class="col-12 d-flex flex-column">
									<span class="font-weight-bold lead align-self-center">Does this {{ getLevelTitle }} offer {{ facility.name }}?</span>
									<div class="align-self-center mt-2">
										<div class="custom-control custom-radio custom-control-inline">
											<input v-model="facility.present_in_school" type="radio" id="facility_yes" value="true" class="custom-control-input">
											<label class="custom-control-label" for="facility_yes">Yes</label>
										</div>
										<div class="custom-control custom-radio custom-control-inline">
											<input v-model="facility.present_in_school" type="radio" id="facility_no" value="false" class="custom-control-input">
											<label class="custom-control-label" for="facility_no">No</label>
										</div>
									</div>
								</div><!-- .col -->
							</div><!-- .row -->
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button @click="resetPracticalSkill()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button :disabled="loading" type="submit" class="btn btn-primary d-flex">
								<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="loading" class="align-self-center">Saving...</span>
								<span v-if="loading" class="sr-only">Loading...</span>
								<span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import ErrorNotifications from "../../../Notifications.vue";
    import SuccessNotifications from "../../../Notifications.vue";

    export default {
		name: "PracticalSkillsSection",
		props: [
            'sectionId',
            'schoolObj',
            'surveyObj',
		    'practicalSkillsObj',
            'schoolTypeIdObj'
        ],
        components: {
            ErrorNotifications,
            SuccessNotifications,
        },
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
                api_url: '/institutions/surveys/practical-skill',
                school: {
                    practical_skills: [],
                },
                survey: {
                    current_section_id: '',
                    sections: [{name:'', is_complete_yn:false}],
                    section_items: [{name:'', section_id:'', is_complete_yn:false}],
                    survey: {name: ''},
                },
                practical_skills_types: [],
                facility: {
                    section_id: '',
                    survey_id: '',
                    practical_skill_id: '',
				    name: '',
                    present_in_school: '',
                },

			}
		},
		methods: {
			initPlugins: function () {
				let self = this;

                this.school = this.schoolObj;
                this.survey = this.surveyObj;
				this.practical_skills_types = this.practicalSkillsObj;
                this.facility.section_id = this.sectionId;
                this.facility.survey_id = this.survey.id;
			},

			facilityExists: function (facility) {
				return this.school.practical_skills.find(item=>{
					return item.practical_skill_id === facility.id && item.present_in_school;
				}) !== undefined;
			},
			editPracticalSkill: function (facility) {
				this.facility.practical_skill_id = facility.id;
				this.facility.name = facility.name;
				if (this.facilityExists(facility)) {
					this.facility.present_in_school = true;
				} else {
                    this.facility.present_in_school = false;
				}
				$('#practicalSkillModal').modal({backdrop: "static"});
			},
			updatePracticalSkill: function () {
				this.loading = true;
				axios.post(this.api_url+'/'+this.survey.id, this.facility)
				.then(response=>{
					this.school.practical_skills = response.data.practical_skills;
					this.resetPracticalSkill();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Extra-curricular activity updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
			},

			resetPracticalSkill: function () {
				this.loading = false;
				this.facility.name = ''
				this.facility.practical_skill_id = ''
                this.facility.section_id = this.sectionId;
                this.facility.survey_id = this.survey.id;
				$('#practicalSkillModal').modal("hide");
			},

            renderError: function (error) {
                if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                } else if (error.response && error.response.status === 401) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                } else if (error.response && error.response.status === 404) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                } else if (error.response && error.response.status === 422) {
                    let text = '';
                    for (let field in error.response.data.errors) {
                        for (let i = 0; i < error.response.data.errors[field].length; i++) {
                            text += '<br>'+ error.response.data.errors[field][i];
                        }
                    }
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
                } else {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                }
                $("html, body").animate({ scrollTop: 0 }, "slow");
            },

		},
		computed: {
            getLevelTitle: function() {
                if (this.schoolTypeIdObj > 3) {
                    return "institution";
                } else {
                    return "school";
                }
            }
		}
	}
</script>

<style scoped>

</style>
