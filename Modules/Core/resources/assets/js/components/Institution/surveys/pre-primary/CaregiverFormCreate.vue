<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <form @submit.prevent="verify ? createCaregiver() : (uganda ? verifyNIN() : verifyWorkPermit())">
            <div class="row">
                <div class="col-lg-4 col-md-12">
                    <div class="form-group d-flex flex-column justify-content-center">
                        <label class="align-self-center form-label">Add Photo</label>
                        <input
                            ref="photo" @change="selectFile"
                            accept="image/x-png,image/jpeg"
                            data-max-file-size="2M"
                            id="caregiverWithPhoto"
                            type="file"
                            class="dropify"
                            data-height="190"
                            data-allowed-file-extensions="jpeg jpg png"
                            :data-default-file="caregiver.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                    </div>
                    <div class="d-flex flex-column justify-content-center">
                        <button @click="caregiver.photo === null || caregiver.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                            <em class="icon ni ni-camera-fill"></em>
                            <span v-if="caregiver.photo === null || caregiver.photo === ''">Choose Photo</span>
                            <span v-else>Remove Photo</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 overflow-auto scrollbar-dark-teal h-425px">
                    <h6 class="overline-title title text-dark-teal">TEACHER DETAILS</h6>
                    <div class="row mt-3">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is this staff member a refugee?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="yes" id="staffIsRefuge">
                                    <label class="custom-control-label text-uppercase" for="staffIsRefuge">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="verify" type="radio" class="custom-control-input" v-model="staff_refugee_no" value="no" id="staffIsNotRefugee">
                                    <label class="custom-control-label text-uppercase" for="staffIsNotRefugee">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                    <select required id="caregiverCountryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-if="uganda" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="caregiverNIN" class="form-label">National ID (NIN) <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="uganda" v-model.trim="caregiver.nin" id="caregiverNIN" maxlength="14" type="text" placeholder="eg. CM74838348F83" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="caregiverWorkPermit" class="form-label">Work Permit <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input pattern="^EP[0-9]{7}$" title="Work Permit Format EP001122" :required="!uganda" v-model.trim="caregiver.work_permit" id="caregiverWorkPermit" minlength="9" maxlength="9" type="text" placeholder="eg. EP001122" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && staff_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="staffRefugeeNumber" class="form-label">Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && staff_refugee_no === 'yes'" v-model.trim="caregiver.refugee_number" id="staffRefugeeNumber" minlength="12" type="text" title="Staff Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="caregiverFirstName" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" v-model.trim="caregiver.first_name" :disabled="uganda" id="caregiverFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="caregiverSurname" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="!uganda" v-model.trim="caregiver.surname" id="caregiverSurname" :disabled="uganda" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="caregiverOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model.trim="caregiver.other_names" id="caregiverOtherNames" type="text" :disabled="uganda" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="caregiverBirthDate">Date Of Birth <span v-if="!uganda" class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input :required="!uganda" v-model.trim="caregiver.birth_date" placeholder="eg. 23/05/2001" :disabled="uganda" id="caregiverBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Gender</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" type="radio" :disabled="uganda" class="custom-control-input" v-model="caregiver.gender" value="M" id="caregiverMale">
                                        <label class="custom-control-label text-uppercase" for="caregiverMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhoto()" type="radio" :disabled="uganda" class="custom-control-input" v-model="caregiver.gender" value="F" id="caregiverFemale">
                                        <label class="custom-control-label text-uppercase" for="caregiverFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Religion <span class="text-danger">*</span></label>
                                    <select required id="religionId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="religion in religions" :key="religion.id" :value="religion.id">{{ religion.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Marital Status <span class="text-danger">*</span></label>
                                    <select required id="caregiverMaritalStatusesId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_grade in marital_statuses" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-show="staff_refugee_no === 'no'" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Teacher Type <span class="text-danger">*</span></label>
                                    <select :required="staff_refugee_no === 'no'" id="caregiverTeacherTypeId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="teacher_type in teacher_types" :value="teacher_type.id">{{ teacher_type.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Education Level <span class="text-danger">*</span></label>
                                    <select required id="caregiverHighestEducationLevelsId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="education_level in education_levels" :value="education_level.id">{{ education_level.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Employment Status <span class="text-danger">*</span></label>
                                    <select required id="caregiverEmploymentStatusId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="employment_status in employment_statuses" :value="employment_status.id">{{ employment_status.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="teacher_type === 'TRAINED'" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Highest Teaching Qualification <span class="text-danger">*</span></label>
                                    <select :required="teacher_type === 'TRAINED'" id="caregiverHighestTeachingQualificationId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="qualification in trainedQualifications" :value="qualification.id">{{ qualification.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Designation <span class="text-danger">*</span></label>
                                    <select :required="teacher_type === 'TRAINED'" id="caregiverDesignation" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="designation in designations" :value="designation.id">{{ designation.name }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input required="" v-model="caregiver.phone_1" id="staffPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="caregiver.phone_2" id="staffPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-lg-12 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="staffEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="caregiver.email" id="staffEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="submit" id="caregiverFormCreateFormSubmit" hidden="hidden"></button>
        </form>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import moment from "moment";
export default {
    name: "CaregiverFormCreate",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'teacherTypesObj',
        'maritalStatusesObj',
        'countriesObj',
        'teacherTypesObj',
        'educationLevelsObj',
        'teacherDesignationsObj',
        'employmentStatusesObj',
        'teacherQualificationsObj',
    ],
    mounted() {
        this.loadReligions();
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            teacher_type: "",
            staff_refugee_no: 'no',
            verify: false,
            uganda: true,
            api_url: '/institutions/surveys/teaching-staff',
            photoDropify: null,
            school: {},
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            caregiver: {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                qualification_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            countries: [],
            marital_statuses: [],
            teacher_types: [],
            education_levels: [],
            designations: [],
            employment_statuses: [],
            teacher_qualifications: [],
            religions: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.marital_statuses = this.maritalStatusesObj;
            this.teacher_types = this.teacherTypesObj;
            this.education_levels = this.educationLevelsObj;
            this.designations = this.teacherDesignationsObj;
            this.employment_statuses = this.employmentStatusesObj;
            this.teacher_qualifications = this.teacherQualificationsObj;

            $('#caregiverBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(18, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.caregiver.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#caregiverWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.caregiver.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#caregiverCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.caregiver.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#caregiverDesignation').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.designation_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#caregiverMaritalStatusesId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.marital_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#caregiverTeacherTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.teacher_type_id = data.id !== "" ? Number(data.id) : data.id;
                    self.teacher_type = data.text;
                    if (self.teacher_type !== "TRAINED") {
                        $('#caregiverDesignation').val("").change();
                        $('#caregiverHighestTeachingQualificationId').val("").change();
                    }
                    self.loadQualifications(data.text);
                    return data.text;
                },
            });
            $('#caregiverHighestEducationLevelsId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.highest_education_level_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#caregiverHighestTeachingQualificationId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.qualification_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#caregiverEmploymentStatusId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.employment_status_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                // Initialize both phone inputs
                this.setupPhoneInput('#staffPhoneOne', 'phone_1');
                this.setupPhoneInput('#staffPhoneTwo', 'phone_2');

                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#caregiverCountryId').val(ug.id).change();
                $('#caregiverTeacherTypeId').val(trained.id).change();
            }, 50);
        },
        setupPhoneInput(inputId, modelKey) {
            const inputElement = document.querySelector(inputId);
            const iti = intlTelInput(inputElement, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: (selectedCountryPlaceholder) => `eg. ${selectedCountryPlaceholder}`,
            });

            const updatePhone = () => {
                const number = iti.getNumber();
                this.caregiver[modelKey] = number;
                inputElement.value = number;
            };

            inputElement.addEventListener('blur', updatePhone);
            inputElement.addEventListener('change', updatePhone);
        },
        loadQualifications: function (teacherType) {
            let self = this;
            let select = $("#caregiverHighestTeachingQualificationId");
            select.empty().trigger('change');
            let newOption = new Option("--SELECT--", "", false, false);
            select.append(newOption).trigger('change');
            self.caregiver.qualification_id = "";

            //load new options
            if (teacherType === "TRAINED") {
                self.trainedQualifications.forEach(qualification=>{
                    let qualificationOption = new Option(qualification.name, qualification.id, false, false);
                    select.append(qualificationOption).trigger('change');
                });
            } else if (teacherType === "QUALIFIED") {
                self.qualifiedQualifications.forEach(qualification=>{
                    let qualificationOption = new Option(qualification.name, qualification.id, false, false);
                    select.append(qualificationOption).trigger('change');
                });
            }
        },
        loadReligions () {
            let self = this;
            $('#religionId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionDModal'),
                templateSelection: function (data, container) {
                    self.caregiver.religion_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            axios
                .get('/lists/religions')
                .then(response=>{
                    this.religions = response.data
                })
                .catch(error=>{
                    console.log(error)
                })
        },
        verifyNIN: function () {
            if (moment(this.caregiver.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The caregiver must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                axios.post('/nira/user-info', {id_number: this.caregiver.nin.toUpperCase()})
                    .then(response => {
                        this.$parent.loading = false;
                        this.nira_person = response.data;
                        this.caregiver.first_name = this.nira_person.given_names;
                        this.caregiver.surname = this.nira_person.surname;
                        this.caregiver.birth_date = moment(this.nira_person.birth_date).format("D MMMM, YYYY");
                        this.caregiver.gender = this.nira_person.gender;
                        this.$parent.verify = false;
                        this.verify = true;
                        this.updateDefaultPhoto();
                        this.$refs.notifySuccess.messages.push({
                            status: 'success',
                            title: 'Success: ',
                            message: 'Teacher NIN was verified successfully'
                        });
                    })
                    .catch(error => {
                        this.$parent.loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyWorkPermit: function () {
            if (moment(this.caregiver.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The caregiver must be at least 18 years or older!'});
            } else {
                this.$parent.loading = true;
                window.setTimeout(() => {
                    this.$parent.loading = false;
                    this.$parent.verify = false;
                    this.verify = true;
                    this.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success!',
                        message: 'Caregiver ID Number is verified successfully'
                    });
                }, 1000);
            }
        },
        createCaregiver: function () {
            this.$parent.loading = true;
            let formData = new FormData();
            let self = this;
            if (moment(this.caregiver.birth_date).isAfter(moment().subtract(18, 'years'))) {
                this.$parent.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:'The staff member must be at least 18 years or older!'});
            } else {
                self.$parent.loading = true;
                formData.append('nin', (self.uganda ? self.caregiver.nin : ''));
                formData.append('work_permit', (!self.uganda ? self.caregiver.work_permit : ''));
                formData.append('refugee_number', (!self.uganda ? self.caregiver.refugee_number : ''));
                formData.append('first_name', self.caregiver.first_name);
                formData.append('surname', self.caregiver.surname);
                formData.append('other_names', self.caregiver.other_names);
                formData.append('birth_date', self.caregiver.birth_date);
                formData.append('gender', self.caregiver.gender);
                formData.append('photo', self.caregiver.photo);
                formData.append('marital_status_id', self.caregiver.marital_status_id);
                formData.append('teacher_type_id', self.caregiver.teacher_type_id);
                formData.append('religion_id', self.caregiver.religion_id);
                formData.append('designation_id', self.caregiver.designation_id);
                formData.append('highest_education_level_id', self.caregiver.highest_education_level_id);
                formData.append('employment_status_id', self.caregiver.employment_status_id);
                formData.append('qualification_id', self.caregiver.qualification_id);
                formData.append('country_id', self.caregiver.country_id);
                formData.append('phone_1', self.caregiver.phone_1);
                formData.append('phone_2', self.caregiver.phone_2);
                formData.append('email', self.caregiver.email);

                formData.append('section_id', self.sectionId);
                formData.append('survey_id', self.survey.id);

                axios.post(self.api_url+'/'+self.survey.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                    .then(response => {
                        self.$parent.teacher_qualifications = response.data.qualifications;
                        self.$parent.education_levels = response.data.education_levels;
                        self.$parent.$refs.notifySuccess.messages.push({status: 'success', title: 'Success: ', message:'Teacher saved successfully'});
                        self.resetCaregiver();
                    })
                    .catch(error=>{
                        self.$parent.loading = false;
                        self.renderError(error);
                    });
            }
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        resetCaregiver: function () {
            $('#updateSectionDModal').modal('hide');
            this.$parent.loading = false;
            this.$parent.verify = true;
            this.$parent.uganda = true;
            this.$parent.active_section = 'form-create';
            this.loading = false;
            this.verify = false;
            this.uganda = true;
            this.staff_refugee_no = 'no';
            this.caregiver= {
                nin: '',
                work_permit: '',
                refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(18, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                religion_id: '',
                country_id: 221,
                country: {title:''},
                marital_status_id: '',
                teacher_type_id: '',
                designation_id: '',
                highest_education_level_id: '',
                employment_status_id: '',
                qualification_id: '',
                phone_1: '',
                phone_2: '',
                email: '',
            };
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let trained = this.teacher_types.find(teacherType => {
                    return teacherType.name === "TRAINED";
                });
                $('#caregiverCountryId').val(ug.id).change();
                $('#caregiverTeacherTypeId').val(trained.id).change();
                $('#caregiverDesignation').val('').change();
                $('#caregiverMaritalStatusesId').val('').change();
                $('#caregiverHighestEducationLevelsId').val('').change();
                $('#caregiverHighestTeachingQualificationId').val('').change();
                $('#caregiverEmploymentStatusId').val('').change();
                $('#religionId').val('').change();
                this.clearPhoto();
            }, 50);
        },
        selectFile() {
            this.caregiver.photo = this.$refs.photo.files[0];
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.caregiver.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.caregiver.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.nira_person.photo !== null) {
                if (this.nira_person.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_person.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_person.photo);
                }
            } else {
                if (this.caregiver.photo === null) {
                    this.clearPhoto();
                }
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        qualifiedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return qualification.pivot.is_qualified;
            });
        },
        trainedQualifications: function () {
            return this.teacher_qualifications.filter(qualification => {
                return !qualification.pivot.is_qualified;
            });
        },
    },
}
</script>

<style scoped>

</style>
