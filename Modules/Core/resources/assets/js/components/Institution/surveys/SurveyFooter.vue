<template>
    <div class="d-flex flex-row mt-5 justify-content-end">
        <span v-if="currentSectionKey > 0" @click="navigateBack()" class="btn btn-outline-dark-teal mr-2">
            <em class="ni ni-arrow-left mr-1"></em>
            <span class="align-self-center">BACK</span>
        </span>
        <span v-if="currentSectionKey <= institutionSections.length - 2" @click="navigateNext()" class="btn btn-primary">
            <span class="align-self-center">NEXT</span>
            <em class="ni ni-arrow-right ml-1"></em>
        </span>
        <span v-if="currentSectionKey === institutionSections.length - 1" @click="saveSurvey()" class="btn btn-secondary ml-2">
            <span class="align-self-center">SUBMIT</span>
            <em class="ni ni-arrow-right ml-1"></em>
        </span>
    </div>
</template>

<script>
export default {
    name: "SurveyFooter",
    props: ['surveyObj', 'sectionId'],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            first_tab: false,
            last_tab: false,
            api_url: '#',
            section_id: '',
            section_item_id: '',
            survey: {
                name: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
        }
    },
    methods: {
        initPlugins: function () {
            this.survey = this.surveyObj;
        },
        navigateBack: function () {
            if (this.currentSectionKey > 0) {
                let previous = this.institutionSections[this.currentSectionKey - 1];
                $('#menuSectionItem'+previous.id).click();
                $("html, body").animate({ scrollTop: 0 }, "slow");
            }
        },
        navigateNext: function() {
            if (this.currentSectionKey < this.institutionSections.length - 1) {
                let next = this.institutionSections[this.currentSectionKey + 1];
                $('#menuSectionItem'+next.id).click();
                $("html, body").animate({ scrollTop: 0 }, "slow");
            }
        },
        saveSurvey: function () {
            window.location.href = "/institution/dashboard";
        }
    },
    computed: {
        institutionSections: function () {
            let sections = [];
            this.survey.sections.forEach(section=>{
                if (this.survey.school_type_id < 4) {
                    if (section.logical !== 'courses') {
                        sections.push(section);
                    }
                } else {
                    sections.push(section);
                }
            })
            return sections;
        },
        currentSection: function () {
            return this.institutionSections.find(section => {
                return section.id === Number(this.sectionId);
            });
        },
        currentSectionKey: function () {
            return this.institutionSections.findIndex(section => {
                return section.id === Number(this.sectionId);
            });
        },
    }
}
</script>

<style scoped>

</style>
