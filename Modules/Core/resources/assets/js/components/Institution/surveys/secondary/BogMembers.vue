<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="">
            <div class="data-item py-1">
                <div class="">
                    <span class="data-label">Does this school have Board Of Governors (BOG)?</span>
                    <span v-if="school.school_governance === null" class="ml-lg-5 data-value text-dark">NOT SET</span>
                    <span v-else-if="school.school_governance.has_cmc_smc_or_bog_yn || school.school_governance.has_cmc_smc_or_bog_yn === 1" class="ml-lg-5 data-value text-dark">YES</span>
                    <span v-else class="pl-lg-5 ml-lg-5 data-value text-dark">NO</span>
                </div>
                <div class="data-col data-col-end">
                    <button @click="editGovernance()" data-backdrop="static" type="button" class="btn btn-sm bg-dark-teal">
                        <em class="ni ni-edit-fill text-white mr-1"></em>Update
                    </button>
                </div>
            </div><!-- data-item -->
            <div class="data-col-end"></div>
        </div>
        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">How many BOG meetings were held in the last 12 months?</span>
                    <span v-if="school.school_governance === null" class="ml-lg-5 data-value text-dark">NOT SET</span>
                    <span v-else class="ml-lg-5 data-value text-dark">{{ school.school_governance.no_of_meetings }}</span>
                </div>

            </div><!-- data-item -->
            <div class="data-col-end"></div>
        </div>
        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Does this School hold assemblies ?</span>
                    <span v-if="school.school_governance === null" class="ml-lg-5 data-value text-dark">NOT SET</span>
                    <span v-else-if="school.school_governance.holds_assemblies_yn || school.school_governance.holds_assemblies_yn === 1" class="ml-lg-5 data-value text-dark">
                        YES, {{ school.school_governance.assembly_frequency.name.toUpperCase() }}
                    </span>
                    <span v-else class="ml-lg-5 data-value text-dark">NO</span>
                </div>
            </div><!-- data-item -->
            <div class="data-col-end"></div>
        </div>
        <div class="table-responsive mt-5">
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-center border-left border-white py-2" colspan="4">How many members does the school's BOG have?</th>
                    <th class="text-white align-middle text-center border-left border-white py-2" colspan="4">How many BOG members were trained in their roles and responsibilities by MoES in the last 12 months?</th>
                </tr>
                <tr>
                    <th colspan="2" style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-left border-secondary text-center">M</th>
                    <th colspan="2" style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-left border-secondary text-center">F</th>
                    <th colspan="2" style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-left border-secondary text-center">M</th>
                    <th colspan="2" style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-left border-secondary text-center">F</th>
                </tr>
                <tr>

                </tr>
                </thead>
                <tbody class="border-top-0 border-secondary">
                <tr>
                    <td colspan="2" class="align-middle border-left border-secondary text-center" >
                        <span>{{ totals.male }}</span>
                    </td>
                    <td colspan="2" class="align-middle border-left border-secondary text-center" >
                        <span>{{ totals.female }}</span>
                    </td>
                    <td colspan="2" class="align-middle border-left border-secondary text-center">
                        <span>{{ totals.trained_male }}</span>
                    </td>
                    <td colspan="2" class="align-middle border-left border-secondary text-center">
                        <span>{{ totals.trained_female }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="table-responsive mt-5">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">RECENTLY ADDED BOG MEMBERS</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">NAMES</th>
                    <th class="text-uppercase border-secondary text-dark">GENDER</th>
                    <th class="text-uppercase border-secondary text-dark">NATIONALITY</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="member in school.smc_committee">
                    <td class="text-uppercase text-dark border-secondary">
                        <div class="user-card">
                            <div class="user-avatar">
                                <img :src="member.person.photo_url" class="rounded-0" :alt="member.person.full_name">
                            </div>
                            <div class="user-name text-uppercase">
                                <span class="tb-lead cursor text-dark-teal">{{ member.person.full_name }}</span>
                            </div>
                        </div>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span class="" v-if="member.person.gender === 'M'">MALE</span>
                        <span class="" v-else>FEMALE</span>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span>UGANDAN</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#schoolSmcCommitteeMemberModal" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">ADD BOG MEMBERS INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="schoolSmcCommitteeMemberModal">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">ADD BOG MEMBERS' INFORMATION</h6>
                    </div>
                    <div class="modal-body">
                        <error-notifications ref="notifyErrorNIN"></error-notifications>
                        <form @submit.prevent="verify ? updateSchoolSmcCommitteeMember() : verifyNIN()">
                            <div v-if="!verify" class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <div class="form-text-hint bg-primary-dim">
                                                <span class="overline-title">NIN</span>
                                            </div>
                                            <input :disabled="verify" required v-model="form_member.id_number" type="text" class="form-control bg-primary-dim text-uppercase" minlength="14" maxlength="14" placeholder="Enter Committee Member NIN">
                                        </div>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <button :disabled="loading" type="submit" class="btn bg-dark-teal btn-block text-center d-flex">
                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span v-if="loading" class="align-self-center">Verifying NIN...</span>
                                            <span v-if="loading" class="sr-only">Verifying NIN...</span>
                                            <span v-if="!loading" class="align-self-center">VERIFY NIN</span>
                                            <em v-if="!loading" class="icon ni ni-user-fill-c"></em>
                                        </button>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->
                            <div v-show="verify" class="row g-4">
                                <div class="col-12 col-lg-6">
                                    <div class="table-responsive py-3">
                                        <table class="table table-sm table-hover">
                                            <tr>
                                                <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                    <div class="user-card">
                                                        <div class="w-150px">
                                                            <img id="committeeMemberAvatar" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                        </div>
                                                    </div><!-- .user-card -->
                                                </td>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                    <span class="">{{ nira_person.national_id }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                    <span class="">{{ nira_person.surname }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                    <span class="">{{ nira_person.given_names }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                    <span class="">{{ nira_person.gender }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                    <span class="">{{ nira_person.date_of_birth }}</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>

                                </div>
                                <div class="col-12 col-lg-6 mt-1">
                                    <div class="row g-4 align-self-center">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="form_member_email" class="form-label">Email <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="verify" v-model="form_member.email" id="form_member_email" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim">
                                                </div>
                                            </div>
                                        </div><!-- .col -->
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="member_phone_1" class="form-label">Phone <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="verify" v-model="form_member.phone_1" id="member_phone_1" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim">
                                                </div>
                                            </div>
                                        </div><!-- .col -->

                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="member_appointment_date" class="form-label">Appointment Date <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="verify" v-model.trim="form_member.appointment_date" id="member_appointment_date" type="text" placeholder="Enter Appointment Date" autocomplete="off" class="form-control bg-primary-dim">
                                                </div>
                                            </div>
                                        </div><!-- .col -->
                                        <!--                                        <div class="col-12 mt-1">-->
                                        <!--                                            <div class="form-group">-->
                                        <!--                                                <div class="form-label-group">-->
                                        <!--                                                    <label for="member_end_date" class="form-label">End Date <span class="text-danger">*</span></label>-->
                                        <!--                                                </div>-->
                                        <!--                                                <div class="form-control-group">-->
                                        <!--                                                    <input :required="verify" v-model.trim="form_member.end_date" id="member_end_date" type="text" placeholder="Enter End Date" autocomplete="off" class="form-control bg-primary-dim">-->
                                        <!--                                                </div>-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>&lt;!&ndash; .col &ndash;&gt;-->

                                    </div><!-- .row -->

                                </div>
                                <span class="ml-4">
                                   Is this member trained on their respective role and responsibilities by MoES in the last 12months?
                                </span>

                                <div class="col-lg-12 ml-2">
                                    <div class="form-group">
                                        <div class="form-control-group">
                                            <ul class="custom-control-group g-3 align-center flex-wrap">
                                                <li>
                                                    <div class="custom-control custom-radio">
                                                        <input v-model.number="form_member.is_trained_yn" type="radio" class="custom-control-input" value="1" id="member_trained">
                                                        <label class="custom-control-label text-uppercase" for="member_trained">Trained</label>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="custom-control custom-radio">
                                                        <input v-model.number="form_member.is_trained_yn" type="radio" class="custom-control-input" value="0" id="member_not_trained">
                                                        <label class="custom-control-label text-uppercase" for="member_not_trained">Not Trained</label>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- .row -->
                            <div  v-show="verify" class="nk-kycfm-action pt-5 row">
                                <div class="col-lg-6">
                                    <button @click="resetSchoolSmcCommitteeMember()" v-if="verify" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                        <span class="align-self-center text-uppercase">CANCEL</span>
                                    </button>
                                </div>
                                <div class="col-lg-6">
                                    <button :disabled="loading" type="submit" class="btn bg-dark-teal text-center btn-block d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                        <span v-if="loading" class="sr-only">Loading...</span>
                                        <span v-if="!loading" class="align-self-center text-uppercase">SAVE</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="governanceModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateGovernanceCommittee()">
                        <div class="modal-header">
                            <h5 class="modal-title">UPDATE SCHOOL GOVERNANCE</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">Does this school have Board Of Governors (BOG) ?</label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.has_cmc_smc_or_bog_yn" type="radio" value="1" class="custom-control-input" id="policy_yes">
                                                    <label class="custom-control-label" for="policy_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.has_cmc_smc_or_bog_yn" type="radio" value="0" class="custom-control-input" id="policy_no">
                                                    <label class="custom-control-label" for="policy_no">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div v-show="form_report.has_cmc_smc_or_bog_yn === 1" class="col-12 mt-5">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">How many BOG meetings were held in the last 12 months?</label>
                                        <input  v-model.number="form_report.no_of_meetings" type="number" placeholder="Enter Number" class="form-control">
                                    </div>
                                </div>
                                <div v-show="form_report.has_cmc_smc_or_bog_yn === 1" class="col-12 mt-5">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label font-weight-bold">Does this School hold assemblies ? </label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.holds_assemblies_yn" type="radio" value="1" class="custom-control-input" id="holds_assemblies_yes">
                                                    <label class="custom-control-label" for="holds_assemblies_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.holds_assemblies_yn" type="radio" value="0" class="custom-control-input" id="holds_assemblies_no">
                                                    <label class="custom-control-label" for="holds_assemblies_no">No</label>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                </div>
                                <div v-show="form_report.holds_assemblies_yn === 1 && form_report.has_cmc_smc_or_bog_yn === 1" class="col-12 mt-4">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolParadeId">Parade</label>
                                        <span class="form-note">Specify the school's parade.</span>
                                    </div>
                                    <div class="form-group">
                                        <select v-model="form_report.assembly_frequency_id" :required="form_report.holds_assemblies_yn === 1" id="schoolParadeId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="parade in assemblies" :value="parade.id">{{ parade.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "BogMembers",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'assembliesObj',
        'totalMembersObj',
        // 'countriesObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            verify: false,
            uganda: true,
            api_url: '/institutions/surveys/',
            school: {
                smc_committee: [],
                secondary_school: {
                    has_cmc_smc_or_bog_yn: 0,
                    holds_assemblies_yn: 0,
                },
                assemblies: [],
                school_governance: {
                    has_cmc_smc_or_bog_yn: 0,
                    no_of_meetings: '',
                    holds_assemblies_yn: 0,
                    assembly_frequency: {
                        name: ''
                    }
                }
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            assemblies: [],
            total_members: [],
            selected_assemblies: [],
            form_report: {
                section_id: '',
                survey_id: '',
                has_cmc_smc_or_bog_yn: 0,
                no_of_meetings: '',
                holds_assemblies_yn: 0,
                assembly_frequency_id: '',
            },
            form_member: {
                section_id: '',
                survey_id: '',
                first_name: '',
                surname: '',
                other_names: '',
                id_number: '',
                phone_1: '',
                gender: 'M',
                is_trained_yn: 0,
                birth_date: '',
                // country_id: '',
                // country: {
                //     name: '',
                // },
                appointment_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                end_date: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            committee_members: [],
            country: 'UGANDA',
        }
    },
    methods: {
        setCountryDefault: function (country_id) {
            $('#member_country_id').val(country_id).change();
        },
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_member.section_id = this.sectionId;
            this.form_member.survey_id = this.survey.id;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
            this.assemblies = this.assembliesObj;
            this.total_members = this.totalMembersObj;
            // this.countries = JSON.parse(this.countriesObj);

            $('#schoolParadeId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#governanceModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.assembly_frequency_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            // $('#member_country_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.form_member.country_id = data.id.length > 0 ? Number(data.id) : "";
            //         self.country = data.text;
            //         return data.text;
            //     },
            // });

            let member_phone_1 = document.querySelector('#member_phone_1');
            let iti_member_phone_1 = intlTelInput(member_phone_1, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            member_phone_1.addEventListener('blur', ()=>{
                self.form_member.phone_1 = iti_member_phone_1.getNumber().slice(-9);
                member_phone_1.value = iti_member_phone_1.getNumber().slice(-9);
            });
            member_phone_1.addEventListener('change', ()=>{
                self.form_member.phone_1 = iti_member_phone_1.getNumber().slice(-9);
                member_phone_1.value = iti_member_phone_1.getNumber().slice(-9);
            });

            $('#member_appointment_date').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.form_member.appointment_date = moment(e.date).format("D MMMM, YYYY");
            });

            $('#member_end_date').datepicker({
                format: 'd MM, yyyy',
                endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.form_member.end_date = e.date === undefined ? '' : moment(e.date).format("D MMMM, YYYY");
            });

            $('#memberBulkAction').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });
        },

        updateGovernanceCommittee: function() {
            this.loading = true;
            axios.post(this.api_url+'smc-committee/update-governance/'+this.survey.id,  this.form_report)
                .then(response => {
                    this.school.school_governance = response.data.school_governance;
                    this.loading = false;
                    this.resetSchoolGovernance();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"School Governance Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                })
        },

        editGovernance: function () {
            this.edit = true;
            this.resetSchoolGovernance();
            $('#governanceModal').modal({backdrop:'static'});
        },

        //verify committee member NIN
        verifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.form_member.id_number.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.nira_person = response.data;
                    this.form_member.first_name = this.nira_person.given_names;
                    this.form_member.surname = this.nira_person.surname;
                    this.form_member.other_names = this.nira_person.other_names;
                    this.form_member.gender = this.nira_person.gender;
                    this.form_member.birth_date = this.nira_person.date_of_birth;
                    this.verify = true;
                    if (this.nira_person.photo !== null) {
                        if (this.nira_person.photo.includes('.png')) {
                            $('#committeeMemberAvatar').attr('src', '/images/nira-photos/' + this.nira_person.photo);
                        } else {
                            $('#committeeMemberAvatar').attr('src', 'data:image/png;base64,' + this.nira_person.photo);
                        }
                    }
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },

        editSchoolSmcCommitteeMember: function (member) {
            $('#member_country_id').val(member.person.country_id).change();
            this.edit = true;
            this.form_member.id = member.person.id;
            this.form_member.first_name = member.person.first_name;
            this.form_member.surname = member.person.surname;
            this.form_member.other_names = member.person.other_names;
            this.form_member.id_number = member.person.id_number;
            this.form_member.phone_1 = member.person.phone_1;
            this.form_member.gender = member.person.gender;
            this.form_member.country_id = member.person.country_id;
            this.form_member.appointment_date = moment(member.person.appointment_date).format("D MMMM, YYYY");
            this.form_member.end_date = member.person.end_date === '' || member.person.end_date === null ? '' : moment(member.person.end_date).format("D MMMM, YYYY");
            $('#schoolSmcCommitteeMemberModal').modal({backdrop:'static'});
        },

        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        updateSchoolSmcCommitteeMember: function () {
            this.loading = true;
            let params = Object.assign({}, this.form_member);
            axios.post(this.api_url+'smc-committee/add-members/'+this.survey.id, params)
                .then(response=>{
                    this.school.smc_committee = response.data.school.smc_committee;
                    this.total_members = response.data.total_members;
                    this.resetSchoolSmcCommitteeMember();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"BOG Member Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        startLoading: function() {
            $.blockUI({
                message: $('#schoolSmcCommitteeLoadingMessage'),
                css: {
                    padding:0,
                    margin:0,
                    width:'30%',
                    top:'40%',
                    left:'35%',
                    textAlign:'center',
                    color:'#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor:'wait'
                },
            });
        },
        resetSchoolGovernance: function () {
            $('#governanceModal').modal('hide');
            this.loading = false;

            if (this.school.school_governance !== null){
                this.form_report.has_cmc_smc_or_bog_yn = this.school.school_governance.has_cmc_smc_or_bog_yn === false ? 0 : 1;
                this.form_report.no_of_meetings = this.school.school_governance.no_of_meetings === null ? 0 : this.school.school_governance.no_of_meetings;
                this.form_report.holds_assemblies_yn = this.school.school_governance.holds_assemblies_yn === false ? 0 : 1;
            }
            window.setTimeout(()=>{
                if (this.school.school_governance === null) {
                    $('#schoolParadeId').val('').change();
                } else {
                    $('#schoolParadeId').val(this.school.school_governance.assembly_frequency_id).change();
                }

            }, 50);

            this.form_report.section_id = this.sectionId;

        },
        resetSchoolSmcCommitteeMember: function () {
            $('#schoolSmcCommitteeMemberModal').modal('hide');
            this.loading = false;
            this.verify = false;
            this.edit = false;
            this.form_member.section_id = this.sectionId;
            this.form_member.survey_id = this.survey.id;
            this.form_member.first_name = '';
            this.form_member.surname = '';
            this.form_member.other_names = '';
            this.form_member.id_number = '';
            this.form_member.phone_1 = '';
            this.form_member.is_trained_yn = 0;
            this.form_member.gender = 'M';
            this.form_member.birth_date = '';
            this.form_member.appointment_date = '';
            this.form_member.end_date = '';
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            }
            // $('#member_country_id').val(this.form_member.country_id).change();
            $('#committeeMemberAvatar').attr('src', '@images/default_male.jpg');
        },


        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                this.$refs.notifyErrorNIN.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                this.$refs.notifyErrorNIN.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                this.$refs.notifyErrorNIN.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
                this.$refs.notifyErrorNIN.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                this.$refs.notifyErrorNIN.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        totals: function () {
            let response = {
                male: 0,
                female: 0,
                trained_male: 0,
                trained_female: 0,
            };

            this.total_members.forEach(member=>{
                response.male += member.male_count;
                response.female += member.female_count;
                response.trained_male += member.male_trained_count;
                response.trained_female += member.female_trained_count;
            });
            return response;
        },
    },
}
</script>

<style scoped>

</style>
