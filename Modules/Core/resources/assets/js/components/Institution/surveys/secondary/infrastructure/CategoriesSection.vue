<template>
    <div class="w-100 vertical-scrollable">
        <survey-infrastructure-main-facilities-secondary
            v-for="categoryObj in infrastructure_types"
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            section-obj="sectionId"
            :category-obj="categoryObj"
            :key="categoryObj.id"
            :subjects-obj="subjectsObj"
        ></survey-infrastructure-main-facilities-secondary>
    </div>
</template>

<script>

export default {
    name: "CategoriesSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'infrastructureTypesObj',
        'subjectsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            infrastructure_types: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.infrastructure_types = this.infrastructureTypesObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
