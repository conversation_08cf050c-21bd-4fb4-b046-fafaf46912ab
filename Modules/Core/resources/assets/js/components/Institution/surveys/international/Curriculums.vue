<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="curriculums">
            <button data-toggle="modal" @click="editCurriculums()" data-target="#updateCurriculumModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                <span class="text-uppercase">UPDATE CURRICULUM</span>
            </button>
            <div class="table-responsive">
                <table class="table table-bordered table-sm mt-2">
                    <tr class="bg-secondary">
                        <td class="text-uppercase align-middle text-white border-1 border-white">curriculums</td>
                        <td class="text-uppercase align-middle text-white border-1 border-white">Sections</td>
                        <td class="text-uppercase align-middle text-white border-1 border-white">Action</td>
                    </tr>
                    <tr v-for="curriculum in school.international_curriculums" class="">
                        <td class="text-uppercase align-middle text-dark border-secondary border-1">
                            {{ curriculum.name }}
                        </td>
                        <td class="text-uppercase align-middle text-dark border-secondary border-1">
                            <ul class="list list-sm">
                                <li v-for="section in curriculum.sections">{{ section.name }}</li>
                            </ul>
                        </td>
                        <td class="text-uppercase align-middle text-dark border-secondary border-1">
                            <button data-toggle="modal" @click="editSections(curriculum)" data-target="#updateSectionsModal" data-backdrop="static" class="btn btn-sm bg-dark-teal">
                                <span class="text-uppercase text-white">UPDATE SECTIONS</span>
                            </button>
                        </td>
                    </tr>
                </table>
            </div>
            <div v-if="!school.international_curriculums.length" class="card card-stretch" style="box-shadow: none;">
                <div class="card-inner-group">
                    <div class="card-body">
                        <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                            <em class="icon ni ni-alert-circle"></em> There are no curriculums to display at the moment.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update curriculums Modal -->
            <div class="modal fade zoom" tabindex="-1" id="updateCurriculumModal">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Curriculum</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyErrorCurriculum"></error-notifications>
                            <form @submit.prevent="updateCurriculum()">
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Curriculum for your school</span>
                                <div v-if="selectedCurriculums.length" :class="[selectedCurriculums.length > 5 ? 'h-150px overflow-auto' : '']">
                                    <ul class="list list-sm">
                                        <li class="py-0 fs-13px" v-for="curriculum in selectedCurriculums">{{ curriculum.name }}</li>
                                    </ul>
                                </div>
                                <hr v-if="selectedCurriculums.length" class="border-dark-teal border-1 my-4">
                                <div class="">
                                    <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available curriculums</span>
                                    <div :class="[all_curriculums.length > 9 ? 'h-200px overflow-auto':'', 'my-1']">
                                        <div v-for="curriculum in all_curriculums" class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input v-model="international_curriculums" :value="curriculum.id" type="checkbox" class="custom-control-input" :id="'curriculumCheck'+curriculum.id">
                                            <label class="custom-control-label" :for="'curriculumCheck'+curriculum.id">{{ curriculum.name }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 d-flex justify-content-center">
                                    <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                                    </button>
                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Updating...</span>
                                        <span v-if="loading" class="sr-only">Updating...</span>
                                        <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Update curriculums Modal -->
            <!--sections modal -->
            <div class="modal fade zoom" tabindex="-1" id="updateSectionsModal">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Sections</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <form @submit.prevent="updateSections()">
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Sections for {{ curriculum.name }} Curriculum</span>
                                <div v-if="selectedSections.length" :class="[selectedSections.length > 5 ? 'h-150px overflow-auto' : '']">
                                    <ul class="list list-sm">
                                        <li class="py-0 fs-13px" v-for="section in selectedSections">{{ section.name }}</li>
                                    </ul>
                                </div>
                                <hr v-if="selectedSections.length" class="border-dark-teal border-1 my-4">
                                <div class="">
                                    <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available Sections</span>
                                    <div :class="[all_sections.length > 9 ? 'h-200px overflow-auto':'', 'my-1']">
                                        <div v-for="section in all_sections" class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input v-model="international_sections" :value="section.id" type="checkbox" class="custom-control-input" :id="'sectionCheck'+section.id">
                                            <label class="custom-control-label" :for="'sectionCheck'+section.id">{{ section.name }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 d-flex justify-content-center">
                                    <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                                    </button>
                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Updating...</span>
                                        <span v-if="loading" class="sr-only">Updating...</span>
                                        <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import SurveyFooter from "../SurveyFooter.vue";
export default {
    name: "Curriculums",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        SurveyFooter,
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            edit: false,
            loading: false,
            api_url: '/institutions/surveys/school/section-b',
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                ownership_status: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                funding_source: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                international_curriculums: [{ sections: []}],
                international_calendars: [],
                international_school: {
                    authority: {name: ''},
                    school_type: {name: ''},
                    supply_number: '',
                    registering_body_id: '',
                    admits_day_scholars_yn: '',
                    admits_boarders_yn: '',
                    campuses: [],
                },
            },
            form_curriculum: {
                id: '',
                name: '',
                survey_id: '',
            },
            form_section: {
                id: '',
                name: '',
                survey_id: '',
            },
            curriculum: {
                id: '',
                name: '',
            },
            all_curriculums: [],
            all_sections: [],
            international_curriculums: [],
            international_sections: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_curriculum.survey_id = this.survey.id;
            this.form_section.survey_id = this.survey.id;
            this.form_curriculum.section_id = this.sectionId;
            this.form_section.section_id = this.sectionId;

            this.fillCheckBoxes();
            this.allCurriculums();

            //this.fillCheckBoxesSections();
            this.allSections();

        },
        fillCheckBoxes: function () {
            this.international_curriculums = [];
            this.school.international_curriculums.forEach(schoolCurriculum => {
                this.international_curriculums.push(schoolCurriculum.pivot.inter_sch_curriculum_id);
            });
        },
        fillCheckBoxesSections: function (curriculum) {
            this.international_sections = [];
            curriculum.sections.forEach(section => {
                if (!this.international_sections.includes(section.id)) {
                    this.international_sections.push(section.pivot.inter_sch_section_id);
                }
            });
        },
        editCurriculums: function () {
            this.fillCheckBoxes();
            $('#updateCurriculumModal').modal({backdrop: "static"});
        },
        editSections: function (curriculum) {
            this.fillCheckBoxesSections(curriculum);
            this.curriculum.name = curriculum.name;
            this.curriculum.id = curriculum.id;
            $('#updateSectionsModal').modal({backdrop: "static"});
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        allCurriculums: function () {
            axios.get('/institutions/surveys/all-international-curriculums')
                .then(response=>{
                    this.all_curriculums = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },
        allSections: function () {
            axios.get('/institutions/surveys/all-international-sections')
                .then(response=>{
                    this.all_sections = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },

        updateCurriculum: function () {
            this.loading = true;
            axios.post('/institutions/surveys/update-international-curriculums', {curriculums: this.international_curriculums, survey_id: this.survey.id})
                .then(response=>{
                    $('#updateCurriculumModal').modal('hide');
                    this.loading = false;
                    this.$set(this.school, 'international_curriculums', response.data);
                    this.$set(this.$parent.school, 'international_curriculums', response.data);
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success!', message:'Curriculum updated successfully'});
                })
            .catch(error => {
                this.loading = false;
                console.log(error);
                this.renderError(error);
            });
        },

        updateSections: function () {
            this.loading = true;
            axios.post('/institutions/surveys/update-international-sections', {curriculum_id: this.curriculum.id, sections: this.international_sections, survey_id: this.survey.id})
                .then(response=>{
                    $('#updateSectionsModal').modal('hide');
                    this.loading = false;
                    this.$set(this.school, 'international_curriculums', response.data);
                    this.$set(this.$parent.school, 'international_curriculums', response.data);
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success!', message:'Sections updated successfully'});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyErrorCurriculum.messages.push({status: 'error', title: 'Data Error!', message:text});
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyErrorCurriculum.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyErrorCurriculum.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyErrorCurriculum.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyErrorCurriculum.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedCurriculums: function () {
            return this.all_curriculums.filter(curriculum => {
                return this.international_curriculums.includes(curriculum.id);
            });
        },
        selectedSections: function () {
            return this.all_sections.filter(section => {
                return this.international_sections.includes(section.id);
            });
        },
    },
}
</script>

<style scoped>

</style>
