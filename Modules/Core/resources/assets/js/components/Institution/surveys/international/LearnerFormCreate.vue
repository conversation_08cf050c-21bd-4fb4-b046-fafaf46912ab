<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <form @submit.prevent="submitForm()">
            <div class="row">
                <div class="col-lg-4 col-md-12">
                    <div class="form-group d-flex flex-column justify-content-center">
                        <label class="align-self-center form-label">Add Photo</label>
                        <input
                            ref="photo" @change="selectFile"
                            accept="image/x-png,image/jpeg"
                            data-max-file-size="2M"
                            id="learnerWithPhoto"
                            type="file"
                            class="dropify"
                            data-height="190"
                            data-allowed-file-extensions="jpeg jpg png"
                            :data-default-file="learner.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg'" />
                    </div>
                    <div class="d-flex flex-column justify-content-center">
                        <button @click="learner.photo === null || learner.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn btn-primary">
                            <em class="icon ni ni-camera-fill"></em>
                            <span v-if="learner.photo === null || learner.photo === ''">Choose Photo</span>
                            <span v-else>Remove Photo</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-8 col-md-12 pb-4 overflow-auto scrollbar-dark-teal h-425px">
                    <h6 class="overline-title title text-dark-teal">LEARNER DETAILS</h6>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Does this learner have a NIN?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="learner_verify || parent_verify" @change="updateDefaultPhotoAndCountry()" type="radio" class="custom-control-input" v-model="learner_nin" value="yes" id="learnerWithNIN">
                                    <label class="custom-control-label text-uppercase" for="learnerWithNIN">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="learner_verify || parent_verify" @change="updateDefaultPhotoAndCountry()" type="radio" class="custom-control-input" v-model="learner_nin" value="no" id="learnerWithoutNIN">
                                    <label class="custom-control-label text-uppercase" for="learnerWithoutNIN">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="learner_nin === 'yes'" class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerNIN" class="form-label">Learner NIN</label>
                                </div>
                                <div class="form-control-group">
                                    <div class="input-group">
                                        <input :disabled="learner_verify" :required="learner_nin === 'yes'" v-model.trim="learner.nin" id="learnerNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                        <div class="input-group-append">
                                            <button :disabled="learner.nin === '' || loading || learner_verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="loading" class="align-self-center">Verifying...</span>
                                                <span v-if="loading" class="sr-only">Verifying...</span>
                                                <span v-if="!loading" class="">Verify Learner NIN</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr v-show="learner_nin === 'no' && learner_verify" class="border-dark-teal my-2">
                    <div v-show="learner_verify" class="row">
                        <div class="col-12 mt-lg-0 mt-3">
                            <div class="table-responsive py-3">
                                <table class="table table-sm">
                                    <tr>
                                        <td class="px-2 align-middle border-dark-teal border-1 text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                            <span class="">{{ nira_learner.surname }} {{ nira_learner.given_names }}</span>
                                        </td>
                                        <td class="px-2 align-middle border-dark-teal border-1 text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                            <span class="">{{ nira_learner.national_id }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle border-bottom border-dark-teal border-1 text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                            <span v-if="nira_learner.gender === 'M'" class="">MALE</span>
                                            <span v-if="nira_learner.gender === 'F'" class="">FEMALE</span>
                                        </td>
                                        <td class="px-2 align-middle border-bottom border-dark-teal border-1 text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                            <span class="">{{ nira_learner.date_of_birth }}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div v-show="learner_nin === 'no'" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Learner Nationality <span class="text-danger">*</span></label>
                                    <select :required="learner_nin === 'no'" id="learnerCountryId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id" :disabled="learner_refugee_no === 'yes' && country.name === 'UGANDA'">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && learner_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="learnerStudentNumber" class="form-label">Student Pass <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input pattern="^(S|s)(T|t)[0-9]{7}$" :required="!uganda && learner_nin === 'no' && learner_refugee_no === 'no'" v-model.trim="learner.student_pass" id="learnerStudentNumber" minlength="9" maxlength="9" type="text" title="Student Pass Format ST0011223" placeholder="eg. ST0011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && learner_nin === 'no' && learner_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="learnerRefugeeNumber" class="form-label">Learner Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && learner_nin === 'no' && learner_refugee_no === 'yes'" v-model.trim="learner.refugee_number" id="learnerRefugeeNumber" minlength="12" type="text" title="Learner Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="learner_nin === 'no'" class="row mt-3">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is this learner a refugee?</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="learner_verify || parent_verify || uganda" type="radio" class="custom-control-input" v-model="learner_refugee_no" value="yes" id="learnerIsRefuge">
                                    <label class="custom-control-label text-uppercase" for="learnerIsRefuge">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input :disabled="learner_verify || parent_verify || uganda" type="radio" class="custom-control-input" v-model="learner_refugee_no" value="no" id="learnerIsNotRefugee">
                                    <label class="custom-control-label text-uppercase" for="learnerIsNotRefugee">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-show="learner_nin === 'no'" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="learner_nin === 'no'" v-model.trim="learner.first_name" id="learnerFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="learner_nin === 'no'" v-model.trim="learner.surname" id="learnerSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="learner_nin === 'no'" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model.trim="learner.other_names" id="learnerOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="learnerBirthDate">Date Of Birth</label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input readonly :required="learner_nin === 'no'" v-model.trim="learner.birth_date" placeholder="eg. 14 APRIL, 2022" id="learnerBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div v-show="learner_nin === 'no'" class="col-lg-12 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Sex</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhotoAndCountry()" type="radio" class="custom-control-input" v-model="learner.gender" value="M" id="learnerMale">
                                        <label class="custom-control-label text-uppercase" for="learnerMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input @change="updateDefaultPhotoAndCountry()" type="radio" class="custom-control-input" v-model="learner.gender" value="F" id="learnerFemale">
                                        <label class="custom-control-label text-uppercase" for="learnerFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="learner_nin === 'no' || learner_verify" class="row my-3">
                        <div class="col-lg-6 mt-2">
                            <div class="form-group">
                                <label class="form-label">Calendar <span class="text-danger">*</span></label>
                                <select :required="learner_verify" id="learnerCalendarId" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option v-for="calendar in school.international_calendars" :value="calendar.id">{{ calendar.name.toUpperCase() }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-2">
                            <div class="form-group">
                                <label class="form-label">Curriculum <span class="text-danger">*</span></label>
                                <select :required="learner_verify" id="learnerCurriculumId" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option v-for="curriculum in school.international_curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-2">
                            <div class="form-group">
                                <label class="form-label">Class <span class="text-danger">*</span></label>
                                <select :required="learner_verify || parent_verify" id="learnerEducationGradeId" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                </select>
                            </div>
                        </div>
                        <div v-show="uganda" class="col-lg-6 mt-2">
                            <div class="form-group">
                                <label class="form-label">District Of Birth <span class="text-danger">*</span></label>
                                <select :required="uganda && learner_verify" id="learnerDistrictOfBirthId" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is learner an orphan?</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" v-model="learner.is_orphan" value="yes" id="learnerIsOrphanYes">
                                        <label class="custom-control-label text-uppercase" for="learnerIsOrphanYes">YES</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" v-model="learner.is_orphan" value="no" id="learnerIsOrphanNo">
                                        <label class="custom-control-label text-uppercase" for="learnerIsOrphanNo">NO</label>
                                    </div>
                                </div>
                            </div>
                        </div>
<!--                        <div class="col-lg-6 mt-3">-->
<!--                            <div class="form-group">-->
<!--                                <label class="form-label">Familiar Language <span class="text-danger">*</span></label>-->
<!--                                <select :required="learner_verify" id="learnerFamiliarLanguageId" class="form-select-sm">-->
<!--                                    <option value="">&#45;&#45;SELECT&#45;&#45;</option>-->
<!--                                    <option v-for="familiar_language in familiar_languages" :value="familiar_language.id">{{ familiar_language.name.toUpperCase() }}</option>-->
<!--                                </select>-->
<!--                            </div>-->
<!--                        </div>-->
                    </div>
                    <div v-show="learner_nin === 'no' || learner_verify" class="row my-3">
                        <div v-show="learner.is_orphan === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="form-label">Type Of Orphan <span class="text-danger">*</span></label>
                                    <select :required="learner.is_orphan === 'yes' && learner_verify" id="learnerOrphanType" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option value="father">ONLY FATHER DEAD</option>
                                        <option value="mother">ONLY MOTHER DEAD</option>
                                        <option value="both-dead">BOTH PARENTS DEAD</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr v-show="learner_nin === 'no' || learner_verify" class="border-dark-teal my-4">
                    <h6 v-show="learner_nin === 'no' || learner_verify" class="overline-title title text-dark-teal">SPECIAL NEEDS</h6>
                    <div v-show="learner_nin === 'no' || learner_verify" class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div v-for="disability in disability_types" class="custom-control custom-control-sm custom-checkbox d-block">
                                    <input v-model="learner.disabilities" type="checkbox" :value="disability.id" class="custom-control-input" :id="'learnerDisability'+disability.id">
                                    <label class="custom-control-label" :for="'learnerDisability'+disability.id">{{ disability.name.toUpperCase() }}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr v-show="learner_nin === 'no' || learner_verify" class="border-dark-teal my-4">
                    <h6 v-show="learner_nin === 'no' || learner_verify" class="overline-title title text-dark-teal">HEALTH ISSUES</h6>
                    <div v-show="learner_nin === 'no' || learner_verify" class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div v-for="issue in health_issues" class="custom-control custom-control-sm custom-checkbox d-block">
                                    <input v-model="learner.learner_health_issues" type="checkbox" :value="issue.id" class="custom-control-input" :id="'learnerHealthIssue'+issue.id">
                                    <label class="custom-control-label" :for="'learnerHealthIssue'+issue.id">{{ issue.name.toUpperCase() }}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr v-show="learner_nin === 'no' || learner_verify" class="border-dark-teal my-4">
                    <h6 v-show="learner_nin === 'no' || learner_verify" class="overline-title title text-dark-teal">TALENTS</h6>
                    <div v-show="learner_nin === 'no' || learner_verify" class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div v-for="talent in talents" class="custom-control custom-control-sm custom-checkbox d-block">
                                    <input v-model="learner.learner_talents" type="checkbox" :value="talent.id" class="custom-control-input" :id="'learnerTalent'+talent.id">
                                    <label class="custom-control-label" :for="'learnerTalent'+talent.id">{{ talent.name.toUpperCase() }}</label>
                                </div>
                            </div>
                        </div>
                    </div>
<!--                    <hr v-show="learner_nin === 'no' || learner_verify" class="border-dark-teal my-4">-->
<!--                    <h6 v-show="learner_nin === 'no' || learner_verify" class="overline-title title text-dark-teal">PRACTICAL SKILLS</h6>-->
<!--                    <div v-show="learner_nin === 'no' || learner_verify" class="row">-->
<!--                        <div class="col-12">-->
<!--                            <div class="form-group">-->
<!--                                <div v-for="skill in practical_skills" class="custom-control custom-control-sm custom-checkbox d-block">-->
<!--                                    <input v-model="learner.learner_practical_skills" type="checkbox" :value="skill.id" class="custom-control-input" :id="'learnerPracticalSkill'+skill.id">-->
<!--                                    <label class="custom-control-label" :for="'learnerPracticalSkill'+skill.id">{{ skill.name.toUpperCase() }}</label>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <hr v-show="learner_nin === 'no' || learner_verify" class="border-dark-teal my-4">
                    <h6 v-show="learner_nin === 'no' || learner_verify" class="overline-title title text-dark-teal">PARENT DETAILS</h6>
                    <div v-show="(learner_nin === 'yes' && learner_verify) || (learner_nin === 'no' && uganda)" class="row">
                        <error-notifications ref="notifyErrorNin"></error-notifications>
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentNIN" class="form-label">Parent NIN</label>
                                </div>
                                <div class="form-control-group">
                                    <div class="input-group">
                                        <input :disabled="parent_verify" :required="learner_nin === 'yes' && learner_verify" v-model.trim="learner.parent_nin" id="learnerParentNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                        <div class="input-group-append">
                                            <button :disabled="learner.parent_nin === '' || loading || parent_verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="loading" class="align-self-center">Verifying...</span>
                                                <span v-if="loading" class="sr-only">Verifying...</span>
                                                <span v-if="!loading" class="">Verify Parent NIN</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="(learner_nin === 'yes' && learner_verify && parent_verify) || (learner_nin === 'no' && uganda && parent_verify)" class="row">
                        <div class="col-12">
                            <div class="table-responsive py-3">
                                <table class="table table-sm table-hover">
                                    <tr>
                                        <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                            <div class="user-card">
                                                <div class="w-150px">
                                                    <img id="learnerParentPhoto" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                </div>
                                            </div><!-- .user-card -->
                                        </td>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                            <span class="">{{ nira_parent.national_id }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                            <span class="">{{ nira_parent.surname }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                            <span class="">{{ nira_parent.given_names }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                            <span class="">{{ nira_parent.gender }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-2 align-middle text-uppercase text-dark">
                                            <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                            <span class="">{{ nira_parent.date_of_birth }}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div v-show="!uganda && (learner_nin === 'no' || learner_verify)" class="row">
                        <div v-if="!uganda && learner_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="learnerParentPassport" class="form-label">Parent Passport <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && learner_nin === 'no' && learner_refugee_no === 'no'" v-model.trim="learner.parent_passport" id="learnerParentPassport" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="!uganda && learner_nin === 'no' && learner_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="learnerParentRefugeeNumber" class="form-label">Parent Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && learner_nin === 'no' && learner_refugee_no === 'yes'" v-model.trim="learner.parent_refugee_number" id="learnerParentRefugeeNumber" minlength="12" type="text" title="Learner Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentFirstName" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :readonly="uganda" :required="!uganda && learner_nin === 'no' && learner_verify" v-model.trim="learner.parent_first_name" id="learnerParentFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-show="!uganda && (learner_nin === 'no' || learner_verify)" class="row mt-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentSurname" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :readonly="uganda" :required="!uganda && learner_nin === 'no' && learner_verify" v-model.trim="learner.parent_surname" id="learnerParentSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentOtherNames" class="form-label">Other Names</label>
                                </div>
                                <div class="form-control-group">
                                    <input :readonly="uganda" v-model.trim="learner.parent_other_names" id="learnerParentOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div v-show="!uganda && (learner_nin === 'no' || learner_verify)" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Sex <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input :disabled="uganda" type="radio" class="custom-control-input" v-model.number="learner.parent_gender" value="M" id="learnerParentMale">
                                        <label class="custom-control-label text-uppercase" for="learnerParentMale">Male</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input :disabled="uganda" type="radio" class="custom-control-input" v-model.number="learner.parent_gender" value="F" id="learnerParentFemale">
                                        <label class="custom-control-label text-uppercase" for="learnerParentFemale">Female</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-show="parent_verify" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="learnerParentRelationship">Parent Relationship <span class="text-danger">*</span></label>
                                <select :required="learner_verify && parent_verify" id="learnerParentRelationship" class="form-select-sm">
                                    <option value="">--SELECT--</option>
                                    <option value="parent">PARENT</option>
                                    <option value="guardian">GUARDIAN</option>
                                </select>
                            </div>
                        </div>

                        <div v-show="parent_verify" class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input :required="parent_verify" v-model="learner.parent_phone_1" id="parentPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="learner.parent_phone_2" id="parentPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input v-model="learner.parent_email" id="parentEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex pt-3 mt-2 border-top border-dark-teal justify-content-center">
                <button @click="resetLearner()" :disabled="saveLoading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                </button>
                <button v-if="!uganda && !parent_verify" :disabled="loading" type="submit" class="btn btn-primary d-flex">
                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span v-if="loading" class="align-self-center">Verifying...</span>
                    <span v-if="loading" class="sr-only">Verifying...</span>
                    <span v-if="!loading && !uganda" class="align-self-center">Verify Parent ID</span>
                    <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                </button>
                <button v-else :disabled="saveLoading" type="submit" class="btn btn-dark-teal d-flex">
                    <span v-if="saveLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span v-if="saveLoading" class="align-self-center">Saving...</span>
                    <span v-if="saveLoading" class="sr-only">Saving...</span>
                    <span v-if="!saveLoading" class="align-self-center">Save Learner</span><em v-if="!saveLoading" class="ni ni-arrow-right ml-2"></em>
                </button>
            </div>
        </form>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "LearnerFormCreate",
    props: [
        'sectionId',
        'districtsObj',
        'schoolObj',
        'surveyObj',
        'healthIssuesObj',
        'educationGradesObj',
        'schoolCurriculumsObj',
        'familiarLanguagesObj',
        'disabilityTypesObj',
        'countriesObj',
        'talentsObj',
        'practicalSkillsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            saveLoading: false,
            loading: false,
            learner_nin: "yes",
            learner_refugee_no: "no",
            learner_verify: false,
            parent_verify: false,
            uganda: true,
            photoDropify: null,
            school: {},
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            learner: {
                nin: '',
                refugee_number: '',
                parent_nin: '',
                student_pass: '',
                parent_passport: '',
                parent_refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_relationship: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                photo: null,
                birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                country_id: 221,
                country: {title:''},
                inter_sch_calendar_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_education_grade_id: '',
                inter_sch_education_level_id: '',
                stream_id: '',
                identities: [],
                familiar_language_id: '',
                district_of_birth_id: '',
                is_orphan: 'no',
                orphan_type: '',
                disabilities: [],
                learner_health_issues: [],
                learner_talents: [],
                learner_practical_skills: [],
            },
            nira_parent: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            nira_learner: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            countries: [],
            grades: [],
            districts: [],
            education_grades: [],
            curriculums: [],
            familiar_languages: [],
            disability_types: [],
            health_issues: [],
            talents: [],
            practical_skills: [],
            education_level_id: '',
            iti_person_phone_1: null,
            iti_person_phone_2: null,
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.education_grades = this.educationGradesObj;
            this.familiar_languages = this.familiarLanguagesObj;
            this.disability_types = this.disabilityTypesObj;
            this.districts = this.districtsObj;
            this.health_issues = this.healthIssuesObj;
            this.talents = this.talentsObj;
            // this.practical_skills = this.practicalSkillsObj;

            $('#learnerBirthDate').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(4, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.learner.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#learnerWithPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.learner.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#learnerCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.learner.country_id = data.id !== "" ? Number(data.id) : data.id;
                    // Disable learner refuge radio buttons if "UGANDA" is selected
                    // $('#learnerIsRefuge, #learnerIsNotRefugee').prop('disabled', self.uganda);

                    return data.text;
                },
            });
            $('#learnerDistrictOfBirthId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.district_of_birth_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerOrphanType').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.orphan_type = data.id;
                    return data.text;
                },
            });
            $('#learnerCalendarId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.inter_sch_calendar_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.inter_sch_curriculum_id = data.id !== "" ? Number(data.id) : data.id;
                    self.loadEducationGrades();
                    return data.text;
                },
            });
            $('#learnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.inter_sch_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    self.updateEducationLevel();
                    return data.text;
                },
            });
            $('#learnerEducationLevelId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.inter_sch_education_level_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
            // $('#learnerFamiliarLanguageId').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     dropdownParent: $('#updateSectionCModal'),
            //     templateSelection: function (data, container) {
            //         self.learner.familiar_language_id = data.id !== "" ? Number(data.id) : data.id;
            //         return data.text;
            //     },
            // });
            $('#learnerParentRelationship').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.parent_relationship = data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                $('#learnerCountryId').val(ug.id).change();
                $('#learnerFamiliarLanguageId').val(english.id).change();
                // $('#learnerParentRelationship').val('parent').change();
            }, 50);

            //Set phone number 1 flag
            let parentPhone1 = document.querySelector('#parentPhoneOne');
            this.iti_person_phone_1 = intlTelInput(parentPhone1, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone1.addEventListener('blur', ()=>{
                self.learner.parent_phone_1 = this.iti_person_phone_1.getNumber();
                parentPhone1.value = this.iti_person_phone_1.getNumber();
            });
            parentPhone1.addEventListener('change', ()=>{
                self.learner.parent_phone_1 = this.iti_person_phone_1.getNumber();
                parentPhone1.value = this.iti_person_phone_1.getNumber();
            });

            //Set phone number 2 flag
            let parentPhone2 = document.querySelector('#parentPhoneTwo');
            this.iti_person_phone_2 = intlTelInput(parentPhone2, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone2.addEventListener('blur', ()=>{
                self.learner.parent_phone_2 = this.iti_person_phone_2.getNumber();
                parentPhone2.value = this.iti_person_phone_2.getNumber();
            });
            parentPhone2.addEventListener('change', ()=>{
                self.learner.parent_phone_2 = this.iti_person_phone_2.getNumber();
                parentPhone2.value = this.iti_person_phone_2.getNumber();
            });
        },
        loadEducationGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#learnerEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.learner.inter_sch_education_grade_id = "";

            //load new options
            if (self.learner.inter_sch_curriculum_id !== "") {
                self.grades = self.school.international_curriculums.find(curriculum=>{
                    return curriculum.id === self.learner.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    //console.log(grade);
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        updateEducationLevel: function () {
            let education_grade = this.grades.find(grade=> {
                return grade.id === this.learner.inter_sch_education_grade_id;
            })

            this.education_level_id = education_grade === undefined ? '' : education_grade.inter_sch_education_level_id;

        },
        learnerVerifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.learner.nin.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.uganda = true;
                    this.nira_learner = response.data;
                    this.learner_verify = true;
                    this.learner.first_name = this.nira_learner.given_names;
                    this.learner.surname = this.nira_learner.surname;
                    this.learner.gender = this.nira_learner.gender;
                    this.learner.birth_date = moment(this.nira_learner.birth_date).format("D MMMM, YYYY");
                    this.updateDefaultPhotoAndCountry();
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        parentVerifyNIN: function () {
            if (this.isNinSame) {
                this.$refs.notifyErrorNin.messages.push({status: 'error', title: 'Error!', message:'Nin cannot be the same as that of a learner, provide a different Nin!!!'});
            } else {
                if (moment(this.learner.birth_date).isAfter(moment())) {
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error!',
                        message: 'This learner has not yet been born. Provide an accurate date of birth!'
                    });
                } else if (moment(this.learner.birth_date).isAfter(moment().subtract(4, 'years'))) {
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error!',
                        message: 'Learner must be at least 4 years to be enrolled!'
                    });
                } else {
                    this.loading = true;
                    axios.post('/nira/user-info', {id_number: this.learner.parent_nin.toUpperCase()})
                        .then(response => {
                            this.loading = false;
                            this.nira_parent = response.data;
                            this.learner.parent_first_name = this.nira_parent.given_names;
                            this.learner.parent_surname = this.nira_parent.surname;
                            this.learner.parent_gender = this.nira_parent.gender;
                            if (this.nira_parent.photo !== null) {
                                if (this.nira_parent.photo.includes('.png')) {
                                    $('#learnerParentPhoto').attr('src', '/images/nira-photos/' + this.nira_parent.photo);
                                } else {
                                    $('#learnerParentPhoto').attr('src', 'data:image/png;base64,' + this.nira_parent.photo);
                                }
                            }
                            this.parent_verify = true;
                        })
                        .catch(error => {
                            this.loading = false;
                            this.renderError(error);
                        });
                }
            }
        },
        parentVerifyPassport: function () {
            if (moment(this.learner.birth_date).isAfter(moment())) {
                this.loading = false;
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error!', message:'This learner has not yet been born. Provide an accurate date of birth!'});
            } else if (moment(this.learner.birth_date).isAfter(moment().subtract(4, 'years'))) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error!', message:'Learner must be at least 4 years to be enrolled!'});
            } else {
                this.loading = true;
                window.setTimeout(() => {
                    this.loading = false;
                    this.parent_verify = true;
                }, 1000);
            }
        },
        createLearner: function () {
            if (this.isNinSame) {
                this.$refs.notifyErrorNin.messages.push({status: 'error', title: 'Error!', message:'Nin cannot be the same as that of a learner, provide a different Nin!!!'});
            } else {
                this.saveLoading = true;
                let formData = new FormData();
                let self = this;

                // Concatenate country code with phone number
                //Phone 1
                let countryCodeOne = self.iti_person_phone_1.getSelectedCountryData().dialCode;
                let phoneNumberOne = self.iti_person_phone_1.getNumber();
                let fullPhoneNumberOne = `+${countryCodeOne}${phoneNumberOne}`;
                self.learner.parent_phone_1 = fullPhoneNumberOne;

                //Phone 2
                let countryCodeTwo = self.iti_person_phone_2.getSelectedCountryData().dialCode;
                let phoneNumberTwo = self.iti_person_phone_2.getNumber();
                let fullPhoneNumberTwo = `+${countryCodeTwo}${phoneNumberTwo}`;
                self.learner.parent_phone_2 = fullPhoneNumberTwo;

                // Remove the country code from the phone number in input if it's already there
                if (self.learner.parent_phone_1.startsWith(`+${countryCodeOne}`)) {
                    self.learner.parent_phone_1 = self.learner.parent_phone_1.replace(`+${countryCodeOne}`, '');
                }
                if (self.learner.parent_phone_2.startsWith(`+${countryCodeTwo}`)) {
                    self.learner.parent_phone_2 = self.learner.parent_phone_2.replace(`+${countryCodeTwo}`, '');
                }

                if (moment(self.learner.birth_date).isAfter(moment())) {
                    self.saveLoading = false;
                    self.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error!',
                        message: 'This learner has not yet been born. Provide an accurate date of birth!'
                    });
                } else if (moment(this.learner.birth_date).isAfter(moment().subtract(4, 'years'))) {
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error!',
                        message: 'Learner must be at least 4 years to be enrolled!'
                    });
                } else {
                    self.saveLoading = true;
                    self.in_national_curriculum = false;
                    formData.append('nin', (self.uganda ? self.learner.nin : ''));
                    formData.append('learner_nin_verify', self.learner_verify);
                    formData.append('parent_nin', (self.uganda ? self.learner.parent_nin : ''));
                    formData.append('parent_nin_verify', self.parent_verify);
                    formData.append('student_pass', (!self.uganda ? self.learner.student_pass.toUpperCase() : ''));
                    formData.append('student_refugee_number', (!self.uganda ? self.learner.refugee_number : ''));
                    formData.append('parent_passport', (!self.uganda ? self.learner.parent_passport : ''));
                    formData.append('parent_refugee_number', (!self.uganda ? self.learner.parent_refugee_number : ''));
                    formData.append('first_name', self.learner.first_name);
                    formData.append('surname', self.learner.surname);
                    formData.append('other_names', self.learner.other_names || null);
                    formData.append('birth_date', self.learner.birth_date);
                    formData.append('gender', self.learner.gender);
                    formData.append('parent_first_name', self.learner.parent_first_name);
                    formData.append('parent_surname', self.learner.parent_surname);
                    formData.append('parent_other_names', self.learner.parent_other_names || null);
                    formData.append('parent_gender', self.learner.parent_gender);
                    formData.append('parent_relationship', self.learner.parent_relationship);
                    formData.append('parent_phone_1', self.learner.parent_phone_1);
                    formData.append('parent_phone_2', self.learner.parent_phone_2);
                    formData.append('parent_email', self.learner.parent_email);
                    formData.append('photo', self.learner.photo);
                    formData.append('in_national_curriculum', self.in_national_curriculum);
                    formData.append('inter_sch_calendar_id', self.learner.inter_sch_calendar_id);
                    formData.append('inter_sch_curriculum_id', self.learner.inter_sch_curriculum_id);
                    formData.append('inter_sch_education_grade_id', self.learner.inter_sch_education_grade_id);
                    formData.append('inter_sch_education_level_id', self.education_level_id);
                    // formData.append('familiar_language_id', self.learner.familiar_language_id);
                    formData.append('district_of_birth_id', self.learner.district_of_birth_id);
                    formData.append('orphan_type', self.learner.orphan_type);
                    formData.append('disabilities', self.learner.disabilities);
                    formData.append('learner_health_issues', self.learner.learner_health_issues);
                    formData.append('learner_talents', self.learner.learner_talents);
                    // formData.append('learner_practical_skills', self.learner.learner_practical_skills);
                    formData.append('section_id', self.sectionId);
                    formData.append('country_id', self.learner.country_id);
                    formData.append('survey_id', self.survey.id);

                    axios.post('/institutions/learners/form-create', formData, {headers: {'Content-Type': 'multipart/form-data'}})
                        .then(response => {
                            //this.$parent.education_grades = response.data.education_grades;
                            this.$parent.curriculums = response.data.curriculums;
                            this.$parent.learner_enrolments = response.data.learner_enrolments;
                            self.$parent.$refs.notifySuccess.messages.push({
                                status: 'success',
                                title: 'Success!',
                                message: 'Learner saved successfully'
                            });
                            self.resetLearner();
                        })
                        .catch(error => {
                            self.saveLoading = false;
                            self.renderError(error);
                        });
                }
            }
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        resetLearner: function () {
            $('#updateSectionCModal').modal('hide');
            this.saveLoading = false;
            this.loading = false;
            this.learner_nin = "yes";
            this.learner_verify = false;
            this.parent_verify = false;
            this.$parent.active_section = 'form-create';
            this.uganda = true;
            this.learner= {
                nin: '',
                parent_nin: '',
                student_pass: '',
                refugee_number: '',
                parent_passport: '',
                parent_refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_relationship: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                photo: null,
                birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                country_id: 221,
                country: {title:''},
                inter_sch_calendar_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_education_grade_id: '',
                inter_sch_education_level_id: '',
                stream_id: '',
                identities: [],
                familiar_language_id: '',
                district_of_birth_id: '',
                is_orphan: 'no',
                orphan_type: '',
                disabilities: [],
                learner_health_issues: [],
                learner_talents: [],
                learner_practical_skills: [],
            };
            this.nira_parent = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.nira_learner = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                $('#learnerCountryId').val(ug.id).change();
                $('#learnerOrphanType').val('').change();
                $('#learnerCurriculumId').val('').change();
                $('#learnerEducationGradeId').val('').change();
                $('#learnerDistrictOfBirthId').val('').change();
                $('#learnerFamiliarLanguageId').val(english.id).change();
                $('#learnerParentRelationship').val('').change();
                $('#learnerBirthDate').datepicker('setDate', moment().toDate());
                this.clearPhoto();
            }, 50);
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        selectFile() {
            this.learner.photo = this.$refs.photo.files[0];
        },
        submitForm() {
            if (this.learner_nin === "yes") {
                return this.learner_verify ? (this.parent_verify ? this.createLearner() : this.parentVerifyNIN()) : this.learnerVerifyNIN();
            }

            return this.parent_verify ? this.createLearner() : (this.uganda ? this.parentVerifyNIN() : this.parentVerifyPassport());
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.learner.gender === 'M' ? '/images/default_male.jpg' : '/images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.learner.photo = null;
        },
        updateDefaultPhotoAndCountry: function () {
            if (this.learner_nin === 'yes' && this.nira_learner.photo !== null) {
                if (this.nira_learner.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_learner.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_learner.photo);
                }
            } else {
                if (this.learner.photo === null) {
                    this.clearPhoto();
                }
            }
            //Set country to Uganda if learner is a ugandan
            // if (this.learner_nin === "yes")  {
            //     let ug = this.countries.find(country => {
            //         return country.name === "UGANDA";
            //     });
            //     $('#learnerCountryId').val(ug.id).change();
            // }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        //Check if learner nin is same as that of a parent
        isNinSame() {
            const learnerNin = this.learner.nin;
            const parentNin = this.learner.parent_nin;

            // Check if both `learner nin` and `parent nin` are not empty before comparing
            if (learnerNin && parentNin) {
                return learnerNin.toUpperCase() === parentNin.toUpperCase();
            }
            return false;
        }
    },
}
</script>

<style scoped>

</style>
