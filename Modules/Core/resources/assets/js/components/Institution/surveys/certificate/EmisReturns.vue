<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="card card-stretch">
            <div class="card-inner-group">
                <div class="card-inner p-0">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col nk-tb-col-check">
                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                    <input @change="toggleAllSurveys()" v-model="select_all_surveys"  type="checkbox" class="custom-control-input" id="uid">
                                    <label class="custom-control-label" for="uid"></label>
                                </div>
                            </div>
                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">EMIS RETURN</span></div>
                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">TIME REMAINING</span></div>
                            <div class="nk-tb-col text-uppercase text-center"><span class="text-white">ACTIONS</span></div>
                        </div><!-- .nk-tb-item -->
                        <div v-for="survey in school_surveys" class="nk-tb-item">
                            <div class="nk-tb-col nk-tb-col-check">
                                <div v-if="survey.active !== 0" class="custom-control custom-control-sm custom-checkbox notext">
                                    <input @change="toggleOneSurvey(survey.id)" v-model="selected_surveys" :value="survey.id" type="checkbox" class="custom-control-input" :id="'uid'+survey.id">
                                    <label class="custom-control-label" :for="'uid'+survey.id"></label>
                                </div>
                            </div>
                            <div class="nk-tb-col text-uppercase">
                                <a :href="'/institution/emis-returns/details/'+survey.survey_id" class="text-dark-teal cursor">{{ survey.survey.name }} - {{ survey.school.school_type.display_name }}</a>
                            </div>
                            <div :id="'countDown'+survey.id" class="nk-tb-col text-uppercase text-center text-dark"></div>
                            <div class="nk-tb-col text-center">
                                <a :href="'/institution/emis-returns/details/'+survey.survey_id" class="btn btn-sm btn-primary cursor">Update</a>
<!--                                <span class="btn btn-sm bg-dark-teal cursor">Submit</span>-->
                            </div>
                        </div><!-- .nk-tb-item -->
                    </div><!-- .nk-tb-list -->
                    <div v-if="!school_surveys.length" class="p-5">
                        <div class="alert alert-secondary alert-icon">
                            <em class="icon ni ni-alert-circle"></em> No EMIS returns to display at the moment...
                        </div>
                    </div>
                </div><!-- .card-inner -->
            </div><!-- .card-inner-group -->
        </div><!-- .card -->
    </div><!-- .nk-block -->
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import {surveyDurationMixin} from "../../../../mixins/surveyDurationMixin";
export default {
    name: "EmisReturns",
    props: [
        'teachingPeriodObj',
        'schoolSurveysObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mixins: [surveyDurationMixin],
    data: function () {
        return {
            loading: false,
            select_all_surveys: false,
            selected_surveys: [],
            filtering: false,
            teaching_period: {},
            school_surveys: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school_surveys = this.schoolSurveysObj;
            this.teaching_period = this.teachingPeriodObj;

            this.school_surveys.forEach(schoolSurvey => {
                schoolSurvey.time_ending = schoolSurvey.survey.extension_date === null ? moment(schoolSurvey.survey.end_date) : moment(schoolSurvey.survey.extension_date);
                window.setInterval(() => {
                    schoolSurvey.time_ending = schoolSurvey.time_ending.subtract(0, 'seconds');
                    $('#countDown'+schoolSurvey.id).html(self.formatSurveyDuration(schoolSurvey.time_ending));
                }, 1000);
            });
        },
        toggleAllSurveys: function () {
            this.selected_surveys =[];

            if (this.select_all_surveys) {
                this.school_surveys.forEach(survey=>{
                    this.selected_surveys.push(survey.id)
                });
            }
        },
        toggleOneSurvey: function (id) {
            this.select_all_surveys = this.selected_surveys.length === this.school_surveys.length
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
