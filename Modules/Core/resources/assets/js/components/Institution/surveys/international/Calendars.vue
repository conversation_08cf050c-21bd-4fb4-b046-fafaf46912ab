<template>
    <div class="w-100 mt-3">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="calendars mt-4">
            <button data-toggle="modal" @click="editCalendars()" data-target="#updateCalendarModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                <span class="text-uppercase">UPDATE CALENDARS</span>
            </button>
            <div class="table-responsive">
                <table class="table table-bordered table-sm mt-2">
                    <tr class="bg-secondary">
                        <td class="text-uppercase align-middle text-white border-1 border-white">Calendars</td>
                    </tr>
                    <tr v-for="calendar in school.international_calendars" class="">
                        <td class="text-uppercase align-middle text-dark border-secondary border-1">
                            {{ calendar.name }}
                        </td>
                    </tr>
                </table>
            </div>
            <div v-if="!school.international_calendars.length" class="card card-stretch" style="box-shadow: none;">
                <div class="card-inner-group">
                    <div class="card-body">
                        <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                            <em class="icon ni ni-alert-circle"></em> There are no calendars to display at the moment.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update curriculums Modal -->
            <div class="modal fade zoom" tabindex="-1" id="updateCalendarModal">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Calendar</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <form @submit.prevent="updateCalendar()">
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Calendars for your school</span>
                                <div v-if="selectedCalendars.length" :class="[selectedCalendars.length > 5 ? 'h-150px overflow-auto' : '']">
                                    <ul class="list list-sm">
                                        <li class="py-0 fs-13px" v-for="calendar in selectedCalendars">{{ calendar.name }}</li>
                                    </ul>
                                </div>
                                <hr v-if="selectedCalendars.length" class="border-dark-teal border-1 my-4">
                                <div class="">
                                    <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available calendars</span>
                                    <div :class="[all_calendars.length > 9 ? 'h-200px overflow-auto':'', 'my-1']">
                                        <div v-for="calendar in all_calendars" class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input v-model="international_calendars" :value="calendar.id" type="checkbox" class="custom-control-input" :id="'calendarCheck'+calendar.id">
                                            <label class="custom-control-label" :for="'calendarCheck'+calendar.id">{{ calendar.name }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 d-flex justify-content-center">
                                    <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                                    </button>
                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Updating...</span>
                                        <span v-if="loading" class="sr-only">Updating...</span>
                                        <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Update calendars Modal -->
        </div>

    </div>

</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import SurveyFooter from "../SurveyFooter.vue";
export default {
    name: "Calendars",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        SurveyFooter,
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            edit: false,
            loading: false,
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                ownership_status: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                funding_source: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                international_curriculums: [{ sections: []}],
                international_calendars: [],
                international_school: {
                    authority: {name: ''},
                    school_type: {name: ''},
                    supply_number: '',
                    registering_body_id: '',
                    admits_day_scholars_yn: '',
                    admits_boarders_yn: '',
                    campuses: [],
                },
            },
            form_calendar: {
                id: '',
                name: '',
                survey_id: '',
            },
            curriculum: {
                id: '',
                name: '',
            },
            all_calendars: [],
            international_calendars: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_calendar.survey_id = this.survey.id;
            this.form_calendar.section_id = this.sectionId;

            this.fillCheckBoxesCalendars();
            this.allCalendars();
        },

        fillCheckBoxesCalendars: function () {
            this.international_calendars = [];
            this.school.international_calendars.forEach(schoolCalendar => {
                this.international_calendars.push(schoolCalendar.pivot.inter_sch_calendar_id);
            });
        },

        editCalendars: function () {
            this.fillCheckBoxesCalendars();
            $('#updateCalendarModal').modal({backdrop: "static"});
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },

        allCalendars: function () {
            axios.get('/institutions/surveys/all-international-calendars')
                .then(response=>{
                    this.all_calendars = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },
        updateCalendar: function () {
            this.loading = true;
            axios.post('/institutions/surveys/update-international-calendars', {calendars: this.international_calendars, survey_id: this.survey.id})
                .then(response=>{
                    $('#updateCalendarModal').modal('hide');
                    this.loading = false;
                    this.school.international_calendars = response.data;
                    this.$parent.school.international_calendars = response.data;
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success!', message:'Calendar updated successfully'});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedCalendars: function () {
            return this.all_calendars.filter(calendar => {
                return this.international_calendars.includes(calendar.id);
            });
        },
    },
}
</script>

<style scoped>

</style>
