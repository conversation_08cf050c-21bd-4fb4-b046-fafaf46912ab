<template>
	<div class="w-100 mt-5">
        <success-notifications ref="notifySuccess"></success-notifications>
        <h4>Hand Washing Facilities</h4>
        <div class="data-item py-1">
            <div class="data-col">
                <span class="" style="font-size: initial">Functional hand washing facility near or in the toilet/latrine</span>
                <span v-if="school.hand_washing === null || school.hand_washing.method === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.hand_washing.method.name }}</span>
            </div>
            <div class="data-col data-col-end">
                <button @click="editFunctionalHandWash()" type="button" class="btn btn-sm bg-dark-teal">
                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                </button>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="table-responsive mt-5">
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Facility</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
                </thead>
                <tbody class="border-top-0 border-secondary">

                <tr v-for="facility in hand_washing_facility_types">
                    <td class="align-middle border-secondary">{{ facility.name.toUpperCase() }}</td>
                    <td class="align-middle border-left border-secondary text-center" >
                        <div class="preview-icon-wrap">
                            <span v-if="facilityExists(facility)" class="text-dark-teal text-uppercase">Yes</span>
                            <span v-else class="text-danger text-uppercase">No</span>
                        </div>
                    </td>
                    <td class="align-middle border-left border-secondary text-center">
                        <button @click="editHandWashingFacility(facility)" class="btn bg-dark-teal btn-xs align-self-center">
                            <em class="icon ni ni-edit-fill"></em>
                            <span class="">Update</span>
                        </button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div><!-- .nk-tb-list -->

        <div class="modal fade zoom" tabindex="-1" id="handWashModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetFunctionalHandWash()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateFacility()">
                        <div class="modal-header">
                            <h6 class="modal-title">Update Hand Washing Method</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="handWashMethodId">
                                        Does this School have a functional hand washing facility near or in the toilet/latrine ?</label>
                                    <span class="form-note">Specify the method used.</span>
                                </div>
                                <div class="form-group">
                                    <select v-model="facility.hand_washing_method_id" required id="handWashMethodId" class="form-select-sm">
                                        <option value="">--Select--</option>
                                        <option v-for="method in hand_washing_methods" :value="method.id">{{ method.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetFunctionalHandWash()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

		<div class="modal fade zoom" tabindex="-1" id="facilityModal">
			<div class="modal-dialog modal-dialog-centered" role="document">
				<div class="modal-content">
					<a @click="resetFacility()" class="cursor close" data-dismiss="modal" aria-label="Close">
						<em class="icon ni ni-cross"></em>
					</a>
					<form @submit.prevent="updateFacility()">
						<div class="modal-header">
							<h6 class="modal-title">Update Facility</h6>
						</div>
						<div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
							<div class="row py-5">
								<div class="col-12 d-flex flex-column">
									<span class="font-weight-bold lead align-self-center">Does this {{ getLevelTitle }} have {{ facility.name }}</span>
									<div class="align-self-center mt-2">
										<div class="custom-control custom-radio custom-control-inline">
											<input v-model="facility.present_in_school" type="radio" id="facility_yes" value="true" class="custom-control-input">
											<label class="custom-control-label" for="facility_yes">Yes</label>
										</div>
										<div class="custom-control custom-radio custom-control-inline">
											<input v-model="facility.present_in_school" type="radio" id="facility_no" value="false" class="custom-control-input">
											<label class="custom-control-label" for="facility_no">No</label>
										</div>
									</div>
								</div><!-- .col -->
							</div><!-- .row -->
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button @click="resetFacility()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button :disabled="loading" type="submit" class="btn btn-primary d-flex">
								<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="loading" class="align-self-center">Saving...</span>
								<span v-if="loading" class="sr-only">Loading...</span>
								<span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import ErrorNotifications from "../../../Notifications.vue";
    import SuccessNotifications from "../../../Notifications.vue";

    export default {
		name: "HandWashingFacilitiesSection",
		props: [
            'sectionId',
            'schoolObj',
            'surveyObj',
            'handWashingMethodsObj',
            'handWashingFacilityTypesObj',
            'schoolTypeIdObj'
        ],
        components: {
            ErrorNotifications,
            SuccessNotifications,
        },
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
                api_url: '/institutions/surveys/sanitation',
                school: {
                    sanitation: [],
                    hand_washing: {
                        method: {
                            name: ''
                        }
                    }
                },
                survey: {
                    current_section_id: '',
                    sections: [{name:'', is_complete_yn:false}],
                    section_items: [{name:'', section_id:'', is_complete_yn:false}],
                    survey: {name: ''},
                },
                hand_washing_methods: [],
                hand_washing_facility_types: [],
                facility: {
                    section_id: '',
                    survey_id: '',
                    hand_washing_method_id: '',
                    hand_washing_facility_id: '',
				    name: '',
                    present_in_school: '',
                },

			}
		},
		methods: {
			initPlugins: function () {
				let self = this;

                this.school = this.schoolObj;
                this.survey = this.surveyObj;
				this.hand_washing_methods = this.handWashingMethodsObj;
				this.hand_washing_facility_types = this.handWashingFacilityTypesObj;
                this.facility.section_id = this.sectionId;
                this.facility.survey_id = this.survey.id;

                $('#handWashMethodId').select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#handWashModal'),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.facility.hand_washing_method_id = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });
			},

			facilityExists: function (facility) {
				return this.school.sanitation.find(item=>{
					return item.hand_washing_facility_id === facility.id && item.present_in_school;
				}) !== undefined;
			},
            editFunctionalHandWash: function () {
                this.resetFunctionalHandWash();

                $('#handWashModal').modal({backdrop: "static"});
            },
            resetFunctionalHandWash: function () {
                $('#handWashModal').modal("hide");
                this.loading = false;

                window.setTimeout(()=>{
                    if (this.school.hand_washing === null) {
                        $('#handWashMethodId').val('').change();
                    } else {
                        $('#handWashMethodId').val(this.school.hand_washing.hand_washing_method_id).change();
                    }

                }, 50);

                this.facility.section_id = this.sectionId;
            },
			editHandWashingFacility: function (facility) {
				this.facility.hand_washing_facility_id = facility.id;
				this.facility.name = facility.name;
				if (this.facilityExists(facility)) {
					this.facility.present_in_school = true;
				} else {
                    this.facility.present_in_school = false;
				}
				$('#facilityModal').modal({backdrop: "static"});
			},
			updateFacility: function () {
				this.loading = true;
				axios.post(this.api_url+'/'+this.survey.id, this.facility)
				.then(response=>{
                    this.school.hand_washing = response.data.hand_washing;
					this.school.sanitation = response.data.sanitation;
                    this.resetFunctionalHandWash();
					this.resetFacility();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Hand Washing Facility Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
			},

			resetFacility: function () {
				this.loading = false;
				this.facility.name = ''
				this.facility.hand_washing_facility_id = ''
                this.facility.section_id = this.sectionId;
                //this.facility.survey_id = this.survey.id;
				$('#facilityModal').modal("hide");
			},

            renderError: function (error) {
                if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                } else if (error.response && error.response.status === 401) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                } else if (error.response && error.response.status === 404) {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                } else if (error.response && error.response.status === 422) {
                    let text = '';
                    for (let field in error.response.data.errors) {
                        for (let i = 0; i < error.response.data.errors[field].length; i++) {
                            text += '<br>'+ error.response.data.errors[field][i];
                        }
                    }
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
                } else {
                    this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                }
                $("html, body").animate({ scrollTop: 0 }, "slow");
            },

		},
		computed: {
            getLevelTitle: function() {
                if (this.schoolTypeIdObj > 3) {
                    return "institution";
                } else {
                    return "school";
                }
            }
		}
	}
</script>

<style scoped>

</style>
