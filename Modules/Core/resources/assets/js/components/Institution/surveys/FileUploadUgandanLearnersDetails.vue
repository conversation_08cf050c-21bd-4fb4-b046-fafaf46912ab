<template>
    <div class="w-100">
        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <tbody>
                <tr class="">
                    <th class="rounded-0 w-30 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        FILE NAME:
                    </th>
                    <td class="rounded-0 text-dark align-middle border-secondary border-top border-1">
                        {{ file_upload.file_name }}
                    </td>
                </tr>
                <tr class="">
    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
        STATUS:
    </th>
    <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
        <span class="badge bg-warning text-dark">Processing</span>
    </td>
</tr>

                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        DATE UPLOADED:
                    </th>
                    <td class="rounded-0 text-uppercase text-dark align-middle border-secondary border-top border-1">
                        {{ formatDateUploaded() }}
                    </td>
                </tr>
                <tr class="">
                    <th class="rounded-0 bg-secondary-dim text-dark text-uppercase align-middle border-secondary border-top border-1">
                        USER:
                    </th>
                    <td class="rounded-0 text-uppercase text-dark align-middle border-secondary border-top border-1">
                        {{ getUploader() }}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="table-responsive mt-5">
            <table class="table table-sm table-hover table-bordered">
                <tbody>
                <tr class="">
                    <th colspan="4" class="rounded-0 bg-secondary text-white text-uppercase align-middle border-secondary border-top text-center border-1">
                        NIN Summaries
                    </th>
                </tr>
                <tr class="">
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        NINs Processed
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Pending
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Successful
                    </th>
                    <th class="rounded-0 w-25 bg-secondary-dim text-dark text-uppercase text-center align-middle border-secondary border-top border-1">
                        Failed
                    </th>
                </tr>
                <tr class="">
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsFailed + getTotalNINsSuccess }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsPending }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsSuccess }}</span>
                    </td>
                    <td class="rounded-0 py-2 text-dark text-uppercase text-center align-middle border-secondary border-1">
                        <span class="fs-18px">{{ getTotalNINsFailed }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div id="ninStatuses" class="accordion accordion-s3 mt-5">
            <div class="accordion-item">
                <a href="#" class="accordion-head" data-toggle="collapse" data-target="#successfulStatus">
                    <h6 class="title">SUCCESSFUL STATUS</h6>
                    <span class="accordion-icon"></span>
                </a>
                <div class="accordion-body collapse show" id="successfulStatus" data-parent="#ninStatuses">
                    <div class="accordion-inner">
                        <table v-if="successfulLearnerNINs.length" class="table table-sm table-hover table-bordered">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id > 3">STUDENT NINS</span>
                                    <span v-else>Learner NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    NATIONALITY
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in successfulLearnerNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.gender }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    UGANDA
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table v-if="successfulParentNINs.length" :class="[successfulLearnerNINs.length ? 'mt-5' : '' , 'table table-sm table-hover table-bordered ']">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id === 6">NEXT OF KIN NINS</span>
                                    <span v-else>PARENT NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    NATIONALITY
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in successfulParentNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_gender }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    UGANDA
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <a href="#" class="accordion-head collapsed" data-toggle="collapse" data-target="#failedStatus">
                    <h6 class="title">FAILED STATUS</h6>
                    <span class="accordion-icon"></span>
                </a>
                <div class="accordion-body collapse" id="failedStatus" data-parent="#ninStatuses" >
                    <div class="accordion-inner">
                        <table v-if="failedLearnerNINs.length" class="table table-sm table-hover table-bordered">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id > 3">STUDENT NINS</span>
                                    <span v-else>Learner NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-40 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    ERRORS
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in failedLearnerNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.gender }}
                                </td>
                                <td class="rounded-0 text-dark align-middle border-secondary border-top border-1">
                                    {{ learner.learner_nin_verification_error }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table v-if="failedParentNINs.length" :class="[failedLearnerNINs.length ? 'mt-5' : '' , 'table table-sm table-hover table-bordered ']">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id === 6">NEXT OF KIN NINS</span>
                                    <span v-else>PARENT NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-40 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    ERRORS
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in failedParentNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_gender }}
                                </td>
                                <td class="rounded-0 text-dark align-middle border-secondary border-top border-1">
                                    {{ learner.parent_nin_verification_error }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table v-if="failedLearnerNINs.length" :class="[failedLearnerNINs.length || failedParentNINs.length ? 'mt-5' : '' , 'table table-sm table-hover table-bordered ']">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span>OTHER VALIDATION ERRORS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 w-40 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    DATE OF BIRTH
                                </th>
                                <th class="rounded-0 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    ERRORS
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in otherErrors" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.day+' '+learner.month+', '+learner.year }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.gender }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    <span @click="showErrors(learner)" class="cursor badge badge-red">View</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <a href="#" class="accordion-head collapsed" data-toggle="collapse" data-target="#pendingStatus">
                    <h6 class="title">PENDING STATUS</h6>
                    <span class="accordion-icon"></span>
                </a>
                <div class="accordion-body collapse" id="pendingStatus" data-parent="#ninStatuses" >
                    <div class="accordion-inner">
                        <table v-if="pendingLearnerNINs.length" class="table table-sm table-hover table-bordered">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id > 3">STUDENT NINS</span>
                                    <span v-else>Learner NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    NATIONALITY
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in pendingLearnerNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.learner_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.gender }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    UGANDA
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table v-if="pendingParentNINs.length" :class="[pendingLearnerNINs.length ? 'mt-5' : '' , 'table table-sm table-hover table-bordered ']">
                            <tbody>
                            <tr class="bg-secondary">
                                <th colspan="4" class="rounded-0 text-white text-uppercase align-middle border-white border-top border-1">
                                    <span v-if="file_upload.user.school_type_id === 6">NEXT OF KIN NINS</span>
                                    <span v-else>PARENT NINS</span>
                                </th>
                            </tr>
                            <tr class="bg-secondary">
                                <th class="rounded-0 text-white text-uppercase align-middle border-white border-top border-left-0 border-1">
                                    NAME
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    NIN
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-1">
                                    SEX
                                </th>
                                <th class="rounded-0 w-20 text-center text-white text-uppercase align-middle border-white border-top border-right-0 border-1">
                                    NATIONALITY
                                </th>
                            </tr>
                            </tbody>
                            <tbody>
                            <tr v-for="learner in pendingLearnerNINs" class="">
                                <td class="rounded-0 text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_full_name }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_nin }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    {{ learner.parent_gender }}
                                </td>
                                <td class="rounded-0 text-center text-dark text-uppercase align-middle border-secondary border-top border-1">
                                    UGANDA
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" data-backdrop="static" tabindex="-1" id="errorPreviewModal">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body modal-body-lg text-center">
                        <div class="nk-modal">
                            <em class="nk-modal-icon icon icon-circle icon-circle-xxl ni ni-cross bg-danger"></em>
                            <h5 class="nk-modal-title text-uppercase">{{ error_learner }}</h5>
                            <div class="nk-modal-text text-left">
                                <ul class="list list-sm">
                                    <li v-for="error in error_messages">{{ error }}</li>
                                </ul>
                            </div>
                            <div class="nk-modal-action mt-5">
                                <a href="#" class="btn btn-lg btn-mw btn-light" data-dismiss="modal">Close</a>
                            </div>
                        </div>
                    </div><!-- .modal-body -->
                </div>
            </div>
        </div>
        <!-- /Error Modal -->
    </div>
</template>

<script>
export default {
    name: "FileUploadUgandanLearnersDetails",
    props: ["fileUploadObj"],
    components: {},
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            file_upload: {
                id: "",
                file_name: "",
                file_type: "",
                total_records: 0,
                processed_records: 0,
                user: {
                    school_type: {
                        name: "",
                    },
                    school: null,
                    person: null,
                },
                ugandan_learners: [],
                ugandan_learner_nins: [],
                ugandan_learner_parent_nins: [],
            },
            error_messages: [],
            error_learner: '',
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.file_upload = this.fileUploadObj;
            window.setInterval(()=>{
                this.loadFileUpload();
            }, 2000)
        },
        getUploader: function () {
            if (this.file_upload.user.school !== null) {
                return this.file_upload.user.school.name;
            }
            if (this.file_upload.user.person !== null) {
                return this.file_upload.user.person.full_name;
            }
            return null;
        },
        loadFileUpload: function () {
            if (this.file_upload !== "") {
                axios.get("/institutions/file-uploads/details/"+this.file_upload.id)
                    .then(response=>{
                        this.file_upload = response.data;
                    })
                    .catch(error=>{
                        console.log(error)
                    });
            }
        },
        getFileType: function () {
            switch (this.file_upload.file_type) {
                case "ugandan_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Ugandan Students" : "Ugandan Learners";

                case "foreigner_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Foreign Students" : "Foreign Learners";

                case "refugee_learners":
                    return this.file_upload.user.school_type_id > 3 ? "Refugee Students" : "Refugee Learners";

                case "ugandan_teachers":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Ugandan Caregivers" : "Ugandan Teachers";

                case "foreigner_teachers":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Foreign Caregivers" : "Foreign Teachers";

                case "ugandan_support_staff":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Ugandan Support/Administrative Staff" : "Ugandan Non Teaching Staff";

                case "foreigner_support_staff":
                    return this.file_upload.user.school_type.name === "preprimary" ? "Foreign Support/Administrative Staff" : "Foreign Non Teaching Staff";
            }
        },
        formatDateUploaded: function () {
            return moment(this.file_upload.date_created).format("DD MMMM, YYYY hh:mm:ssa")
        },
        formatCarbonDate: function (row) {
            return moment(row).format("DD MMMM, YYYY");
        },
        showErrors: function (entry) {
            this.error_learner = entry.learner_full_name;
            this.error_messages = [];
            let errors = JSON.parse(entry.validation_errors);
            for (let field in errors) {
                for (let i = 0; i < errors[field].length; i++) {
                    if (typeof errors[field] !== 'string') {
                        this.error_messages.push(errors[field][i]);
                    }
                }
            }

            $('#errorPreviewModal').modal('show')
        },
        formatErrors: function (validation_errors) {
            let errors = JSON.parse(validation_errors);
            let formatted = [];
            for (let field in errors) {
                for (let i = 0; i < errors[field].length; i++) {
                    formatted.push(errors[field][i]);
                }
            }
            return formatted;
        },
    },
    computed: {
        getTotalNINsPending: function () {
            let total = 0

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 3) {
                    total++
                }
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 3) {
                    total++
                }
            });

            return total;
        },
        getTotalNINsFailed: function () {
            let total = 0

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 2) {
                    total++
                }
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 2) {
                    total++
                }
            });

            return total;
        },
        getTotalNINsSuccess: function () {
            let total = 0

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 1) {
                    total++
                }
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 1) {
                    total++
                }
            });

            return total;
        },
        successfulLearnerNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 1) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        successfulParentNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 1) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        failedLearnerNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 2) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        failedParentNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 2) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        pendingLearnerNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.learner_nin !== null && learner.learner_nin_verification_status === 3) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        pendingParentNINs: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (learner.parent_nin !== null && learner.parent_nin_verification_status === 3) {
                    entries.push(learner)
                }
            });

            return entries;
        },
        otherErrors: function () {
            let entries = [];

            this.file_upload.ugandan_learners.forEach(learner => {
                if (!learner.passed_validation || learner.validation_errors !== null) {
                    entries.push(learner)
                }
            });

            return entries;
        },
    }
}
</script>

<style scoped>

</style>