<template>
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">Wall Charts & Work Cards</h5>

                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolWallChartsModal"  class="cursor btn bg-dark-teal btn-md d-sm-none"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Wall Charts & Work Cards</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolWallChartsModal"  class="cursor btn bg-dark-teal btn-md"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Wall Charts & Work Cards</span></button>
                            </div>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Class</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Chart Name</span></div>
                        <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span></div>
<!--                        <div class="nk-tb-col text-center"><span class="text-white ucap">Action</span></div>-->
                    </div><!-- .nk-tb-item -->
                    <div v-for="item in school.wall_charts" class="nk-tb-item">
                        <div class="nk-tb-col">
                            <span class="text-secondary text-center">{{ item.education_grade.name }}</span>
                        </div>
                        <div class="nk-tb-col">
                            <span class="text-secondary text-center">{{ item.chart.name }}</span>
                        </div>
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ item.quantity }}</span>
                        </div>

<!--                        <div @click="editWallChart(item)" class="nk-tb-col text-center">-->
<!--                            <span data-toggle="tooltip" data-placement="top"  title="Edit Facility" class="cursor lead text-dark-teal mr-1">-->
<!--                                <em class="icon ni ni-edit-fill"></em>-->
<!--                            </span>-->
<!--                        </div>-->
                    </div>
                </div>
                <div v-if="!school.wall_charts.length"  class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There are no Wall Charts & Work Cards to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolWallChartsModal">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetWallChart()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateWallChart() : createWallChart()">
                        <div class="modal-header">
                            <h5 class="modal-title ucap">{{ edit ? 'EDIT' : 'ADD' }} Wall Charts & Work Cards</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="wallChartGradeId">Select Class <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select v-model="form_report.education_grade_id" id="wallChartGradeId" data-placeholder="Select Class" class="form-select-sm">
                                                <option value="">--Select Class--</option>
                                                <option v-for="grade in education_grades" :value="grade.id">{{ grade.name }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="wallChartId">Select Wall Chart<span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select v-model="form_report.chart_id" id="wallChartId" class="form-select-sm">
                                                <option value="">--Select--</option>
                                                <option v-for="chart in wall_charts" :value="chart.id">{{ chart.name }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="quantity" class="form-label">Number <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input v-model.number="form_report.quantity" type="number" id="quantity" class="form-control" autocomplete="off" required placeholder="Enter Number">
                                        </div>
                                    </div>
                                </div>

                            </div><!-- .form-inline -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "WallCharts",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'educationGradesObj',
        'wallChartsObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            edit: false,
            api_url: '/institutions/surveys/instructional-materials',
            school: {
                wall_charts: []
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            education_grades: [],
            wall_charts: [],
            form_report: {
                id: '',
                section_id: '',
                chart_id: '',
                education_grade_id: '',
                quantity: 0,
            }

        }
    },
    methods: {

        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
            this.education_grades = this.educationGradesObj;
            this.wall_charts = this.wallChartsObj;

            $('#wallChartGradeId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolWallChartsModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#wallChartId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolWallChartsModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.chart_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

        },

        createWallChart: function () {
            this.loading = true;
            axios.post(this.api_url+'/charts-create/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.wall_charts = response.data.wall_charts;
                    this.resetWallChart();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Wall Chart & Work Cards added successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        editWallChart: function (item) {
            this.edit = true;
            this.form_report = item;
            this.form_report.quantity = item.quantity;
            $('#wallChartGradeId').val(item.education_grade_id).change();
            $('#wallChartId').val(item.chart_id).change();
            window.setTimeout(()=>{ $('#schoolWallChartsModal').modal({backdrop: "static"}) }, 10);
        },

        updateWallChart: function () {
            this.loading = true;

            axios.post(this.api_url+'/charts-update/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.wall_charts = response.data.wall_charts;
                    this.resetWallChart();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Wall Chart & Work Card updated successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetWallChart: function () {
            this.loading = false;
            this.edit = false;

            $('#wallChartGradeId').val('').change();
            $('#wallChartId').val('').change();
            this.form_report.quantity = 0;
            this.form_report.section_id = this.sectionId;
            $('#schoolWallChartsModal').modal("hide");
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
