<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <error-notifications ref="notifyMainError"></error-notifications>
        <div class="table-responsive">
            <table class="table table-bordered table-sm">
                <thead>
                <tr class="bg-secondary">
                    <td rowspan="4" class="text-white text-uppercase border-1 align-middle text-center px-1">
                        <span class="">ACTIVITY</span>
                    </td>
                </tr>
                <tr class="bg-secondary">
                    <td :colspan="2*extra_curricular_activity_levels.length" class="text-white border-1 text-uppercase align-middle text-center px-1">
                        <span class="">Level OF Participation</span>
                    </td>
                </tr>
                <tr class="bg-secondary-dim">
                    <td v-for="level in extra_curricular_activity_levels" colspan="2" class="text-dark border-secondary border-1 text-uppercase align-middle text-center px-1">
                        <span class="">{{ level.name }}</span>
                    </td>
                </tr>
                <tr class="bg-secondary-dim">
                    <td v-for="label in levelGenderLabels" class="text-dark border-secondary border-1 text-uppercase align-middle text-center px-1">
                        <span class="">{{ label }}</span>
                    </td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="activity in extra_curricular_activities">
                    <th class="text-uppercase text-dark border-secondary align-middle px-1 border-1">
                        <span class="">{{ activity.name }}</span>
                    </th>
                    <td v-for="total in levelGenderTotals(activity)" class="text-uppercase text-dark border-secondary align-middle text-center px-1 border-1">
                        <span class="">{{ total }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#updateExtraCurricularActivitiesModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE LEARNERS' PARTICIPATION INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="updateExtraCurricularActivitiesModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">UPDATE LEARNERS' PARTICIPATION INFORMATION</h6>
                    </div>
                    <div class="modal-body">
                        <error-notifications ref="notifyError"></error-notifications>
                        <div class="row">
                            <!-- --------------- Download Template ---------------    -->
                            <div class="col-12">
                                <span class="fs-16 font-weight-bold mr-lg-2">Step 1:</span>
                                <span>Download and fill out the Excel with learners</span>
                            </div>
                            <div class="col-12 mt-2">
                                <button @click="downloadTemplate()" class="btn btn-block bg-dark-teal">
                                    <span v-if="downloading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span v-if="downloading" class="align-self-center">Generating Excel...</span>
                                    <span v-if="downloading" class="sr-only">Generating Excel...</span>
                                    <em v-if="!downloading" class="icon ni ni-file-download"></em>
                                    <span v-if="!downloading">Download Excel</span>
                                </button>
                            </div>

                            <!-- --------------- Upload filled Template ---------------    -->
                            <div class="col-12 mt-5">
                                <span class="fs-16 font-weight-bold mr-lg-2">Step 2:</span>
                                <span>Upload the Excel Template you filled from Step 1</span>
                            </div>
                            <div class="mt-2 col-12">
                                <form @submit.prevent="uploadFilledTemplate()">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="custom-file">
                                                <input :disabled="loading" ref="filledTemplate" type="file" :class="[loading ? '' : 'cursor', 'custom-file-input']" id="filledTemplate" @change="checkFile()" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" required>
                                                <label id="filledNonRefugeeLearnersTemplateLabel" :class="[valid_file ? 'text-dark':'font-italic text-muted', 'custom-file-label']" for="filledTemplate">Upload Filled Excel Template</label>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <button type="submit" :disabled="!valid_file || loading" class="btn btn-block bg-dark-teal">
                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="loading" class="align-self-center">Uploading...</span>
                                                <span v-if="loading" class="sr-only">Uploading...</span>
                                                <span v-if="!loading" class="align-self-center">Upload</span>
                                                <em v-if="!loading" class="ni ni-upload ml-2"></em>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "ExtraCurricularActivityParticipation",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'extraCurricularActivitiesObj',
        'extraCurricularActivityLevelsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            downloading: false,
            preview: false,
            uploaded: false,
            api_url: '/institutions/learners/extracurricular-template',
            valid_file: false,
            file_name: null,
            filled_template: '',
            school: {
                enrolments: 0,
            },
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            upload: {
                id: '',
                display_name: '',
            },
            file: {
                id: '',
                file_name: '',
                total_records: 0,
                total_male: 0,
                total_female: 0,
                clean_records: 0,
            },
            failures: {
                total: 0,
                current_page: 1,
                data: [],
            },
            extra_curricular_activities: [],
            extra_curricular_activity_levels: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.extra_curricular_activities = this.extraCurricularActivitiesObj;
            this.extra_curricular_activity_levels = this.extraCurricularActivityLevelsObj;

            window.setTimeout(()=>{
                self.$refs.notifyMainError.messages = [];
                if (self.school.enrolments === 0) {
                    self.$refs.notifyMainError.messages.push({status: 'error', title: 'Note!', message: 'First Enroll Learners to update this section'});
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                }
            }, 500);
        },
        levelGenderTotals: function (activity) {
            let labels = [];

            this.extra_curricular_activity_levels.forEach(level=>{
                let maleCount = activity.learner_cocurricular_activities.filter(entry=>{
                    return entry.person.gender === "M" && entry.performing_level_id === level.id;
                }).length;

                let femaleCount = activity.learner_cocurricular_activities.filter(entry=>{
                    return entry.person.gender === "F" && entry.performing_level_id === level.id;
                }).length;

                labels.push(maleCount);
                labels.push(femaleCount);
            });

            return labels;
        },
        downloadTemplate: function () {
            this.downloading = true;
            axios({
                method: 'post',
                url: this.api_url+'?x='+moment().unix(),
                data: {performing_level: this.performing_level,},
                responseType: 'arraybuffer'
            })
                .then(response=>{
                    this.downloading = false;
                    let blob = new Blob([response.data], { type: 'application/octet-stream' });
                    let a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(blob, {
                        type: 'data:attachment/xlsx'
                    })
                    a.download = this.school.emis_number.toLowerCase()+'_extra_curricular_activities_participants_'+moment().unix()+'.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                })
                .catch(error=>{
                    this.downloading = false;
                    this.renderError(error)
                });
        },
        showErrors: function (entry) {
            let errors = JSON.parse(entry.validation_errors);
            this.clearErrors();
            for (let field in errors) {
                for (let i = 0; i < errors[field].length; i++) {
                    this.error_messages.push(errors[field][i]);
                }
            }
        },
        clearErrors: function () {
            this.error_messages = [];
        },
        uploadFilledTemplate: function () {
            this.loading = true;
            let formData = new FormData();
            formData.append('filled_template', this.filled_template);
            formData.append('file_name', this.file_name);
            formData.append('survey_id', this.survey.id);

            axios.post(this.api_url+"/process", formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response=>{
                    $("#updateExtraCurricularActivitiesModal").modal('hide');
                    this.extra_curricular_activities = response.data;
                    this.preview = true;
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Upload Success!', message:this.file_name+" was uploaded successfully."});
                    this.resetFile();
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        checkFile: function () {
            let validExts = [".xlsx", ".xls", ".csv"];
            let fileExt = this.$refs.filledTemplate.value;
            fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

            if (validExts.indexOf(fileExt) < 0) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'File Error:', message: "Invalid file selected, valid files are of " + validExts.toString() + " types."});

                this.resetFile();
                return false;
            }

            let fileName = this.$refs.filledTemplate.value;
            fileName = fileName.substring(fileName.lastIndexOf('\\')+1);
            this.file_name = fileName;
            $('#filledNonRefugeeLearnersTemplateLabel').text(fileName)

            this.valid_file = true;
            this.filled_template = this.$refs.filledTemplate.files[0];

            return true;
        },
        resetFile: function () {
            this.file_name = null;
            this.filled_template = '';
            this.valid_file = false;
            this.loading = false;
            this.$refs.filledTemplate.value = null;
            window.setTimeout(()=>{$('#filledNonRefugeeLearnersTemplateLabel').text("Upload Filled Excel Template")}, 10);
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:"Select a level of participation to proceed"});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        levelGenderLabels: function () {
            let labels = [];

            this.extra_curricular_activity_levels.forEach(()=>{
                labels.push("M");
                labels.push("F");
            });

            return labels;
        },
     },
}
</script>

<style scoped>

</style>
