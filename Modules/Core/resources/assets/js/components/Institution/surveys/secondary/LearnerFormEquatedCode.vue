<template>
    <div style="overflow-x: hidden !important;" :class="[verify ? '' : '','w-100']">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <div style="max-height: 450px; overflow-y: auto;">
            <div class="alert alert-info alert-icon">
                <em class="icon ni ni-alert-circle"></em>
                <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select Class</h6>
                <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Select Exam Level</h6>
                <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Select Equated Year</h6>
                <h6 class="small mb-2"><span class="text-danger">Step 4:</span> Enter Equated Code</h6>
                <h6 class="small mb-2"><span class="text-danger">Step 5:</span> Click Verify Button</h6>
            </div>
            <form @submit.prevent="verify ? (learner.nin.length && !nin_verify ? verifyLearnerNIN() : (learner.parent_nin.length && !nin_parent_verify ? verifyParentNIN() : createLearner())) : verifyUnebLearner()">
                <div class="row">
                    <div class="col-lg-2 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label">Select Class <span class="text-danger">*</span></label>
                            <select :disabled="verify" required="" id="learnerClassId" class="form-select-sm">
                                <option value="">--SELECT--</option>
                                <option v-for="grade in education_grades" :value="grade.id">{{ grade.name.toUpperCase() }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label">Select Exam Level <span class="text-danger">*</span></label>
                            <select :disabled="verify" required="" id="learnerExamLevelId" class="form-select-sm">
                                <option value="">--SELECT--</option>
                                <option value="PLE">PLE</option>
                                <option value="UCE">UCE</option>
                                <option value="UACE">UACE</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label">Select Equated Year <span class="text-danger">*</span></label>
                            <select :disabled="verify" required="" id="learnerEquatedYearId" class="form-select-sm">
                                <option value="">--SELECT--</option>
                                <option v-for="year in academicYears" :value="year">{{ year }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-4 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerEquatedCode" class="form-label">Enter Equated Code <span class="text-danger">*</span> </label>
                            </div>
                            <div class="form-control-group">
                                <div class="input-group">
                                    <input :disabled="verify" required="" v-model.trim="learner.equated_code" id="learnerEquatedCode" minlength="10" type="text" placeholder="eg. ART/O/0302 or 2020/PLE/PA/0341" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    <div class="input-group-append">
                                        <button :disabled="learner.equated_year === '' || learner.equated_code === '' || loading || verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span v-if="loading" class="align-self-center">Verifying...</span>
                                            <span v-if="loading" class="sr-only">Verifying...</span>
                                            <span v-if="!loading" class="">Verify</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="verify" class="row mt-3">
                    <div class="col-lg-8 d-flex">
                        <div class="table-responsive align-self-start">
                            <table class="table table-sm">
                                <tbody>
                                <tr>
                                    <td rowspan="6" class="px-0 align-middle text-dark text-uppercase w-220px">
                                        <div class="form-group d-flex flex-column justify-content-center">
                                            <input
                                                ref="photo" @change="selectFile"
                                                accept="image/x-png,image/jpeg"
                                                data-max-file-size="2M"
                                                id="learnerWithPhotoEquated"
                                                type="file"
                                                class="dropify"
                                                data-height="190"
                                                data-allowed-file-extensions="jpeg jpg png"
                                                :data-default-file="defaultPhoto" />
                                        </div>
                                        <div class="d-flex flex-column justify-content-center">
                                            <button @click="learner.photo === null || learner.photo === '' ? uploadImage() : clearPhoto()" type="button" class="align-self-center btn bg-dark-teal">
                                                <em class="icon ni ni-camera-fill"></em>
                                                <span v-if="learner.photo === null || learner.photo === ''">Choose Photo</span>
                                                <span v-else>Remove Photo</span>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="px-2 align-middle text-dark text-uppercase">
                                        <h6 class="overline-title mb-0 text-dark-teal">LEARNER</h6>
                                        <span class="">{{ uneb_learner.name }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-dark text-uppercase">
                                        <h6 class="overline-title mb-0 text-dark-teal">EQUATED CODE</h6>
                                        <span class="">{{ uneb_learner.equated_code }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-dark text-uppercase">
                                        <h6 class="overline-title mb-0 text-dark-teal">EXAM LEVEL</h6>
                                        <span class="">{{ uneb_learner.exam_level }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-dark text-uppercase">
                                        <h6 class="overline-title mb-0 text-dark-teal">EQUATED YEAR</h6>
                                        <span class="">{{ uneb_learner.equated_year }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td v-if="uneb_learner.gender" class="px-2 align-middle text-dark text-uppercase">
                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                        <span v-if="uneb_learner.gender === 'F'" class="">FEMALE</span>
                                        <span v-if="uneb_learner.gender === 'M'" class="">MALE</span>

                                    </td>
                                    <td v-else >
                                        <div class="form-group">
                                    <div class="form-label-group">
                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="learner.gender" value="M" id="learnerGenderMaleEquated" required="">
                                            <label class="custom-control-label text-uppercase" for="learnerGenderMaleEquated">Male</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input  type="radio" class="custom-control-input" v-model="learner.gender" value="F" id="learnerGenderFemaleEquated" required="">
                                            <label class="custom-control-label text-uppercase" for="learnerGenderFemaleEquated">Female</label>
                                        </div>
                                    </div>

                                    </div>

                                    </td>
                                </tr>
    <!--                            <tr>-->
    <!--                                <td class="px-2 align-middle text-dark text-uppercase">-->
    <!--                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>-->
    <!--                                    <span class="">{{ formatDate(uneb_learner.date_of_birth) }}</span>-->
    <!--                                </td>-->
    <!--                            </tr>-->
    <!--                            <tr>-->
    <!--                                <td class="px-2 align-middle text-dark text-uppercase">-->
    <!--                                    <h6 class="overline-title mb-0 text-dark-teal">NATIONALITY</h6>-->
    <!--                                    <span class="">{{ uneb_learner.nationality }}</span>-->
    <!--                                </td>-->
    <!--                            </tr>-->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-lg-4 d-flex">
                        <div class="row align-self-start">
                            <div class="col-12">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Is learner a refugee?</label>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input required" v-model="learner_refugee_no" value="yes" id="learnerIsRefugeEquated" required>
                                            <label class="custom-control-label text-uppercase" for="learnerIsRefugeEquated">YES</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input required" v-model="learner_refugee_no" value="no" id="learnerIsNotRefugeeEquated" required>
                                            <label class="custom-control-label text-uppercase" for="learnerIsNotRefugeeEquated">NO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Is learner an orphan?</label>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="learner.is_orphan" value="yes" id="learnerIsOrphanYesEquated">
                                            <label class="custom-control-label text-uppercase" for="learnerIsOrphanYesEquated">YES</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="learner.is_orphan" value="no" id="learnerIsOrphanNoEquated">
                                            <label class="custom-control-label text-uppercase" for="learnerIsOrphanNoEquated">NO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-show="learner.is_orphan === 'yes'" class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label">Type Of Orphan <span class="text-danger">*</span></label>
                                    <select :required="learner.is_orphan === 'yes'" id="learnerOrphanTypeEquated" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option value="father">ONLY FATHER DEAD</option>
                                        <option value="mother">ONLY MOTHER DEAD</option>
                                        <option value="both-dead">BOTH PARENTS DEAD</option>
                                    </select>
                                </div>
                            </div>

                            <div v-show="!uganda && learner_refugee_no === 'no'" class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label" for="learnerStudentPassEquated">Student Pass <span class="text-danger">*</span></label>
                                    <input :required="!uganda && learner_refugee_no === 'no'" pattern="^(S|s)(T|t)[0-9]{7}$" title="Student Pass Format ST0011223" v-model.trim="learner.student_pass" id="learnerStudentPassEquated" minlength="9" maxlength="9" type="text" placeholder="eg. ST0011223" class="form-control text-center bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                            <div v-show="!uganda && learner_refugee_no === 'yes'" class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label" for="learnerRefugeeNumberEquated">Refugee Number <span class="text-danger">*</span></label>
                                    <input :required="!uganda && learner_refugee_no === 'yes'" v-model.trim="learner.refugee_number" id="learnerRefugeeNumberEquated" minlength="12" type="text" title="Learner Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label">Learner Nationality <span class="text-danger">*</span></label>
                                    <select :required="verify" id="learnerCountryIdEquated" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="country in countries" :value="country.id" :disabled="learner_refugee_no === 'yes' && country.name === 'UGANDA'">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label" for="learnerBirthDateEquated">Learner Date Of Birth <span class="text-danger">*</span></label>
                                    <div class="form-control-wrap">
                                        <div class="form-icon form-icon-left">
                                            <em class="icon ni ni-calendar"></em>
                                        </div>
                                        <input required="" v-model.trim="learner.birth_date" placeholder="eg. 02 MAY, 2002" id="learnerBirthDateEquated" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div v-show="uganda && verify" class="col-12 mt-3">
                                <div class="form-group">
                                    <label class="form-label">District Of Birth <span class="text-danger">*</span></label>
                                    <select :required="uganda && verify && (!learner.nin.length !== nin_verify)" id="learnerEquatedDistrictOfBirthId" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="verify" class="row">
                    <div class="col-lg-4 d-flex">
                        <div class="row align-self-start">
                            <div v-if="aLevel" class="col-12">
                                <h6 class="overline-title title text-dark-teal">Subject Combination</h6>
                                <div class="form-group">
                                    <div v-for="subject in principalSubjects" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input :disabled="getPrincipalSubjectDisabled(subject)" v-model="learner.principal_subjects" type="checkbox" :value="subject.id" class="custom-control-input" :id="'learnerSubjectEquated'+subject.id">
                                        <label class="custom-control-label" :for="'learnerSubjectEquated'+subject.id">{{ subject.name.toUpperCase() }}</label>
                                    </div>
                                    <hr class="border-dark-teal my-1">
                                    <div v-for="subject in subsidiarySubjects" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input :disabled="getSubSubjectDisabled(subject)" v-model="learner.subsidiary_subject" type="checkbox" :value="subject.id" class="custom-control-input" :id="'learnerSubjectEquatedSub'+subject.id">
                                        <label class="custom-control-label" :for="'learnerSubjectEquatedSub'+subject.id">{{ subject.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                            <div :class="[aLevel ? 'mt-3' : '', 'col-12']">
                                <h6 class="overline-title title text-dark-teal">SPECIAL NEEDS</h6>
                                <div class="form-group">
                                    <div v-for="disability in disability_types" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input v-model="learner.disabilities" type="checkbox" :value="disability.id" class="custom-control-input" :id="'learnerDisabilityEquated'+disability.id">
                                        <label class="custom-control-label" :for="'learnerDisabilityEquated'+disability.id">{{ disability.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!aLevel" class="col-12 mt-3">
                                <h6 class="overline-title title text-dark-teal">HEALTH ISSUES</h6>
                                <div class="form-group">
                                    <div v-for="issue in health_issues" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input v-model="learner.learner_health_issues" type="checkbox" :value="issue.id" class="custom-control-input" :id="'learnerHealthIssueEquatedEquated'+issue.id">
                                        <label class="custom-control-label" :for="'learnerHealthIssueEquatedEquated'+issue.id">{{ issue.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                <h6 class="overline-title title text-dark-teal">PRACTICAL SKILLS</h6>
                                <div class="form-group">
                                    <div v-for="skill in practical_skills" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input v-model="learner.learner_practical_skills" type="checkbox" :value="skill.id" class="custom-control-input" :id="'learnerPracticalSkillEquated'+skill.id">
                                        <label class="custom-control-label" :for="'learnerPracticalSkillEquated'+skill.id">{{ skill.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mt-3 d-flex">
                        <div class="row align-self-start">
                            <div class="col-12">
                                <h6 class="overline-title title text-dark-teal">TALENTS</h6>
                                <div class="form-group">
                                    <div v-for="talent in talents" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input v-model="learner.learner_talents" type="checkbox" :value="talent.id" class="custom-control-input" :id="'learnerTalentEquated'+talent.id">
                                        <label class="custom-control-label" :for="'learnerTalentEquated'+talent.id">{{ talent.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                            <div v-if="aLevel" class="col-12 mt-3">
                                <h6 class="overline-title title text-dark-teal">HEALTH ISSUES</h6>
                                <div class="form-group">
                                    <div v-for="issue in health_issues" class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input v-model="learner.learner_health_issues" type="checkbox" :value="issue.id" class="custom-control-input" :id="'learnerHealthIssueEquated'+issue.id">
                                        <label class="custom-control-label" :for="'learnerHealthIssueEquated'+issue.id">{{ issue.name.toUpperCase() }}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 my-3">
                        <div class="row">
                            <error-notifications ref="notifyErrorNin"></error-notifications>
                            <div class="col-12">
                                <h6 class="overline-title title text-dark-teal">PARENT DETAILS</h6>
                                <div v-if="uganda" class="form-group">
                                    <div class="form-label-group">
                                        <label for="learnerParentNINEquated" class="form-label">Parent NIN</label>
                                    </div>
                                    <div class="form-control-group">
                                        <div class="input-group">
                                            <input :disabled="nin_parent_verify" :required="uganda && verify && (!learner.nin.length !== nin_verify)" v-model.trim="learner.parent_nin" id="learnerParentNINEquated" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                            <div class="input-group-append">
                                                <button :disabled="learner.parent_nin === '' || nin_parent_loading || nin_parent_verify" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                    <span v-if="nin_parent_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="nin_parent_loading" class="align-self-center">Verifying...</span>
                                                    <span v-if="nin_parent_loading" class="sr-only">Verifying...</span>
                                                    <span v-if="!nin_parent_loading" class="">Verify</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-show="nin_parent_verify" class="">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <tr>
                                                <td colspan="2" class="align-middle text-center text-uppercase text-dark text-center px-2">
                                                    <div class="w-150px mx-auto">
                                                        <img id="learnerParentPhoto" src="@images/default_male.jpg" class="rounded-0" alt="learner parent photo">
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2" class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                                    <span class="">{{ nira_parent.surname }} {{ nira_parent.given_names }} {{ nira_parent.maiden_names }} {{ nira_parent.previous_surnames }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                    <span class="">{{ nira_parent.gender }}</span>
                                                </td>
                                                <td class="px-2 w-75 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                    <span class="">{{ nira_parent.date_of_birth }}</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <div v-if="!uganda && learner_refugee_no === 'no'" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="learnerParentPassportEquated" class="form-label">Parent Passport <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && learner_refugee_no === 'no' && (!learner.nin.length !== nin_verify)" v-model.trim="learner.parent_passport" id="learnerParentPassportEquated" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                                <div v-if="!uganda && learner_refugee_no === 'yes'" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="learnerParentRefugeeNumberEquated" class="form-label">Parent Refugee Number <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && learner_refugee_no === 'yes' && (!learner.nin.length !== nin_verify)" v-model.trim="learner.parent_refugee_number" id="learnerParentRefugeeNumberEquated" minlength="12" type="text" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                                <div v-if="!uganda" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="learnerParentFirstNameEquated" class="form-label">First Name <span v-if="!uganda" class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && (!learner.nin.length !== nin_verify)" v-model.trim="learner.parent_first_name" id="learnerParentFirstNameEquated" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                                <div v-if="!uganda" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="learnerParentSurnameEquated" class="form-label">Surname <span v-if="!uganda" class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="!uganda && verify && (!learner.nin.length !== nin_verify)" v-model.trim="learner.parent_surname" id="learnerParentSurnameEquated" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                                <div v-if="!uganda" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="learnerParentOtherNamesEquated" class="form-label">Other Names</label>
                                    </div>
                                    <div class="form-control-group">
                                        <input v-model.trim="learner.parent_other_names" id="learnerParentOtherNamesEquated" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                    </div>
                                </div>
                                <div v-if="!uganda" class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label class="form-label">Sex <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model.number="learner.parent_gender" value="M" id="learnerParentMaleEquated">
                                            <label class="custom-control-label text-uppercase" for="learnerParentMaleEquated">Male</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model.number="learner.parent_gender" value="F" id="learnerParentFemaleEquated">
                                            <label class="custom-control-label text-uppercase" for="learnerParentFemaleEquated">Female</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group mt-3">
                                    <label class="form-label" for="learnerParentRelationshipEquated">Parent Relationship <span class="text-danger">*</span></label>
                                    <select :required="verify && (!learner.nin.length !== nin_verify)" id="learnerParentRelationshipEquated" class="form-select-sm">
                                        <option value="">--SELECT--</option>
                                        <option value="parent">PARENT</option>
                                        <option value="guardian">GUARDIAN</option>
                                    </select>
                                </div>

                                <div class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="parentPhoneOneEquated" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input :required="verify" v-model="learner.parent_phone_1" id="parentPhoneOneEquated" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="parentPhoneTwoEquated" class="form-label">Phone Number 2</label>
                                    </div>
                                    <div class="form-control-group">
                                        <input v-model="learner.parent_phone_2" id="parentPhoneTwoEquated" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    <div class="form-label-group">
                                        <label for="parentEmailEquated" class="form-label">Email Address</label>
                                    </div>
                                    <div class="form-control-group">
                                        <input v-model="learner.parent_email" id="parentEmailEquated" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="verify" class="modal-footer d-flex justify-content-center">
                    <button @click="resetUnebEquatedLearner()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                    </button>
                    <button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span v-if="loading" class="align-self-center">Saving...</span>
                        <span v-if="loading" class="sr-only">Saving...</span>
                        <span v-if="!loading" class="align-self-center">Save Learner</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                    </button>
                </div>
                <!-- <button type="submit" id="learnerEquatedFormSubmit" hidden="hidden"></button> -->
            </form>
        </div>

    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import moment from "moment";
export default {
    name: "LearnerFormEquatedCode",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'districtsObj',
        'subjectsObj',
        'healthIssuesObj',
        'educationGradesObj',
        'familiarLanguagesObj',
        'disabilityTypesObj',
        'countriesObj',
        'talentsObj',
        'practicalSkillsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            verify: false,
            nin_loading: false,
            nin_verify: false,
            learner_refugee_no: 'no',
            nin_parent_loading: false,
            nin_parent_verify: false,
            uganda: true,
            aLevel: false,
            min_age: 11,
            photoDropify: null,
            school: {},
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            learner: {
                equated_code: '',
                equated_year: '',
                exam_level: '',
                nin: '',
                refugee_number: '',
                parent_nin: '',
                student_pass: '',
                parent_passport: '',
                parent_refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                photo: null,
                birth_date: moment().subtract(11, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                country_id: 221,
                country: {title:''},
                education_grade_id: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_relationship: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                stream_id: '',
                identities: [],
                familiar_language_id: '',
                district_of_birth_id: '',
                is_orphan: 'no',
                orphan_type: '',
                disabilities: [],
                learner_health_issues: [],
                learner_talents: [],
                learner_practical_skills: [],
                principal_subjects: [],
                subsidiary_subject: [],
            },
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                gender: '',
                date_of_birth: '',
                name: '',
                equated_code: '',
                exam_level: '',
                equated_year: '',
                exam_year: '',
                code_type: '',
            },
            nin_learner: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            nira_parent: {
                national_id: '',
                surname: '',
                given_names: '',
                previous_surnames: '',
                maiden_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            countries: [],
            districts: [],
            subjects: [],
            education_grades: [],
            familiar_languages: [],
            disability_types: [],
            health_issues: [],
            talents: [],
            practical_skills: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.countries = this.countriesObj;
            this.education_grades = this.educationGradesObj;
            this.familiar_languages = this.familiarLanguagesObj;
            this.disability_types = this.disabilityTypesObj;
            this.districts = this.districtsObj;
            this.health_issues = this.healthIssuesObj;
            this.talents = this.talentsObj;
            this.practical_skills = this.practicalSkillsObj;
            this.subjects = this.subjectsObj;

            $('#learnerBirthDateEquated').datepicker({
                format: 'd MM, yyyy',
                endDate: moment().subtract(this.min_age, 'years').toDate(),
                autoclose: true,
            }).on('hide', e=>{
                self.learner.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            this.photoDropify = $('#learnerWithPhotoEquated').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong uploaded.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max allowed).'
                }
            });
            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.learner.photo = null;
                self.$refs.photo.value=null;
            });
            $('.dropify-clear').click(()=>{
                this.clearPhoto();
            });

            $('#learnerClassId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    self.learner.equated_code = "";
                    return data.text;
                },
            });

            $('#learnerExamLevelId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.exam_level = data.id;
                    return data.text;
                },
            });
            $('#learnerEquatedYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.equated_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerCountryIdEquated').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.uganda = data.text === 'UGANDA';
                    self.$parent.uganda = data.text === 'UGANDA';
                    self.learner.country_id = data.id !== "" ? Number(data.id) : data.id;
                    // Disable learner refuge radio buttons if "UGANDA" is selected
                    let isUgandaSelected = data.text === 'UGANDA';
                    $('#learnerIsRefugeEquated, #learnerIsNotRefugeeEquated').prop('disabled', isUgandaSelected);

                    return data.text;
                },
            });
            $('#learnerEquatedDistrictOfBirthId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.district_of_birth_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerOrphanTypeEquated').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.orphan_type = data.id;
                    return data.text;
                },
            });
            $('#learnerFamiliarLanguageIdEquated').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.familiar_language_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#learnerParentRelationshipEquated').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateSectionCModal'),
                templateSelection: function (data, container) {
                    self.learner.parent_relationship = data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                let s1 = this.education_grades.find(grade => {
                    return grade.grade_rank === 1;
                });
                $('#learnerCountryIdEquated').val(ug.id).change();
                $('#learnerClassId').val(s1.id).change();
                $('#learnerFamiliarLanguageIdEquated').val(english.id).change();
                // $('#learnerParentRelationshipEquated').val('parent').change();
            }, 50);

            //Set phone number 1 flag
            let parentPhone1 = document.querySelector('#parentPhoneOneEquated');
            let iti_person_phone_1 = intlTelInput(parentPhone1, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone1.addEventListener('blur', ()=>{
                self.learner.parent_phone_1 = iti_person_phone_1.getNumber();
                parentPhone1.value = iti_person_phone_1.getNumber();
            });
            parentPhone1.addEventListener('change', ()=>{
                self.learner.parent_phone_1 = iti_person_phone_1.getNumber();
                parentPhone1.value = iti_person_phone_1.getNumber();
            });

            //Set phone number 2 flag
            let parentPhone2 = document.querySelector('#parentPhoneTwoEquated');
            let iti_person_phone_2 = intlTelInput(parentPhone2, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone2.addEventListener('blur', ()=>{
                self.learner.parent_phone_2 = iti_person_phone_2.getNumber();
                parentPhone2.value = iti_person_phone_2.getNumber();
            });
            parentPhone2.addEventListener('change', ()=>{
                self.learner.parent_phone_2 = iti_person_phone_2.getNumber();
                parentPhone2.value = iti_person_phone_2.getNumber();
            });
        },
        verifyUnebLearner: function () {
            if (this.learner.exam_level ===  "UACE") {
                this.aLevel = true;
            }
            this.loading = true;
            axios.post('/uneb/equated-code', {exam_level: this.learner.exam_level, equated_code: this.learner.equated_code.toUpperCase(), equated_year: this.learner.equated_year})
                .then(response => {
                    this.loading = false;
                    this.$parent.pending = false;
                    this.uneb_learner = response.data;
                    this.learner.first_name = this.uneb_learner.first_name;
                    this.learner.surname = this.uneb_learner.surname;
                    this.learner.gender = this.uneb_learner.gender;
                    this.learner.birth_date = this.uneb_learner.date_of_birth ? moment(this.uneb_learner.date_of_birth).format('D MMMM, YYYY').toUpperCase():'';
                    $("#learnerBirthDateEquated").datepicker('update', moment(this.uneb_learner.date_of_birth).toDate())
                    this.uganda = this.uneb_learner.nationality === "UGANDA";
                    let ug = this.countries.find(c=>{
                        return c.name.toUpperCase() === 'UGANDA';
                    })
                    $('#learnerCountryIdEquated').val(ug.id).change();
                    this.verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        verifyLearnerNIN() {
            this.nin_loading = true;
            axios.post('/nira/user-info', {id_number: this.learner.nin.toUpperCase()})
                .then(response => {
                    this.nin_loading = false;
                    this.uganda = true;
                    this.nira_learner = response.data;
                    this.learner.first_name = this.nira_learner.given_names;
                    this.learner.surname = this.nira_learner.surname;
                    this.nin_verify = true;
                    this.updateDefaultPhoto();
                })
                .catch(error => {
                    this.nin_loading = false;
                    this.renderError(error);
                });
        },
        verifyParentNIN() {
            if (this.isNinSame) {
                this.$refs.notifyErrorNin.messages.push({status: 'error', title: 'Error: ', message:'Nin cannot be the same as that of a learner, provide a different Nin!!!'});
            } else {
                this.nin_parent_loading = true;
                axios.post('/nira/user-info', {id_number: this.learner.parent_nin.toUpperCase()})
                    .then(response => {
                        this.nin_parent_loading = false;
                        this.uganda = true;
                        this.nira_parent = response.data;
                        this.learner.parent_first_name = this.nira_parent.given_names;
                        this.learner.parent_surname = this.nira_parent.surname;
                        this.learner.parent_gender = this.nira_parent.gender;
                        this.nin_parent_verify = true;
                        if (this.nira_parent.photo !== null) {
                            if (this.nira_parent.photo.includes('.png')) {
                                $('#learnerParentPhoto').attr('src', '/images/nira-photos/' + this.nira_parent.photo);
                            } else {
                                $('#learnerParentPhoto').attr('src', 'data:image/png;base64,' + this.nira_parent.photo);
                            }
                        }
                    })
                    .catch(error => {
                        this.nin_parent_loading = false;
                        this.renderError(error);
                    });
            }
        },
        //Learner create starts
        showError(ref, message) {
            this.loading = false;
            this.$refs[ref].messages.push({
                status: 'error',
                title: 'Error: ',
                message: message
            });
            $('#updateSectionCModal').animate({ scrollTop: 0 }, 'slow');
        },
        validateLearnerData() {
            const birthDate = moment(this.learner.birth_date);

            if (this.isNinSame) {
                this.showError('notifyErrorNin', 'NIN cannot be the same as that of a learner, provide a different NIN!!!');
                return false;
            } else if (!this.learner.gender && !this.uneb_learner.date_of_birth) {
                this.showError('notifyError', 'Sex is required!!!');
                return false;
            } else if (this.aLevel) {
                if (!this.learner.principal_subjects.length) {
                    this.showError('notifyError', 'Select at least 3 principal subjects to proceed!');
                    return false;
                }
                if (!this.learner.subsidiary_subject.length) {
                    this.showError('notifyError', 'Select a subsidiary subject to proceed!');
                    return false;
                }
                if (birthDate.isAfter(moment().subtract(14, 'years'))) {
                    this.showError('notifyError', 'Learner must be at least 14 years to be enrolled!');
                    return false;
                }
            } else {
                if (birthDate.isAfter(moment().subtract(11, 'years'))) {
                    this.showError('notifyError', 'Learner must be at least 11 years to be enrolled!');
                    return false;
                }
            }

            return true;
        },
        buildFormData() {
            let self = this;
            const formData = new FormData();

            formData.append('equated_code', self.learner.equated_code.toUpperCase());
            formData.append('equated_year', self.learner.equated_year);
            formData.append('exam_year', self.uneb_learner.exam_year);
            formData.append('exam_level', self.learner.exam_level);
            formData.append('code_type', self.uneb_learner.code_type);
            formData.append('parent_nin', (self.uganda ? self.learner.parent_nin : ''));
            formData.append('student_pass', (!self.uganda ? self.learner.student_pass.toUpperCase() : ''));
            formData.append('student_refugee_number', (!self.uganda ? self.learner.refugee_number : ''));
            formData.append('parent_passport', (!self.uganda ? self.learner.parent_passport : ''));
            formData.append('parent_refugee_number', (!self.uganda ? self.learner.parent_refugee_number : ''));
            formData.append('first_name', self.learner.first_name);
            formData.append('surname', self.learner.surname);
            formData.append('other_names', self.learner.other_names);
            formData.append('birth_date', self.learner.birth_date);
            formData.append('gender', self.learner.gender);
            formData.append('parent_first_name', self.learner.parent_first_name);
            formData.append('parent_surname', self.learner.parent_surname);
            formData.append('parent_other_names', self.learner.parent_other_names);
            formData.append('parent_gender', self.learner.parent_gender);
            formData.append('parent_relationship', self.learner.parent_relationship);
            formData.append('parent_phone_1', self.learner.parent_phone_1);
            formData.append('parent_phone_2', self.learner.parent_phone_2);
            formData.append('parent_email', self.learner.parent_email);
            formData.append('photo', self.learner.photo);
            formData.append('education_grade_id', self.learner.education_grade_id);
            formData.append('familiar_language_id', self.learner.familiar_language_id);
            formData.append('district_of_birth_id', self.learner.district_of_birth_id);
            formData.append('orphan_type', self.learner.orphan_type);
            formData.append('disabilities', self.learner.disabilities);
            formData.append('learner_health_issues', self.learner.learner_health_issues);
            formData.append('learner_talents', self.learner.learner_talents);
            formData.append('learner_practical_skills', self.learner.learner_practical_skills);
            formData.append('principal_subjects', self.learner.principal_subjects);
            formData.append('subsidiary_subject', self.learner.subsidiary_subject);
            formData.append('section_id', self.sectionId);
            formData.append('country_id', self.learner.country_id);
            formData.append('survey_id', self.survey.id);
            formData.append('uneb_id', self.uneb_learner.id);
            formData.append('uneb_level', self.aLevel ? "UCE" : "PLE");
            formData.append('hasEquatedCode', 'YES');

            return formData;
        },
        createLearner() {
            if (!this.validateLearnerData()) return;

            this.loading = true;
            const formData = this.buildFormData();

            axios.post('/institutions/learners/form-create', formData, {headers: { 'Content-Type': 'multipart/form-data' }})
            .then(response => {
                this.$parent.education_grades = response.data.education_grades;
                this.$parent.learner_enrolments = response.data.learner_enrolments;
                this.$parent.$refs.notifySuccess.messages.push({
                    status: 'success',
                    title: 'Success: ',
                    message: 'Learner saved successfully'
                });
                this.resetEquatedLearner();
            })
            .catch(error => {
                this.loading = false;
                this.renderError(error);
            });
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("DD/MM/YYYY")
        },
        resetUnebEquatedLearner: function () {
            this.resetEquatedLearner();
            this.verify = false;
            this.learner.equated_code = '';
            //this.pending = true;
            $('#learnerClassId').val('').change();
            $('#learnerEquatedYearId').val('').change();
            $('#learnerExamLevelId').val('').change();
        },
        resetEquatedLearner: function () {
            $('#updateSectionCModal').modal('hide');
            this.$parent.loading = false;
            this.$parent.pending = true;
            this.$parent.verify = true;
            this.$parent.uganda = true;
            this.$parent.active_section = 'form-create-equated-code';
            this.loading = false;
            this.verify = false;
            this.min_age = 11;
            this.nin_parent_verify = false;
            this.uganda = true;
            this.learner= {
                equated_code: '',
                equated_year: '',
                nin: '',
                refugee_number: '',
                parent_nin: '',
                student_pass: '',
                parent_passport: '',
                parent_refugee_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: this.uneb_learner.gender ? this.uneb_learner.gender: null,
                photo: null,
                birth_date: moment().subtract(this.min_age, 'years').format("D MMMM, YYYY"),
                photo_url: '',
                country_id: 221,
                country: {title:''},
                education_grade_id: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_relationship: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                stream_id: '',
                identities: [],
                familiar_language_id: '',
                district_of_birth_id: '',
                is_orphan: 'no',
                orphan_type: '',
                disabilities: [],
                learner_health_issues: [],
                learner_talents: [],
                learner_practical_skills: [],
                principal_subjects: [],
                subsidiary_subject: [],
            };
            this.nira_parent = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.nin_learner = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
            this.uneb_learner = {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                equated_code: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                equated_year: '',
                exam_year: '',
                code_type: '',
            };
            this.$refs.notifyError.messages = [];
            this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                let ug = this.countries.find(country => {
                    return country.name === "UGANDA";
                });
                let english = this.familiar_languages.find(language => {
                    return language.name.includes('ENGLISH');
                });
                $('#learnerCountryIdEquated').val(ug.id).change();
                $('#learnerClassId').val('').change();
                $('#learnerExamLevelId').val('').change();
                $('#learnerEquatedYearId').val('').change();
                $('#learnerOrphanTypeEquated').val('').change();
                $('#learnerEquatedDistrictOfBirthId').val('').change();
                $('#learnerFamiliarLanguageIdEquated').val(english.id).change();
                $('#learnerParentRelationshipEquated').val('').change();
                $('#learnerBirthDateEquated').datepicker('setDate', moment().toDate());
                this.clearPhoto();
            }, 50);
        },
        selectFile() {
            this.learner.photo = this.$refs.photo.files[0];
        },
        getPrincipalSubjectDisabled: function (subject) {
            return this.learner.principal_subjects.length >= 3 && !this.learner.principal_subjects.includes(subject.id);
        },
        getSubSubjectDisabled: function (subject) {
            return this.learner.subsidiary_subject.length >= 1 && !this.learner.subsidiary_subject.includes(subject.id);
        },
        uploadImage: function () {
            $('.dropify').click();
        },
        clearPhoto: function () {
            let fileDropper = this.photoDropify.data('dropify');
            fileDropper.resetPreview();
            fileDropper.clearElement();
            fileDropper.settings['defaultFile'] = this.learner.gender === 'M' ? '@images/default_male.jpg' : '@images/default_female.jpg';
            fileDropper.destroy();
            fileDropper.init();
            this.learner.photo = null;
        },
        updateDefaultPhoto: function () {
            if (this.nira_learner.photo !== null) {
                if (this.nira_learner.photo.includes('.png')) {
                    let fileDropper = this.photoDropify.data('dropify');
                    fileDropper.resetPreview();
                    fileDropper.clearElement();
                    fileDropper.settings['defaultFile'] = '/images/nira-photos/'+ this.nira_learner.photo;
                    fileDropper.destroy();
                    fileDropper.init();
                } else {
                    $('.dropify-render').children('img').attr('src', 'data:image/png;base64,'+this.nira_learner.photo);
                }
            } else {
                if (this.learner.photo === null) {
                    this.clearPhoto();
                }
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $('#updateSectionCModal').animate({ scrollTop: 0 }, 'slow');
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        academicYears: function () {
            let years = [];

            for (let i = Number(moment().format("YYYY"))-1; i >= 2003; i--) {
                years.push(i);
            }

            return years;
        },
        //Check if learner nin is same as that of a parent
        isNinSame() {
            const learnerNin = this.learner.nin;
            const parentNin = this.learner.parent_nin;

            // Check if both `learner nin` and `parent nin` are not empty before comparing
            if (learnerNin && parentNin) {
                return learnerNin.toUpperCase() === parentNin.toUpperCase();
            }
            return false;
        },
        defaultPhoto() {
            return this.learner.gender === "M"
                ? "@images/default_male.jpg"
                : "@images/default_female.jpg";
        },
        oLevelClasses: function () {
            return this.education_grades.filter(grade => {
                return grade.grade_rank <= 4;
            });
        },
        aLevelClasses: function () {
            return this.education_grades.filter(grade => {
                return grade.grade_rank > 4;
            });
        },
        subsidiarySubjects: function () {
            return this.subjects.filter(subject=>{
                return !subject.is_principal_subject && subject.name.startsWith('SUB');
            });
        },
        principalSubjects: function () {
            return this.subjects.filter(subject=>{
                return subject.is_principal_subject;
            });
        },
    },
}
</script>

<style scoped>

</style>
