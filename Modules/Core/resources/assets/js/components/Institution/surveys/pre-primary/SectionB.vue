<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                <tr class="bg-secondary">
                    <th colspan="2" class="align-middle text-uppercase text-white border-secondary border">Section B: SCHOOL PARTICULARS</th>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="align-middle text-uppercase text-dark border-secondary border w-35">Identifier</th>
                    <th class="align-middle text-uppercase text-dark border-secondary border">Name/Detail</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">ECCE CENTER TYPE</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.pre_primary_school.has_daycare_center && school.pre_primary_school.has_nursery_section">Nursery and Day Care Centre</span>
                        <span v-if="school.pre_primary_school.has_daycare_center && !school.pre_primary_school.has_nursery_section">Day Care Only</span>
                        <span v-if="!school.pre_primary_school.has_daycare_center && school.pre_primary_school.has_nursery_section">Nursery Only</span>
                        <span v-if="!school.pre_primary_school.has_daycare_center && !school.pre_primary_school.has_nursery_section" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">ATTACHED TO PRIMARY SCHOOL</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.pre_primary_school.attached_primary_school !== null">YES</span>
                        <span v-else>NO</span>
                    </td>
                </tr>
                <tr v-if="school.pre_primary_school.attached_primary_school !== null">
                    <th class="align-middle text-uppercase text-dark border border-secondary">PRIMARY SCHOOL</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">{{ school.pre_primary_school.attached_primary_school.name }}</td>
                </tr>
                <tr v-if="school.pre_primary_school.attached_primary_school !== null">
                    <th class="align-middle text-uppercase text-dark border border-secondary">PRIMARY SCHOOL PHONE</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">{{ school.pre_primary_school.attached_primary_school.phone }}</td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.legal_ownership_status !== null">{{ school.legal_ownership_status.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.founding_body !== null">{{ school.founding_body.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.year_founded !== null">{{ school.year_founded }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.registration_status_id === 1">REGISTERED</span>
                        <span v-if="school.registration_status_id === 2">LICENSED</span>
                        <span v-if="school.registration_status_id === 0">NOT LICENSED</span>
                        <span v-if="school.registration_status_id === null" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school_form.registration_status_id === 1">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.registration_number !== null">{{ school.registration_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school_form.registration_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.license_number !== null">{{ school.license_number }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.license_number !== null">{{ formatDate(school.licence_certificate_expiry_date) }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">sex composition</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.has_male_students && school.has_female_students">Mixed</span>
                        <span v-if="school.has_male_students && !school.has_female_students">Males Only</span>
                        <span v-if="!school.has_male_students && school.has_female_students">Females Only</span>
                        <span v-if="!school.has_male_students && !school.has_female_students" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">DAY OR BOARDING</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.pre_primary_school.admits_day_scholars_yn && school.pre_primary_school.admits_boarders_yn">Day and Boarding</span>
                        <span v-if="school.pre_primary_school.admits_day_scholars_yn && !school.pre_primary_school.admits_boarders_yn">Day Only</span>
                        <span v-if="!school.pre_primary_school.admits_day_scholars_yn && school.pre_primary_school.admits_boarders_yn">Boarding Only</span>
                        <span v-if="!school.pre_primary_school.admits_day_scholars_yn && !school.pre_primary_school.admits_boarders_yn" class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr v-show="school.school_ownership_status_id === 2">
                    <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.capital_for_establishment !== null">UGX {{ formatMoney(school.capital_for_establishment) }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Distance to DEO/MEO/CEO Main office</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.pre_primary_school.deo_office_distance !== null">{{ school.pre_primary_school.deo_office_distance.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Pre-Primary School</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.pre_primary_school.school_distance_range !== null">{{ school.pre_primary_school.school_distance_range.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health facility</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.health_facility_distance !== null">{{ school.health_facility_distance.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="2" class="border-0 text-center">
                        <button data-toggle="modal" data-target="#updateSectionBModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                            <span class="text-uppercase">UPDATE SCHOOL PARTICULARS</span>
                        </button>
                    </td>
                </tr>
                </tfoot>
            </table>
        </div>

        <!-- Update Section B Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateSectionBModal">
            <form @submit.prevent="updateSectionB()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetSectionB()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update School Particulars</h5>
                        </div>
                        <div class="modal-body overflow-auto scrollbar-dark-teal h-500px" data-simplebar data-simplebar-auto-hide="false">
                            <error-notifications ref="notifyError"></error-notifications>
                            <!-- ECCE Type -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolEcceCenterType">ECCE Center Type</label>
                                        <span class="form-note">Specify this ECCE Center's type.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolEcceCenterType" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="daycare">DAYCARE ONLY</option>
                                            <option value="nursery">NURSERY ONLY</option>
                                            <option value="both">BOTH DAYCARE & NURSERY</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /ECCE Type -->

                            <!-- Attached Primary School YES/NO -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label">Attached To Primary School</label>
                                        <span class="form-note">Is this school attached to a primary school?</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="school_form.attached_to_primary" type="radio" value="1" class="custom-control-input" id="attachedToPrimaryYes">
                                                    <label class="custom-control-label" for="attachedToPrimaryYes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="school_form.attached_to_primary" type="radio" value="0" class="custom-control-input" id="attachedToPrimaryNo">
                                                    <label class="custom-control-label" for="attachedToPrimaryNo">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- /Attached Primary School YES/NO -->

                            <!-- Attached Primary School -->
                            <div v-show="school_form.attached_to_primary === 1" class="row g-3 align-center">
                                <div v-show="school_form.attached_to_primary === 1" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="primarySchoolEmisNumber">Primary School EMIS Number</label>
                                        <span class="form-note">Specify the EMIS number this school is attached to</span>
                                    </div>
                                </div>
                                <div v-show="school_form.attached_to_primary === 1" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.attached_to_primary === 1" v-model.trim="school_form.primary_school_emis" placeholder="eg 2A000000" id="primarySchoolEmisNumber" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Attached Primary School -->

                            <!-- Legal Ownership Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                        <span class="form-note">Specify the school's legal ownership status.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolLegalOwnershipStatusId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option :disabled="(school.school_ownership_status_id === '2' || school.school_ownership_status_id === 2) && legal_ownership_status.name.startsWith('GOVERNMENT')" v-for="legal_ownership_status in legal_ownership_statuses" :value="legal_ownership_status.id">{{ legal_ownership_status.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Legal Ownership Status -->

                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                        <span class="form-note">Specify the school's founding body.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolFoundingBodyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option :disabled="(school.school_ownership_status_id === '2' || school.school_ownership_status_id === 2) && founding_body.name.startsWith('GOVERNMENT')" v-for="founding_body in founding_bodies" :value="founding_body.id">{{ founding_body.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Founding Body -->

                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                        <span class="form-note">Specify the year this School was founded.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolYearFounded" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="year in founderYears" :value="year">{{ year }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Year Founded -->

                            <!-- Registration Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationStatus">Registration Status</label>
                                        <span class="form-note">Specify if this school is registered or licensed.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolRegistrationStatus" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="1">REGISTERED</option>
                                            <option value="2">LICENCED</option>
                                            <option value="0">NOT LICENSED</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Registration Status -->

                            <!-- Registration Number -->
                            <div v-show="school_form.registration_status_id === 1" :class="school_form.registration_status_id === 1 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 1" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationNumber">Registration Number</label>
                                        <span class="form-note">Specify this school's registration number</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 1" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 1" v-model.trim="school_form.registration_number" id="schoolRegistrationNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="Enter Registration Number" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Registration Number -->

                            <!-- License Number -->
                            <div v-show="school_form.registration_status_id === 2" :class="school_form.registration_status_id === 2 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLicenseNumber">License Number</label>
                                        <span class="form-note">Specify this school's license number</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 2" v-model.trim="school_form.license_number" id="schoolLicenseNumber" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="Enter License Number" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-show="school_form.registration_status_id === 2" :class="school_form.registration_status_id === 2 ? 'row g-3 align-center' : 'm-0'">
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="licenseNumberDateExpiry">License Number Expiry Date</label>
                                        <span class="form-note">Specify this School's license expiry date</span>
                                    </div>
                                </div>
                                <div v-show="school_form.registration_status_id === 2 && school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input :required="school_form.registration_status_id === 2" v-model.trim="school_form.licence_certificate_expiry_date" id="licenseNumberDateExpiry" placeholder="Enter License Number Expiry Date." type="text" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /License Number -->

                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSexComposition">Sex Composition</label>
                                        <span class="form-note">Specify this school's sex composition.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolSexComposition" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="male">MALES ONLY</option>
                                            <option value="female">FEMALES ONLY</option>
                                            <option value="mixed">MIXED</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Sex Composition -->

                            <!-- DAY OR BOARDING -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDayOrBoarding">Day Or Boarding</label>
                                        <span class="form-note">Specify whether this school is day, boarding or both.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolDayOrBoarding" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="day">DAY ONLY</option>
                                            <option value="boarding">BOARDING ONLY</option>
                                            <option value="both">DAY & BOARDING</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /DAY OR BOARDING -->

                            <!-- Capital for establishment -->
                            <div v-show="school.school_ownership_status_id === 2" class="row g-3 align-center">
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                        <span class="form-note">Specify this School's Capital For Establishment.</span>
                                    </div>
                                </div>
                                <div v-show="school.school_ownership_status_id === 2" class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input @keyup="cleanUpEstablishmentCapital()" v-model.trim="school_form.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control text-center bg-primary-dim text-uppercase" placeholder="E.g 5,000,000" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Capital for establishment -->

                            <!-- DEO Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDeoDistanceId">Distance to DEO/MEO/CEO Main Office</label>
                                        <span class="form-note">Distance from this school to the DEO/MEO/CEO's office.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolDeoDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="distance in deo_distances" :value="distance.id">{{ distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /DEO Distance -->

                            <!-- School Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolEcceSchoolDistanceId">Distance to nearest ECCE Center</label>
                                        <span class="form-note">Distance from this school to the nearest ECCE Center.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolEcceSchoolDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="distance in school_distances" :value="distance.id">{{ distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /School Distance -->

                            <!-- Health Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                        <span class="form-note">Distance from this school to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="schoolHealthDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="distance in health_distances" :value="distance.id">{{ distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- /Health Distance -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetSectionB()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Section B Modal -->
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import SurveyFooter from "../SurveyFooter.vue";
export default {
    name: "SectionB",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'legalOwnershipStatusesObj',
        'foundingBodiesObj',
        'schoolDistancesObj',
        'deoDistancesObj',
        'healthDistancesObj',
    ],
    components: {
        SurveyFooter,
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/school/section-b',
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                pre_primary_school: {
                    attached_primary_school: {name: '', phone: '',},
                    school_distance_range: {name: ''},
                    deo_office_distance: {name: ''},
                    has_daycare_center: false,
                    has_nursery_section:false,
                },
            },
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school_form: {
                is_operational_yn: 1,
                school_closure_reason: '',
                ecce_type: '',
                attached_to_primary: 0,
                primary_school_emis: '',
                legal_ownership_status_id: '',
                founding_body_id: '',
                year_founded: '',
                registration_status_id: '',
                registration_number: '',
                license_number: '',
                licence_certificate_expiry_date: '',
                sex_composition: '',
                day_or_boarding: '',
                capital_for_establishment: '',
                estimated_distance_to_deo_office_id: '',
                health_facility_distance_range_id: '',
                estimated_distance_to_ecce_school_id: '',
                section_id: '',
            },
            legal_ownership_statuses: [],
            founding_bodies: [],
            deo_distances: [],
            school_distances: [],
            health_distances: [],
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school_form.section_id = this.sectionId;

            $('#schoolLegalOwnershipStatusId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.legal_ownership_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolDeoDistanceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.estimated_distance_to_deo_office_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolEcceSchoolDistanceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.estimated_distance_to_ecce_school_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolHealthDistanceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.health_facility_distance_range_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolFoundingBodyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.founding_body_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolYearFounded').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.year_founded = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolEcceCenterType').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.ecce_type = data.id;
                    return data.text;
                },
            });

            $('#schoolSexComposition').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.sex_composition = data.id;
                    return data.text;
                },
            });

            $('#schoolDayOrBoarding').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.day_or_boarding = data.id;
                    return data.text;
                },
            });

            $('#schoolRegistrationStatus').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#updateSectionBModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.registration_status_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#licenseNumberDateExpiry').datepicker({
                format: 'd MM, yyyy',
                //endDate: '0D',
                autoclose: true,
            }).on('hide', e=>{
                self.school_form.licence_certificate_expiry_date = moment(e.date).format("D MMMM, YYYY");
            });

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.legal_ownership_statuses = this.legalOwnershipStatusesObj;
            this.founding_bodies = this.foundingBodiesObj;
            this.school_distances = this.schoolDistancesObj;
            this.deo_distances = this.deoDistancesObj;
            this.health_distances = this.healthDistancesObj;

            this.resetSectionB();
        },

        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },

        cleanUpEstablishmentCapital: function () {
            if (this.school_form.capital_for_establishment.length) {
                this.school_form.capital_for_establishment = this.school_form.capital_for_establishment.replace(/[^0-9]/g,"");
            }

            if (this.school_form.capital_for_establishment.length) {
                this.school_form.capital_for_establishment = moneyFormat.to(moneyFormat.from(this.school_form.capital_for_establishment));
            }
        },

        formatMoney: function (raw_money = '') {
            if (typeof raw_money === "string" && raw_money.length) {
                return moneyFormat.to(moneyFormat.from(raw_money.replace(/[^0-9]/g,"")));
            } else if(typeof raw_money === "number") {
                return moneyFormat.to(moneyFormat.from(raw_money.toString().replace(/[^0-9]/g,"")));
            }
            return null
        },

        updateSectionB: function () {
            this.loading = true;
            axios.post(this.api_url+"/"+this.survey.id, this.school_form)
                .then(response=>{
                    this.school = response.data.school;
                    this.$parent.school = response.data.school;
                    this.$parent.survey = response.data.survey;
                    this.resetSectionB();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"School Particulars Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetSectionB: function() {
            $('#updateSectionBModal').modal('hide');
            this.loading = false;

            let type = '';
            if (this.school.pre_primary_school.has_daycare_center && this.school.pre_primary_school.has_nursery_section) {
                type = 'both';
            } else if (this.school.pre_primary_school.has_daycare_center && !this.school.pre_primary_school.has_nursery_section) {
                type = 'daycare';
            } else if (!this.school.pre_primary_school.has_daycare_center && this.school.pre_primary_school.has_nursery_section) {
                type = 'nursery';
            }

            let gender = '';
            if (this.school.has_male_students && this.school.has_female_students) {
                gender = 'mixed';
            } else if (this.school.has_male_students && !this.school.has_female_students) {
                gender = 'male';
            } else if (!this.school.has_male_students && this.school.has_female_students) {
                gender = 'female';
            }

            let boarding = '';
            if (this.school.pre_primary_school.admits_day_scholars_yn && this.school.pre_primary_school.admits_boarders_yn) {
                boarding = 'both';
            } else if (this.school.pre_primary_school.admits_day_scholars_yn && !this.school.pre_primary_school.admits_boarders_yn) {
                boarding = 'day';
            } else if (!this.school.pre_primary_school.admits_day_scholars_yn && this.school.pre_primary_school.admits_boarders_yn) {
                boarding = 'boarding';
            }

            this.school_form = {
                is_operational_yn: this.school.is_operational_yn ? 1 : 0,
                school_closure_reason: this.school.school_closure_reason,
                ecce_type: type,
                attached_to_primary: this.school.pre_primary_school.attached_primary_school === null ? 0 : 1,
                primary_school_emis: this.school.pre_primary_school.attached_primary_school === null ? 0 : this.school.pre_primary_school.attached_primary_school.emis_number,
                legal_ownership_status_id: this.school.legal_ownership_status_id === null ? '' : this.school.legal_ownership_status_id,
                founding_body_id: this.school.founding_body_id === null ? '' : this.school.founding_body_id,
                year_founded: this.school.year_founded,
                registration_status_id: this.school.registration_status_id === null ? '' : this.school.registration_status_id,
                registration_number: this.school.registration_number,
                license_number: this.school.license_number,
                licence_certificate_expiry_date: this.school.licence_certificate_expiry_date,
                sex_composition: gender,
                day_or_boarding: boarding,
                capital_for_establishment: this.school.capital_for_establishment,
                estimated_distance_to_deo_office_id: '',
                health_facility_distance_range_id: '',
                estimated_distance_to_ecce_school_id: '',
                section_id: '',
            }
            this.school_form.section_id = this.sectionId;

            window.setTimeout(()=>{
                $('#schoolEcceCenterType').val(type).change();
                $('#schoolLegalOwnershipStatusId').val(this.school_form.legal_ownership_status_id).change();
                $('#schoolFoundingBodyId').val(this.school_form.founding_body_id).change();
                $('#schoolYearFounded').val(this.school.year_founded).change();
                $('#schoolRegistrationStatus').val(this.school.registration_status_id).change();
                $('#schoolSexComposition').val(this.school_form.sex_composition).change();
                $('#schoolDayOrBoarding').val(this.school_form.day_or_boarding).change();
                $('#schoolDeoDistanceId').val(this.school.pre_primary_school.estimated_distance_to_deo_office_id).change();
                $('#schoolEcceSchoolDistanceId').val(this.school.pre_primary_school.estimated_distance_to_ecce_school_id).change();
                $('#schoolHealthDistanceId').val(this.school.health_facility_distance_range_id).change();
            }, 50)
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        founderYears: function () {
            let years = [];

            for (let i = Number(moment().format('YYYY')); i >= 1800; i--) {
                years.push(i);
            }

            return years;
        },
    },
}
</script>

<style scoped>

</style>
