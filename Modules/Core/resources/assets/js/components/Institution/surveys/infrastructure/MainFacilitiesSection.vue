<template>
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ category.name }}</h5>

                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button @click="openModal()"  class="cursor btn bg-dark-teal btn-md d-sm-none"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">ADD</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button @click="openModal()"  class="cursor btn bg-dark-teal btn-md"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">ADD {{ category.name }}</span></button>
                            </div>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div v-if="school.school_type_id === 7" class="nk-tb-col"><span class="sub-text text-white">Education Level</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white">Infrastructure Type</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white">Completion Status</span></div>
                        <div v-show="category.gender_usage_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">Usage By Gender</span>
                        </div>
                        <div v-show="category.user_category_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">User Category</span>
                        </div>
                        <div v-show="category.is_area_captured_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">Total Area Per Room</span>
                        </div>
                        <div class="nk-tb-col text-center">
                            <span v-if="category.is_area_captured_yn" class="sub-text text-white">Total Count of Rooms</span>
                            <span v-else class="sub-text text-white">Total Count</span>
                        </div>
                        <div class="nk-tb-col text-center"><span class="text-white">ACTIONS</span></div>
                    </div><!-- .nk-tb-item -->
                    <div v-for="facility in schoolCategory" class="nk-tb-item">
                        <div v-if="categoryExists(category) && facility.inter_education_level_id !== '' && school.school_type_id === 7" class="nk-tb-col text-center">
                            <span  class="text-secondary">{{ facility.inter_education_level.name }}</span>
                        </div>
                        <div v-if="categoryExists(category)" class="nk-tb-col ucap">
                            <span class="text-secondary" v-if="facility.usage_mode === 1">Permanent</span>
                            <span class="text-secondary" v-if="facility.usage_mode === 2">Temporary</span>
                        </div>
                        <div v-if="categoryExists(category)" class="nk-tb-col ucap">
                            <span class="text-secondary" v-if="facility.completion_status === 1">Complete</span>
                            <span class="text-secondary" v-if="facility.completion_status === 2">Incomplete</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.gender_usage !== '' && category.gender_usage_yn" class="nk-tb-col text-center ucap">
                            <span v-if="facility.gender_usage === 1"  class="text-secondary">Male</span>
                            <span v-if="facility.gender_usage === 2" class="text-secondary">Female</span>
                            <span v-if="facility.gender_usage === 3" class="text-secondary">Both Male & Female</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.user_category !== '' && category.user_category_yn" class="nk-tb-col text-center ucap">
                            <span v-if="facility.user_category === 1"  class="text-secondary">Learners</span>
                            <span v-if="facility.user_category === 2" class="text-secondary">{{ getLevelTitle }}</span>
                            <span v-if="facility.user_category === 3" class="text-secondary">Learners & {{ getLevelTitle }}</span>
                            <span v-if="facility.user_category === 4" class="text-secondary">Learners & {{ getLevelTitle }} With Disability(SNE)</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.area !== '' && category.is_area_captured_yn" class="nk-tb-col text-center">
                            <span  class="text-secondary">{{ facility.area }} SQ<sup>M</sup></span>
                        </div>
                        <div v-if="categoryExists(category) && facility.total_number !== ''" class="nk-tb-col text-center">
                            <span class="text-secondary" >{{ facility.total_number }}</span>
                        </div>
                        <div v-if="categoryExists(category)" class="nk-tb-col text-center">
                            <span  @click="editInfrastructure(facility)" data-toggle="tooltip" data-placement="top"  title="Edit Facility" class="cursor lead text-dark-teal mr-1">
                                <em class="icon ni ni-edit-fill"></em>
                            </span>
                        </div>
                    </div>
                </div>
                <div v-if="!categoryExists(category) && !loading" class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There is no infrastructure information to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" tabindex="-1" :id="'schoolInfrastructureModal'+category.id">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetInfrastructure()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateInfrastructure() : createInfrastructure()">
                        <div class="modal-header">
                            <h6 class="modal-title">{{ edit ? 'EDIT' : 'ADD' }} {{ category.name }}</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div v-show="school.school_type_id === 7" class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label :for="'inter_education_level'+category.id" class="form-label">Education Level <span class="text-danger">*</span></label>
                                        <select :id="'inter_education_level'+category.id" :required="school.school_type_id === 7" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="level in inter_education_levels" :value="level.id">{{ level.name }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div><!-- .row -->
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label :for="'building_usage_mode'+category.id" class="form-label">Infrastructure Type <span class="text-danger">*</span></label>
                                        <select v-model="form_infrastructure.usage_mode" :id="'building_usage_mode'+category.id" class="form-select-sm">
                                            <option value="">--Select All--</option>
                                            <option value="1">Permanent</option>
                                            <option value="2">Temporary</option>
                                        </select>
                                    </div>
                                </div>
                            </div><!-- .row -->
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label :for="'building_completion_status'+category.id" class="form-label">Completion Status <span class="text-danger">*</span></label>
                                        <select v-model="form_infrastructure.completion_status" :id="'building_completion_status'+category.id" class="form-select-sm">
                                            <option value="">--Select All--</option>
                                            <option value="1">Complete</option>
                                            <option value="2">Incomplete</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div v-show="category.gender_usage_yn" class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label :for="'building_gender_usage'+category.id" class="form-label">Usage By Gender <span class="text-danger">*</span></label>
                                        <select v-model="form_infrastructure.gender_usage" :id="'building_gender_usage'+category.id" class="form-select-sm">
                                            <option value="">--Select All--</option>
                                            <option value="1">Male</option>
                                            <option value="2">Female</option>
                                            <option value="3">Both Male & Female</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div v-show="category.user_category_yn" class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label :for="'building_user_category'+category.id" class="form-label">User Category <span class="text-danger">*</span></label>
                                        <select v-model="form_infrastructure.user_category" :id="'building_user_category'+category.id" class="form-select-sm">
                                            <option value="">--Select All--</option>
                                            <option value="1">Learners Only</option>
                                            <option value="2">{{ getLevelTitle }} Only</option>
                                            <option value="3">Learners & {{ getLevelTitle }}</option>
                                            <option value="4">Learners & {{ getLevelTitle }} With Disability(SNE)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-4">
                                <div v-if="category.is_area_captured_yn" class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="classrooms_total_area" class="form-label">Total Area Per Room<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <div class="form-control-wrap">
                                                <div class="form-text-hint bg-primary-dim">
                                                    <span class="overline-title">SQ<sup>M</sup> </span>
                                                </div>
                                                <input min="1" v-model.number="form_infrastructure.area" id="classrooms_total_area" type="number" class="form-control text-center bg-primary-dim" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label v-if="category.is_area_captured_yn" for="classrooms_quantity" class="form-label">Total Count of Rooms with Above Area <span class="text-danger">*</span></label>
                                            <label v-else for="classrooms_quantity" class="form-label">Total Count <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-control-group">
                                            <input min="1" v-model.number="form_infrastructure.total_number" id="classrooms_quantity" type="number" class="form-control text-center bg-primary-dim" required>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- .row -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetInfrastructure()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";

export default {
    name: "MainFacilitiesSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'categoryObj',
        'internationalEducationLevelsObj',
        'preprimary',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            edit: false,
            api_url: '/institutions/surveys/infrastructure',
            school: {
                infrastructure: []
            },
            inter_education_levels: [],
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            category: {
                id: '',
                name: '',
                is_area_captured_yn: false,
                is_lab_yn: false,
                gender_usage_yn: false,
                user_category_yn: false,
            },
            infrastructure: [],
            form_infrastructure: {
                id: '',
                building_id: '',
                inter_education_level_id: '',
                section_id: '',
                usage_mode: '',
                completion_status: '',
                gender_usage: '',
                user_category: '',
                area: 0,
                total_number: 0,
            },
            facility: {
                id: '',
                international_education_level_id: '',
                building_id: '',
                section_id: '',
                gender_usage: '',
                completion_status: '',
                user_category: '',
                usage_mode: '',
                area: 0,
                total_number: 0,
            },
        }
    },
    methods: {

        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.category = this.categoryObj;
            this.inter_education_levels = this.internationalEducationLevelsObj;
            this.survey = this.surveyObj;
            this.form_infrastructure.section_id = this.sectionId;
            this.form_infrastructure.building_id = this.category.id;
            this.form_infrastructure.survey_id = this.survey.id;

            window.setTimeout(()=>{
                $('#inter_education_level'+this.category.id).select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#schoolInfrastructureModal'+this.category.id),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.form_infrastructure.inter_education_level_id = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });

                $('#building_usage_mode'+this.category.id).select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#schoolInfrastructureModal'+this.category.id),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.form_infrastructure.usage_mode = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });

                $('#building_completion_status'+this.category.id).select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#schoolInfrastructureModal'+this.category.id),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.form_infrastructure.completion_status = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });

                $('#building_gender_usage'+this.category.id).select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#schoolInfrastructureModal'+this.category.id),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.form_infrastructure.gender_usage = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });

                $('#building_user_category'+this.category.id).select2({
                    minimumResultsForSearch: 0,
                    dropdownParent: $('#schoolInfrastructureModal'+this.category.id),
                    containerCssClass: 'bg-primary-dim',
                    templateSelection: function (data, container) {
                        self.form_infrastructure.user_category = data.id !== "" ? Number(data.id) : data.id;
                        return data.text;
                    },
                });
            },50);
        },

        openModal: function () {
            this.resetInfrastructure();
            $('#schoolInfrastructureModal'+this.category.id).modal({backdrop: "static"});
        },

        categoryExists: function (category) {
            return this.school.infrastructure.find(cat=>{
                return cat.building_id === category.id;
            }) !== undefined;
        },

        createInfrastructure: function () {
            this.loading = true;
            axios.post(this.api_url+'/create/'+this.survey.id, this.form_infrastructure)
                .then(response=>{
                    this.school.infrastructure = response.data.infrastructure;
                    this.resetInfrastructure();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:this.category.name.toLowerCase()+" added successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        editInfrastructure: function (facility) {
            this.facility = facility;
            this.edit = true;
            this.form_infrastructure.area = facility.area;
            this.form_infrastructure.total_number = facility.total_number;
            $('#inter_education_level'+this.category.id).val(facility.international_education_level_id).change();
            $('#building_completion_status'+this.category.id).val(facility.completion_status).change();
            $('#building_usage_mode'+this.category.id).val(facility.usage_mode).change();
            $('#building_gender_usage'+this.category.id).val(facility.gender_usage).change();
            $('#building_user_category'+this.category.id).val(facility.user_category).change();
            window.setTimeout(()=>{ $('#schoolInfrastructureModal'+this.category.id).modal({backdrop: "static"}) }, 10);
        },

        updateInfrastructure: function () {
            this.loading = true;

            axios.post(this.api_url+'/update/'+this.survey.id,{
                    facility_id: this.facility.id,
                    inter_education_level_id: this.form_infrastructure.inter_education_level_id,
                    usage_mode: this.form_infrastructure.usage_mode,
                    completion_status: this.form_infrastructure.completion_status,
                    gender_usage: this.form_infrastructure.gender_usage,
                    user_category: this.form_infrastructure.user_category,
                    area: this.form_infrastructure.area,
                    total_number: this.form_infrastructure.total_number
                })
                .then(response=>{
                    this.school.infrastructure = response.data.infrastructure;
                    this.resetInfrastructure();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:this.category.name.toLowerCase()+" updated successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetInfrastructure: function () {

            this.loading = false;
            this.edit = false;

            $('#inter_education_level'+this.category.id).val('').change();
            $('#building_completion_status'+this.category.id).val('').change();
            $('#building_usage_mode'+this.category.id).val('').change();
            $('#building_gender_usage'+this.category.id).val('').change();
            $('#building_user_category'+this.category.id).val('').change();
            this.form_infrastructure.area = 0;
            this.form_infrastructure.total_number = 0;
            this.form_infrastructure.section_id = this.sectionId;
            $('#schoolInfrastructureModal'+this.category.id).modal("hide");
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        schoolCategory: function () {
            if (this.categoryExists(this.category)) {
                return this.school.infrastructure.filter(cat=>{
                    return cat.building_id === this.category.id
                });
            } else {
                return {
                    inter_education_level_id: '',
                    usage_mode: '',
                    completion_status: '',
                    gender_usage: '',
                    user_category: '',
                    area: 0,
                    total_number: 0,
                }
            }
        },

        getLevelTitle: function() {
            return this.preprimary === 'yes' ? 'Caregivers' : 'Teachers';
        }
    }
}
</script>

<style scoped>

</style>
