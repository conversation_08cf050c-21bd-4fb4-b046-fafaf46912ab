<template>
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">Textbooks</h5>

                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolTextBooksModal"  class="cursor btn bg-dark-teal btn-md d-sm-none"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Textbooks</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolTextBooksModal"  class="cursor btn bg-dark-teal btn-md"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Textbooks</span></button>
                            </div>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Class</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Subject</span></div>
<!--                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Book Name</span></div>-->
                        <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span></div>
                        <!--                        <div class="nk-tb-col text-center"><span class="text-white ucap">Action</span></div>-->
                    </div><!-- .nk-tb-item -->
                    <div v-for="item in school.text_books" class="nk-tb-item">
                        <div class="nk-tb-col">
                            <span class="text-secondary text-center ucap">{{ item.education_grade.name }}</span>
                        </div>
                        <div class="nk-tb-col">
                            <span class="text-secondary text-center ucap">{{ item.secondary_subject.name }}</span>
                        </div>
<!--                        <div class="nk-tb-col tb-col-sm">-->
<!--                            <span class="text-secondary text-center ucap">{{ item.education_grade.name }}-{{ item.secondary_subject.name }}</span>-->
<!--                        </div>-->
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ item.quantity }}</span>
                        </div>

                        <!--                        <div @click="editTextbook(item)" class="nk-tb-col text-center">-->
                        <!--                            <span data-toggle="tooltip" data-placement="top"  title="Edit Book" class="cursor lead text-dark-teal mr-1">-->
                        <!--                                <em class="icon ni ni-edit-fill"></em>-->
                        <!--                            </span>-->
                        <!--                        </div>-->
                    </div>
                </div>
                <div v-if="!school.text_books.length"  class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There are no Textbooks to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolTextBooksModal" aria-hidden="true">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetTextbook()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateTextbook() : createTextbook()">
                        <div class="modal-header">
                            <h5 class="modal-title ucap">{{ edit ? 'EDIT' : 'UPDATE' }} Textbooks</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="textBookGradeId">Select Class <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select required v-model="form_report.education_grade_id" id="textBookGradeId" data-placeholder="Select Class" class="form-select-sm">
                                                <option value="">--Select Class--</option>
                                                <option v-for="grade in education_grades" :value="grade.id">{{ grade.name }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="secondaryTextbookSubjectId">Select Subject<span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select required id="secondaryTextbookSubjectId" data-placeholder="Select Subject" class="form-select-sm">
                                                <option value="">--Select--</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

<!--                                <div class="col-12">-->
<!--                                    <div class="form-group">-->
<!--                                        <label for="textBookId">Select Textbook<span class="text-danger">*</span></label>-->
<!--                                        <div class="form-control-wrap">-->
<!--                                            <select id="textBookId" data-placeholder="Select Textbook" class="form-select-sm">-->
<!--                                                <option value="">&#45;&#45;Select&#45;&#45;</option>-->
<!--                                            </select>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="quantity" class="form-label">Number <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input required v-model.number="form_report.quantity" type="number" id="quantity" class="form-control" placeholder="Enter Number" autocomplete="off">
                                        </div>
                                    </div>
                                </div>

                            </div><!-- .form-inline -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "Textbooks",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'educationGradesObj',
        'subjectsObj',
        'textbooksObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            edit: false,
            api_url: '/institutions/surveys/instructional-materials',
            school: {
                text_books: []
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            education_grades: [],
            secondary_subjects: [],
            subjects: [],
            textbooks: [],
            all_textbooks: [],
            form_report: {
                id: '',
                section_id: '',
                // book_id: '',
                education_grade_id: '',
                subject_id: '',
                quantity: 0,
            }

        }
    },
    methods: {

        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
            this.education_grades = this.educationGradesObj;
            this.secondary_subjects = this.subjectsObj;
            this.all_textbooks = this.textbooksObj;

            $('#textBookGradeId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolTextBooksModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    self.loadSecondarySubjects();
                    return data.text;
                },
            });

            $('#secondaryTextbookSubjectId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolTextBooksModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.subject_id = data.id !== "" ? Number(data.id) : data.id;
                    // self.loadTextbooks();
                    return data.text;
                },
            });

            // $('#textBookId').select2({
            //     minimumResultsForSearch: 0,
            //     dropdownParent: $('#schoolTextBooksModal'),
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.form_report.book_id = data.id !== "" ? Number(data.id) : data.id;
            //         return data.text;
            //     },
            // });

        },

        loadSecondarySubjects: function () {
            let self = this;
            // clean subjects
            this.subjects = [];
            let select = $("#secondaryTextbookSubjectId");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.form_report.subject_id = "";

            //load new options
            if (self.form_report.education_grade_id !== "") {
                self.subjects = self.education_grades.find(grade=>{
                    return grade.id === self.form_report.education_grade_id
                }).secondary_subjects;

                self.subjects.forEach(subject=>{
                    let subjectOption = new Option(subject.name.toUpperCase(), subject.id, false, false);
                    select.append(subjectOption).trigger('change');
                });
            }
        },

        // loadTextbooks: function () {
        //     let self = this;
        //     // clean textbooks
        //     this.textbooks = [];
        //     let select = $("#textBookId");
        //     select.empty().trigger('change');
        //     let newOption = new Option("--Select--", "", false, false);
        //     select.append(newOption).trigger('change');
        //     self.form_report.book_id = "";
        //
        //     //load new options
        //     if (self.form_report.subject_id !== "") {
        //         self.textbooks = self.all_textbooks.filter(textbook=>{
        //             return textbook.subject_id === self.form_report.subject_id && textbook.education_grade_id === self.form_report.education_grade_id
        //         });
        //
        //         self.textbooks.forEach(textbook=>{
        //             let subjectOption = new Option(textbook.name.toUpperCase(), textbook.id, false, false);
        //             select.append(subjectOption).trigger('change');
        //         });
        //     }
        // },

        createTextbook: function () {
            this.loading = true;
            axios.post(this.api_url+'/textbooks-create/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.text_books = response.data.text_books;
                    this.resetTextbook();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Textbook added successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        editTextbook: function (item) {
            this.edit = true;
            this.form_report = item;
            this.form_report.quantity = item.quantity;
            $('#textBookGradeId').val(item.education_grade_id).change();
            $('#secondaryTextbookSubjectId').val(item.subject_id).change();
            // $('#textBookId').val(item.book_id).change();
            window.setTimeout(()=>{ $('#schoolTextBooksModal').modal({backdrop: "static"}) }, 10);
        },

        updateTextbook: function () {
            this.loading = true;

            axios.post(this.api_url+'/textbooks-update/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.text_books = response.data.text_books;
                    this.resetTextbook();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Textbook updated successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetTextbook: function () {
            this.loading = false;
            this.edit = false;

            $('#textBookGradeId').val('').change();
            $('#secondaryTextbookSubjectId').val('').change();
            $('#textBookId').val('').change();
            this.form_report.quantity = 0;
            this.form_report.section_id = this.sectionId;
            $('#schoolTextBooksModal').modal("hide");
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
