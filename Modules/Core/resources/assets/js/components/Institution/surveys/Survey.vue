<template>
    <div>
        <h3 class="text-center text-uppercase">
            EMIS BASELINE INFORMATION FORM
        </h3>

        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-aside-wrap">
                <div
                    :class="[
                        sideMenu ? 'content-active' : '',
                        'card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg',
                    ]"
                    data-content="userAside"
                    data-toggle-screen="lg"
                    data-toggle-overlay="true"
                >
                    <div class="card-inner-group">
                        <div class="card-inner p-0">
                            <ul class="link-list-menu">
                                <li
                                    :id="'menuSectionItem' + section.id"
                                    @click="toggleSection(section)"
                                    class="cursor"
                                    v-for="section in institutionSections"
                                >
                                    <a
                                        :class="
                                            current_section.logical ===
                                            section.logical
                                                ? 'bg-dark-teal text-white'
                                                : ''
                                        "
                                    >
                                        <em
                                            :class="[
                                                current_section.logical ===
                                                section.logical
                                                    ? 'text-white'
                                                    : '',
                                                'icon ni ni-grid-add-fill-c',
                                            ]"
                                        ></em>
                                        <span class="text-uppercase">{{
                                                getSectionName(section.name)
                                            }}</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <!-- .card-inner -->
                    </div>
                    <!-- .card-inner-group -->
                </div>
                <div class="progress-bar-container"  v-if="loadingSectionData">
                    <div class="progress-bar">
                        <div class="progress-bar-value"></div>
                    </div>
                </div>
                <div class="card-inner card-inner-lg" v-show="loadingSectionDataError">
                    <div class="card card-preview">
                        <div class="nk-block-head nk-block-head-lg">
                            <div class="nk-block-between">
                                <div class="nk-block-head-content">
                                    <h4 class="nk-block-title">
                                        Error Connecting
                                    </h4>
                                    <div class="nk-block-des">
                                        Check your internet connection or the server resources may be busy at this time. Try again!
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex flex-row mt-3 justify-content-end">
                                <a class="btn btn-primary ml-2 text-white" href="#" v-on:click="reloadSection">
                                    <span class="align-self-center">Retry</span>
                                    <em class="ni ni-redo ml-1"></em>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- card-aside -->
                <div class="card-inner card-inner-lg" v-if="!loadingSectionData && !loadingSectionDataError">
                    <!-- PRE-PRIMARY LEVEL STARTS HERE -->
                    <div v-if="school.school_type.name === 'preprimary'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            SCHOOL PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <pre-primary-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></pre-primary-section-a>
                            <pre-primary-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                            ></pre-primary-section-b>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            LEARNER INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your learners</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-pre-primary-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :districts-obj="districtsObj"
                            ></survey-pre-primary-section-c>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your teachers.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <pre-primary-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="teacherDesignations"
                                :employment-statuses-obj="employmentStatuses"
                            ></pre-primary-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            SUPPORT & ADMINISTRATIVE STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all your support and
                                                administrative staff here
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-pre-primary-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-teaching-staff-roles-obj="nonTeachingStaffRoles"
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-pre-primary-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div v-if="current_section.logical === 'infrastructure'" class="card card-preview">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title text-uppercase">
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em class="icon ni ni-menu-alt-r"></em>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div v-if="current_section.logical === 'other_facilities'" class="card card-preview">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->

                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical ===
                                'instructional_materials'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            LEARNING AND PLAYING MATERIALS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-pre-primary-learning-playing-materials
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :learning-and-playing-materials-obj="learningAndPlayingMaterialsObj"
                            ></survey-pre-primary-learning-playing-materials>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'health&_meals'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            HEALTH & MEALS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-pre-primary-health-meals-section
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :meal-types-obj="mealTypesObj"
                                :food-sources-obj="foodSourcesObj"
                                :health-services-learners-obj="healthServicesLearners"
                                :health-services-parents-obj="healthServicesParents"
                            ></survey-pre-primary-health-meals-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-pre-primary-section-sports-equipment
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :pre-primary-equipment-obj="prePrimaryEquipment"
                                school-sports-equipment-updates-obj="schoolSportsEquipmentUpdates"
                            ></survey-pre-primary-section-sports-equipment>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'governance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GOVERNANCE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-pre-primary-cmc-members
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :school-obj="school"
                                :assemblies-obj="assembliesObj"
                                :total-members-obj="totalMembersObj"
                            ></survey-pre-primary-cmc-members>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-pre-primary-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :school-obj="school"
                                :google-maps-api="googleMapsApi"
                            ></survey-pre-primary-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- PRE-PRIMARY LEVEL ENDS HERE -->

                    <!-- PRIMARY LEVEL STARTS HERE -->
                    <div v-if="school.school_type.name === 'primary'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            SCHOOL PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <primary-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></primary-section-a>
                            <primary-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                            ></primary-section-b>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            LEARNER INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your learners</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-primary-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :practical-skills-obj="practicalSkillsObj"
                                :districts-obj="districtsObj"
                            ></survey-primary-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your teachers.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-primary-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="teacherDesignations"
                                :employment-statuses-obj="employmentStatuses"
                            ></survey-primary-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non teaching
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-primary-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-teaching-staff-roles-obj="
                                    nonTeachingStaffRoles
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-primary-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical ===
                                'instructional_materials'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Instructional Materials
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-primary-section-instructional-materials
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :textbooks-obj="textbooks"
                                :primary-subjects-obj="primarySubjectsObj"
                                :reference-books-obj="referenceBooks"
                                :sne-kits-obj="sneKits"
                                :wall-charts-obj="wallCharts"
                            ></survey-primary-section-instructional-materials>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-primary-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-primary-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'health&_meals'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            HEALTH & MEALS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-primary-health-meals-section
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :meal-types-obj="mealTypesObj"
                                :food-sources-obj="foodSourcesObj"
                            ></survey-primary-health-meals-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#primarySportsEquipment"
                                    >SPORTS EQUIPMENT</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#primaryLearnerCocurricular"
                                    >PARTICIPATION IN EXTRA-CURRICULAR
                                        ACTIVITIES</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div
                                    class="tab-pane active"
                                    id="primarySportsEquipment"
                                >
                                    <survey-primary-section-sports
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :sports-equipment-categories-obj="
                                            sportsEquipmentCategories
                                        "
                                        :sports-equipment-obj="sportsEquipment"
                                        :sports-facilities-obj="
                                            sportsFacilities
                                        "
                                    ></survey-primary-section-sports>
                                </div>
                                <div
                                    class="tab-pane"
                                    id="primaryLearnerCocurricular"
                                >
                                    <survey-primary-extra-curricular-activity-participation
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :extra-curricular-activities-obj="
                                            extraCurricularActivitiesObj
                                        "
                                        :extra-curricular-activity-levels-obj="
                                            extraCurricularActivityLevelsObj
                                        "
                                    ></survey-primary-extra-curricular-activity-participation>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'extra_curricular_activities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Extra-curricular Activities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-section-practical-skills
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :practical-skills-obj="practicalSkillsObj"
                            ></survey-section-practical-skills>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'governance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GOVERNANCE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-primary-smc-members
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :school-obj="school"
                                :assemblies-obj="assembliesObj"
                                :total-members-obj="totalMembersObj"
                            ></survey-primary-smc-members>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-primary-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :google-maps-api="googleMapsApi"
                                :school-obj="school"
                            ></survey-primary-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- PRIMARY LEVEL ENDS HERE -->

                    <!-- SECONDARY LEVEL STARTS HERE -->
                    <div v-if="school.school_type.name === 'secondary'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            SCHOOL PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></survey-secondary-section-a>
                            <survey-secondary-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                            ></survey-secondary-section-b>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            LEARNER INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your learners</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :practical-skills-obj="practicalSkillsObj"
                                :districts-obj="districtsObj"
                                :subjects-obj="subjectsObj"
                            ></survey-secondary-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your teachers.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-secondary-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="teacherDesignations"
                                :employment-statuses-obj="employmentStatuses"
                                :subjects-obj="subjectsObj"
                            ></survey-secondary-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non teaching
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-secondary-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-teaching-staff-roles-obj="
                                    nonTeachingStaffRoles
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-secondary-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories-secondary
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :subjects-obj="subjectsObj"
                            ></survey-infrastructure-categories-secondary>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical ===
                                'instructional_materials'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Instructional Materials
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-section-instructional-materials
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :subjects-obj="subjectsObj"
                                :reference-books-obj="referenceBooks"
                                :sne-kits-obj="sneKits"
                                :lab-equipment-obj="labEquipment"
                                :lab-reagents-obj="labReagents"
                            ></survey-secondary-section-instructional-materials>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-secondary-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-secondary-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'health&_meals'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            HEALTH & MEALS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-health-meals-section
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :meal-types-obj="mealTypesObj"
                                :food-sources-obj="foodSourcesObj"
                            ></survey-secondary-health-meals-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#secondarySportsEquipment"
                                    >SPORTS EQUIPMENT</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#secondaryLearnerCocurricular"
                                    >PARTICIPATION IN EXTRA-CURRICULAR
                                        ACTIVITIES</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div
                                    class="tab-pane active"
                                    id="secondarySportsEquipment"
                                >
                                    <survey-primary-section-sports
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :sports-equipment-categories-obj="
                                            sportsEquipmentCategories
                                        "
                                        :sports-equipment-obj="sportsEquipment"
                                        :sports-facilities-obj="
                                            sportsFacilities
                                        "
                                    ></survey-primary-section-sports>
                                </div>
                                <div
                                    class="tab-pane"
                                    id="secondaryLearnerCocurricular"
                                >
                                    <survey-secondary-extra-curricular-activity-participation
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :extra-curricular-activities-obj="
                                            extraCurricularActivitiesObj
                                        "
                                        :extra-curricular-activity-levels-obj="
                                            extraCurricularActivityLevelsObj
                                        "
                                    ></survey-secondary-extra-curricular-activity-participation>
                                </div>
                            </div>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'extra_curricular_activities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Extra-curricular Activities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-section-practical-skills
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :practical-skills-obj="practicalSkillsObj"
                            ></survey-section-practical-skills>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'governance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GOVERNANCE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-bog-members
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :school-obj="school"
                                :assemblies-obj="assembliesObj"
                                :total-members-obj="totalMembersObj"
                            ></survey-secondary-bog-members>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-secondary-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :school-obj="school"
                                :google-maps-api="googleMapsApi"
                            ></survey-secondary-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- SECONDARY LEVEL ENDS HERE -->

                    <!-- CERTIFICATE LEVEL STARTS HERE -->
                    <div v-if="school.school_type.name === 'certificate'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            INSTITUTION IDENTIFICATION &
                                            PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-certificate-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></survey-certificate-section-a>
                            <div
                                v-if="school.school_type.name === 'certificate'"
                            >
                                <survey-certificate-certificate-section-b
                                    :section-id="current_section.id"
                                    class="mt-5"
                                    :school-obj="school"
                                    :survey-obj="survey"
                                    :legal-ownership-statuses-obj="
                                        legalOwnershipStatusesObj
                                    "
                                    :founding-bodies-obj="foundingBodiesObj"
                                    :funding-sources-obj="fundingSourcesObj"
                                    :school-authorities-obj="schoolAuthoritiesObj"
                                    :school-distances-obj="schoolDistancesObj"
                                    :deo-distances-obj="deoDistancesObj"
                                    :health-distances-obj="healthDistancesObj"
                                    :districts-obj="districtsObj"
                                ></survey-certificate-certificate-section-b>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'courses'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            COURSE INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of all courses offered
                                                at your institution
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-certificate-institution-courses
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :course-durations-obj="courseDurations"
                            ></survey-certificate-institution-courses>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            STUDENTS INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your students</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-certificate-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :course-durations-obj="courseDurations"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :districts-obj="districtsObj"
                            ></survey-certificate-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your academic
                                                staff.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-certificate-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="
                                    academicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                            ></survey-certificate-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non teaching
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-certificate-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-academic-staff-designations-obj="
                                    nonAcademicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-certificate-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-certificate-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-certificate-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#nonTertiarySportsEquipment"
                                    >SPORTS EQUIPMENT</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#nonTertiaryLearnerCocurricular"
                                    >PARTICIPATION IN EXTRA-CURRICULAR SPORTS
                                        ACTIVITIES</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div
                                    class="tab-pane active"
                                    id="nonTertiarySportsEquipment"
                                >
                                    <survey-certificate-section-sports
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :sports-equipment-categories-obj="
                                            sportsEquipmentCategories
                                        "
                                        :sports-equipment-obj="sportsEquipment"
                                        :sports-facilities-obj="
                                            sportsFacilities
                                        "
                                        :sports-activities-obj="
                                            sportsActivities
                                        "
                                    ></survey-certificate-section-sports>
                                </div>
                                <div
                                    class="tab-pane"
                                    id="nonTertiaryLearnerCocurricular"
                                >
                                    <survey-certificate-extra-curricular-activity-participation
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :extra-curricular-activities-obj="
                                            extraCurricularActivitiesObj
                                        "
                                        :extra-curricular-activity-levels-obj="
                                            extraCurricularActivityLevelsObj
                                        "
                                    ></survey-certificate-extra-curricular-activity-participation>
                                </div>
                            </div>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-certificate-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :google-maps-api="googleMapsApi"
                                :school-obj="school"
                            ></survey-certificate-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- CERTIFICATE LEVEL ENDS HERE -->

                    <!-- DIPLOMA LEVELS STARTS HERE -->
                    <div v-if="school.school_type.name === 'diploma'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            INSTITUTION IDENTIFICATION &
                                            PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-diploma-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></survey-diploma-section-a>
                            <survey-diploma-diploma-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :funding-sources-obj="fundingSourcesObj"
                                :school-authorities-obj="schoolAuthoritiesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                                :districts-obj="districtsObj"
                            ></survey-diploma-diploma-section-b>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'courses'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            COURSE INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of all courses offered
                                                at your institution
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-diploma-institution-courses
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :course-durations-obj="courseDurations"
                            ></survey-diploma-institution-courses>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            STUDENTS INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your students</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-diploma-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :course-durations-obj="courseDurations"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :districts-obj="districtsObj"
                            ></survey-diploma-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your academic
                                                staff.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-diploma-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="
                                    academicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                            ></survey-diploma-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non teaching
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-diploma-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-academic-staff-designations-obj="
                                    nonAcademicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-diploma-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-diploma-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-diploma-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#diplomaSportsEquipment"
                                    >SPORTS EQUIPMENT</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#diplomaLearnerCocurricular"
                                    >PARTICIPATION IN EXTRA-CURRICULAR
                                        ACTIVITIES</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div
                                    class="tab-pane active"
                                    id="diplomaSportsEquipment"
                                >
                                    <survey-diploma-section-sports
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :sports-equipment-categories-obj="
                                            sportsEquipmentCategories
                                        "
                                        :sports-equipment-obj="sportsEquipment"
                                        :sports-facilities-obj="
                                            sportsFacilities
                                        "
                                        :sports-activities-obj="
                                            sportsActivities
                                        "
                                    ></survey-diploma-section-sports>
                                </div>
                                <div
                                    class="tab-pane"
                                    id="diplomaLearnerCocurricular"
                                >
                                    <survey-diploma-extra-curricular-activity-participation
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :extra-curricular-activities-obj="
                                            extraCurricularActivitiesObj
                                        "
                                        :extra-curricular-activity-levels-obj="
                                            extraCurricularActivityLevelsObj
                                        "
                                    ></survey-diploma-extra-curricular-activity-participation>
                                </div>
                            </div>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-diploma-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :google-maps-api="googleMapsApi"
                                :school-obj="school"
                            ></survey-diploma-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- DIPLOMA LEVELS ENDS HERE -->

                    <!-- DEGREE LEVEL STARTS HERE -->
                    <div v-if="school.school_type.name === 'degree'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            INSTITUTION IDENTIFICATION &
                                            PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-degree-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></survey-degree-section-a>
                            <survey-degree-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :funding-sources-obj="fundingSourcesObj"
                                :school-authorities-obj="schoolAuthoritiesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                                :districts-obj="districtsObj"
                            ></survey-degree-section-b>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'courses'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            COURSE INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of all courses offered
                                                at your institution
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-degree-institution-courses
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :courses-obj="courses"
                            ></survey-degree-institution-courses>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            STUDENTS INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your students</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-degree-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :entry-modes-obj="entryModes"
                                :learner-sponsors-obj="learnerSponsors"
                                :course-list-obj="course_list"
                                :education-grades-obj="educationGrades"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :districts-obj="districtsObj"
                            ></survey-degree-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your academic
                                                staff.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-degree-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="teacherDesignations"
                                :employment-statuses-obj="employmentStatuses"
                            ></survey-degree-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON ACADEMIC STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non academic
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-degree-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-teaching-staff-obj="nonTeachingStaff"
                                :non-academic-staff-designations-obj="
                                    nonAcademicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-degree-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'library_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            LIBRARY FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-library-facilities-section
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                            ></survey-library-facilities-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-degree-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-degree-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#tertiarySportsEquipment"
                                    >SPORTS EQUIPMENT</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#tertiaryLearnerCocurricular"
                                    >PARTICIPATION IN EXTRA-CURRICULAR
                                        ACTIVITIES</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div
                                    class="tab-pane active"
                                    id="tertiarySportsEquipment"
                                >
                                    <survey-degree-section-sports
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :sports-equipment-categories-obj="
                                            sportsEquipmentCategories
                                        "
                                        :sports-equipment-obj="sportsEquipment"
                                        :sports-facilities-obj="
                                            sportsFacilities
                                        "
                                        :sports-activities-obj="
                                            sportsActivities
                                        "
                                    ></survey-degree-section-sports>
                                </div>
                                <div
                                    class="tab-pane"
                                    id="tertiaryLearnerCocurricular"
                                >
                                    <survey-degree-extra-curricular-activity-participation
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :extra-curricular-activities-obj="
                                            extraCurricularActivitiesObj
                                        "
                                        :extra-curricular-activity-levels-obj="
                                            extraCurricularActivityLevelsObj
                                        "
                                    ></survey-degree-extra-curricular-activity-participation>
                                </div>
                            </div>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-degree-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :google-maps-api="googleMapsApi"
                                :school-obj="school"
                            ></survey-degree-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'finance'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Finance
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a
                                        class="nav-link active"
                                        data-toggle="tab"
                                        href="#income"
                                    >Income</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#budgets"
                                    >Budgets</a
                                    >
                                </li>
                                <li class="nav-item">
                                    <a
                                        class="nav-link"
                                        data-toggle="tab"
                                        href="#expenses"
                                    >Expenses</a
                                    >
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="income">
                                    <survey-finance-income-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :incomes-obj="incomes"
                                        :total-incomes-obj="totalIncomes"
                                        :income-sources-obj="incomeSources"
                                    ></survey-finance-income-section>
                                </div>
                                <div class="tab-pane" id="budgets">
                                    <survey-finance-budget-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :budgets-obj="budgets"
                                        :total-budgets-obj="totalBudgets"
                                        :budget-items-obj="budgetItems"
                                    ></survey-finance-budget-section>
                                </div>
                                <div class="tab-pane" id="expenses">
                                    <survey-finance-expenses-section
                                        :section-id="current_section.id"
                                        :school-obj="school"
                                        :survey-obj="survey"
                                        :expenses-obj="expenses"
                                        :total-expenses-obj="totalExpenses"
                                        :generic-expense-items-obj="
                                            genericExpenseItems
                                        "
                                    ></survey-finance-expenses-section>
                                </div>
                            </div>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- DEGREE LEVEL ENDS HERE -->

                    <!-- INTERNATIONAL SCHOOL STARTS HERE -->
                    <div v-if="school.school_type.name === 'international'">
                        <div
                            v-if="current_section.logical === 'school'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            SCHOOL IDENTIFICATION & PARTICULARS
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-international-section-a
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :districts-obj="districtsObj"
                            ></survey-international-section-a>
                            <survey-international-section-b
                                :section-id="current_section.id"
                                class="mt-5"
                                :school-obj="school"
                                :survey-obj="survey"
                                :legal-ownership-statuses-obj="
                                    legalOwnershipStatusesObj
                                "
                                :founding-bodies-obj="foundingBodiesObj"
                                :funding-sources-obj="fundingSourcesObj"
                                :school-distances-obj="schoolDistancesObj"
                                :deo-distances-obj="deoDistancesObj"
                                :health-distances-obj="healthDistancesObj"
                                :districts-obj="districtsObj"
                            ></survey-international-section-b>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'curriculum'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            CURRICULUM INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of all curriculums in
                                                your school
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-international-curriculums
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                            ></survey-international-curriculums>
                            <survey-international-calendars
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                            ></survey-international-calendars>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'learners'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            LEARNERS INFORMATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>A summary of your learners</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-international-section-c
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-curriculums-obj="schoolCurriculumsObj"
                                :familiar-languages-obj="familiarLanguages"
                                :learner-enrolments-obj="learnerEnrolments"
                                :disability-types-obj="disabilityTypes"
                                :countries-obj="countries"
                                :talents-obj="talentsObj"
                                :health-issues-obj="healthIssues"
                                :practical-skills-obj="practicalSkillsObj"
                                :districts-obj="districtsObj"
                            ></survey-international-section-c>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'teaching_staff'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            TEACHING STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your academic
                                                staff.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-international-section-d
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :teacher-qualifications-obj="
                                    teacherQualifications
                                "
                                :teacher-types-obj="teacherTypes"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                                :teacher-designations-obj="teacherDesignations"
                                :employment-statuses-obj="employmentStatuses"
                            ></survey-international-section-d>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'non_teaching_staff'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            NON ACADEMIC STAFF
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                A summary of your non academic
                                                staff
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-international-non-teaching-staff
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :countries-obj="countries"
                                :non-teaching-staff-obj="nonTeachingStaff"
                                :non-academic-staff-designations-obj="
                                    nonAcademicStaffDesignations
                                "
                                :employment-statuses-obj="employmentStatuses"
                                :education-levels-obj="educationLevels"
                                :marital-statuses-obj="maritalStatuses"
                            ></survey-international-non-teaching-staff>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'infrastructure'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            INFRASTRUCTURE
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-infrastructure-categories
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :infrastructure-types-obj="infrastructureTypes"
                                :preprimary="preprimaryObj"
                                :international-education-levels-obj="internationalEducationLevelsObj"
                            ></survey-infrastructure-categories>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->

                        <div
                            v-if="
                                current_section.logical === 'other_facilities'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            Other Facilities
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-other-facilities
                                :section-id="current_section.id"
                                :school-obj="school"
                                :school-type-id-obj="schoolTypeId"
                                :survey-obj="survey"
                                :school-other-facilities-obj="
                                    schoolOtherFacilities
                                "
                                :other-facility-types-obj="otherFacilityTypes"
                            ></survey-facilities-section-other-facilities>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'water&_sanitation'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">
                                            WATER AND SANITATION
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-water
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-water-sources-obj="water_sources"
                                :water-source-types-obj="waterSourceTypes"
                                :water-source-distance-ranges-obj="
                                    waterSourceDistanceRanges
                                "
                            ></survey-facilities-section-water>

                            <survey-facilities-section-sanitation
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :hand-washing-facility-types-obj="
                                    handWashingFacilityTypes
                                "
                                :hand-washing-methods-obj="handWashingMethods"
                            ></survey-facilities-section-sanitation>

                            <survey-facilities-section-garbage-disposal-methods
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-garbage-disposal-methods-obj="
                                    schoolGarbageDisposalMethods
                                "
                                :garbage-disposal-methods-obj="
                                    garbageDisposalMethods
                                "
                            ></survey-facilities-section-garbage-disposal-methods>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="
                                current_section.logical === 'sources_of_energy'
                            "
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            sources of energy
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update the details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >.
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-facilities-section-energy
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :school-energy-sources-obj="schoolEnergySources"
                                :energy-types-obj="energyTypes"
                            ></survey-facilities-section-energy>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'i_c_t'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            ICT FACILITIES
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-international-section-ict
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :all-internet-sources-obj="allInternetSources"
                            ></survey-international-section-ict>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'p._e.&_sports'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            P.E. & Sports
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>Update all the details below.</p>
                                        </div>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->

                            <survey-international-section-sports
                                :section-id="current_section.id"
                                :school-obj="school"
                                :survey-obj="survey"
                                :sports-equipment-categories-obj="
                                    sportsEquipmentCategories
                                "
                                :sports-equipment-obj="sportsEquipment"
                                :sports-facilities-obj="sportsFacilities"
                            ></survey-international-section-sports>

                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                        <div
                            v-if="current_section.logical === 'gps_location'"
                            class="card card-preview"
                        >
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4
                                            class="nk-block-title text-uppercase"
                                        >
                                            GPS Location
                                        </h4>
                                        <div class="nk-block-des">
                                            <p>
                                                Update all details that are
                                                marked
                                                <span
                                                    class="text-muted text-uppercase font-italic"
                                                >NOT SET</span
                                                >
                                            </p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content">
                                        <button
                                            data-toggle="modal"
                                            data-target="#updateLocationDetailsModal"
                                            data-backdrop="static"
                                            type="button"
                                            class="btn btn-sm mt-4 bg-dark-teal"
                                        >
                                            <em
                                                class="icon ni ni-edit-fill text-white mr-1"
                                            ></em
                                            >Update
                                        </button>
                                    </div>
                                    <div
                                        class="nk-block-head-content align-self-start d-lg-none"
                                    >
                                        <a
                                            @click="toggleSideMenu()"
                                            class="toggle btn btn-icon btn-trigger mt-n1 cursor"
                                            data-target="userAside"
                                        ><em
                                            class="icon ni ni-menu-alt-r"
                                        ></em
                                        ></a>
                                    </div>
                                </div>
                            </div>
                            <!-- .nk-block-head -->
                            <survey-international-gps-location-section
                                :section-id="current_section.id"
                                :survey-obj="survey"
                                :google-maps-api="googleMapsApi"
                                :school-obj="school"
                            ></survey-international-gps-location-section>
                            <survey-footer
                                ref="footer"
                                :survey-obj="survey"
                                :section-id="current_section.id"
                            ></survey-footer>
                        </div>
                        <!-- .card-preview -->
                    </div>
                    <!-- INTERNATIONAL SCHOOL ENDS HERE -->
                </div>
            </div>
            <!-- .card-inner -->
        </div>
        <!-- .card-aside-wrap -->
    </div>
</template>

<script>
import PrePrimarySectionA from "./pre-primary/SectionA.vue";
import PrePrimarySectionB from "./pre-primary/SectionB.vue";
import PrePrimarySectionD from "./pre-primary/SectionD.vue";
import PrimarySectionA from "./primary/SectionA.vue";
import PrimarySectionB from "./primary/SectionB.vue";
import SurveyFooter from "./SurveyFooter.vue";
export default {
    name: "Survey",
    props: [
        "schoolObj",
        "surveyObj",
        "districtsObj",
        "countriesObj",
        "legalOwnershipStatusesObj",
        "foundingBodiesObj",
        "fundingSourcesObj",
        "schoolAuthoritiesObj",
        "schoolDistancesObj",
        "healthDistancesObj",
        "deoDistancesObj",
        "extraCurricularActivitiesObj",
        "extraCurricularActivityLevelsObj",
        "talentsObj",
        "practicalSkillsObj",
        "assembliesObj",
        "totalMembersObj",
        "subjectsObj",
        "primarySubjectsObj",
        "learningAndPlayingMaterialsObj",
        "internationalEducationLevelsObj",
        "preprimaryObj",
        "schoolCurriculumsObj",
        "mealTypesObj",
        "foodSourcesObj",
        "googleMapsApi",
    ],
    components: {
        PrePrimarySectionA,
        PrePrimarySectionB,
        PrePrimarySectionD,
        PrimarySectionA,
        PrimarySectionB,
        SurveyFooter,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            api_url: "/institutions/surveys/school",
            loadingSectionData:false,
            loadingSectionDataError:false,
            sectionMap:new Map(),
            sideMenu: false,
            map: null,
            marker: null,
            selected_section: "",
            survey: {
                current_section_id: "",
                sections: [{ name: "", is_complete_yn: false }],
                section_items: [
                    { name: "", section_id: "", is_complete_yn: false },
                ],
                survey: { name: "" },
            },
            school: {
                enrolments: 0,
                school_type: { name: "" },
                region: { name: "" },
                district: { name: "" },
                county: { name: "" },
                sub_county: { name: "" },
                parish: { name: "" },
                legal_ownership_status: { name: "" },
                founding_body: { name: "" },
                registration_status: { name: "" },
                health_facility_distance: { name: "" },
                pre_primary_school: {
                    attached_primary_school: { name: "", phone: "" },
                    school_distance_range: { name: "" },
                    deo_office_distance: { name: "" },
                    has_daycare_center: false,
                    has_nursery_section: false,
                },
            },
            current_section: { name: "", logical: "" },
            settings: [],
            levels: [],
            course_list: [],
            water_sources: {
                id: "",
                section_id: "",
                drinking_water_source_id: "",
                water_source_other_purposes_id: "",
                drinking_water_source_distance_id: "",
                water_source_other_purposes_distance_id: "",
            },
            educationGrades: [],
            learnerEnrolments: [],
            disabilityTypes: [],
            familiarLanguages: [],
            teacherQualifications: [],

            nonTeachingStaffRoles: [],
            countries: [],
            infrastructureTypes: [],
            otherFacilityTypes: [],
            schoolOtherFacilities: [],
            schoolWaterSources: [],
            waterSourceDistanceRanges: [],
            waterSourceTypes: [],
            schoolGarbageDisposalMethods: [],
            garbageDisposalMethods: [],

            handWashingFacilityTypes: [],
            handWashingMethods: [],

            schoolEnergySources: [],
            energyTypes: [],

            allInternetSources: [],

            courses: [],
            courseList: [],
            learnerSponsors: [],
            entryModes: [],
            subjects: [],
            courseDurations: [],
            healthIssues: [],

            prePrimaryEquipment: [],
            sportsEquipmentCategories: [],
            sportsEquipment: [],
            sportsFacilities: [],
            sportsActivities: [],
            schoolSportsEquipmentUpdates: [],

            textbooks: [],
            primarySubjects: [],
            referenceBooks: [],
            sneKits: [],
            wallCharts: [],
            labEquipment: [],
            labReagents: [],

            mealTypes: [],
            foodSources: [],
            healthServicesLearners: [],
            healthServicesParents: [],

            teacherTypes: [],
            educationLevels: [],
            maritalStatuses: [],
            teacherDesignations: [],
            academicStaffDesignations: [],
            employmentStatuses: [],
            nonTeachingStaff: [],
            nonAcademicStaffDesignations: [],
            //googleMapsApi: [],

            incomes: [],
            totalIncomes: [],
            incomeSources: [],
            budgets: [],
            totalBudgets: [],
            budgetItems: [],
            expenses: [],
            totalExpenses: [],
            genericExpenseItems: [],
            preprimary: [],

            schoolTypeId: -1,
        };
    },
    methods: {
        initPlugins: function () {
            // console.log(this.survey)
            this.survey = this.surveyObj;
            // console.log("initPlugins");
            this.school = this.schoolObj;
            this.schoolTypeId = this.schoolObj.school_type_id;
            this.levels = this.extraCurricularActivityLevelsObj;
            this.course_list = this.courseList;
            this.current_section = this.survey.sections[0];
            this.countries = this.countriesObj;
        },
        reloadSection: function(){
            this.toggleSection(this.current_section);
        },
        getSectionName: function (section) {
            let name = section;
            if (
                name.includes("Learners") &&
                this.school.school_type_id >= 4 &&
                this.school.school_type_id !== 7
            ) {
                name = name.replace("Learners", "Students");
            }

            // if (name === "Teaching Staff" && this.school.school_type_id === 1) {
            //     name = name.replace("Teaching Staff", "Caregivers");
            // }

            if (
                name === "Non Teaching Staff" &&
                this.school.school_type_id === 1
            ) {
                name = name.replace("Non Teaching Staff", "Support Staff");
            }

            return name;
        },
        toggleSection: function (section) {
            this.current_section = section;
            this.loadingSectionDataError = false;
            this.toggleSideMenu();
            let section_name = section.logical.replaceAll(/[^a-z_]/g, "");
            if(['school','curriculums','curriculum'].indexOf(section_name)>=0 || this.sectionMap.get(section_name)){
                return;
            }
            this.loadingSectionData = true;
            axios
                .get(this.api_url + "/" + section_name + "/" + this.survey.id)
                .then((response) => {
                    this.sectionMap.set(section_name, true);

                    switch (section_name) {
                        case "learners":
                            this.educationGrades = response.data.educationGrades;
                            this.learnerEnrolments = response.data.learners;
                            this.disabilityTypes = response.data.disabilityTypes;
                            this.familiarLanguages = response.data.familiarLanguages;
                            this.healthIssues = response.data.healthIssues;
                            this.foodSources = response.data.foodSources;
                            this.mealTypes = response.data.mealTypes;
                            this.healthServicesLearners = response.data.healthServicesLearners;
                            this.healthServicesParents = response.data.healthServicesParents;
                            break;
                        case "teaching_staff":
                            this.teacherQualifications =
                                response.data.teacherQualifications;
                            this.teacherTypes = response.data.teacherTypes;
                            this.educationLevels = response.data.educationLevels;
                            this.maritalStatuses = response.data.maritalStatuses;
                            this.teacherDesignations = response.data.teacherDesignations;
                            this.employmentStatuses = response.data.employmentStatuses;
                            this.academicStaffDesignations = response.data.academicStaffDesignations;
                            break;
                        case "non_teaching_staff":
                            this.nonTeachingStaffRoles = response.data.nonTeachingStaffRoles;
                            this.nonAcademicStaffDesignations = response.data.nonAcademicStaffDesignations;
                            this.maritalStatuses = response.data.maritalStatuses;
                            this.teacherDesignations = response.data.teacherDesignations;
                            this.employmentStatuses = response.data.employmentStatuses;
                            this.educationLevels = response.data.educationLevels;
                            break;
                        case "infrastructure":
                            this.infrastructureTypes = response.data.infrastructureTypes;
                            break;
                        case "institutional_facilities":
                        case "other_facilities":
                            this.otherFacilityTypes = response.data.otherFacilityTypes;
                            this.schoolOtherFacilities = response.data.schoolOtherFacilities;
                            break;
                        case "water_sanitation":
                            this.schoolWaterSources = response.data.schoolWaterSources;
                            this.waterSourceTypes = response.data.waterSourceTypes;
                            this.waterSourceDistanceRanges = response.data.waterSourceDistanceRanges;
                            this.schoolGarbageDisposalMethods = response.data.schoolGarbageDisposalMethods;
                            this.garbageDisposalMethods = response.data.garbageDisposalMethods;
                            this.handWashingFacilityTypes = response.data.handWashingFacilityTypes;
                            this.handWashingMethods = response.data.handWashingMethods;
                            break;
                        case "sources_of_energy":
                            this.schoolEnergySources = response.data.schoolEnergySources;
                            this.energyTypes = response.data.energyTypes;
                            break;
                        case "i_c_t":
                            this.allInternetSources = response.data.allInternetSources;
                            break;
                        case "instructional_materials":
                            // this.subjects = response.data.subjects;
                            // this.primarySubjects = response.data.primarySubjects;
                            this.textbooks = response.data.textbooks;
                            this.referenceBooks = response.data.referenceBooks;
                            this.educationGrades = response.data.educationGrades;
                            this.sneKits = response.data.sneKits;
                            this.wallCharts = response.data.wallCharts;
                            this.labEquipment = response.data.labEquipment;
                            this.labReagents = response.data.labReagents;
                            break;
                        // case "health&_meals":
                        //     this.mealTypes = response.data.mealTypes;
                        //     this.foodSources = response.data.foodSources;
                        //     break;
                        case "finance":
                            this.genericExpenseItems = response.data.genericExpenseItems;
                            this.budgets = response.data.budgets;
                            this.totalBudgets = response.data.totalBudgets;
                            this.incomeSources = response.data.incomeSources;
                            this.incomes = response.data.incomes;
                            this.totalIncomes = response.data.totalIncomes;
                            this.budgetItems = response.data.budgetItems;
                            this.expenses = response.data.expenses;
                            this.totalExpenses = response.data.totalExpenses;
                            break;
                        case "extra_curricular_activities":
                            //this.talentsObj = response.data.talentsObj;
                            // this.practicalSkillsObj = response.data.practicalSkillsObj;
                            break;
                        case "p_e_sports":
                            this.prePrimaryEquipment = response.data.prePrimaryEquipment;
                            this.schoolSportsEquipmentUpdates = response.data.schoolSportsEquipmentUpdates;
                            this.sportsActivities = response.data.sportsActivities;
                            this.sportsFacilities = response.data.sportsFacilities;
                            this.sportsEquipment = response.data.sportsEquipment;
                            this.sportsEquipmentCategories = response.data.sportsEquipmentCategories;
                            break;
                        case "courses":
                            // this.entryModes = response.data.entryModes;
                            // this.learnerSponsors = response.data.learnerSponsors;
                            // this.courseList = response.data.courseList;
                            break;
                    }
                })
                .catch(()=>{
                    this.loadingSectionDataError = true;
                })
                .finally(()=>{
                    this.loadingSectionData = false;
                })
        },
        toggleSideMenu: function () {
            this.sideMenu = !this.sideMenu;
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        institutionSections: function () {
            let unchanged = this.survey.sections;
            let sections = [];
            if (this.survey.school_type_id === 7) {
                sections.push(unchanged[0]);
                unchanged.shift();
                sections.push(unchanged[10]);
                unchanged.splice(10, 1);
                unchanged.forEach((section) => {
                    if (section.logical !== "courses") {
                        sections.push(section);
                    }
                });
                return sections;
            } else {
                this.survey.sections.forEach((section) => {
                    if (this.survey.school_type_id < 4) {
                        if (section.logical !== "courses") {
                            sections.push(section);
                        }
                    } else {
                        sections.push(section);
                    }
                });
                return sections;
            }
        },
    },
};
</script>

<style scoped>

.progress-bar-container {
    width: 100%;
    margin: auto;
    position: absolute;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0);
    top: 0;
    z-index: 5000;
    cursor: progress;
}

.progress-bar {
    height: 4px;
    background-color: rgba(0, 135, 155, 0.2);
    width: 100%;
    overflow: hidden;
}

.progress-bar-value {
    width: 100%;
    height: 100%;
    background-color: rgb(0, 135, 155);
    animation: indeterminateAnimation 1s infinite linear;
    transform-origin: 0 50%;
}

@keyframes indeterminateAnimation {
    0% {
        transform:  translateX(0) scaleX(0);
    }
    40% {
        transform:  translateX(0) scaleX(0.4);
    }
    100% {
        transform:  translateX(100%) scaleX(0.5);
    }
}
</style>
