<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>

        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Latitude</span>
                    <span v-if="school.latitude === null" class="data-value text-muted font-italic">Not Set</span>
                    <span v-else class="data-value text-dark">{{ school.latitude }}</span>
                </div>
                <div class="data-col data-col-end"></div>
            </div><!-- data-item -->
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Longitude</span>
                    <span v-if="school.longitude === null" class="data-value text-muted font-italic">Not Set</span>
                    <span v-else class="data-value text-dark">{{ school.longitude }}</span>
                </div>
                <div class="data-col data-col-end"></div>
            </div><!-- data-item -->
        </div>
        <div v-if="googleMapsApi.length" class="card-inner">
            <div id="map"></div>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="updateLocationDetailsModal">
            <form @submit.prevent="updateLocationDetails()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetLocationDetails()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Institution GPS Location Details</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLatitude">Latitude</label>
                                        <span class="form-note">Specify the latitude of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input required placeholder="latitude" v-model.number="form_location.latitude" id="schoolLatitude" type="text" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLongitude">Longitude</label>
                                        <span class="form-note">Specify the longitude of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input required placeholder="longitude" v-model.number="form_location.longitude" id="schoolLongitude" type="text" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetLocationDetails()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Location Modal -->
    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";
import { Loader } from "@googlemaps/js-api-loader";

export default {
    name: "LocationSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'googleMapsApi',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/degree-schools/',
            map: null,
            marker: null,
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            form_location: {
                section_id: '',
                survey_id: '',
                longitude: null,
                latitude: null,
            },
            school: {
                longitude: '',
                latitude: '',
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;

            this.form_location.section_id = this.sectionId;
            this.form_location.survey_id = this.survey.id;

            this.resetLocationDetails();
        },

        updateLocationDetails: function () {
            this.loading = true;
            axios.post(this.api_url+'update-location',
                {id: this.school.id, longitude: this.form_location.longitude, latitude: this.form_location.latitude})
                .then(response=>{
                    this.school.longitude = response.data.longitude;
                    this.school.latitude = response.data.latitude;
                    $('#updateLocationDetailsModal').modal('hide');
                    this.resetLocationDetails();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success', message:"School location details updated successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetLocationDetails: function () {
            this.loading = false;
            this.form_location = {
                latitude: parseFloat(this.school.latitude === null ? 0.313887 : this.school.latitude),
                longitude: parseFloat(this.school.longitude === null ? 32.585409 : this.school.longitude),
            };

            if (this.googleMapsApi.length) {
                let pin = { lat: this.form_location.latitude, lng: this.form_location.longitude };
                let loader = new Loader({
                    apiKey: this.googleMapsApi,
                    version: "weekly",
                });

                if (this.map === null) {
                    loader.load().then(() => {
                        this.map = new google.maps.Map(document.getElementById("map"), {
                            center: pin,
                            zoom: 18,
                            mapTypeControl: false,
                            streetViewControl: false,
                        });

                        this.marker = new google.maps.Marker({
                            position: pin,
                            map: this.map,
                        });

                        this.map.addListener("center_changed", () => {
                            this.marker.setPosition(this.map.getCenter());
                        });

                        this.map.addListener("dragend", () => {
                            this.form_location.latitude = this.map.getCenter().lat();
                            this.form_location.longitude = this.map.getCenter().lng();
                            this.updateLocationDetails();
                        });

                        let locationButton = document.createElement("button");
                        locationButton.className = "btn btn-round btn-icon bg-dark-teal mt-2 ml-2";
                        locationButton.innerHTML = '<em class="icon ni ni-target"></em>';
                        this.map.controls[google.maps.ControlPosition.TOP_LEFT].push(locationButton);
                        locationButton.addEventListener("click", () => {
                            if (navigator.geolocation) {
                                navigator.geolocation.getCurrentPosition(
                                    (position) => {
                                        let pos = {
                                            lat: position.coords.latitude,
                                            lng: position.coords.longitude,
                                        };
                                        this.map.setCenter(pos);
                                        this.marker.setPosition(pos);
                                        this.form_location.latitude = position.coords.latitude;
                                        this.form_location.longitude = position.coords.longitude;
                                        this.updateLocationDetails();
                                    }, () => {
                                        this.$refs.notifyError.messages.push({status: 'error', title: 'Error', message:"The Geolocation service failed."});
                                    });
                            } else {
                                this.$refs.notifyError.messages.push({status: 'error', title: 'Error', message:"Your browser doesn't support geolocation."});
                            }
                        });
                    });
                } else {
                    this.map.setCenter(pin);
                    this.marker.setPosition(pin);
                }
            }
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>
#map {
    height: 400px;
}
</style>
