<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <table class="table table-bordered table-hover table-sm">
                <thead>
                    <tr class="bg-secondary">
                        <td colspan="3" class="text-uppercase text-white">SECTION F: SOURCE OF ENERGY</td>
                    </tr>
                    <tr class="bg-secondary-dim">
                        <th class="text-uppercase border-secondary text-dark">USAGE</th>
                        <th class="text-uppercase border-secondary text-dark">ENERGY SOURCE TYPE</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-uppercase border-secondary text-dark">MAIN SOURCE OF ENERGY FOR COOKING</td>
                        <td class="text-uppercase border-secondary text-dark">
                            <span v-if="school.energy_sources !== null" class="">{{ school.energy_sources.cooking.name }}</span>
                            <span v-else class="font-italic text-muted">Not set</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-uppercase border-secondary text-dark">MAIN SOURCE OF ENERGY FOR LIGHTING</td>
                        <td class="text-uppercase border-secondary text-dark">
                            <span v-if="school.energy_sources !== null" class="">{{ school.energy_sources.lighting.name }}</span>
                            <span v-else class="font-italic text-muted">Not set</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button @click="editEnergySources()" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE ENERGY SOURCES INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="schoolEnergySourcesModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetEnergySources()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateEnergySources()">
                        <div class="modal-header">
                            <h6 class="modal-title">UPDATE ENERGY SOURCES</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="cookingEnergySourceId" class="form-label">Cooking Energy Source<span class="text-danger">*</span></label>
                                        <select required id="cookingEnergySourceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="energy_type in cookingSources" :value="energy_type.id">{{ energy_type.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="lightingEnergySourceId" class="form-label">Lighting Energy Source<span class="text-danger">*</span></label>
                                        <select required id="lightingEnergySourceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="energy_type in lightingSources" :value="energy_type.id">{{ energy_type.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->

                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetEnergySources()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";

export default {
    name: "EnergySection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'schoolEnergySourcesObj',
        'energyTypesObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/sources-of-energy',
            school: {
                energy_sources: {
                    cooking: {
                        name: ''
                    },
                    lighting: {
                        name: ''
                    }
                },
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            energy_types: [],
            form_energy_sources: {
                id: '',
                section_id: '',
                survey_id: '',
                lighting_source_id: '',
                cooking_source_id: '',
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.energy_types = this.energyTypesObj;
            this.form_energy_sources.section_id = this.sectionId;
            this.form_energy_sources.survey_id = this.survey.id;

            $('#cookingEnergySourceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolEnergySourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_energy_sources.cooking_source_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#lightingEnergySourceId').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#schoolEnergySourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_energy_sources.lighting_source_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
        },

        editEnergySources: function () {
            this.resetEnergySources();

            $('#schoolEnergySourcesModal').modal({backdrop: "static"});
        },
        updateEnergySources: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.survey.id, this.form_energy_sources)
                .then(response=>{
                    this.school.energy_sources = response.data.energy_sources;
                    this.resetEnergySources();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Energy Sources Updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetEnergySources: function () {
            $('#schoolEnergySourcesModal').modal('hide');
            this.loading = false;

            window.setTimeout(()=>{
                if (this.school.energy_sources === null) {
                    $('#cookingEnergySourceId').val('').change();
                    $('#lightingEnergySourceId').val('').change();
                } else {
                    $('#cookingEnergySourceId').val(this.school.energy_sources.cooking_source_id).change();
                    $('#lightingEnergySourceId').val(this.school.energy_sources.lighting_source_id).change();
                }

            }, 50);

            this.form_energy_sources.section_id = this.sectionId;
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        lightingSources: function () {
            return this.energy_types.filter(energy_type => {
                return energy_type.is_lighting_yn
            })
        },

        cookingSources: function () {
            return this.energy_types.filter(energy_type => {
                return energy_type.is_cooking_yn
            })
        },
    }
}
</script>

<style scoped>

</style>
