<template>
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">LAB EQUIPMENT</h5>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolLabEquipmentModal"  class="cursor btn bg-dark-teal btn-md d-sm-none"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Lab Equipment</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolLabEquipmentModal"  class="cursor btn bg-dark-teal btn-md"><em class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Lab Equipment</span></button>
                            </div>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Item</span></div>
                        <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span></div>
<!--                        <div class="nk-tb-col text-center"><span class="text-white ucap">Action</span></div>-->
                    </div><!-- .nk-tb-item -->
                    <div v-for="item in school.lab_equipment" class="nk-tb-item">
                        <div class="nk-tb-col">
                            <span class="text-secondary text-center ucap">{{ item.equipment.name }}</span>
                        </div>
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ item.quantity }}</span>
                        </div>

<!--                        <div @click="editLabEquipment(item)" class="nk-tb-col text-center">-->
<!--                            <span data-toggle="tooltip" data-placement="top"  title="Edit Sne Kit" class="cursor lead text-dark-teal mr-1">-->
<!--                                <em class="icon ni ni-edit-fill"></em>-->
<!--                            </span>-->
<!--                        </div>-->
                    </div>
                </div>
                <div v-if="!school.lab_equipment.length"  class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There is no Lab Equipment information to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolLabEquipmentModal">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetLabEquipment()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateLabEquipment() : createLabEquipment()">
                        <div class="modal-header">
                            <h5 class="modal-title ucap">{{ edit ? 'EDIT' : 'UPDATE' }} Lab Equipment</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="labEquipmentId">Select Lab Equipment<span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select v-model="form_report.lab_equipment_id" id="labEquipmentId" data-placeholder="Select Lab Equipment" class="form-select-sm">
                                                <option value="">--Select SNE Kit--</option>
                                                <option v-for="item in lab_equipment" :value="item.id">{{ item.name.toUpperCase() }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="quantity" class="form-label">Number <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input v-model.number="form_report.quantity" type="number" id="quantity" class="form-control" required autocomplete="off" placeholder="Enter Number">
                                        </div>
                                    </div>
                                </div>

                            </div><!-- .form-inline -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "LabEquipment",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'labEquipmentObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            edit: false,
            api_url: '/institutions/surveys/instructional-materials',
            school: {
                lab_equipment: []
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            lab_equipment: [],
            form_report: {
                id: '',
                section_id: '',
                lab_equipment_id: '',
                quantity: 0,
            }

        }
    },
    methods: {

        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;
            this.lab_equipment = this.labEquipmentObj;

            $('#labEquipmentId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolLabEquipmentModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_report.lab_equipment_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

        },

        createLabEquipment: function () {
            this.loading = true;
            axios.post(this.api_url+'/lab-equipment-create/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.lab_equipment = response.data.lab_equipment;
                    this.resetLabEquipment();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Lab Equipment Saved Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        editLabEquipment: function (item) {
            this.edit = true;
            this.form_report = item;
            this.form_report.quantity = item.quantity;
            $('#labEquipmentId').val(item.lab_equipment_id).change();
            window.setTimeout(()=>{ $('#schoolLabEquipmentModal').modal({backdrop: "static"}) }, 10);
        },

        updateLabEquipment: function () {
            this.loading = true;

            axios.post(this.api_url+'/lab-equipment-update/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.lab_equipment = response.data.lab_equipment;
                    this.resetLabEquipment();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Lab Equipment Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetLabEquipment: function () {
            this.loading = false;
            this.edit = false;

            $('#labEquipmentId').val('').change();
            this.form_report.quantity = 0;
            this.form_report.section_id = this.sectionId;
            $('#schoolLabEquipmentModal').modal("hide");
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
