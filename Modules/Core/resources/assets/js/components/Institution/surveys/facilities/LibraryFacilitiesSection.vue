<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="align-center">
            <button @click="editInstitutionalFacility()" type="button" class="btn btn-sm bg-dark-teal">
                <em class="ni ni-edit-fill text-white mr-1"></em>Update Library Facilities
            </button>
        </div>
        <div class="mt-3">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Reference (directories, encyclopedia’s) Books are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_reference_books }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Text Books (core books for the programme) are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_textbooks }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">Does the Library have Subscription to E-Journals?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else-if="school.degree_school.institutional_facility.has_ejournal_subscription === true" class="ml-lg-5 data-value text-dark">YES</span>
                <span v-else class="ml-lg-5 data-value text-dark">NO</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">Does the Library have Subscription to Hard copy Journals?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else-if="school.degree_school.institutional_facility.has_hardcopy_journal === true" class="ml-lg-5 data-value text-dark">YES</span>
                <span v-else class="ml-lg-5 data-value text-dark">NO</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">Does the Library have Subscription to E-Books?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else-if="school.degree_school.institutional_facility.has_ebook_subscription === true" class="ml-lg-5 data-value text-dark">YES</span>
                <span v-else class="ml-lg-5 data-value text-dark">NO</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Chairs/Benches are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_chairs_benches }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Desks are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_desks }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Tables are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_tables }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Shelves are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_shelves }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">How many Functional computers are in the library?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else class="ml-lg-5 data-value text-dark">{{ school.degree_school.institutional_facility.no_of_functional_computers }}</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>
        <div class="mt-4">
            <div class="data-col">
                <span class="" style="font-size: initial">Does the library have Access to internet?</span>
                <span v-if="school.degree_school.institutional_facility === null" class="ml-lg-5 data-value text-muted">NOT SET</span>
                <span v-else-if="school.degree_school.institutional_facility.has_internet_access_library === true" class="ml-lg-5 data-value text-dark">YES</span>
                <span v-else class="ml-lg-5 data-value text-dark">NO</span>
            </div>
        </div><!-- data-item -->
        <div class="data-col data-col-end"></div>


        <div class="modal fade zoom" tabindex="-1" id="institutionalFacilityId">
            <div class="modal-dialog modal-lg modal-dialog-top" role="document">
                <div class="modal-content">
                    <a @click="resetInstitutionalFacility()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateInstitutionalFacility()">
                        <div class="modal-header">
                            <h6 class="modal-title">Update Library Facilities</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="col-12">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="no_of_reference_books" class="form-label">How many Reference (directories, encyclopedia’s) Books are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_reference_books" id="no_of_reference_books" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="no_of_textbooks" class="form-label">How many Text Books (core books for the programme) are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_textbooks" id="no_of_textbooks" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Does the Library have Subscription to E-Journals? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <div class="align-self-center">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_ejournal_subscription" type="radio" id="e_journals_yes" value="1" class="custom-control-input">
                                                <label class="custom-control-label" for="e_journals_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_ejournal_subscription" type="radio" id="e_journals_no" value="0" class="custom-control-input">
                                                <label class="custom-control-label" for="e_journals_no">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Does the Library have Subscription to Hard copy Journals? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <div class="align-self-center">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_hardcopy_journal" type="radio" id="hard_copy_journals_yes" value="1" class="custom-control-input">
                                                <label class="custom-control-label" for="hard_copy_journals_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_hardcopy_journal" type="radio" id="hard_copy_journals_no" value="0" class="custom-control-input">
                                                <label class="custom-control-label" for="hard_copy_journals_no">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Does the Library have Subscription to E-Books? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <div class="align-self-center">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_ebook_subscription" type="radio" id="ebooks_yes" value="1" class="custom-control-input">
                                                <label class="custom-control-label" for="ebooks_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_ebook_subscription" type="radio" id="ebooks_no" value="0" class="custom-control-input">
                                                <label class="custom-control-label" for="ebooks_no">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="chairs_benches" class="form-label">How many Chairs/Benches are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_chairs_benches" id="chairs_benches" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="desks" class="form-label">How many Desks are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_desks" id="desks" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="tables_library" class="form-label">How many Tables are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_tables" id="tables_library" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="shelves_library" class="form-label">How many Shelves are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_shelves" id="shelves_library" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label for="functional_computers" class="form-label">How many Functional computers are in the library? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <input min="1" v-model.number="form_report.no_of_functional_computers" id="functional_computers" type="number" class="form-control text-center bg-primary-dim">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="form-group">
                                    <div class="form-label-group">
                                        <label class="form-label">Does the library have Access to internet? <span class="text-danger">*</span></label>
                                    </div>
                                    <div class="form-control-group">
                                        <div class="align-self-center">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_internet_access_library" type="radio" id="library_internet_yes" value="1" class="custom-control-input">
                                                <label class="custom-control-label" for="library_internet_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input v-model="form_report.has_internet_access_library" type="radio" id="library_internet_no" value="0" class="custom-control-input">
                                                <label class="custom-control-label" for="library_internet_no">No</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetInstitutionalFacility()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";

export default {
    name: "LibraryFacilitiesSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/institutional-facilities',
            school: {
                degree_school: {
                    institutional_facility: {
                    }
                },
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            form_report: {
                section_id: '',
                survey_id: '',
                no_of_reference_books: '',
                no_of_textbooks: '',
                has_ejournal_subscription: 0,
                has_hardcopy_journal: 0,
                has_ebook_subscription: 0,
                no_of_chairs_benches: '',
                no_of_desks: '',
                no_of_tables: '',
                no_of_shelves: '',
                no_of_functional_computers: '',
                has_internet_access_library: 0,
            },

        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_report.section_id = this.sectionId;
            this.form_report.survey_id = this.survey.id;

            this.resetInstitutionalFacility();

        },

        editInstitutionalFacility: function () {
            this.resetInstitutionalFacility();

            if (this.school.degree_school.institutional_facility !== null) {
                this.form_report.no_of_reference_books = this.school.degree_school.institutional_facility.no_of_reference_books;
                this.form_report.no_of_textbooks = this.school.degree_school.institutional_facility.no_of_textbooks;
                this.form_report.has_ejournal_subscription = this.school.degree_school.institutional_facility.has_ejournal_subscription === false ? 0 : 1;
                this.form_report.has_hardcopy_journal = this.school.degree_school.institutional_facility.has_hardcopy_journal === false ? 0 : 1;
                this.form_report.has_ebook_subscription = this.school.degree_school.institutional_facility.has_ebook_subscription === false ? 0 : 1;
                this.form_report.no_of_chairs_benches = this.school.degree_school.institutional_facility.no_of_chairs_benches;
                this.form_report.no_of_desks = this.school.degree_school.institutional_facility.no_of_desks;
                this.form_report.no_of_tables = this.school.degree_school.institutional_facility.no_of_tables;
                this.form_report.no_of_shelves = this.school.degree_school.institutional_facility.no_of_shelves;
                this.form_report.no_of_functional_computers = this.school.degree_school.institutional_facility.no_of_functional_computers;
                this.form_report.has_internet_access_library = this.school.degree_school.institutional_facility.has_internet_access_library === false ? 0 : 1;
            }
            $('#institutionalFacilityId').modal({backdrop: "static"});
        },
        updateInstitutionalFacility: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.survey.id, this.form_report)
                .then(response=>{
                    this.school.degree_school.institutional_facility = response.data.degree_school.institutional_facility;
                    this.resetInstitutionalFacility();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Institutional Facilities Updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },

        resetInstitutionalFacility: function () {
            $('#institutionalFacilityId').modal("hide");
            this.loading = false;

            this.form_report.section_id = this.sectionId;
            this.form_report.no_of_reference_books = '';
            this.form_report.no_of_textbooks = '';
            this.form_report.has_ejournal_subscription = 0;
            this.form_report.has_hardcopy_journal = 0;
            this.form_report.has_ebook_subscription = 0;
            this.form_report.no_of_chairs_benches = '';
            this.form_report.no_of_desks = '';
            this.form_report.no_of_tables = '';
            this.form_report.no_of_shelves = '';
            this.form_report.no_of_functional_computers = '';
            this.form_report.has_internet_access_library = 0;
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },

    },
    computed: {

    }
}
</script>

<style scoped>

</style>
