<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="">
            <ul class="nav nav-tabs">
                <li class="nav-item">
                    <a class="nav-link ucap active" data-toggle="tab" href="#examinableCoursesTable">National Level Courses</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link ucap" data-toggle="tab" href="#nonExaminableCoursesTable">Institution Level Courses (Institution Specific)</a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="examinableCoursesTable">
                    <button data-toggle="modal" data-target="#updateExaminableCoursesModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                        <span class="text-uppercase">UPDATE COURSES</span>
                    </button>
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm mt-2">
                            <tr class="bg-secondary">
                                <td class="text-uppercase align-middle text-white border-1 border-white">Course</td>
                                <td class="text-uppercase align-middle text-white border-1 border-white">Duration</td>
                            </tr>
                            <tr v-for="schoolCourse in school.certificate_institution_courses" class="">
                                <td class="text-uppercase align-middle text-dark border-secondary border-1">
                                    {{ schoolCourse.post_primary_institution_course.name }}
                                </td>
                                <td class="text-uppercase align-middle text-dark border-secondary border-1">
                                    {{ schoolCourse.post_primary_institution_course.course_duration.name }}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div v-if="!school.certificate_institution_courses.length" class="card card-stretch" style="box-shadow: none;">
                        <div class="card-inner-group">
                            <div class="card-body">
                                <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                    <em class="icon ni ni-alert-circle"></em> There are no institution specific courses to display at the moment.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Update Courses Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="updateExaminableCoursesModal">
                        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <div class="modal-header">
                                    <h5 class="modal-title">Update Institution Courses</h5>
                                </div>
                                <div class="modal-body">
                                    <error-notifications ref="notifyError"></error-notifications>
                                    <form @submit.prevent="updateExaminableCourses()">
                                        <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Examinable Courses Offered in your institution</span>
                                        <div v-if="selectedCourses.length" :class="[selectedCourses.length > 5 ? 'h-150px overflow-auto' : '']">
                                            <ul class="list list-sm">
                                                <li class="py-0 fs-13px" v-for="course in selectedCourses">{{ course.name }}</li>
                                            </ul>
                                        </div>
                                        <hr v-if="selectedCourses.length" class="border-dark-teal border-1 my-4">
                                        <div class="">
                                            <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">All available examinable Courses</span>
                                            <div :class="[all_courses.length > 9 ? 'h-200px overflow-auto':'', 'my-1']">
                                                <div v-for="course in all_courses" class="d-block custom-control custom-control-sm custom-checkbox">
                                                    <input v-model="certificate_institution_courses" :value="course.id" type="checkbox" class="custom-control-input" :id="'courseCheck'+course.id">
                                                    <label class="custom-control-label" :for="'courseCheck'+course.id">{{ course.name }}</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 d-flex justify-content-center">
                                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                                            </button>
                                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="loading" class="align-self-center">Updating...</span>
                                                <span v-if="loading" class="sr-only">Updating...</span>
                                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /Update Courses Modal -->
                </div>
                <div class="tab-pane" id="nonExaminableCoursesTable">
                    <button data-toggle="modal" data-target="#updateNonExaminableCoursesModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                        <span class="text-uppercase">UPDATE COURSES</span>
                    </button>
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm mt-2">
                            <thead>
                            <tr class="bg-secondary">
                                <td class="text-white text-uppercase align-middle">Course</td>
                                <td class="text-white text-uppercase align-middle">Duration</td>
                                <td class="text-white text-uppercase align-middle text-center">Actions</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr v-for="course in school.institution_examined_courses">
                                <td class="border-secondary text-dark text-uppercase align-middle">{{ course.name.toUpperCase() }}</td>
                                <td class="border-secondary text-dark text-uppercase align-middle">{{ course.course_duration.name.toUpperCase() }}</td>
                                <td class="border-secondary text-dark align-middle text-center">
                                    <span @click="editNonExaminableCourses(course)" class="badge badge-dark-teal cursor">Update</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div v-if="!school.institution_examined_courses.length" class="card card-stretch" style="box-shadow: none;">
                        <div class="card-inner-group">
                            <div class="card-body">
                                <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                    <em class="icon ni ni-alert-circle"></em> There are no non-examinable courses to display at the moment.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Update Courses Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="updateNonExaminableCoursesModal">
                        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <div class="modal-header">
                                    <h5 class="modal-title">Update Institution Courses</h5>
                                </div>
                                <div class="modal-body">
                                    <error-notifications ref="notifyError"></error-notifications>
                                    <form @submit.prevent="edit ? updateNonExaminableCourses() : createNonExaminableCourses()">
                                        <div class="row g-3 align-center">
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="form-label" for="courseName">Course Name</label>
                                                    <span class="form-note">Specify Course Name</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-8">
                                                <div class="form-group">
                                                    <div class="form-control-wrap">
                                                        <input v-model.trim="form_course.name" placeholder="Enter Course Name" id="courseName" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off" required>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row g-3 align-center">
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="form-label" for="courseDurationId">Duration</label>
                                                    <span class="form-note">Specify Course Duration.</span>
                                                </div>
                                            </div>
                                            <div class="col-lg-8">
                                                <div class="form-group">
                                                    <select required id="courseDurationId" class="form-select-sm">
                                                        <option value="">--SELECT DURATION--</option>
                                                        <option v-for="duration in course_durations" :value="duration.id">{{ duration.name.toUpperCase() }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 d-flex justify-content-center">
                                            <button @click="edit = false" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                                            </button>
                                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span v-if="loading" class="align-self-center">Updating...</span>
                                                <span v-if="loading" class="sr-only">Updating...</span>
                                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /Update Courses Modal -->
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
import SurveyFooter from "../SurveyFooter.vue";
export default {
    name: "InstitutionCourses",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    components: {
        SurveyFooter,
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            edit: false,
            loading: false,
            api_url: '/institutions/surveys/school/section-b',
            funding_sources: [],
            all_courses: [],
            course_durations: [],
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                ownership_status: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                funding_source: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                certificate_institution_courses: [],
                institution_examined_courses: [],
                certificate_school: {
                    authority: {name: ''},
                    school_type: {name: ''},
                    supply_number: '',
                    registering_body_id: '',
                    admits_day_scholars_yn: '',
                    admits_boarders_yn: '',
                    campuses: [],
                },
            },
            form_course: {
                id: '',
                name: '',
                course_duration_id: '',
                survey_id: '',
            },
            certificate_institution_courses: [],
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.form_course.survey_id = this.survey.id;
            this.form_course.section_id = this.sectionId;

            this.school.certificate_institution_courses.forEach(schoolCourse => {
                this.certificate_institution_courses.push(schoolCourse.post_primary_institution_course_id);
            });

            $('#courseDurationId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateNonExaminableCoursesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_course.course_duration_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            this.allCertificateCourses();
            this.allCourseDurations();
        },
        allCertificateCourses: function () {
            axios.get('/institutions/surveys/all-certificate-courses')
                .then(response=>{
                    this.all_courses = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },
        allCourseDurations: function () {
            axios.get('/institutions/surveys/all-course-durations')
                .then(response=>{
                    this.course_durations = response.data;
                })
                .catch(error => {
                    console.log(error);
                    this.renderError(error);
                });
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        updateExaminableCourses: function () {
            this.loading = true;
            axios.post('/institutions/surveys/update-certificate-courses', {courses: this.certificate_institution_courses, survey_id: this.survey.id})
                .then(response=>{
                    $('#updateExaminableCoursesModal').modal('hide');
                    this.loading = false;
                    this.school.certificate_institution_courses = response.data === '' && !Array.isArray(response.data) ? [] : response.data;
                    this.$parent.school.certificate_institution_courses = response.data === '' && !Array.isArray(response.data) ? [] : response.data;
                    this.certificate_institution_courses = [];
                    this.school.certificate_institution_courses.forEach(schoolCourse => {
                        this.certificate_institution_courses.push(schoolCourse.post_primary_institution_course_id);
                    });
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        createNonExaminableCourses: function () {
            this.loading = true;
            this.form_course.name = this.form_course.name.toUpperCase();
            axios.post('/institutions/surveys/create-non-examinable-courses', this.form_course)
                .then(response => {
                    $('#updateNonExaminableCoursesModal').modal('hide');
                    this.loading = false;
                    this.school.institution_examined_courses = response.data;
                    this.$parent.school.institution_examined_courses = response.data;
                    this.form_course.name = "";
                    $("#courseDurationId").val("").change();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        updateNonExaminableCourses: function () {
            this.loading = true;
            this.form_course.name = this.form_course.name.toUpperCase();
            axios.put('/institutions/surveys/update-non-examinable-courses/'+this.form_course.id, this.form_course)
                .then(response => {
                    $('#updateNonExaminableCoursesModal').modal('hide');
                    this.loading = false;
                    this.edit = false;
                    this.school.institution_examined_courses = response.data;
                    this.$parent.school.institution_examined_courses = response.data;
                    this.form_course.name = "";
                    $("#courseDurationId").val("").change();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        editNonExaminableCourses: function (course) {
            this.edit = true;
            this.form_course.id = course.id;
            this.form_course.name = course.name;
            $("#courseDurationId").val(course.course_duration_id).change();
            $("#updateNonExaminableCoursesModal").modal({backdrop: "static"});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedCourses: function () {
            return this.all_courses.filter(course => {
                return this.certificate_institution_courses.includes(course.id);
            });
        },
    },
}
</script>

<style scoped>

</style>
