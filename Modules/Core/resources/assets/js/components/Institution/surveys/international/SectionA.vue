<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                <tr class="bg-secondary">
                    <th colspan="2" class="align-middle text-uppercase text-white border-secondary border">Section A: School Identification</th>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="align-middle text-uppercase text-dark border-secondary border w-35">Identifier</th>
                    <th class="align-middle text-uppercase text-dark border-secondary border">Name/Detail</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Name Of The School</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <div v-if="edit === 'school_name'" class="form-group ">
                            <div class="form-control-wrap">
                                <input v-model.trim="school_form.name" type="text" class="form-control" id="default-01" placeholder="Input placeholder" >
                            </div>
                        </div>
                        <span v-else>{{ school.name }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">EMIS Number</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">{{ school.emis_number }}</td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Region</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.region !== null">{{ school.region.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">District</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.district !== null">{{ school.district.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">County/Municipality</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.county !== null">{{ school.county.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Sub-County/Division/Town Council</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.sub_county !== null">{{ school.sub_county.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Parish</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.parish !== null">{{ school.parish.name }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Physical Address</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.physical_address !== null">{{ school.physical_address }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Postal Address</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.postal_address !== null">{{ school.postal_address }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Email Address</th>
                    <td class="align-middle text-dark border border-secondary">
                        <span v-if="school.email !== null">{{ school.email }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Telephone Contact</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.phone !== null">+{{ school.phone }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Website</th>
                    <td class="align-middle text-dark border border-secondary">
                        <span v-if="school.website !== null">{{ school.website }}</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border border-secondary">Area (Acres)</th>
                    <td class="align-middle text-uppercase text-dark border border-secondary">
                        <span v-if="school.school_land_area !== null">{{ school.school_land_area }} Acres</span>
                        <span v-else class="text-muted font-italic">NOT SET</span>
                    </td>
                </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="2" class="border-0 text-center">
                        <button data-toggle="modal" data-target="#updateSectionAModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-1">
                            <span class="text-uppercase">UPDATE INSTITUTION IDENTIFICATION</span>
                        </button>
                    </td>
                </tr>
                </tfoot>
            </table>
        </div>

        <!-- Update Section A Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateSectionAModal">
            <form @submit.prevent="updateSectionA()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetSectionA()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title text-uppercase">Update School Identification</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolPhysicalAddress">Physical Address</label>
                                        <span class="form-note">Specify the physical address of the institution</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.number="school_form.physical_address" placeholder="Plot 20, Kiwatule Road" id="schoolPhysicalAddress" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolPostalAddress">Postal Address</label>
                                        <span class="form-note">Specify the postal address of the institution</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.number="school_form.postal_address" placeholder="P.0 BOX 243, KAMPALA" id="schoolPostalAddress" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
<!--                            <div class="row g-3 align-center">-->
<!--                                <div class="col-lg-6">-->
<!--                                    <div class="form-group">-->
<!--                                        <label class="form-label" for="schoolEmail">Email Address</label>-->
<!--                                        <span class="form-note">Specify the email address of the school</span>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="col-lg-6">-->
<!--                                    <div class="form-group">-->
<!--                                        <div class="form-control-wrap">-->
<!--                                            <input v-model.trim="school_form.email" id="schoolEmail" type="text" class="form-control bg-primary-dim" autocomplete="off">-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolPhone">Phone Contact</label>
                                        <span class="form-note">Specify the phone number of the institution</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="school_form.phone" id="schoolPhone" type="text" maxlength="10" class="form-control bg-primary-dim" placeholder="Enter Phone No." autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolWebsite">Website</label>
                                        <span class="form-note">Specify website of the institution</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="school_form.website" id="schoolWebsite" type="text" class="form-control bg-primary-dim" placeholder="Enter Website" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLandArea">Area (Acres)</label>
                                        <span class="form-note">Specify the land area of the institution</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="school_form.school_land_area" id="schoolLandArea" placeholder="Enter Area (Acres)" type="text" class="form-control bg-primary-dim" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center mt-4">
                            <button @click="resetSectionA()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Section A Modal -->
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "SectionA",
    props: [
        'sectionId',
        'schoolObj',
        'districtsObj',
        'surveyObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/school/section-a',
            edit: '',
            districts: [],
            counties: [],
            sub_counties: [],
            parishes: [],
            school_form: {
                name: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
                physical_address: '',
                postal_address: '',
                email: '',
                phone: '',
                website: '',
                school_land_area: '',
                section_id: '',
            },
            school: {
                old_emis_number: '',
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                physical_address: '',
                postal_address: '',
                email: '',
                phone: '',
                school_land_area: '',
                website: '',
            },
            survey: {
                id: '',
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.districts = this.districtsObj;
            this.survey = this.surveyObj;
            this.school_form.section_id = this.sectionId;

            $('#schoolDistrictId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionAModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.district_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadCounties();
                    return data.text;
                },
            });

            $('#schoolCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionAModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadSubCounties();
                    return data.text;
                },
            });

            $('#schoolSubCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionAModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadParishes();
                    return data.text;
                },
            });

            $('#schoolParishId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateSectionAModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.school_form.parish_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            let school_phone = document.querySelector('#schoolPhone');
            let iti_school_phone = intlTelInput(school_phone, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });

            school_phone.addEventListener('blur', ()=>{
                self.school_form.phone = iti_school_phone.getNumber();
                school_phone.value = iti_school_phone.getNumber();
            });
            school_phone.addEventListener('change', ()=>{
                self.school_form.phone = iti_school_phone.getNumber();
                school_phone.value = iti_school_phone.getNumber();
            });

            this.resetSectionA();
        },
        updateSectionA: function () {
            this.loading = true;
            axios.post(this.api_url+"/"+this.survey.id, this.school_form)
                .then(response=>{
                    this.school = response.data.school;
                    this.$parent.school = response.data.school;
                    this.$parent.survey = response.data.survey;
                    this.resetSectionA();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success', message:"School Identification Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        loadCounties: function () {
            let self = this;
            // clean counties
            this.counties = [];
            let select = $("#schoolCountyId");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.school_form.county_id = "";

            //load new options
            if (self.school_form.district_id !== "") {
                self.counties = self.districts.find(district=>{
                    return district.id === self.school_form.district_id
                }).county;

                self.counties.forEach(county=>{
                    let countyOption = new Option(county.name, county.id, false, false);
                    select.append(countyOption).trigger('change');
                });
            }
        },
        loadSubCounties: function () {
            let self = this;
            // clean sub counties
            this.sub_counties = [];
            let select = $("#schoolSubCountyId");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.school_form.sub_county_id = "";

            //load new options
            if (self.school_form.county_id !== "") {
                self.sub_counties = self.counties.find(county=>{
                    return county.id === self.school_form.county_id
                }).sub_county;

                self.sub_counties.forEach(sub_county=>{
                    let subCountyOption = new Option(sub_county.name, sub_county.id, false, false);
                    select.append(subCountyOption).trigger('change');
                });
            }
        },
        loadParishes: function () {
            let self = this;
            // clean parishes
            this.parishes = [];
            let select = $("#schoolParishId");
            select.empty().trigger('change');
            let newOption = new Option("--Select--", "", false, false);
            select.append(newOption).trigger('change');
            self.school_form.parish_id = "";

            //load new options
            if (self.school_form.sub_county_id !== "") {
                self.parishes = self.sub_counties.find(sub_county=>{
                    return sub_county.id === self.school_form.sub_county_id
                }).parish;

                self.parishes.forEach(parish=>{
                    let parishOption = new Option(parish.name, parish.id, false, false);
                    select.append(parishOption).trigger('change');
                });
            }
        },
        resetSectionA: function() {
            $('#updateSectionAModal').modal('hide');
            this.edit = '';
            this.loading = false;
            this.school_form = {
                name: this.school.name,
                district_id: this.school.district_id,
                county_id: this.school.county_id,
                sub_county_id: this.school.sub_county_id,
                parish_id: this.school.parish_id,
                physical_address: this.school.physical_address,
                postal_address: this.school.postal_address,
                email: this.school.email,
                phone: this.school.phone,
                website: this.school.website,
                school_land_area: this.school.school_land_area,
            };
            this.school_form.section_id = this.sectionId;
            window.setTimeout(()=>{
                $('#schoolDistrictId').val(this.school.district_id).trigger('change');
                window.setTimeout(()=>{
                    $('#schoolCountyId').val(this.school.county_id).trigger('change');
                    window.setTimeout(()=>{
                        $('#schoolSubCountyId').val(this.school.sub_county_id).trigger('change');
                        window.setTimeout(()=>{
                            $('#schoolParishId').val(this.school.parish_id).trigger('change');
                        }, 50);
                    }, 50);
                }, 50);
            }, 50);
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {},
}
</script>

<style scoped>

</style>
