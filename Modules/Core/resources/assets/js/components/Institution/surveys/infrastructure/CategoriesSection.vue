<template>
    <div class="w-100 vertical-scrollable">
        <survey-infrastructure-main-facilities
            v-for="categoryObj in infrastructure_types"
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            section-obj="sectionId"
            :category-obj="categoryObj"
            :key="categoryObj.id"
            :preprimary="preprimary"
            :international-education-levels-obj="internationalEducationLevelsObj"
        ></survey-infrastructure-main-facilities>
    </div>
</template>

<script>

export default {
    name: "CategoriesSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'infrastructureTypesObj',
        'preprimary',
        'internationalEducationLevelsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            infrastructure_types: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.infrastructure_types = this.infrastructureTypesObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
