<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Has your school adopted the HIV/AIDS and Sexuality Education Policy ?</span>
                    <span v-if="school.primary_school.has_adopted_sex_hiv_policy === true || school.primary_school.has_adopted_sex_hiv_policy === 1" class="ml-lg-5 data-value text-dark">YES</span>
                    <span v-else class="ml-lg-5 data-value text-dark">NO</span>
                </div>
                <div class="data-col data-col-end">
                    <button @click="editPolicy()" data-backdrop="static" type="button" class="btn btn-sm bg-dark-teal">
                        <em class="ni ni-edit-fill text-white mr-1"></em>Update
                    </button>
                </div>
            </div><!-- data-item -->
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Does your school provide a hot midday meal to learners ?</span>
                    <span v-if="school.primary_school.offer_hot_midday_meal_to_learners === true || school.primary_school.offer_hot_midday_meal_to_learners === 1" class="ml-lg-5 data-value text-dark">YES</span>
                    <span v-else class="ml-lg-5 data-value text-dark">NO</span>
                </div>
                <div class="data-col data-col-end">
                    <button @click="editMealTypes()" type="button" class="btn btn-sm bg-dark-teal">
                        <em class="ni ni-edit-fill text-white mr-1"></em>Update
                    </button>
                </div>
            </div><!-- data-item -->
            <div v-if="school.meal_types.length" class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Meal Types</span>
                    <span class="ml-lg-5 text-dark">
                        <span v-for="meal_type in school.meal_types" class="d-block">
                            - {{ meal_type.name.toUpperCase() }}
                        </span>
                    </span>
                </div>
                <div class="data-col data-col-end"></div>
            </div><!-- data-item -->
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Source of Food</span>
                    <span v-if="school.food_sources.length" class="ml-lg-5 text-dark">
                        <span v-for="food_source in school.food_sources" class="">
                            - {{ food_source.name.toUpperCase() }}
                        </span>
                    </span>
                    <span v-else class="ml-lg-5 data-value text-dark">NONE</span>
                </div>
                <div class="data-col data-col-end">
                    <button @click="editFoodSources()" type="button" class="btn btn-sm bg-dark-teal">
                        <em class="ni ni-edit-fill text-white mr-1"></em>Update
                    </button>
                </div>
            </div><!-- data-item -->

            <!-- HIV/AIDS CASES -->
            <div class="card mt-5">
                <success-notifications ref="notifySuccessHealth"></success-notifications>
                <div class="table-responsive">
                    <h5 class="mb-2">Integrated Health Services</h5>
                    <table class="table border border-dark-teal">
                        <thead class="bg-secondary">
                        <tr>
                            <th class="text-white align-middle text-uppercase" rowspan="3">Category</th>
                        </tr>
                        <tr>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Learners</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Teachers</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Support Staff</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Total</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Action</th>
                        </tr>
                        <tr>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary"></th>
                        </tr>
                        </thead>
                        <tbody class="border-top-0 border-secondary">
                        <tr>
                            <td class="align-middle">Registered and Supported HIV/AIDS Cases</td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_learners" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleLearnersCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_learners" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleLearnersCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_teachers" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleTeachersCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_teachers" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleTeachersCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_support_staff" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleNonTeachingStaffCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 1" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_support_staff" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+1">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleNonTeachingStaffCount(1) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span>
                                    {{ getServiceFemaleLearnersCount(1) + getServiceFemaleTeachersCount(1) + getServiceFemaleNonTeachingStaffCount(1) }}
                                </span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span>
                                    {{ getServiceMaleLearnersCount(1) + getServiceMaleTeachersCount(1) + getServiceMaleNonTeachingStaffCount(1) }}
                                </span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span v-if="editService !== 1" @click="toggleUpdate(1)" class="cursor btn-sm btn-primary">
                                    <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                </span>
                                <div v-if="editService === 1" @click="saveUpdates()" class="cursor btn-sm bg-dark-teal">
                                    <em class="icon ni ni-check"></em><span>Save</span>
                                </div>
                                <div v-if="editService === 1" @click="toggleUpdate(1)" class="cursor btn-sm text-white bg-secondary mt-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Sexuality -->
            <div class="card mt-4">
                <div class="table-responsive">
                    <table class="table border border-dark-teal">
                        <thead class="bg-secondary">
                        <tr>
                            <th class="text-white align-middle text-uppercase" rowspan="3">Category</th>
                        </tr>
                        <tr>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Learners</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Teachers</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Support Staff</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Total</th>
                            <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2">Action</th>
                        </tr>
                        <tr>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>
                            <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary"></th>
                        </tr>
                        </thead>
                        <tbody class="border-top-0 border-secondary">
                        <tr>
                            <td class="">Number of persons that received HIV/AIDS and Sexuality Education based life skills</td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_learners" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleLearnersCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_learners" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleLearnersCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_teachers" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleTeachersCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_teachers" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleTeachersCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_female_support_staff" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceFemaleNonTeachingStaffCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div v-if="editService=== 2" class="form-group mx-auto">
                                    <div class="form-control-wrap">
                                        <input v-model.number="form_service.total_male_support_staff" type="text" min="0" class="form-control text-center border-dark-teal" :id="'outlined'+2">
                                    </div>
                                </div>
                                <span v-else>{{ getServiceMaleNonTeachingStaffCount(2) }}</span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span>
                                    {{ getServiceFemaleLearnersCount(2) + getServiceFemaleTeachersCount(2) + getServiceFemaleNonTeachingStaffCount(2) }}
                                </span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span>
                                    {{ getServiceMaleLearnersCount(2) + getServiceMaleTeachersCount(2) + getServiceMaleNonTeachingStaffCount(2) }}
                                </span>
                            </td>
                            <td class="align-middle border-left border-secondary text-center">
                                <span v-if="editService !== 2" @click="toggleUpdate(2)" class="cursor btn-sm btn-primary">
                                    <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                </span>
                                <div v-if="editService === 2" @click="saveUpdates()" class="cursor btn-sm bg-dark-teal">
                                    <em class="icon ni ni-check"></em><span>Save</span>
                                </div>
                                <div v-if="editService === 2" @click="toggleUpdate(2)" class="cursor btn-sm text-white bg-secondary mt-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

        <div class="modal fade zoom" tabindex="-1" id="policyModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateSexualityEducationPolicy()">
                        <div class="modal-header">
                            <h5 class="modal-title">Update HIV/AIDS & Sexuality Education</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyErrors"></error-notifications>
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">Has your school adopted the HIV/AIDS and Sexuality Education Policy ?</label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.has_adopted_sex_hiv_policy" type="radio" value="1" class="custom-control-input" id="policy_yes">
                                                    <label class="custom-control-label" for="policy_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.has_adopted_sex_hiv_policy" type="radio" value="0" class="custom-control-input" id="policy_no">
                                                    <label class="custom-control-label" for="policy_no">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="hotMealModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateHotMiddayMeal()">
                        <div class="modal-header">
                            <h5 class="modal-title">Update hot midday meals</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyErrors"></error-notifications>
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label font-weight-bold">Does your school provide a hot midday meal to learners ? </label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.offer_hot_midday_meal_to_learners" type="radio" value="1" class="custom-control-input" id="hot_day_meal_yes">
                                                    <label class="custom-control-label" for="hot_day_meal_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input v-model.number="form_report.offer_hot_midday_meal_to_learners" type="radio" value="0" class="custom-control-input" id="hot_day_meal_no">
                                                    <label class="custom-control-label" for="hot_day_meal_no">No</label>
                                                </div>
                                            </li>
                                        </ul>

                                    </div>
                                </div>
                                <div v-if="form_report.offer_hot_midday_meal_to_learners === 1" class="col-12 mt-4">
                                    <div v-for="meal in meal_types" class="custom-control custom-checkbox d-block mb-2">
                                        <input type="checkbox" v-model.number="selected_meal_types" :value="meal.id" class="custom-control-input" :id="'mealType'+meal.id">
                                        <label class="custom-control-label" :for="'mealType'+meal.id">{{ meal.name}}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="foodSourceModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateSourceOfFood()">
                        <div class="modal-header">
                            <h5 class="modal-title">Update school source of food</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label font-weight-bold">What is the source of food for learners in your school ? </label>
                                        <div v-for="source in food_sources" class="custom-control custom-checkbox d-block mb-2">
                                            <input type="checkbox" v-model.number="selected_food_sources" :value="source.id" class="custom-control-input" :id="'foodSource'+source.id">
                                            <label class="custom-control-label" :for="'foodSource'+source.id">{{ source.name}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../../Notifications.vue";
import SuccessNotifications from "../../../../Notifications.vue";

export default {
    name: "MealsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'mealTypesObj',
        'foodSourcesObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/primary/',
            form_report:{
                has_adopted_sex_hiv_policy: 0,
                offer_hot_midday_meal_to_learners: 0,
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            meal_types: [],
            selected_meal_types: [],
            food_sources: [],
            selected_food_sources: [],
            school: {
                primary_school: {
                    offer_hot_midday_meal_to_learners: 0,
                    has_adopted_sex_hiv_policy: 0,
                },
                meal_types: [],
                food_sources: [],
                health_services: [],
            },
            form_service: {
                section_id: '',
                survey_id: '',
                total_male_learners: 0,
                total_female_learners: 0,
                total_male_teachers: 0,
                total_female_teachers: 0,
                total_male_support_staff: 0,
                total_female_support_staff: 0,
            },
            editService: '',
        }
    },
    methods: {
        isNumeric: function (n) {
            return !isNaN(parseFloat(n)) && isFinite(n);
        },

        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.meal_types = this.mealTypesObj;
            this.food_sources = this.foodSourcesObj;
            this.form_service.section_id = this.sectionId;
            this.form_service.survey_id = this.survey.id;
        },

        getServiceMaleLearnersCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_male_learners;
            }
        },
        getServiceFemaleLearnersCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_female_learners;
            }
        },
        // Teachers
        getServiceMaleTeachersCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_male_teachers;
            }
        },
        getServiceFemaleTeachersCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_female_teachers;
            }
        },

        // Non Teaching Staff
        getServiceMaleNonTeachingStaffCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_male_support_staff;
            }
        },
        getServiceFemaleNonTeachingStaffCount: function (serviceId) {
            let totalReport = this.school.health_services.find(serviceReport=>{
                return serviceReport.health_service_id === serviceId;
            })

            if (totalReport === undefined) {
                return 0;
            } else {
                return totalReport.total_female_support_staff;
            }
        },

        toggleUpdate: function (serviceId) {
            if (this.editService === "" || this.editService !== serviceId) {
                this.editService = serviceId;
                this.form_service.health_service_id = serviceId;

                let exists = this.school.health_services.find(entry=>{
                    return entry.health_service_id === serviceId;
                });

                if (exists !== undefined) {
                    this.form_service.total_male_learners = exists.total_male_learners;
                    this.form_service.total_female_learners = exists.total_female_learners;
                    this.form_service.total_male_teachers = exists.total_male_teachers;
                    this.form_service.total_female_teachers = exists.total_female_teachers;
                    this.form_service.total_male_support_staff = exists.total_male_support_staff;
                    this.form_service.total_female_support_staff = exists.total_female_support_staff;
                } else {
                    this.form_service.total_male_learners = 0;
                    this.form_service.total_female_learners = 0;
                    this.form_service.total_male_teachers = 0;
                    this.form_service.total_female_teachers = 0;
                    this.form_service.total_male_support_staff = 0;
                    this.form_service.total_female_support_staff = 0;
                }
            } else {
                this.editService = "";
                this.form_service.health_service_id = ''
                this.form_service.total_male_learners = 0
                this.form_service.total_female_learners = 0
                this.form_service.total_male_teachers = 0;
                this.form_service.total_female_teachers = 0;
                this.form_service.total_male_support_staff = 0;
                this.form_service.total_female_support_staff = 0;
                this.form_service.section_id = this.sectionId;
                this.form_service.survey_id = this.survey.id;
            }
        },

        saveUpdates: function () {

        axios.post(this.api_url + 'health-services/' + this.survey.id, this.form_service)
            .then(response => {
                this.school.health_services = response.data.health_services
                this.$refs.notifySuccessHealth.messages.push({
                    status: 'success',
                    title: 'Success',
                    message: "Health Integrated Service Updated Successfully"
                });

                this.editService = "";
                this.form_service.health_service_id = ''
                this.form_service.total_male_learners = 0
                this.form_service.total_female_learners = 0
                this.form_service.total_male_teachers = 0;
                this.form_service.total_female_teachers = 0;
                this.form_service.total_male_support_staff = 0;
                this.form_service.total_female_support_staff = 0;
                this.form_service.section_id = this.sectionId;
                this.form_service.survey_id = this.survey.id;
            })
            .catch(error => {
                this.loading = false;
                console.log(error);
                this.renderError(error);
            });

        },

        updateSexualityEducationPolicy: function() {
            this.loading = true;
            axios.post(this.api_url+'sexuality-policy', {id: this.school.id, has_adopted_sex_hiv_policy: this.form_report.has_adopted_sex_hiv_policy})
                .then(response => {
                    this.school.primary_school.has_adopted_sex_hiv_policy = response.data.has_adopted_sex_hiv_policy;
                    this.loading = false;
                    $('#policyModal').modal("hide");
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"Sexuality School Policy Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                })
        },

        editPolicy: function () {
            this.form_report.has_adopted_sex_hiv_policy = this.school.primary_school.has_adopted_sex_hiv_policy === false ? 0 : 1;
            $('#policyModal').modal({backdrop: "static"})
        },

        editMealTypes: function () {
            this.selected_meal_types = [];
            this.school.meal_types.forEach((meal_type) => {
                this.selected_meal_types.push(meal_type.id)
            });
            if (this.school.primary_school.offer_hot_midday_meal_to_learners === false) {
                this.form_report.offer_hot_midday_meal_to_learners = 0;
            } else {
                this.form_report.offer_hot_midday_meal_to_learners = 1;
            }
            // this.form_report.offer_hot_midday_meal_to_learners = this.school.primary_school.offer_hot_midday_meal_to_learners === false ? 0 : 1;
            $('#hotMealModal').modal({backdrop: "static"})
        },

        updateHotMiddayMeal: function() {
            if (this.form_report.offer_hot_midday_meal_to_learners === 1 && !this.selected_meal_types.length) {
                this.$refs.notifyErrors.messages.push({status: 'error', title: 'Error', message:"Select at least one meal type to proceed."});
            } else {
                if (this.form_report.offer_hot_midday_meal_to_learners === 0) {
                    this.selected_meal_types = [];
                }
                this.loading = true;
                axios.post(this.api_url+'hot-midday-meal',
                    {id: this.school.id, offer_hot_midday_meal_to_learners: this.form_report.offer_hot_midday_meal_to_learners, selected_meal_types: this.selected_meal_types})
                    .then(response => {
                        this.school.primary_school.offer_hot_midday_meal_to_learners = response.data.offer_hot_midday_meal_to_learners;
                        this.school.meal_types = response.data.meal_types;
                        this.loading = false;
                        this.selected_meal_types = [];
                        $('#hotMealModal').modal("hide");
                        this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"School Hot Midday Meal Updated Successfully"});
                    })
                    .catch(error=>{
                        this.loading = false;
                        this.renderError(error)
                    })
            }
        },

        editFoodSources: function () {
            this.selected_food_sources = [];
            this.school.food_sources.forEach((food_source) => {
                this.selected_food_sources.push(food_source.id)
            });
            $('#foodSourceModal').modal({backdrop: "static"})
        },
        updateSourceOfFood: function() {
            this.loading = true;
            axios.post(this.api_url+'source-of-food',
                {id: this.school.id, selected_food_sources: this.selected_food_sources})
                .then(response => {
                    this.school.food_sources = response.data.food_sources;
                    this.loading = false;
                    this.selected_food_sources = [];
                    $('#foodSourceModal').modal("hide");
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success:', message:"School Source Of Food Updated Successfully"});
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                })
        },

        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
