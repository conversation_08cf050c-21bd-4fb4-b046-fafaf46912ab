<template>
    <div class="w-100 vertical-scrollable">
        <survey-secondary-textbooks
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :sne-kits-obj="sneKitsObj"
            :education-grades-obj="educationGradesObj"
            :subjects-obj="subjectsObj"
            :textbooks-obj="textbooksObj"
        ></survey-secondary-textbooks>
        <survey-secondary-reference-books
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :reference-books-obj="referenceBooksObj"
            :education-grades-obj="educationGradesObj"
            :subjects-obj="subjectsObj"
        ></survey-secondary-reference-books>
        <survey-secondary-sne-kits
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :sne-kits-obj="sneKitsObj"
            :education-grades-obj="educationGradesObj"
        ></survey-secondary-sne-kits>
        <survey-secondary-lab-equipment
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :lab-equipment-obj="labEquipmentObj"
        ></survey-secondary-lab-equipment>
        <survey-secondary-lab-reagents
            :school-obj="schoolObj"
            :survey-obj="surveyObj"
            :lab-reagents-obj="labReagentsObj"
        ></survey-secondary-lab-reagents>
    </div>
</template>

<script>

export default {
    name: "MaterialsSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'educationGradesObj',
        'textbooksObj',
        'referenceBooksObj',
        'subjectsObj',
        'sneKitsObj',
        'labEquipmentObj',
        'labReagentsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
