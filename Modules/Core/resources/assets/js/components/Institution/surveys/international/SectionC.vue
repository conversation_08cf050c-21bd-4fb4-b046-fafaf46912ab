<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>
        <error-notifications ref="notifyMainError"></error-notifications>
        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">SECTION C: LEARNERS' INFORMATION</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">CURRICULUM</th>
                    <th class="text-uppercase border-secondary text-dark">SEX</th>
                    <th class="text-uppercase border-secondary text-dark text-center">TOTAL</th>
                </tr>
                </thead>
                <tbody class="border-secondary border-top" v-for="curriculum in curriculums">
                <tr>
                    <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                        <span class="">{{ curriculum.name }}</span>
                    </th>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span class="">MALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        <span class="">{{ curriculum.male_count }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span class="">FEMALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        <span class="">{{ curriculum.female_count }}</span>
                    </td>
                </tr>
                </tbody>
                <tbody class="border-secondary border-top">
                <tr>
                    <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                        <span class="">Total</span>
                    </th>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span class="">MALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        <span class="">{{ getTotalMale() }}</span>
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span class="">FEMALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        <span class="">{{ getTotalFemale() }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="table-responsive mt-5">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">RECENTLY ADDED LEARNERS</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">LEARNER</th>
                    <th class="text-uppercase border-secondary text-dark">SEX</th>
                    <th class="text-uppercase border-secondary text-dark">AGE</th>
                    <th class="text-uppercase border-secondary text-dark">CLASS</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="enrolment in learner_enrolments">
                    <td class="text-uppercase text-dark border-secondary">
                        <a target="_blank" :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin">
                            <div class="user-card">
                                <div class="user-avatar">
                                    <img :src="enrolment.learner.person.photo_url" class="rounded-0" :alt="enrolment.learner.person.full_name">
                                </div>
                                <div class="user-name text-uppercase">
                                    <span class="tb-lead cursor text-dark-teal">{{ enrolment.learner.person.full_name }}</span>
                                </div>
                            </div>
                        </a>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span class="" v-if="enrolment.learner.person.gender === 'M'">MALE</span>
                        <span class="" v-else>FEMALE</span>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span class="">{{ formatAge(enrolment.learner.person.birth_date) }}</span>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span v-if="enrolment.international_education_grade !== null">{{ enrolment.international_education_grade.name }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
<!--        <div v-if="school.international_curriculums.length > 0 && school.international_calendars.length > 0" class="d-flex justify-content-center">-->
        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#updateSectionCModal" data-backdrop="static" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE LEARNERS' INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="updateSectionCModal">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">UPDATE LEARNERS' INFORMATION</h6>
                    </div>
                    <div class="modal-body pt-0">
                        <error-notifications ref="notifyError"></error-notifications>
                        <ul class="nav nav-tabs">
                            <li @click="loadSection('form-create')" class="nav-item">
                                <span :class="[active_section === 'form-create' ? 'active' : '', 'nav-link cursor']" href="#addLearnerFormTab">Add Single Learner</span>
                            </li>
<!--                            <li @click="loadSection('import-ugandans')" class="nav-item">-->
<!--                                <span :class="[active_section === 'import-ugandans' ? 'active' : '', 'nav-link cursor']" href="#addLearnerExcelUgandan">Upload Ugandan Learners</span>-->
<!--                            </li>-->
<!--                            <li @click="loadSection('import-non-refugees')" class="nav-item">-->
<!--                                <span :class="[active_section === 'import-non-refugees' ? 'active' : '', 'nav-link cursor']" href="#addLearnerExcelNonRefugees">Upload Foreign Non Refugee Learners</span>-->
<!--                            </li>-->
<!--                            <li @click="loadSection('import-refugees')" class="nav-item">-->
<!--                                <span :class="[active_section === 'import-refugees' ? 'active' : '', 'nav-link cursor']" href="#addLearnerExcelRefugees">Upload Refugee Learners</span>-->
<!--                            </li>-->
                        </ul>
                        <div class="tab-content">
                            <div :class="[active_section === 'form-create' ? 'active' : '', 'tab-pane']" id="addLearnerFormTab">
                                <survey-international-learner-form-create
                                    :section-id="sectionId"
                                    :school-obj="schoolObj"
                                    :school-curriculums-obj="schoolCurriculumsObj"
                                    :survey-obj="surveyObj"
                                    :countries-obj="countriesObj"
                                    :familiar-languages-obj="familiarLanguagesObj"
                                    :disability-types-obj="disabilityTypesObj"
                                    :health-issues-obj="healthIssuesObj"
                                    :districts-obj="districtsObj"
                                    :talents-obj="talentsObj"
                                    :practical-skills-obj="practicalSkillsObj"
                                ></survey-international-learner-form-create>
                            </div>
                            <div :class="[active_section === 'import-ugandans' ? 'active' : '', 'tab-pane']" id="addLearnerExcelUgandan">
<!--                                <survey-international-learner-import-ugandans-->
<!--                                    :section-id="sectionId"-->
<!--                                    :school-obj="schoolObj"-->
<!--                                    :survey-obj="surveyObj"-->
<!--                                ></survey-international-learner-import-ugandans>-->
                            </div>
                            <div :class="[active_section === 'import-non-refugees' ? 'active' : '', 'tab-pane']" id="addLearnerExcelNonRefugees">
<!--                                <survey-international-learner-import-foreigners-->
<!--                                    :section-id="sectionId"-->
<!--                                    :school-obj="schoolObj"-->
<!--                                    :survey-obj="surveyObj"-->
<!--                                ></survey-international-learner-import-foreigners>-->
                            </div>
                            <div :class="[active_section === 'import-refugees' ? 'active' : '', 'tab-pane']" id="addLearnerExcelRefugees">
<!--                                <survey-international-learner-import-refugees-->
<!--                                    :section-id="sectionId"-->
<!--                                    :school-obj="schoolObj"-->
<!--                                    :survey-obj="surveyObj"-->
<!--                                ></survey-international-learner-import-refugees>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "SectionC",
    props: [
        'sectionId',
        'districtsObj',
        'schoolObj',
        'schoolCurriculumsObj',
        'surveyObj',
        'healthIssuesObj',
        'familiarLanguagesObj',
        'learnerEnrolmentsObj',
        'disabilityTypesObj',
        'countriesObj',
        'talentsObj',
        'practicalSkillsObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            verify: true,
            uganda: true,
            active_section: 'form-create',
            api_url: '/institutions/surveys/school/section-c',
            school: {
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                legal_ownership_status: {name: ''},
                founding_body: {name: ''},
                registration_status: {name: ''},
                health_facility_distance: {name: ''},
                international_curriculums: [],
                international_calendars: [],
                international_school: {
                    school_distance_range: {name: ''},
                    deo_office_distance: {name: ''},
                },
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            school_form: {
                is_operational_yn: 1,
                school_closure_reason: '',
                legal_ownership_status_id: '',
                founding_body_id: '',
                year_founded: '',
                registration_status_id: '',
                registration_number: '',
                license_number: '',
                sex_composition: '',
                day_or_boarding: '',
                estimated_distance_to_deo_office_id: '',
                health_facility_distance_range_id: '',
                estimated_distance_to_ecce_school_id: '',
            },
            curriculums: [],
            disability_types: [],
            learner_enrolments: [],
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        formatAge: function (dob) {
            let now = moment();
            let date_of_birth = moment(dob),
                diff = now.diff(date_of_birth, 'years');

            return diff > 1 ? diff+" YEARS" : (diff < 1 ? 'Under 1 Year' : diff+" YEAR")
        },
        getTotalMale: function () {
            let total = 0;

            this.curriculums.forEach(curriculum=>{
                total += curriculum.male_count
            });

            return total;
        },
        getTotalFemale: function () {
            let total = 0;

            this.curriculums.forEach(curriculum=>{
                total += curriculum.female_count
            });

            return total;
        },
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.curriculums = this.schoolCurriculumsObj;
            this.disability_types = this.disabilityTypesObj;
            this.learner_enrolments = this.learnerEnrolmentsObj;

            window.setTimeout(()=>{
                self.$refs.notifyMainError.messages = [];
                if (self.school.international_curriculums.length === 0) {
                    self.$refs.notifyMainError.messages.push({status: 'error', title: 'Note!', message: 'First register curriculums and calendars to update this section'});
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                }
            }, 50);
        },
        loadSection: function (section) {
            this.active_section = section;
        },
        saveLearnerInformation: function () {
            if (this.active_section === 'form-create') {
                $('#learnerFormCreateFormSubmit').click();
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        toDay: function () {
            return moment().format("Do MMMM YYYY").replace( /(\d)(st|nd|rd|th)/g, '$1<sup>$2</sup>' );
        },
        schoolGender: function () {
            if (this.school.has_male_students && this.school.has_female_students) {
                return "mixed"
            } else if (!this.school.has_male_students && this.school.has_female_students) {
                return "female"
            } else if (this.school.has_male_students && !this.school.has_female_students) {
                return "male"
            } else {
                return null;
            }
        },
        founderYears: function () {
            let years = [];

            for (let i = Number(moment().format('YYYY')); i >= 1900; i--) {
                years.push(i);
            }

            return years;
        },
    },
}
</script>

<style scoped>

</style>
