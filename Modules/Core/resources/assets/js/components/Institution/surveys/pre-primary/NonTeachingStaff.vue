<template>
    <div class="w-100">
        <success-notifications ref="notifySuc<PERSON>"></success-notifications>

        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">SECTION D: SUPPORT/ADMINISTRATIVE STAFF INFORMATION</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th rowspan="2" class="align-middle text-uppercase border-secondary text-dark">Category</th>
                    <th rowspan="2" class="align-middle text-uppercase border-secondary text-dark">JOB TITLE</th>
                    <th colspan="2" class="text-uppercase border-secondary text-dark text-center">Sex of Staff</th>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark text-center">M</th>
                    <th class="text-uppercase border-secondary text-dark text-center">F</th>
                </tr>
                </thead>
                <tbody class="border-secondary border-top">
                <tr>
                    <th :rowspan="administrativeRoles.length+1" class="align-middle text-uppercase border-secondary text-dark">ADMINISTRATIVE STAFF</th>
                </tr>
                <tr v-for="role in administrativeRoles">
                    <td class="align-middle text-uppercase border-secondary text-dark">{{ role.name }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ role.male_count }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ role.female_count }}</td>
                </tr>
                <tr>
                    <th :rowspan="supportRoles.length+1" class="align-middle text-uppercase border-secondary text-dark">SUPPORT STAFF</th>
                </tr>
                <tr v-for="role in supportRoles">
                    <td class="align-middle text-uppercase border-secondary text-dark">{{ role.name }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ role.male_count }}</td>
                    <td class="align-middle text-uppercase border-secondary text-dark text-center">{{ role.female_count }}</td>
                </tr>
                </tbody>
                <tfoot>
                <tr class="bg-secondary-dim">
                    <th colspan="2" class="align-middle text-uppercase border-secondary text-dark">
                        TOtal
                    </th>
                    <th class="align-middle text-uppercase border-secondary text-dark text-center">{{ countTotalMaleStaff() }}</th>
                    <th class="align-middle text-uppercase border-secondary text-dark text-center">{{ countTotalFemaleStaff() }}</th>
                </tr>
                </tfoot>
            </table>
        </div>

        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#updateNonTeachingStaffModal" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE SUPPORT/ADMINISTRATIVE STAFF INFORMATION</span>
            </button>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="updateNonTeachingStaffModal">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">UPDATE SUPPORT/ADMINISTRATIVE STAFF INFORMATION</h6>
                    </div>
                    <div class="modal-body pt-0">
                        <error-notifications ref="notifyError"></error-notifications>
                        <ul class="nav nav-tabs">
                            <li @click="loadSection('form-create')" class="nav-item">
                                <span :class="[active_section === 'form-create' ? 'active' : '', 'nav-link cursor']" href="#addSupportStaffFormTab">Add Single Support Staff</span>
                            </li>
                            <!-- <li @click="loadSection('import-ugandans')" class="nav-item">
                                <span :class="[active_section === 'import-ugandans' ? 'active' : '', 'nav-link cursor']" href="#addSupportStaffExcelUgandan">Upload Ugandan Support Staff</span>
                            </li> -->
                            <!-- <li @click="loadSection('import-non-ugandans')" class="nav-item">
                                <span :class="[active_section === 'import-non-ugandans' ? 'active' : '', 'nav-link cursor']" href="#addSupportStaffExcelNonUgandans">Upload Foreigner Support Staff</span>
                            </li> -->
                        </ul>
                        <div class="tab-content">
                            <div :class="[active_section === 'form-create' ? 'active' : '', 'tab-pane']" id="addSupportStaffFormTab">
                                <survey-pre-primary-support-staff-form-create
                                    :section-id="sectionId"
                                    :school-obj="schoolObj"
                                    :survey-obj="surveyObj"
                                    :countries-obj="countriesObj"
                                    :marital-statuses-obj="maritalStatusesObj"
                                    :education-levels-obj="educationLevelsObj"
                                    :employment-statuses-obj="employmentStatusesObj"
                                    :non-teaching-staff-roles-obj="nonTeachingStaffRolesObj"
                                ></survey-pre-primary-support-staff-form-create>
                            </div>
                            <div :class="[active_section === 'import-ugandans' ? 'active' : '', 'tab-pane']" id="addSupportStaffExcelUgandan">
                                <survey-pre-primary-support-staff-import-ugandans
                                    :section-id="sectionId"
                                    :school-obj="schoolObj"
                                    :survey-obj="surveyObj"
                                ></survey-pre-primary-support-staff-import-ugandans>
                            </div>
                            <div :class="[active_section === 'import-non-ugandans' ? 'active' : '', 'tab-pane']" id="addSupportStaffExcelNonUgandans">
                                <survey-pre-primary-support-staff-import-foreigners
                                    :section-id="sectionId"
                                    :school-obj="schoolObj"
                                    :survey-obj="surveyObj"
                                ></survey-pre-primary-support-staff-import-foreigners>
                            </div>
                        </div>
                    </div>
                    <div v-if="active_section === 'form-create'"  class="modal-footer d-flex justify-content-center">
                        <button @click="" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button v-if="verify && active_section === 'form-create'" @click="saveSupportStaffInformation()" :disabled="loading" type="submit" class="btn btn-primary d-flex">
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading" class="align-self-center">Verifying...</span>
                            <span v-if="loading" class="sr-only">Verifying...</span>
                            <span v-if="!loading && uganda" class="align-self-center">Verify Staff NIN</span>
                            <span v-if="!loading && !uganda" class="align-self-center">Verify Staff Work Permit</span>
                            <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                        </button>
                        <button v-else @click="saveSupportStaffInformation()" :disabled="loading" type="submit" class="btn btn-primary d-flex">
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading" class="align-self-center">Saving...</span>
                            <span v-if="loading" class="sr-only">Saving...</span>
                            <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "NonTeachingStaff",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'countriesObj',
        'employmentStatusesObj',
        'educationLevelsObj',
        'maritalStatusesObj',
        'nonTeachingStaffRolesObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            verify: true,
            uganda: true,
            active_section: 'form-create',
            api_url: '/institutions/surveys/school/non-teaching-staff',
            school: {},
            survey: {},
            non_teaching_staff_roles: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.non_teaching_staff_roles = this.nonTeachingStaffRolesObj;
        },
        loadSection: function (section) {
            this.active_section = section;
        },
        countTotalMaleStaff: function () {
            let count = 0;

            this.non_teaching_staff_roles.forEach(role => {
                count += role.male_count;
            });

            return count;
        },
        countTotalFemaleStaff: function () {
            let count = 0;

            this.non_teaching_staff_roles.forEach(role => {
                count += role.female_count;
            });

            return count;
        },
        saveSupportStaffInformation: function () {
            if (this.active_section === 'form-create') {
                $('#supportStaffFormCreateFormSubmit').click();
            }
        },
    },
    computed: {
        supportRoles: function () {
            return this.non_teaching_staff_roles.filter(role => {
                return role.non_teaching_staff_category.name === "SUPPORT";
            });
        },
        administrativeRoles: function () {
            return this.non_teaching_staff_roles.filter(role => {
                return role.non_teaching_staff_category.name === "ADMINISTRATIVE";
            });
        },
    },
}
</script>

<style scoped>

</style>
