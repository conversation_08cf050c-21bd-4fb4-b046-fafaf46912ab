<template>
    <div class="w-100">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notifySuccess"></success-notifications>
        <div v-if="!preview" class="row">
            <div class="col-12">
                <span class="fs-16 font-weight-bold mr-lg-2">Step 1:</span>
                <span>Download and fill out the Excel Template then proceed to Step 2</span>
            </div>
            <div class="col-lg-6 mt-2">
                <button :disabled="downloading" @click="downloadTemplate()" class="btn btn-block bg-dark-teal">
                    <span v-if="downloading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span v-if="downloading" class="align-self-center">Generating Excel...</span>
                    <span v-if="downloading" class="sr-only">Generating Excel...</span>
                    <em v-if="!downloading" class="icon ni ni-file-download"></em>
                    <span v-if="!downloading">Download Excel Template</span>
                </button>
            </div>
        </div>
        <div v-if="!preview" class="row mt-5">
            <div class="col-12 d-flex flex-column">
                <span class="align-self-start fs-16 font-weight-bold mr-lg-2">Step 2:</span>
                <span>Upload the Excel Template you filled from Step 1 then click upload</span>
            </div>
            <div class="col-12">
                <form @submit.prevent="uploadFilledTemplate()">
                    <div class="mt-2 row">
                        <div class="col-lg-8">
                            <div class="custom-file">
                                <input :disabled="loading" ref="filledTemplate" type="file" :class="[loading ? '' : 'cursor', 'custom-file-input']" id="filledTemplate" @change="checkFile()" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" required>
                                <label id="filledUgandanTeachersTemplateLabel" class="custom-file-label" for="filledTemplate">Upload Filled Excel Template</label>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <button type="submit" :disabled="!valid_file || loading" class="btn btn-block bg-dark-teal mt-2 mt-lg-0">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Uploading...</span>
                                <span v-if="loading" class="sr-only">Uploading...</span>
                                <span v-if="!loading" class="align-self-center">Upload</span>
                                <em v-if="!loading" class="ni ni-upload ml-2"></em>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div v-if="preview" class="row">
            <div class="col-12">
                <span class="align-self-start fs-16 font-weight-bold mr-lg-2">Step 3:</span>
                <span>Confirm your uploads for processing.</span>
            </div>
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-bordered table-sm table-hover">
                        <tbody>
                        <tr>
                            <td class="align-middle border-secondary text-uppercase text-dark border-top border-1 bg-secondary-dim">File Name:</td>
                            <td class="align-middle border-secondary text-dark border-top border-1">{{ file.file_name }}</td>
                        </tr>
                        <tr>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1 bg-secondary-dim">Total Uploaded Teachers:</td>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1">{{ file.total_records }}</td>
                        </tr>
                        <tr>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1 bg-secondary-dim">Total Teachers With Errors:</td>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1">{{ file.total_records - file.clean_records }}</td>
                        </tr>
                        <tr>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1 bg-secondary-dim">Total Male Teachers:</td>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1">{{ file.total_male }}</td>
                        </tr>
                        <tr>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1 bg-secondary-dim">Total Female Teachers:</td>
                            <td class="align-middle text-uppercase border-secondary text-dark border-1">{{ file.total_female }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div v-if="failures.data.length" class="col-12 mt-5">
                <div v-if="error_messages.length" class="alert alert-danger alert-dismissible mt-2">
                    <div class="alert-text">
                        <p v-for="error in error_messages">{{ error }}</p>
                    </div>
                    <button @click="clearErrors()" class="close" data-dismiss="alert"></button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm table-hover">
                        <tbody>
                        <tr>
                            <td class="align-middle border-white text-uppercase text-white border-top border-left-0 border-1 bg-secondary">Teacher</td>
                            <td class="align-middle border-white text-uppercase text-white border-top border-right-0 border-1 bg-secondary">DAte Of Birth</td>
                            <td class="align-middle border-white text-uppercase text-white border-top border-right-0 border-1 bg-secondary">Gender</td>
                            <td class="align-middle border-white text-uppercase text-white border-top border-right-0 border-1 bg-secondary">ACtions</td>
                        </tr>
                        <tr v-for="entry in failures.data">
                            <td class="align-middle border-secondary text-uppercase text-dark border-top border-1 bg-secondary-dim">{{ entry.full_name }}</td>
                            <td class="align-middle border-secondary text-uppercase text-dark border-top border-1 bg-secondary-dim">
                                {{ entry.date }} {{ entry.month }}, {{ entry.year }}
                            </td>
                            <td class="align-middle border-secondary text-uppercase text-dark border-top border-1 bg-secondary-dim">{{ entry.gender }}</td>
                            <td class="align-middle border-secondary text-center text-dark border-top border-1">
                                <span @click="showErrors(entry)" class="badge badge-red cursor">View Errors</span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-lg-6 mt-5">
                <button @click="processUploads()" class="btn btn-block bg-dark-teal">
                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span v-if="loading" class="align-self-center">Submitting...</span>
                    <span v-if="loading" class="sr-only">Submitting...</span>
                    <span v-if="!loading">Submit for Verification by NIRA</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "TeacherImportUgandans",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
    ],
    mounted() {
        this.initPlugins();
    },
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    data: function () {
        return {
            loading: false,
            uploaded: false,
            downloading: false,
            api_url: '/institutions/teachers/import-ugandans',
            valid_file: false,
            file_name: null,
            filled_template: '',
            school: {},
            survey: {
                id: '',
                name: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
            },
            upload: {
                id: '',
                display_name: '',
            },
            preview: false,
            file: {
                id: '',
                file_name: '',
                total_records: 0,
                total_male: 0,
                total_female: 0,
                clean_records: 0,
            },
            failures: {
                total: 0,
                current_page: 1,
                data: [],
            },
            error_messages: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.survey = this.surveyObj;
        },
        downloadTemplate: function () {
            this.downloading = true;
            axios({
                method: 'get',
                url: this.api_url+'?x='+moment().unix(),
                responseType: 'arraybuffer'
            })
                .then(response=>{
                    this.downloading = false;
                    let blob = new Blob([response.data], { type: 'application/octet-stream' });
                    let a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(blob, {
                        type: 'data:attachment/xlsx'
                    })
                    a.download = 'secondary_ugandan_teachers_'+moment().unix()+'.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                    document.body.removeChild(a);
                })
                .catch(error=>{
                    this.downloading = false;
                    this.renderError(error)
                });
        },
        processUploads: function () {
            this.loading = true;
            axios.get(this.api_url+"/process/"+this.file.id)
                .then(()=>{
                    this.preview = false;
                    this.loading = false;
                    window.location.replace("/institution/file-uploads/details/"+this.file.id);
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        showErrors: function (entry) {
            let errors = JSON.parse(entry.validation_errors);
            this.clearErrors();
            for (let field in errors) {
                for (let i = 0; i < errors[field].length; i++) {
                    this.error_messages.push(errors[field][i]);
                }
            }
        },
        clearErrors: function () {
            this.error_messages = [];
        },
        uploadFilledTemplate: function () {
            this.loading = true;
            let formData = new FormData();
            formData.append('filled_template', this.filled_template);
            formData.append('file_name', this.file_name);formData.append('survey_id', this.survey.id);

            axios.post(this.api_url, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response=>{
                    this.file = response.data.file;
                    this.failures = response.data.failures;
                    this.preview = true;
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Upload Success!', message:this.file_name+" was uploaded successfully."});
                    this.resetFile();
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        checkFile: function () {
            let validExts = [".xlsx", ".xls", ".csv"];
            let fileExt = this.$refs.filledTemplate.value;
            fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

            if (validExts.indexOf(fileExt) < 0) {
                new Noty({
                    type: "error",
                    text: "Invalid file selected, valid files are of " + validExts.toString() + " types."
                }).show();

                this.resetFile();
                return false;
            }

            let fileName = this.$refs.filledTemplate.value;
            fileName = fileName.substring(fileName.lastIndexOf('\\')+1);
            this.file_name = fileName;
            $('#filledUgandanTeachersTemplateLabel').text(fileName)

            this.valid_file = true;
            this.filled_template = this.$refs.filledTemplate.files[0];

            return true;
        },
        resetFile: function () {
            this.file_name = null;
            this.filled_template = '';
            this.valid_file = false;
            this.loading = false;
            this.$refs.filledTemplate.value = null;
            window.setTimeout(()=>{$('#filledUgandanTeachersTemplateLabel').text("Upload Filled Excel Template")}, 10);
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
