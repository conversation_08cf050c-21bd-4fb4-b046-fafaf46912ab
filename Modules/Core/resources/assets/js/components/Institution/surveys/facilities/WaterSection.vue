<template>
    <div class="w-100">
        <success-notifications ref="notifySuccess"></success-notifications>
        <h4>Main Sources Of Water</h4>
        <div class="table-responsive">
            <table class="table table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="3" class="text-uppercase border-secondary text-white">SECTION E.4: MAIN SOURCE OF WATER</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">MAIN WATER PURPOSE</th>
                    <th class="text-uppercase border-secondary text-dark">MAIN WATER SOURCE TYPE</th>
                    <th class="text-uppercase border-secondary text-dark">DISTANCE TO MAIN SOURCE OF WATER</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td class="bg-secondary-dim text-uppercase border-secondary text-dark">MAIN SOURCE OF DRINKING WATER</td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span v-if="water_sources.drinking_water_source_id === ''" class="font-italic text-muted">NOT SET</span>
                        <span v-else>{{ getWaterSourceType(water_sources.drinking_water_source_id).name }}</span>
                    </td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span v-if="water_sources.drinking_water_source_distance_id === ''" class="font-italic text-muted">NOT SET</span>
                        <span v-else>{{ getWaterSourceDistanceRange(water_sources.drinking_water_source_distance_id).name }}</span>
                    </td>
                </tr>
                <tr>
                    <td class="bg-secondary-dim text-uppercase border-secondary text-dark">MAIN SOURCE OF WATER FOR OTHER PURPOSES</td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span v-if="water_sources.water_source_other_purposes_id === ''" class="font-italic text-muted">NOT SET</span>
                        <span v-else>{{ getWaterSourceType(water_sources.water_source_other_purposes_id).name }}</span>
                    </td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span v-if="water_sources.water_source_other_purposes_distance_id === ''" class="font-italic text-muted">NOT SET</span>
                        <span v-else>{{ getWaterSourceDistanceRange(water_sources.water_source_other_purposes_distance_id).name }}</span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button @click="editWaterSources()" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">UPDATE MAIN SOURCES OF WATER INFORMATION</span>
            </button>
        </div>

        <div class="modal fade zoom" tabindex="-1" id="schoolWaterSourcesModal">
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetWaterSources()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="updateWaterSources()">
                        <div class="modal-header">
                            <h6 class="modal-title">UPDATE MAIN SOURCE OF WATER</h6>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row g-4">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="drinkingWaterSourceId" class="form-label">Main Source Of Drinking Water<span class="text-danger">*</span></label>
                                        <select required id="drinkingWaterSourceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="source_type in water_source_types" :value="source_type.id">{{ source_type.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="drinkingWaterSourceDistanceId" class="form-label">Distance to Main Source Of Drinking Water<span class="text-danger">*</span></label>
                                        <select required id="drinkingWaterSourceDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="source_distance in water_source_distances" :value="source_distance.id">{{ source_distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->
                            <hr class="border-dark-teal my-4">
                            <div class="row g-4">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="otherPurposeWaterSourceId" class="form-label">Main Source Of Water For Other Purpose<span class="text-danger">*</span></label>
                                        <select required id="otherPurposeWaterSourceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="source_type in water_source_types" :value="source_type.id">{{ source_type.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="otherPurposeWaterSourceDistanceId" class="form-label">Distance to Main Source Of Water For Other Purpose<span class="text-danger">*</span></label>
                                        <select required id="otherPurposeWaterSourceDistanceId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="source_distance in water_source_distances" :value="source_distance.id">{{ source_distance.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetWaterSources()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Loading...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ErrorNotifications from "../../../Notifications.vue";
import SuccessNotifications from "../../../Notifications.vue";
export default {
    name: "WaterSection",
    props: [
        'sectionId',
        'schoolObj',
        'surveyObj',
        'schoolWaterSourcesObj',
        'waterSourceTypesObj',
        'waterSourceDistanceRangesObj',
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/water',
            school: {water_sources: []},
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            water_source_types: [],
            water_source_distances: [],
            form_water_sources: {
                id: '',
                section_id: '',
                drinking_water_source_id: '',
                water_source_other_purposes_id: '',
                drinking_water_source_distance_id: '',
                water_source_other_purposes_distance_id: '',
            },
            water_sources: {
                id: '',
                section_id: '',
                drinking_water_source_id: '',
                water_source_other_purposes_id: '',
                drinking_water_source_distance_id: '',
                water_source_other_purposes_distance_id: '',
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;

            this.school = this.schoolObj;
            this.survey = this.surveyObj;
            this.water_source_types = this.waterSourceTypesObj;
            this.water_source_distances = this.waterSourceDistanceRangesObj;
            this.form_water_sources.section_id = this.sectionId;

            $('#drinkingWaterSourceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolWaterSourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_water_sources.drinking_water_source_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#drinkingWaterSourceDistanceId').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#schoolWaterSourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_water_sources.drinking_water_source_distance_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#otherPurposeWaterSourceId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#schoolWaterSourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_water_sources.water_source_other_purposes_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#otherPurposeWaterSourceDistanceId').select2({
                minimumResultsForSearch: Infinity,
                dropdownParent: $('#schoolWaterSourcesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_water_sources.water_source_other_purposes_distance_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

                this.water_sources = this.schoolWaterSourcesObj;

                window.setTimeout(()=>{
                    $('#drinkingWaterSourceId').val(self.water_sources.drinking_water_source_id).change();
                    $('#drinkingWaterSourceDistanceId').val(self.water_sources.drinking_water_source_distance_id).change();
                    $('#otherPurposeWaterSourceId').val(self.water_sources.water_source_other_purposes_id).change();
                    $('#otherPurposeWaterSourceDistanceId').val(self.water_sources.water_source_other_purposes_distance_id).change();
                }, 50);

        },
        getWaterSourceType: function (source_id) {
            return this.water_source_types.find(water_source_type => {
                return water_source_type.id === source_id;
            })
        },
        getWaterSourceDistanceRange: function (range_id) {
            return this.water_source_distances.find(water_source_distance_range => {
                return water_source_distance_range.id === range_id;
            })
        },
        editWaterSources: function () {
            this.resetWaterSources();
            $('#schoolWaterSourcesModal').modal({backdrop: "static"});
        },
        updateWaterSources: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.survey.id, this.form_water_sources)
                .then(response=>{
                    this.water_sources = response.data;
                    this.$parent.water_sources = response.data;
                    this.resetWaterSources();
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success', message:"Main Sources Of Water Updated Successfully."});
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    this.renderError(error);
                });
        },
        resetWaterSources: function () {
            $('#schoolWaterSourcesModal').modal('hide');
            this.loading = false;

            window.setTimeout(()=>{
                $('#drinkingWaterSourceId').val(this.water_sources.drinking_water_source_id).change();
                $('#drinkingWaterSourceDistanceId').val(this.water_sources.drinking_water_source_distance_id).change();
                $('#otherPurposeWaterSourceId').val(this.water_sources.water_source_other_purposes_id).change();
                $('#otherPurposeWaterSourceDistanceId').val(this.water_sources.water_source_other_purposes_distance_id).change();
            }, 50);

            this.form_water_sources.section_id = this.sectionId;
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {},
}
</script>

<style scoped>

</style>
