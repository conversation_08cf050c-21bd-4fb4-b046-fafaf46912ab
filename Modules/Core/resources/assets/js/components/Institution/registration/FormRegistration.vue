<template>
    <div class="nk-content nk-content-lg nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="kyc-app wide-md m-auto">
                        <div class="nk-block-head nk-block-head-lg wide-xs mx-auto">
                            <div class="nk-block-head-content text-center">
                                <h2 class="nk-block-title fw-normal">EMIS USER ACCOUNT FOR INSTITUTIONS</h2>
                                <div class="nk-block-des">
                                    <p>Schools and other Institutions require a user account
                                        in order to access or upload information for their
                                        institution on EMIS Portal.</p>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                        <div class="nk-block">
                            <notifications ref="notify"></notifications>
                            <div class="card card-bordered border-dark-teal">
                                <div class="card-body p-0">
                                    <ul class="nav nav-tabs nav-tabs-s2 justify-content-start py-2 px-4 border-bottom">
                                        <li class="nav-item flex-fill">
                                            <a :class="[current_section.location ? 'active' : 'disabled text-disabled', 'nav-link py-1']" data-toggle="tab" href="#institutionLocationTab">Step 1: LOCATION</a>
                                        </li>
                                        <li class="nav-item flex-fill">
                                            <a :class="[current_section.ownership ? 'active' : 'disabled text-disabled', 'nav-link py-1']" data-toggle="tab" href="#institutionOwnershipAndOperationsTab">Step 2: OWNERSHIP</a>
                                        </li>
                                        <li class="nav-item flex-fill">
                                            <a :class="[current_section.institution ? 'active' : 'disabled text-disabled', 'nav-link py-1']" data-toggle="tab" href="#institutionContactsTab">Step 3: INSTITUTION CONTACTS</a>
                                        </li>
                                        <li class="nav-item flex-fill">
                                            <a :class="[current_section.contact_person ? 'active' : 'disabled text-disabled', 'nav-link py-1']" data-toggle="tab" href="#contactPersonsDetailsTab">Step 4: CONTACT PERSON DETAILS</a>
                                        </li>
                                    </ul>
                                    <div class="tab-content card-inner nk-kycfm-content mt-0">
                                        <div v-if="!current_section.contact_person" class="nk-kycfm-note">
                                            <em class="icon ni ni-info-fill"></em>
                                            <p>Please check the details to see if they match with those of your institution</p>
                                        </div>
                                        <div :class="[current_section.location ? 'active' : '', 'tab-pane']" id="institutionLocationTab">
                                            <form @submit.prevent="saveInstitutionLocation()">
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution Type <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the type  of your institution.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="school_type_id" class="form-control" required="">
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="school_type in institution_types" :value="school_type.id">{{ school_type.display_name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
<!--                                                <div v-show="application.school_type_id === 7" class="row g-4 mb-3">-->
<!--                                                    <div class="col-lg-5">-->
<!--                                                        <div class="form-group">-->
<!--                                                            <label class="form-label" for="curriculum">Institution Curriculum <span class="text-danger">*</span></label>-->
<!--                                                            <span class="form-note">Select the curriculum that your institution follows.</span>-->
<!--                                                        </div>-->
<!--                                                    </div>-->
<!--                                                    <div class="col-lg-7">-->
<!--                                                        <div class="form-group">-->
<!--                                                            <div class="form-control-wrap">-->
<!--                                                                <select id="curriculum" class="form-control">-->
<!--                                                                    <option value="">&#45;&#45;SELECT&#45;&#45;</option>-->
<!--                                                                    <option value="1">INTERNATIONAL CURRICULAR</option>-->
<!--                                                                </select>-->
<!--                                                            </div>-->
<!--                                                        </div>-->
<!--                                                    </div>-->
<!--                                                </div>-->
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution Name <span class="text-danger">*</span></label>
                                                            <span class="form-note">Enter the registered name of your school.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <input v-model.trim="application.school_name" type="text" placeholder="Enter Institution Name" class="text-uppercase form-control bg-primary-dim" required id="school-name">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution District <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the District of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="district_id" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution County <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the County of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="county_id" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution Sub County <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the Sub County of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="sub_county_id" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Institution Parish <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the Parish of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="parish_id" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                                        <span v-if="loading" class="sr-only">Saving...</span>
                                                        <span v-if="!loading" class="align-self-center">Next</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div :class="[current_section.ownership ? 'active' : '', 'tab-pane']" id="institutionOwnershipAndOperationsTab">
                                            <form @submit.prevent="saveInstitutionOwnershipAndOperations()">
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Ownership Status <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select Ownership status of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="school_ownership_status_id" class="form-control ucap" :disabled="application.school_type_id === 1 || application.school_type_id === 7" required>
                                                                    <option value="">--SELECT--</option>
                                                                    <option
                                                                        v-for="ownership_status in ownership_statuses" :value="ownership_status.id">{{ ownership_status.name }}
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Year Founded <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the year your institution was founded.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="institution_year_founded" class="form-control" required>
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="year in foundingYears" :value="year">{{ year }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Founding Body <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select founding body of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select id="institution_founding_body_id" class="form-control" required>
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="founding_body in founding_bodies" :disabled="founding_body.name.toUpperCase() === 'GOVERNMENT' && disableGovernmentOption(application.school_ownership_status_id)" v-if="application.school_type_id !== 1 || founding_body.name.toLowerCase().indexOf(search.toLowerCase()) === -1" :value="founding_body.id">{{ founding_body.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div v-show="application.school_type_id !== 6" class="row g-4 mb-3">
                                                    <div class="col-lg-5 mt-lg-3">
                                                        <div v-show="application.school_type_id !== 6" class="form-group">
                                                            <label class="form-label" for="school-name">Sex Composition <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select sex composition  of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div v-show="application.school_type_id !== 6" class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select :required="application.school_type_id !== 6" id="schoolGenderCategory" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                    <option value="has_male_students">MALES ONLY</option>
                                                                    <option value="has_female_students">FEMALES ONLY</option>
                                                                    <option value="has_both">MIXED</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="application.school_type_id !== 6" class="row g-4 mb-3">
                                                    <div v-show="application.school_type_id !== 6" class="col-lg-5 mt-lg-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="school-name">Boarding Status <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select boarding status of your application.</span>
                                                        </div>
                                                    </div>
                                                    <div v-show="application.school_type_id !== 6" class="col-lg-7 mt-lg-3">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <select :required="application.school_type_id !== 6" v-model="application.school_boarding_status" id="schoolBoardingStatus" class="form-control">
                                                                    <option value="">--SELECT--</option>
                                                                    <option value="admits_day_scholars_yn">DAY ONLY</option>
                                                                    <option value="admits_boarders_yn">BOARDING ONLY</option>
                                                                    <option value="admits_both">DAY AND BOARDING</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                                        <span v-if="loading" class="sr-only">Saving...</span>
                                                        <span v-if="!loading" class="align-self-center">Next</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div :class="[current_section.institution ? 'active' : '', 'tab-pane']" id="institutionContactsTab">
                                            <form @submit.prevent="requested_otp ? saveInstitutionContacts() : sendVerificationCode()">
                                                <div v-show="!requested_otp && institutionLevel === 'certificate'" :class="[institutionLevel === 'certificate' ? 'mt-4' : '', 'row align-center']">
                                                    <div v-show="!requested_otp && institutionLevel === 'certificate'" class="col-lg-5">
                                                        <div v-show="!requested_otp && institutionLevel === 'certificate'" class="form-group">
                                                            <label class="form-label" for="certificateAwardingSchoolTypeId">Institution Type <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the type of your institution.</span>
                                                        </div>
                                                    </div>
                                                    <div v-show="!requested_otp && institutionLevel === 'certificate'" class="col-lg-7">
                                                        <div v-show="!requested_otp && institutionLevel === 'certificate'" class="form-group">
                                                            <div v-show="!requested_otp && institutionLevel === 'certificate'" class="form-control-wrap">
                                                                <select id="certificateAwardingSchoolTypeId" class="form-control" :required="!requested_otp && institutionLevel === 'certificate'">
                                                                    <option value="">--Select Institution Level--</option>
                                                                    <option v-for="school_type in certificate_awarding_institution_types" :value="school_type.id">{{ school_type.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-show="!requested_otp && institutionLevel === 'diploma'" :class="[institutionLevel === 'diploma' ? 'mt-4' : '', 'row align-center']">
                                                    <div v-show="!requested_otp && institutionLevel === 'diploma'" class="col-lg-5">
                                                        <div v-show="!requested_otp && institutionLevel === 'diploma'" class="form-group">
                                                            <label class="form-label" for="diplomaAwardingSchoolTypeId">Institution Type <span class="text-danger">*</span></label>
                                                            <span class="form-note">Select the type of your institution.</span>
                                                        </div>
                                                    </div>
                                                    <div v-show="!requested_otp && institutionLevel === 'diploma'" class="col-lg-7">
                                                        <div v-show="!requested_otp && institutionLevel === 'diploma'" class="form-group">
                                                            <div v-show="!requested_otp && institutionLevel === 'diploma'" class="form-control-wrap">
                                                                <select id="diplomaAwardingSchoolTypeId" class="form-control" :required="!requested_otp && institutionLevel === 'diploma'">
                                                                    <option value="">--Select Institution Level--</option>
                                                                    <option v-for="school_type in diploma_awarding_institution_types" :value="school_type.id">{{ school_type.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="!requested_otp" class="row my-4 align-center">
                                                    <div class="col-lg-5">
                                                        <div class="form-group">
                                                            <label class="form-label" for="institution-email">Institution Email <span class="text-danger">*</span></label>
                                                            <span class="form-note">Specify the main email address of your institution.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <input v-model.trim="application.email" autocomplete="off" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" id="institution-email" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="!requested_otp" class="row my-4 align-center">
                                                    <div class="col-lg-5">
                                                        <div class="form-group">
                                                            <label class="form-label" for="institution_phone">Institution Mobile Phone <span class="text-danger">*</span></label>
                                                            <span class="form-note">Specify the main phone number of your institution.</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <input v-model.trim="application.phone" autocomplete="off" type="text" placeholder="Enter Phone Number" maxlength="10" class="form-control bg-primary-dim w-100" id="institution_phone" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div v-if="requested_otp" class="row g-4 mb-3">
                                                    <div class="col-lg-5">
                                                        <div class="form-group">
                                                            <label class="form-label" for="verification_code">Verification Code <span class="text-danger">*</span></label>
                                                            <span class="form-note">Enter the 5-digit verification code that was sent to <span class="text-primary">{{ application.email }}</span>.</span>
                                                            <!--															<span class="d-block text-dark mt-2">Copy This Code <span class="text-dark-teal font-weight-bold fx-16">{{ verification_code }}</span></span>-->
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-7">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <input v-model.trim="application.email_code" :disabled="loading" autocomplete="off" placeholder="Enter Verification Code" type="text" class="form-control bg-primary-dim text-uppercase text-center" id="verification_code" required>
                                                            </div>
                                                        </div>
                                                        <span v-show="resend_code" class="text-dark fs-12px">Didn't get the code? <span @click="resendVerifyCode()" class="text-dark-teal cursor form-label" style="text-decoration: underline;">click here</span> to send new code</span>
<!--                                                        <button @click="resendVerifyCode()" :disabled="sending" class="ml-3 btn bg-dark-teal btn-xs" style="float: right">-->
<!--                                                            <span v-if="sending" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                                            <span v-if="sending" class="align-self-center">Resending Code...</span>-->
<!--                                                            <span v-if="sending" class="sr-only">Resending Code...</span>-->
<!--                                                            <span v-if="!sending" class="align-self-center">Resend Code?</span>-->
<!--                                                        </button>-->
                                                    </div>

                                                </div>

                                                <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                    <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                                        <span v-if="loading" class="sr-only">Saving...</span>
                                                        <span v-if="!loading" class="align-self-center">Proceed</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div :class="[current_section.contact_person ? 'active' : '', 'tab-pane']" id="contactPersonsDetailsTab">
                                            <div v-if="current_section.contact_person" class="nk-kycfm-note">
                                                <em class="icon ni ni-info-fill"></em>
                                                <p>Please fill in the form below in order to complete your EMIS number application..</p>
                                            </div>
                                            <div v-show="institutionLevel === 'international'" class="row g-4">
                                                <div class="col-lg-6 d-flex">
                                                    <div class="form-group">
                                                        <div class="form-label-group">
                                                            <label class="form-label">Does this contact person have a NIN?</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 d-flex">
                                                    <div class="form-group">
                                                        <div class="custom-control custom-control-inline custom-radio">
                                                            <input :disabled="verify || loading" type="radio" class="custom-control-input" v-model="contact_person_nin" value="yes" id="personWithNIN">
                                                            <label class="custom-control-label text-uppercase" for="personWithNIN">YES</label>
                                                        </div>
                                                        <div class="custom-control custom-control-inline custom-radio">
                                                            <input :disabled="verify || loading" type="radio" class="custom-control-input" v-model="contact_person_nin" value="no" id="personWithoutNIN">
                                                            <label class="custom-control-label text-uppercase" for="personWithoutNIN">NO</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <form @submit.prevent="verify || contact_person_nin === 'no' ? saveContactPersonsDetails() : (contact_person_nin === 'yes' ? verifyNIN() : verifyPassport())">
                                                <div v-if="!verify" class="row g-4 mt-2">
                                                    <div v-show="contact_person_nin === 'yes'" class="col-md-6">
                                                        <div class="form-group">
                                                            <div class="form-control-wrap">
                                                                <div class="form-text-hint bg-primary-dim">
                                                                    <span class="overline-title">NIN <span class="text-danger">*</span></span>
                                                                </div>
                                                                <input :disabled="verify" required v-model="contact_person.id_number" type="text" class="form-control bg-primary-dim text-uppercase" minlength="14" maxlength="14" placeholder="Enter CONTACT PERSON NIN">
                                                            </div>
                                                        </div>
                                                    </div><!-- .col -->
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <button v-if="contact_person_nin === 'yes'" :disabled="loading" type="submit" class="btn btn-primary btn-block text-center d-flex">
                                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                <span v-if="loading" class="align-self-center">Verifying NIN...</span>
                                                                <span v-if="loading" class="sr-only">Verifying NIN...</span>
                                                                <span v-if="!loading" class="align-self-center">Verify NIN</span>
                                                                <em v-if="!loading" class="icon ni ni-user-fill-c"></em>
                                                            </button>
                                                        </div>
                                                    </div><!-- .col -->
                                                </div>

                                                <div class="row g-4">
                                                    <div v-show="verify && contact_person_nin === 'yes'" class="col-12 col-lg-6">
                                                        <div class="table-responsive py-3">
                                                            <table class="table table-sm table-hover">
                                                                <tr>
                                                                    <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                                        <div class="user-card">
                                                                            <div class="w-150px">
                                                                                <img id="contactPersonAvatar" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                                            </div>
                                                                        </div><!-- .user-card -->
                                                                    </td>
                                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                                        <span class="">{{ nira_person.national_id }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                                        <span class="">{{ nira_person.surname }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                                        <span class="">{{ nira_person.given_names }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                        <span class="">{{ nira_person.gender }}</span>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                                        <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                        <span class="">{{ nira_person.date_of_birth }}</span>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div :class='[contact_person_nin === "yes" ? "col-12 col-lg-6 d-flex" : "col-12 col-lg-12 d-flex"]'>
                                                        <div v-show="verify || contact_person_nin === 'no'" class="row g-4 align-self-center">
                                                            <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label for="contactPersonFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input :required="verify && contact_person_nin === 'no'" v-model.trim="contact_person.first_name" id="contactPersonFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label for="contactPersonSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input :required="verify && contact_person_nin === 'no'" v-model.trim="contact_person.surname" id="contactPersonSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label for="contactPersonOtherNames" class="form-label">Other Names</label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input v-model.trim="contact_person.other_names" id="contactPersonOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-show="contact_person_nin === 'no'" class="col-lg-6 mt-lg-0">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label class="form-label">Gender <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <div class="custom-control custom-control-inline custom-radio">
                                                                            <input type="radio" class="custom-control-input" v-model.number="contact_person.gender" value="M" id="contactPersonMale">
                                                                            <label class="custom-control-label text-uppercase" for="contactPersonMale">Male</label>
                                                                        </div>
                                                                        <div class="custom-control custom-control-inline custom-radio">
                                                                            <input type="radio" class="custom-control-input" v-model.number="contact_person.gender" value="F" id="contactPersonFemale">
                                                                            <label class="custom-control-label text-uppercase" for="contactPersonFemale">Female</label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div :class='[contact_person_nin === "yes" ? "col-lg-12" : "col-6"]'>
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label for="contact_person_email" class="form-label">Email <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input :required="verify" v-model="contact_person.email" id="contact_person_email" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim">
                                                                    </div>
                                                                </div>
                                                            </div><!-- .col -->
                                                            <div :class='[contact_person_nin === "yes" ? "col-lg-12" : "col-6"]'>
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label for="contact_person_phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input v-model.trim="contact_person.phone" autocomplete="off" type="text" placeholder="Enter Phone Number" maxlength="10" class="form-control bg-primary-dim" id="contact_person_phone" :required="verify">
                                                                    </div>
                                                                </div>
                                                            </div><!-- .col -->
                                                            <div v-show="contact_person_nin === 'no'" class="col-lg-6">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <select id="userCountryId" class="form-select-sm">
                                                                            <option value="">--SELECT--</option>
                                                                            <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div v-show="contact_person_nin === 'no'" class="col-lg-6">
                                                                <div class="form-group">
                                                                    <div class="form-label-group">
                                                                        <label class="form-label">Passport <span class="text-danger">*</span></label>
                                                                    </div>
                                                                    <div class="form-control-group">
                                                                        <input v-model.trim="contact_person.id_number" id="contactPersonPassport" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12">
                                                                <div class="custom-control custom-control-xs custom-checkbox">
                                                                    <input :required="verify" type="checkbox" class="custom-control-input" id="tc-agree">
                                                                    <label class="custom-control-label" for="tc-agree">I Have Read The <a target="_blank" href="/terms-and-conditions">Terms Of Condition</a> And <a target="_blank" href="/privacy-policy">Privacy Policy</a></label>
                                                                </div>
                                                            </div><!-- .col -->
                                                            <div class="col-12">
                                                                <div class="custom-control custom-control-xs custom-checkbox">
                                                                    <input :required="verify" type="checkbox" class="custom-control-input" id="info-assure">
                                                                    <label class="custom-control-label" for="info-assure">All The Personal Information I Have Entered Is Correct.</label>
                                                                </div>
                                                            </div><!-- .col -->
                                                        </div><!-- .row -->
                                                    </div>
                                                </div><!-- .row -->
                                                <div v-show="verify || contact_person_nin === 'no'" class="nk-kycfm-action pt-5 row">
                                                    <div class="col-lg-6">
                                                        <button @click="resetContactPerson()" v-if="verify || contact_person_nin === 'no'" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                                            <span class="align-self-center text-uppercase">CANCEL AND TRY AGAIN</span>
                                                        </button>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <button :disabled="loading" type="submit" class="btn btn-primary text-center btn-block d-flex">
                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            <span v-if="loading" class="align-self-center">Saving...</span>
                                                            <span v-if="loading" class="sr-only">Loading...</span>
                                                            <span v-if="!loading" class="align-self-center text-uppercase">Complete Registration</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                    </div><!-- .kyc-app -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import OtpTimer from "../OtpTimer.vue";
import CSPMixin from "../../../mixins/CSPMixin";


export default {
    name: "FormRegistration",
    props: [
        'countriesObj',
        'districtsObj',
        'institutionTypesObj',
        'operationModesObj',
        'boardingStatusesObj',
        'foundingBodiesObj',
        'foundersObj',
        'ownershipStatusesObj',
        'certificateAwardingInstitutionTypesObj',
        'diplomaAwardingInstitutionTypesObj'
    ],
    mixins:[CSPMixin],
    components: {
        Notifications
    },
    mounted() {
        this.initCSPMixin(null, '#district_id','#county_id','#sub_county_id','#parish_id', this.application);
        this.initPlugins();
    },
    data: function () {
        return {
            search: 'Government',
            loading: false,
            resend_code: false,
            sending: false,
            verify: false,
            contact_person_nin: 'yes',
            requested_otp: false,
            institution_types: [],
            operation_modes: [],
            boarding_statuses: [],
            ownership_statuses: [],
            founding_bodies: [],
            founders: [],
            countries: [],
            districts: [],
            counties: [],
            sub_counties: [],
            parishes: [],
            villages: [],
            certificate_awarding_institution_types: [],
            diploma_awarding_institution_types: [],
            current_section: {
                location: true,
                ownership: false,
                institution: false,
                contact_person: false,
            },
            application: {
                is_international_school: '',
                school_type_id: '', // School type
                certificate_awarding_school_type_id: '', // Certificate Awarding Institution type
                diploma_awarding_school_type_id: '', // Diploma Awarding Institution type
                id: '',
                school_name: '',
                email: '',
                phone: '',
                email_code: '',
                region_id: '',
                district_id: '',
                local_government_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
                school_ownership_status_id: '', //Ownership status
                year_founded: Number(moment().format('YYYY')),
                founding_body_id: '', //founding body
                school_gender_category: '', //Gender composition
                school_boarding_status: '', // Boarding status
            },
            contact_person: {
                id_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                gender: 'M',
                email: '',
                phone: '',
                school_email: '',
                country_id: '',
            },
            verification_code: '',
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            console.clear();
            let self = this;

            this.countries = this.countriesObj;
            this.districts = this.districtsObj;
            this.institution_types = this.institutionTypesObj;
            this.operation_modes = this.operationModesObj;
            this.boarding_statuses = this.boardingStatusesObj;
            this.founding_bodies = this.foundingBodiesObj;
            this.ownership_statuses = this.ownershipStatusesObj;
            this.certificate_awarding_institution_types = this.certificateAwardingInstitutionTypesObj;
            this.diploma_awarding_institution_types = this.diplomaAwardingInstitutionTypesObj;

            $('#school_type_id').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    //Check if school type is pre-primary or international set ownership to private
                    if (self.application.school_type_id === 7 || self.application.school_type_id === 1) {
                        self.setOwnershipType();
                    }
                    return data.text;
                },
            });

            $('#school_ownership_status_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.school_ownership_status_id = data.id !== "" ? Number(data.id) : data.id;
                    self.disableGovernmentOption(self.application.school_ownership_status_id);
                    self.handleOwnershipStatusChange(self.application.school_ownership_status_id);
                    return data.text;
                },
            });
            // $('#curriculum').select2({
            //     minimumResultsForSearch: Infinity,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.application.is_international_school = data.id.length > 0 ? Number(data.id) : "";
            //         if (data.text.toUpperCase().includes('INTERNATIONAL')) {
            //             let status = self.ownership_statuses.find(status => {
            //                 return status.name.toUpperCase().includes('PRIVATE');
            //             });
            //
            //             if (status !== undefined) {
            //                 $('#school_ownership_status_id').val(status.id).change();
            //             } else {
            //                 $('#school_ownership_status_id').val('').change();
            //             }
            //
            //             // window.setTimeout(()=>{
            //             //     $('#school_type_id').val('').change();
            //             //     self.application.school_type_id = '';
            //             // }, 100);
            //         }
            //         return data.text;
            //     },
            // });
            // $('#curriculum').val('').change();

            // $('#district_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.application.district_id = data.id.length > 0 ? Number(data.id) : "";
            //         if (data.id.length) {
            //             let district = self.districts.find(district => {
            //                 return district.id === Number(data.id);
            //             });

            //             self.application.region_id = district !== undefined ? district.region_id : '';
            //         }
            //         self.loadCounties();
            //         return data.text;
            //     },
            // });

            // $('#county_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.application.county_id = data.id.length > 0 ? Number(data.id) : "";
            //         if (data.id.length) {
            //             let county = self.counties.find(county => {
            //                 return county.id === Number(data.id);
            //             });
            //             console.log(county);
            //             self.application.local_government_id = county !== undefined ? county.local_government_id : '';
            //         }
            //         self.loadSubCounties();
            //         return data.text;
            //     },
            // });

            // $('#sub_county_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.application.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
            //         self.loadParishes();
            //         return data.text;
            //     },
            // });

            // $('#parish_id').select2({
            //     minimumResultsForSearch: 0,
            //     containerCssClass: 'bg-primary-dim',
            //     templateSelection: function (data, container) {
            //         self.application.parish_id = data.id.length > 0 ? Number(data.id) : "";
            //         return data.text;
            //     },
            // });

            $('#institution_year_founded').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.year_founded = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#institution_founding_body_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.founding_body_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#schoolGenderCategory').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.school_gender_category = data.id;
                    return data.text;
                },
            });

            $('#schoolBoardingStatus').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.application.school_boarding_status = data.id;
                    return data.text;
                },
            });

            $('#certificateAwardingSchoolTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.application.certificate_awarding_school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#diplomaAwardingSchoolTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.application.diploma_awarding_school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#userCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.contact_person.country_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            let institution_phone = document.querySelector('#institution_phone');
            let iti_institution_phone = intlTelInput(institution_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            institution_phone.addEventListener('blur', ()=>{
                self.application.phone = iti_institution_phone.getNumber().slice(-9);
                institution_phone.value = iti_institution_phone.getNumber().slice(-9);
            });
            institution_phone.addEventListener('change', ()=>{
                self.application.phone = iti_institution_phone.getNumber().slice(-9);
                institution_phone.value = iti_institution_phone.getNumber().slice(-9);
            });

            let contact_person_phone = document.querySelector('#contact_person_phone');
            let iti_contact_person_phone = intlTelInput(contact_person_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            contact_person_phone.addEventListener('blur', ()=>{
                self.contact_person.phone = iti_contact_person_phone.getNumber().slice(-9);
                contact_person_phone.value = iti_contact_person_phone.getNumber().slice(-9);
            });
            contact_person_phone.addEventListener('change', ()=>{
                self.contact_person.phone = iti_contact_person_phone.getNumber().slice(-9);
                contact_person_phone.value = iti_contact_person_phone.getNumber().slice(-9);
            });
        },

        //set ownership if the school type is pre-primary or international
        setOwnershipType: function (){
            window.setTimeout(() => {
                let ownership = this.ownership_statuses.find(ownership_status => {
                    return ownership_status.name.toUpperCase().includes('PRIVATE');
                });
                $('#school_ownership_status_id').val(ownership.id).change();
            }, 50);
        },
        disableGovernmentOption: function(ownershipStatusId) {
            // Return true if ownership_id is 2 (PRIVATE) and founding body is GOVERNMENT
            return ownershipStatusId === 2;
        },
        handleOwnershipStatusChange: function (ownershipStatusId) {
            if (ownershipStatusId === 2) {
                // Clear the founding body dropdown value
                $('#institution_founding_body_id').val(null).trigger('change');
            }
        },
        //verify contact person NIN
        verifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.contact_person.id_number.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.nira_person = response.data;
                    this.contact_person.first_name = this.nira_person.given_names;
                    this.contact_person.surname = this.nira_person.surname;
                    this.contact_person.other_names = this.nira_person.other_names;
                    this.contact_person.gender = this.nira_person.gender;
                    this.verify = true;
                    if (this.nira_person.photo !== null) {
                        if (this.nira_person.photo.includes('.png')) {
                            $('#contactPersonAvatar').attr('src', '/images/nira-photos/' + this.nira_person.photo);
                        } else {
                            $('#contactPersonAvatar').attr('src', 'data:image/png;base64,' + this.nira_person.photo);
                        }
                    }
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        //Verify passport
        verifyPassport: function () {
            this.loading = true;
            window.setTimeout(() => {
                this.loading = false;
                this.verify = true;
            }, 1000);
        },
        saveInstitutionLocation: function () {
            this.current_section = {
                location: false,
                ownership: true,
                institution: false,
                contact_person: false,
            };
        },
        saveInstitutionOwnershipAndOperations: function () {
            this.current_section = {
                location: false,
                ownership: false,
                institution: true,
                contact_person: false,
            };
            //Resend code show
            this.resend_code = false;
            if (this.current_section.institution === true) {
                window.setTimeout(()=>{
                    this.resend_code = true;
                }, 30000);
            }
        },
        // Send verification code
        sendVerificationCode: function () {
            this.loading = true;
            axios.post('/institutions/send-verify-code/apply', this.application)
                .then(response=>{
                    this.verification_code = response.data;
                    this.requested_otp = true;
                    this.loading = false;
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"Verification code sent to "+this.application.email });
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error);
                })
        },
        // Resend verification code
        resendVerifyCode: function () {
            this.application.email_code = '';
            this.sending = true;
            this.resend_code = false;

            axios.post('/institutions/send-verify-code/apply', this.application)
                .then(response=>{
                    this.verification_code = response.data;
                    this.requested_otp = true;
                    this.sending = false;
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"Verification code sent to "+this.application.email });
                    if (this.current_section.institution) {
                        window.setTimeout(()=>{
                            this.resend_code = true;
                        }, 30000);
                    }
                })
                .catch(error=>{
                    this.sending = false;
                    this.renderError(error);
                })
        },
        saveInstitutionContacts: function () {
            if (this.application.email_code === this.verification_code) {
                this.$refs.notify.messages.push({status: 'success', title: 'Success', message:this.application.email + " was verified successfully"});
                this.current_section = {
                    location: false,
                    ownership: false,
                    institution: false,
                    contact_person: true,
                };
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Error', message: "You have entered a wrong code!!! Try again."});
            }
        },
        resetContactPerson: function () {
            this.loading = false;
            this.verify = false;
            this.contact_person_nin = 'yes';
            this.contact_person.id_number = '';
            this.contact_person.first_name = '';
            this.contact_person.surname = '';
            this.contact_person.other_names = '';
            $('#userCountryId').val('').change();
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            }
            $('#contactPersonAvatar').attr('src', '@images/default_male.jpg');
        },
        saveContactPersonsDetails: function () {
            if (this.application.email.trim().toLowerCase() === this.contact_person.email.trim().toLowerCase()) {
                this.$refs.notify.messages.push({status: 'error', title: 'Error', message: "Contact person email should be different from Institution email!"});
            } else {
                this.loading = true;
                this.contact_person.school_email = this.application.email.trim();

                axios.post('/institutions/contact-person/apply', this.contact_person, {contact_person_nin: this.contact_person_nin})
                    .then(() => {
                        window.location.href = "/institution/registration-complete";
                    })
                    .catch(error => {
                        this.loading = false;
                        if (error.response.data.errors !== undefined) {
                            for (let field in error.response.data.errors) {
                                this.showError(error.response.data.errors[field]);
                            }
                        } else {
                            this.renderError(error)
                        }
                    });

            }
        },
        // loadCounties: function () {
        //     let self = this;
        //     // clean counties
        //     this.counties = [];
        //     let select = $("#county_id");
        //     select.empty().trigger('change');
        //     let newOption = new Option("--SELECT--", "", false, false);
        //     select.append(newOption).trigger('change');
        //     self.application.county_id = "";

        //     //load new options
        //     if (self.application.district_id !== "") {
        //         self.counties = self.districts.find(district=>{
        //             return district.id === self.application.district_id
        //         }).county;

        //         self.counties.forEach(county=>{
        //             let countyOption = new Option(county.name, county.id, false, false);
        //             select.append(countyOption).trigger('change');
        //         });
        //     }
        // },
        // loadSubCounties: function () {
        //     let self = this;
        //     // clean sub counties
        //     this.sub_counties = [];
        //     let select = $("#sub_county_id");
        //     select.empty().trigger('change');
        //     let newOption = new Option("--SELECT--", "", false, false);
        //     select.append(newOption).trigger('change');
        //     self.application.sub_county_id = "";

        //     //load new options
        //     if (self.application.county_id !== "") {
        //         self.sub_counties = self.counties.find(county=>{
        //             return county.id === self.application.county_id
        //         }).sub_county;

        //         self.sub_counties.forEach(sub_county=>{
        //             let sub_countyOption = new Option(sub_county.name, sub_county.id, false, false);
        //             select.append(sub_countyOption).trigger('change');
        //         });
        //     }
        // },
        // loadParishes: function () {
        //     let self = this;
        //     // clean parishes
        //     this.parishes = [];
        //     let select = $("#parish_id");
        //     select.empty().trigger('change');
        //     let newOption = new Option("--SELECT--", "", false, false);
        //     select.append(newOption).trigger('change');
        //     self.application.parish_id = "";

        //     //load new options
        //     if (self.application.sub_county_id !== "") {
        //         self.parishes = self.sub_counties.find(sub_county=>{
        //             return sub_county.id === self.application.sub_county_id
        //         }).parish;

        //         self.parishes.forEach(parish=>{
        //             let parishOption = new Option(parish.name, parish.id, false, false);
        //             select.append(parishOption).trigger('change');
        //         });
        //     }
        // },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        foundingYears: function () {
            let years = [];
            for (let i = Number(moment().format('YYYY')); i >= 1900; i--) {
                years.push(i);
            }
            return years;
        },
        institutionLevel: function () {
            let level = this.institution_types.find(type=>{
                return type.id === this.application.school_type_id
            });

            return level === undefined ? '' : level.name;
        },
        // disableGovernmentOption() {
        //     return this.application.school_ownership_status_id === 2 ? 'GOVERNMNET' : null;
        // }
    }
}
</script>

<style scoped>

</style>
