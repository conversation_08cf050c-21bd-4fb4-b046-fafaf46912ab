<template>
    <div class="nk-content nk-content-lg nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="kyc-app wide-md m-auto">
                        <div class="nk-block-head nk-block-head-lg wide-xs mx-auto">
                            <div class="nk-block-head-content text-center">
                                <h2 class="nk-block-title fw-normal">EMIS USER ACCOUNT FOR INSTITUTIONS</h2>
                                <div class="nk-block-des">
                                    <p>Schools and other Institutions require a user account
                                        in order to access or upload information for their
                                        institution on EMIS Portal.</p>
                                </div>
                            </div>
                        </div><!-- nk-block -->
                        <div class="nk-block">
                            <notifications ref="notify"></notifications>
                            <div class="card card-bordered border-dark-teal">
                                <div class="py-2 px-4 d-flex flex-row flex-wrap border-bottom">
                                    <button
                                        :style="active_tab === 'emis-number' ? 'border-bottom-width: initial;' : ''"
                                        :class="[active_tab === 'emis-number' ? 'text-primary border-primary' : '' ,'flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none']">
                                        Step 1: VERIFY EMIS NUMBER
                                    </button>
                                    <button
                                        :disabled="completed.old_emis_number === false"
                                        :style="active_tab === 'contact-information' ? 'border-bottom-width: initial;' : ''"
                                        :class="[active_tab === 'contact-information' ? 'text-primary border-primary' : '' ,'flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none']">
                                        Step 2: INSTITUTION EMAIL
                                    </button>
                                    <button
                                        :disabled="completed.old_emis_number === false || completed.contact_info === false"
                                        :style="active_tab === 'email-verification' ? 'border-bottom-width: initial;' : ''"
                                        :class="[active_tab === 'email-verification' ? 'text-primary border-primary' : '' ,'flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none']">
                                        Step 3: VERIFY EMAIL
                                    </button>
                                    <button
                                        :disabled="completed.old_emis_number === false || completed.contact_info === false || completed.email_verified === false"
                                        :style="active_tab === 'contact-person' ? 'border-bottom-width: initial;' : ''"
                                        :class="[active_tab === 'contact-person' ? 'text-primary border-primary' : '' ,'flex-fill rounded-0 btn border-top-0 border-left-0 border-right-0 shadow-none']">
                                        Step 4: CONTACT PERSON DETAILS
                                    </button>
                                </div>
                                <div class="nk-kycfm">
                                    <div v-show="active_tab === 'emis-number'" class="nk-kycfm-content">
                                        <div class="nk-kycfm-note">
                                            <em class="icon ni ni-info-fill"></em>
                                            <p>Please check the details to see if they match with those of your institution</p>
                                        </div>
                                        <form @submit.prevent="completed.old_emis_number ? toggleTabs('contact-information') : verifyEmisNumber()">
                                            <div v-if="completed.old_emis_number" class="row g-4">
                                                <div class="col-md-12">
                                                    <h5 class="title">Institution Details</h5>
                                                    <dl class="row">
                                                        <dt class="col-sm-3">Institution Name:</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.name !== null ? institution.name : "" }}</dd>

                                                        <dt class="col-sm-3">EMIS Number:</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.old_emis_number !== null ? institution.old_emis_number : (institution.emis_number !== null ? institution.emis_number.substring(2, 8) : "") }}</dd>

                                                        <dt class="col-sm-3">District</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.district !== null ? institution.district.name.toUpperCase() : "" }}</dd>

                                                        <dt class="col-sm-3">County</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.county !== null ? institution.county.name.toUpperCase() : "" }}</dd>

                                                        <dt class="col-sm-3">Sub County</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.sub_county !== null ? institution.sub_county.name.toUpperCase() : "" }}</dd>

                                                        <dt class="col-sm-3">Parish</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.parish !== null ? institution.parish.name.toUpperCase() : "" }}</dd>

                                                        <dt class="col-sm-3">Institution Level</dt>
                                                        <dd class="col-sm-9 ucap">{{ institution.school_type !== null ? institution.school_type.display_name.toUpperCase() : "" }}</dd>

                                                    </dl>
                                                    <!--									<span class="text-primary ff-italic fs-15px">Are the above details correct for your institution ?</span>-->
                                                </div><!-- .col -->
                                            </div><!-- .row -->
                                            <div v-if="completed.old_emis_number === false" class="row g-3 align-center">
                                                <div class="col-lg-5">
                                                    <div class="form-group">
                                                        <label class="form-label" for="school_type_id">Institution Type <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the type of your institution.</span>
                                                    </div>
                                                </div>
                                                <div class="col-lg-7">
                                                    <div class="form-group">
                                                        <div class="form-control-wrap">
                                                            <div class="form-control-wrap">
                                                                <select id="school_type_id" class="form-control" required>
                                                                    <option value="">--Select Institution Type--</option>
                                                                    <option v-for="school_type in institution_types" :value="school_type.id">{{ school_type.display_name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-5 mt-lg-3">
                                                    <div class="form-group">
                                                        <label class="form-label" for="institution-emis-number">Institution EMIS Number <span class="text-danger">*</span></label>
                                                        <span class="form-note">Specify the EMIS number of your institution.</span>
                                                    </div>
                                                </div>
                                                <div class="col-lg-7 mt-lg-3">
                                                    <div class="form-group text-center">
                                                        <div class="form-control-wrap">
                                                            <input v-model.trim="institution.old_emis_number" placeholder="eg. 4651358" autocomplete="off" type="text" class="form-control bg-primary-dim text-center" id="institution-emis-number" required>
                                                        </div>
                                                        <a href="/emis/search" target="_blank" class="form-label d-block text-dark-teal mt-3">
                                                            To find your EMIS Number <span style="text-decoration: underline" class="text-uppercase">click here</span> and search
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                <button @click="resetTab('old_emis_number')" v-if="completed.old_emis_number" :disabled="back" type="button" class="btn btn-secondary mr-2 d-flex">
                                                    <span v-if="back" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="back" class="align-self-center">Wait...</span>
                                                    <span v-if="back" class="sr-only">Wait...</span>
                                                    <em v-if="!back" class="ni ni-arrow-left mr-2"></em><span v-if="!back" class="align-self-center">Back</span>
                                                </button>
                                                <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Verifying...</span>
                                                    <span v-if="loading" class="sr-only">Loading...</span>
                                                    <span v-if="!loading" class="align-self-center"><span v-if="completed.old_emis_number"></span>Proceed</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </form>
                                        <hr class="my-4">
                                        <div class="d-flex justify-content-center">
                                            <div class="align-self-center">
                                                Don't have an EMIS number?
                                                <a href="/institution/register-without-emis-number" class="form-label text-dark-teal">
                                                    <span style="text-decoration: underline" class="text-uppercase">Click here</span>
                                                </a> to apply for one.
                                            </div>
                                        </div>
                                    </div><!-- nk-kycfm-content -->
                                    <div v-show="active_tab === 'contact-information'" class="nk-kycfm-content">
                                        <div class="nk-kycfm-note">
                                            <em class="icon ni ni-info-fill"></em>
                                            <p>Check details below before you submit.</p>
                                        </div>
                                        <form @submit.prevent="completed.contact_info ? toggleTabs('email-verification') : sendVerifyCode()">
                                            <div v-show="institutionLevel === 'certificate'" :class="[institutionLevel === 'certificate' ? 'mt-4' : '', 'row align-center']">
                                                <div v-show="institutionLevel === 'certificate'" class="col-lg-5">
                                                    <div v-show="institutionLevel === 'certificate'" class="form-group">
                                                        <label class="form-label" for="certificateAwardingSchoolTypeId">Institution Type <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the type of your institution.</span>
                                                    </div>
                                                </div>
                                                <div v-show="institutionLevel === 'certificate'" class="col-lg-7">
                                                    <div v-show="institutionLevel === 'certificate'" class="form-group">
                                                        <div v-show="institutionLevel === 'certificate'" class="form-control-wrap">
                                                            <select id="certificateAwardingSchoolTypeId" class="form-control" :required="institutionLevel === 'certificate'">
                                                                <option value="">--Select Institution Level--</option>
                                                                <option v-for="school_type in certificate_awarding_institution_types" :value="school_type.id">{{ school_type.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-show="institutionLevel === 'diploma'" :class="[institutionLevel === 'diploma' ? 'mt-4' : '', 'row align-center']">
                                                <div v-show="institutionLevel === 'diploma'" class="col-lg-5">
                                                    <div v-show="institutionLevel === 'diploma'" class="form-group">
                                                        <label class="form-label" for="diplomaAwardingSchoolTypeId">Institution Type <span class="text-danger">*</span></label>
                                                        <span class="form-note">Select the type of your institution.</span>
                                                    </div>
                                                </div>
                                                <div v-show="institutionLevel === 'diploma'" class="col-lg-7">
                                                    <div v-show="institutionLevel === 'diploma'" class="form-group">
                                                        <div v-show="institutionLevel === 'diploma'" class="form-control-wrap">
                                                            <select id="diplomaAwardingSchoolTypeId" class="form-control" :required="institutionLevel === 'diploma'">
                                                                <option value="">--Select Institution Level--</option>
                                                                <option v-for="school_type in diploma_awarding_institution_types" :value="school_type.id">{{ school_type.name.toUpperCase() }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row my-4 align-center">
                                                <div class="col-lg-5">
                                                    <div class="form-group">
                                                        <label class="form-label" for="institution-email">Institution Email <span class="text-danger">*</span></label>
                                                        <span class="form-note">Specify the main email address of your institution.</span>
                                                    </div>
                                                </div>
                                                <div class="col-lg-7">
                                                    <div class="form-group">
                                                        <div class="form-control-wrap">
                                                            <input v-model.trim="institution.email" autocomplete="off" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" id="institution-email" required>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row my-4 align-center">
                                                <div class="col-lg-5">
                                                    <div class="form-group">
                                                        <label class="form-label" for="institution_phone">Institution Mobile Phone <span class="text-danger">*</span></label>
                                                        <span class="form-note">Specify the main phone number of your institution.</span>
                                                    </div>
                                                </div>
                                                <div class="col-lg-7">
                                                    <div class="form-group">
                                                        <div class="form-control-wrap">
                                                            <input v-model.trim="institution.phone" autocomplete="off" type="text" placeholder="Enter Phone Number" maxlength="10" class="form-control bg-primary-dim w-100" id="institution_phone" required>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                <!--                                                <button @click="toggleTabs('emis-number')" type="button" class="btn btn-primary mr-2 d-flex">-->
                                                <!--                                                    <em class="ni ni-arrow-left mr-2"></em><span class="align-self-center">Back</span>-->
                                                <!--                                                </button>-->
                                                <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Updating...</span>
                                                    <span v-if="loading" class="sr-only">Loading...</span>
                                                    <span v-if="!loading" class="align-self-center">Proceed</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </form>
                                    </div><!-- nk-kycfm-content -->
                                    <div v-show="active_tab === 'email-verification'" class="nk-kycfm-content">
                                        <form @submit.prevent="completed.email_verified ? toggleTabs('contact-person') : saveInstitutionContacts()">
                                            <div class="row g-3 align-center">
                                                <div class="col-lg-5">
                                                    <div class="form-group">
                                                        <label class="form-label" for="verification-code">Verification Code <span class="text-danger">*</span></label>
                                                        <span class="form-note">Enter the 5-digit verification code that was sent to <span class="text-primary">{{ institution.email }}</span></span>
                                                        <!--                                                        <span class="d-block text-dark mt-2">Copy This Code <span class="text-dark-teal font-weight-bold fx-16">{{ verification_code }}</span></span>-->
                                                    </div>
                                                </div>
                                                <div class="col-lg-7 text-center">
                                                    <div class="form-group mb-1">
                                                        <div class="form-control-wrap">
                                                            <div v-if="completed.email_verified" class="form-icon form-icon-left text-success">
                                                                <em class="icon ni ni-check-circle-fill text-success"></em>
                                                            </div>
                                                            <input v-model.trim="institution.email_code" :disabled="completed.email_verified" autocomplete="off" type="text" placeholder="Enter Verification Code" class="form-control bg-primary-dim text-center" id="verification-code" required>
                                                        </div>
                                                    </div>
                                                    <span v-show="resend_code" class="text-dark fs-12px">Didn't get the code? <span @click="resendVerifyCode()" class="text-dark-teal cursor form-label" style="text-decoration: underline;">click here</span> to send new code</span>
                                                </div>
                                            </div>

                                            <div class="nk-kycfm-action pt-5 d-flex flex-row">
                                                <!--                                                <button @click="toggleTabs('contact-information')" type="button" class="btn btn-primary mr-2 d-flex">-->
                                                <!--                                                    <em class="ni ni-arrow-left mr-2"></em><span class="align-self-center">Back</span>-->
                                                <!--                                                </button>-->
                                                <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Verifying...</span>
                                                    <span v-if="loading" class="sr-only">Loading...</span>
                                                    <span v-if="!loading" class="align-self-center">Proceed</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </form>
                                    </div><!-- nk-kycfm-content -->
                                    <div v-show="active_tab === 'contact-person'" class="nk-kycfm-content">
                                        <div v-if="verify" class="alert-dark-teal mb-4 pl-3 alert alert-icon alert-dismissible d-flex">
                                            <div class="preview-icon-wrap p-0 d-inline mr-2 my-auto">
                                                <em class="ni ni-check-circle-fill"></em>
                                            </div>
                                            <span class="my-auto text-center">
                                                <strong>Success</strong>
                                                <span>Fill in the email and the remaining details to confirm the contact person</span>
                                            </span>
                                            <button class="close" data-dismiss="alert"></button>
                                        </div>
                                        <div v-if="!verify" class="nk-kycfm-note">
                                            <em class="icon ni ni-info-fill"></em>
                                            <p>Please fill in the details below in order to complete account creation.</p>
                                        </div>
                                        <div v-show="institutionLevel === 'international'" class="row g-4">
                                            <div class="col-lg-6 d-flex">
                                                <div class="form-group">
                                                    <div class="form-label-group">
                                                        <label class="form-label">Does this contact person have a NIN?</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 d-flex">
                                                <div class="form-group">
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input :disabled="verify || loading" type="radio" class="custom-control-input" v-model="contact_person_nin" value="yes" id="personWithNIN">
                                                        <label class="custom-control-label text-uppercase" for="personWithNIN">YES</label>
                                                    </div>
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input :disabled="verify || loading" type="radio" class="custom-control-input" v-model="contact_person_nin" value="no" id="personWithoutNIN">
                                                        <label class="custom-control-label text-uppercase" for="personWithoutNIN">NO</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <form @submit.prevent="verify || contact_person_nin === 'no' ? storeContactPerson() : (contact_person_nin === 'yes' ? verifyNIN() : verifyPassport())">
                                            <div v-if="!verify" class="row g-4 mt-2">
                                                <div v-show="contact_person_nin === 'yes'" class="col-md-6">
                                                    <div class="form-group">
                                                        <div class="form-control-wrap">
                                                            <div class="form-text-hint bg-primary-dim">
                                                                <span class="overline-title">NIN <span class="text-danger">*</span></span>
                                                            </div>
                                                            <input :disabled="verify" required v-model="contact_person.id_number" type="text" class="form-control bg-primary-dim text-uppercase" minlength="14" maxlength="14" placeholder="Enter CONTACT PERSON NIN">
                                                        </div>
                                                    </div>
                                                </div><!-- .col -->
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <button v-if="contact_person_nin === 'yes'" :disabled="loading" type="submit" class="btn btn-primary btn-block text-center d-flex">
                                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            <span v-if="loading" class="align-self-center">Verifying NIN...</span>
                                                            <span v-if="loading" class="sr-only">Verifying NIN...</span>
                                                            <span v-if="!loading" class="align-self-center">Verify Contact Person NIN</span>
                                                            <em v-if="!loading" class="icon ni ni-user-fill-c"></em>
                                                        </button>
                                                    </div>
                                                </div><!-- .col -->
                                            </div>


                                            <div class="row g-4">
                                                <div v-show="verify && contact_person_nin === 'yes'" class="col-12 col-lg-6">
                                                    <div class="table-responsive py-3">
                                                        <table class="table table-sm table-hover">
                                                            <tr>
                                                                <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                                    <div class="user-card">
                                                                        <div class="w-150px">
                                                                            <img id="contactPersonAvatar" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                                        </div>
                                                                    </div><!-- .user-card -->
                                                                </td>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                                    <span class="">{{ nira_person.national_id }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                                    <span class="">{{ nira_person.surname }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                                    <span class="">{{ nira_person.given_names }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                    <span class="">{{ nira_person.gender }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                    <span class="">{{ nira_person.date_of_birth }}</span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div :class='[contact_person_nin === "yes" ? "col-12 col-lg-6 d-flex" : "col-12 col-lg-12 d-flex"]'>
                                                    <div v-show="verify || contact_person_nin === 'no'" class="row g-4 align-self-center">
                                                        <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="contactPersonFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input :required="verify && contact_person_nin === 'no'" v-model.trim="contact_person.first_name" id="contactPersonFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="contactPersonSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input :required="verify && contact_person_nin === 'no'" v-model.trim="contact_person.surname" id="contactPersonSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-show="contact_person_nin === 'no'" class="col-6 mt-lg-0">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="contactPersonOtherNames" class="form-label">Other Names</label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input v-model.trim="contact_person.other_names" id="contactPersonOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-show="contact_person_nin === 'no'" class="col-lg-6 mt-lg-0">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label class="form-label">Gender <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-group">
                                                                    <div class="custom-control custom-control-inline custom-radio">
                                                                        <input type="radio" class="custom-control-input" v-model.number="contact_person.gender" value="M" id="contactPersonMale">
                                                                        <label class="custom-control-label text-uppercase" for="contactPersonMale">Male</label>
                                                                    </div>
                                                                    <div class="custom-control custom-control-inline custom-radio">
                                                                        <input type="radio" class="custom-control-input" v-model.number="contact_person.gender" value="F" id="contactPersonFemale">
                                                                        <label class="custom-control-label text-uppercase" for="contactPersonFemale">Female</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div :class='[contact_person_nin === "yes" ? "col-lg-12" : "col-6"]'>
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="contact_person_email" class="form-label">Email <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input :required="verify" v-model="contact_person.email" id="contact_person_email" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim">
                                                                </div>
                                                            </div>
                                                        </div><!-- .col -->
                                                        <div :class='[contact_person_nin === "yes" ? "col-lg-12" : "col-6"]'>
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="contact_person_phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input v-model.trim="contact_person.phone" autocomplete="off" type="text" placeholder="Enter Phone Number" maxlength="10" class="form-control bg-primary-dim" id="contact_person_phone" :required="verify">
                                                                </div>
                                                            </div>
                                                        </div><!-- .col -->
                                                        <div v-show="contact_person_nin === 'no'" class="col-lg-6">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <select id="userCountryId" class="form-select-sm">
                                                                        <option value="">--SELECT--</option>
                                                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-show="contact_person_nin === 'no'" class="col-lg-6">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label class="form-label">Passport <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input v-model.trim="contact_person.id_number" id="contactPersonPassport" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="custom-control custom-control-xs custom-checkbox">
                                                                <input :required="verify" type="checkbox" class="custom-control-input" id="tc-agree">
                                                                <label class="custom-control-label" for="tc-agree">I Have Read The <a target="_blank" href="/terms-and-conditions">Terms Of Condition</a> And <a target="_blank" href="/privacy-policy">Privacy Policy</a></label>
                                                            </div>
                                                        </div><!-- .col -->
                                                        <div class="col-12">
                                                            <div class="custom-control custom-control-xs custom-checkbox">
                                                                <input :required="verify" type="checkbox" class="custom-control-input" id="info-assure">
                                                                <label class="custom-control-label" for="info-assure">All The Personal Information I Have Entered Is Correct.</label>
                                                            </div>
                                                        </div><!-- .col -->
                                                    </div><!-- .row -->
                                                </div>
                                            </div><!-- .row -->

                                            <div v-show="verify || contact_person_nin === 'no'" class="nk-kycfm-action pt-5 row">
                                                <div class="col-lg-6">
                                                    <button @click="resetContactPerson()" v-if="verify || contact_person_nin === 'no'" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                                        <span class="align-self-center text-uppercase">CANCEL AND TRY AGAIN</span>
                                                    </button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button :disabled="loading" type="submit" class="btn btn-primary text-center btn-block d-flex">
                                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                                        <span v-if="loading" class="sr-only">Loading...</span>
                                                        <span v-if="!loading" class="align-self-center text-uppercase">Complete Registration</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div><!-- nk-kycfm-content -->
                                </div><!-- nk-kycfm -->
                            </div><!-- .card -->
                        </div><!-- nk-block -->
                    </div><!-- .kyc-app -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import OtpTimer from './../OtpTimer.vue';
import Notifications from "../../Notifications.vue";

export default {
    name: "EmisRegistration",
    props: [
        'countriesObj',
        'institutionTypesObj',
        'certificateAwardingInstitutionTypesObj',
        'diplomaAwardingInstitutionTypesObj'
    ],
    components: {
        OtpTimer,
        Notifications
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            back: false,
            resend_code: false,
            loading: false,
            sending: false,
            verify: false,
            contact_person_nin: 'yes',
            timer_expired: false,
            edit: false,
            active_tab: 'emis-number',
            countries: [],
            institution_types: [],
            certificate_awarding_institution_types: [],
            diploma_awarding_institution_types: [],
            completed: {
                old_emis_number: false,
                contact_info: false,
                email_verified: false,
                contact_person: false,
            },
            institution: {
                school_type_id: '', // School type
                certificate_awarding_school_type_id: '', // Certificate Awarding Institution type
                diploma_awarding_school_type_id: '', // Diploma Awarding Institution type
                name: '',
                old_emis_number: '',
                emis_number: '',
                school_type: {
                    display_name: '',
                },
                email: '',
                phone: '',
                email_code: '',
            },
            contact_person: {
                id_number: '',
                first_name: '',
                surname: '',
                other_names: '',
                photo: '',
                designation: '',
                gender: 'M',
                birth_date: '',
                email: '',
                phone: '',
                country_id: '',
                school_id: '',
                school_type_id: '', // School type
                emis_number: '',
            },
            verification_code: '',
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            console.clear();
            let self = this;

            this.countries = this.countriesObj;
            this.institution_types = this.institutionTypesObj;
            this.certificate_awarding_institution_types = this.certificateAwardingInstitutionTypesObj;
            this.diploma_awarding_institution_types = this.diplomaAwardingInstitutionTypesObj;

            let institution_phone = document.querySelector('#institution_phone');
            let iti_institution_phone = intlTelInput(institution_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            institution_phone.addEventListener('blur', ()=>{
                self.institution.phone = iti_institution_phone.getNumber().slice(-9);
                institution_phone.value = iti_institution_phone.getNumber().slice(-9);
            });
            institution_phone.addEventListener('change', ()=>{
                self.institution.phone = iti_institution_phone.getNumber().slice(-9);
                institution_phone.value = iti_institution_phone.getNumber().slice(-9);
            });

            let contact_person_phone = document.querySelector('#contact_person_phone');
            let iti_contact_person_phone = intlTelInput(contact_person_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            contact_person_phone.addEventListener('blur', ()=>{
                self.contact_person.phone = iti_contact_person_phone.getNumber().slice(-9);
                contact_person_phone.value = iti_contact_person_phone.getNumber().slice(-9);
            });
            contact_person_phone.addEventListener('change', ()=>{
                self.contact_person.phone = iti_contact_person_phone.getNumber().slice(-9);
                contact_person_phone.value = iti_contact_person_phone.getNumber().slice(-9);
            });

            this.refreshPlugins();
        },
        refreshPlugins: function () {
            let self = this;

            $('#school_type_id').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.institution.school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#certificateAwardingSchoolTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.institution.certificate_awarding_school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#diplomaAwardingSchoolTypeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: "bg-primary-dim",
                templateSelection: function (data, container) {
                    self.institution.diploma_awarding_school_type_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#userCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.contact_person.country_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
        },
        toggleTabs: function (active_tab) {
            this.active_tab = active_tab;

            this.resend_code = false;
            if (this.active_tab === "email-verification") {
                window.setTimeout(()=>{
                    this.resend_code = true;
                }, 30000);
            }

            if (this.active_tab === "emis-number") {
                this.institution.email = '';
                this.institution.old_emis_number = '';
                this.institution.emis_number = '';
                this.institution.phone = '';
                this.verification_code = '';
                this.completed = {
                    old_emis_number: false,
                    contact_info: false,
                    email_verified: false,
                    contact_person: false,
                };
            } else if (this.active_tab === "contact-information") {
                this.institution.email = '';
                this.institution.phone = '';
                this.verification_code = '';
                this.completed = {
                    old_emis_number: true,
                    contact_info: false,
                    email_verified: false,
                    contact_person: false,
                };
            }
        },
        resetTab: function (section) {
            this.back = true;
            window.setTimeout(()=>{
                switch (section) {
                    case 'old_emis_number':
                        this.institution.old_emis_number = null;
                        this.completed.old_emis_number = false;
                        this.toggleTabs('emis-number');
                        break;

                    case 'contact_info':
                        this.toggleTabs('emis-number');
                        break;

                    case 'email_verified':
                        this.toggleTabs('contact-information');
                        break;

                    case 'contact_person':
                        this.toggleTabs('contact-person');
                        break;
                }
                this.back = false;
                window.setTimeout(()=>{this.refreshPlugins()}, 50);
            }, 50)
        },
        // Verify EMIS Number
        verifyEmisNumber: function () {
            this.loading = true;

            axios.post('/institutions/verify-emis-number', {old_emis_number: this.institution.old_emis_number, school_type_id: this.institution.school_type_id})
                .then(response => {
                    this.institution = response.data;
                    this.completed.old_emis_number = true;
                    this.loading = false
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error)
                });
        },
        // Send verification code
        sendVerifyCode: function () {
            this.loading = true;
            let data = {
                name: this.institution.name,
                email: this.institution.email,
                phone: this.institution.phone,
                school_id: this.institution.id,
                certificate_awarding_school_type_id: this.institution.certificate_awarding_school_type_id,
                diploma_awarding_school_type_id: this.institution.diploma_awarding_school_type_id,
                school_type: this.institutionLevel,
            };
            axios.post('/institutions/send-verify-code/'+this.institution.emis_number, data)
                .then(response=>{
                    this.verification_code = response.data;
                    this.completed.contact_info = true;
                    this.loading = false;
                    this.toggleTabs('email-verification');
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"Verification code sent to "+this.institution.email });
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error);
                })
        },
        // Resend verification code
        resendVerifyCode: function () {
            this.institution.email_code = '';
            this.sending = true;
            let data = {
                name: this.institution.name,
                email: this.institution.email,
                phone: this.institution.phone,
                school_id: this.institution.id,
                certificate_awarding_school_type_id: this.institution.certificate_awarding_school_type_id,
                diploma_awarding_school_type_id: this.institution.diploma_awarding_school_type_id,
                school_type: this.institutionLevel,
            };
            this.resend_code = false;
            axios.post('/institutions/send-verify-code/'+this.institution.old_emis_number, data)
                .then(response=>{
                    this.verification_code = response.data;
                    this.completed.contact_info = true;
                    this.sending = false;
                    this.toggleTabs('email-verification');
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:"Verification code sent to "+this.institution.email });
                    if (this.active_tab === "email-verification") {
                        window.setTimeout(()=>{
                            this.resend_code = true;
                        }, 30000);
                    }
                })
                .catch(error=>{
                    this.sending = false;
                    this.renderError(error)
                })
        },
        // Save School email and phone
        saveInstitutionContacts: function () {
            this.loading = true;
            if (this.institution.email_code === this.verification_code) {
                let data = {
                    school_type_id: this.institution.school_type_id,
                    email: this.institution.email,
                    phone: this.institution.phone,
                    school_id: this.institution.id,
                };

                let emisNumber = this.institution.old_emis_number === null && this.institution.emis_number !== null ? this.institution.emis_number : this.institution.old_emis_number;

                axios.put('/institutions/'+emisNumber+'/contact-info', data)
                    .then(()=>{
                        this.contact_person.school_id = this.institution.id;
                        this.contact_person.school_type_id = this.institution.school_type_id;
                        this.completed.email_verified = true;
                        this.$refs.notify.messages.push({status: 'success', title: 'Success', message:this.institution.email + " was verified successfully"});

                        this.toggleTabs('contact-person');
                        this.loading = false
                    })
                    .catch(error=>{
                        this.renderError(error)
                    })

            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Error', message: "You have entered a wrong code!!! Try again."});
                this.loading = false
            }
        },
        resetContactPerson: function () {
            this.loading = false;
            this.verify = false;
            this.contact_person.id_number = '';
            this.contact_person.first_name = '';
            this.contact_person.surname = '';
            this.contact_person.other_names = '';
            $('#userCountryId').val('').change();
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            }
            $('#contactPersonAvatar').attr('src', '@images/default_male.jpg');
        },
        //verify contact person NIN
        verifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.contact_person.id_number.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.nira_person = response.data;
                    this.contact_person.first_name = this.nira_person.given_names;
                    this.contact_person.surname = this.nira_person.surname;
                    this.contact_person.other_names = this.nira_person.other_names;
                    this.contact_person.gender = this.nira_person.gender;
                    this.contact_person.birth_date = this.nira_person.date_of_birth;
                    this.contact_person.photo = this.nira_person.photo;
                    this.verify = true;
                    if (this.nira_person.photo !== null) {
                        if (this.nira_person.photo.includes('.png')) {
                            $('#contactPersonAvatar').attr('src', '/images/nira-photos/' + this.nira_person.photo);
                        } else {
                            $('#contactPersonAvatar').attr('src', 'data:image/png;base64,' + this.nira_person.photo);
                        }
                    }
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        //Verify passport
        verifyPassport: function () {
            this.loading = true;
            window.setTimeout(() => {
                this.loading = false;
                this.verify = true;
            }, 1000);
        },
        // Store contact person
        storeContactPerson: function () {
            if (this.institution.email.trim().toLowerCase() === this.contact_person.email.trim().toLowerCase()) {
                this.$refs.notify.messages.push({status: 'error', title: 'Error', message: "Contact person email should be different from Institution email!"});
            } else {
                this.loading = true;
                let params = Object.assign({}, this.contact_person, {contact_person_nin: this.contact_person_nin});

                axios.post('/institutions/contact-person/store', params)
                    .then(() => {
                        this.completed.contact_person = true;
                        window.location.href = "/institution/registration-complete";
                    })
                    .catch(error => {
                        this.loading = false;
                        if (error.response.data.errors !== undefined) {
                            for (let field in error.response.data.errors) {
                                this.showError(error.response.data.errors[field]);
                            }
                        } else {
                            this.renderError(error)
                        }
                    });
            }
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        institutionLevel: function () {
            let level = this.institution_types.find(type=>{
                return type.id === this.institution.school_type_id
            });

            return level === undefined ? '' : level.name;
        },
    }
}
</script>

<style scoped>

</style>
