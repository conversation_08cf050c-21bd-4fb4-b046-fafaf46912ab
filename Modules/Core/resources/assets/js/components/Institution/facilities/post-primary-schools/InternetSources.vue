<template>
  <div class="row">
    <div class="col-lg-6 d-flex flex-row">
      <div class="d-flex flex-row">
        <div class="d-flex flex-row align-self-center">
          <div class="preview-icon-wrap mr-1 pb-0">
            <em :class="[internet_source.internet_source_id === '' || internet_source.internet_source_id === null ? 'text-muted':'text-dark-teal','ni ni-signal']"></em>
          </div>
          <h5 class="nice-title align-self-center">School Internet Source:</h5>
        </div>
        <span v-if="internet_source.internet_source_id === '' || internet_source.internet_source_id === null" class="fs-15px ml-lg-3 align-self-center text-muted font-italic">No Internet Source</span>
        <span v-else class="fs-15px ml-lg-3 align-self-center">{{ internet_source.source.name.toUpperCase() }}</span>
      </div>
    </div>
    <div class="col-lg-6 d-flex flex-row">
      <div class="d-flex flex-row">
        <div class="d-flex flex-row">
          <div class="preview-icon-wrap align-self-center mr-1 pb-0">
            <em v-if="internet_source.connectivity_status === 1" class="ni ni-wifi text-dark-teal"></em>
            <em v-if="internet_source.connectivity_status === 0" class="ni ni-wifi-off text-muted"></em>
          </div>
          <h5 class="nice-title align-self-center">Internet Connectivity Status:</h5>
        </div>
        <div class="d-flex flex-row">
          <label class="mr-2 ml-lg-3 mb-0 align-self-center" for="internetConnectivityStatus">In-Active</label>
          <div class="custom-control custom-switch custom-control-inline align-self-center">
            <input :disabled="internet_source.internet_source_id === '' || internet_source.internet_source_id === null" v-model="toggle" type="checkbox" class="custom-control-input custom-control-input-dark-teal" id="internetConnectivityStatus" disabled>
            <label class="custom-control-label custom-control-label-dark-teal" for="internetConnectivityStatus">Active</label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "InternetSources",
  props: ['allInternetSourcesObj', 'internetSourceObj'],
  mounted() {
    this.initPlugins();
  },
  data: function () {
    return {
      toggle: false,
      internet_sources: [],
      internet_source: {
        internet_source_id: '',
        connectivity_status: 0,
        source: {
          name: '',
        },
      },
    }
  },
  methods: {
    initPlugins: function () {
      this.internet_sources = JSON.parse(this.allInternetSourcesObj);

      if (this.internetSourceObj !== "") {
        this.internet_source = JSON.parse(this.internetSourceObj);
        this.toggle = this.internet_source.connectivity_status === 1;
      }
    },
  },
  computed: {

  }
}
</script>

<style scoped>
.nice-title {
  font-size: 1.15rem;
  letter-spacing: -0.01rem;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
}
</style>
