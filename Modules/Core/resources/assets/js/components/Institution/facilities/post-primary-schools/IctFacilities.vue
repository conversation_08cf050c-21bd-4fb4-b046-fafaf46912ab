<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">ICT Equipment</h4>
						<div class="nk-block-des">
							<p>Information about your school's ICT Equipment</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
			<div class="nk-block">
				<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
					<div class="card-inner-group">
						<div class="card-inner p-0">
							<internet-sources class="mb-5"
							@updated-internet-source="updateIctFacilitiesAgain"
							ref="internetSources"
							:all-internet-sources-obj="allInternetSourcesObj"
							:internet-source-obj="internetSourceObj"
							></internet-sources>

							<div class="nk-tb-list nk-tb-ulist is-compact">
								<div class="nk-tb-item bg-secondary nk-tb-head">
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">Facility User</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Functional Computers</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Non Functional Computers</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Over head Projector</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Distance Learning Facilities</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Computers with Internet Access</span></div>
									<div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Total</span></div>
								</div><!-- .nk-tb-item -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="text-dark text-uppercase">Academic Staff</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_non_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_over_head_projectors }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-muted font-italic"></span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_with_internet }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="teacherFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ teacherFacilities.total_computers_functional + teacherFacilities.total_computers_non_functional + teacherFacilities.total_over_head_projectors }}</span>
                                    </div>
                                </div><!-- .nk-tb-item -->
								<div class="nk-tb-item">
									<div class="nk-tb-col">
										<span class="text-dark text-uppercase">Learners</span>
									</div>
									<div class="nk-tb-col text-center">
										<span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_functional }}</span>
									</div>
									<div class="nk-tb-col text-center">
										<span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_non_functional }}</span>
									</div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_over_head_projectors }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ learnerFacilities.distance_learning_facilities }}</span>
                                    </div>
									<div class="nk-tb-col text-center">
										<span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">{{ learnerFacilities.total_computers_with_internet }}</span>
									</div>
									<div class="nk-tb-col text-center">
										<span v-if="learnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">
                                            {{ learnerFacilities.total_computers_functional + learnerFacilities.total_computers_non_functional +
                                            learnerFacilities.total_over_head_projectors + learnerFacilities.distance_learning_facilities }}</span>
									</div>
								</div><!-- .nk-tb-item -->

                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="text-dark text-uppercase">Staff & Learner</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="staffLearnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ staffLearnerFacilities.total_computers_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="staffLearnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ staffLearnerFacilities.total_computers_non_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="staffLearnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ staffLearnerFacilities.total_over_head_projectors }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-muted font-italic"></span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="staffLearnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ staffLearnerFacilities.total_computers_with_internet }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="staffLearnerFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ staffLearnerFacilities.total_computers_functional + staffLearnerFacilities.total_computers_non_functional + staffLearnerFacilities.total_over_head_projectors }}</span>
                                    </div>
                                </div><!-- .nk-tb-item -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="text-dark text-uppercase">Administrative Staff</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="adminFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ adminFacilities.total_computers_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="adminFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ adminFacilities.total_computers_non_functional }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="adminFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ adminFacilities.total_over_head_projectors }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-muted font-italic"></span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="adminFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ adminFacilities.total_computers_with_internet }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span v-if="adminFacilities === undefined" class="text-muted font-italic">Not Set</span>
                                        <span v-else class="text-dark text-uppercase">{{ adminFacilities.total_computers_functional + adminFacilities.total_computers_non_functional + adminFacilities.total_over_head_projectors }}</span>
                                    </div>
                                </div><!-- .nk-tb-item -->

							</div><!-- .nk-tb-list -->
						</div><!-- .card-inner -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import InternetSources from './InternetSources.vue';
	export default {
		name: "IctFacilities",
		props: ['allIctFacilitiesObj', 'allInternetSourcesObj', 'internetSourceObj'],
		components: {InternetSources},
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				ict_facilities: [],
			}
		},
		methods: {
			initPlugins: function () {
				this.ict_facilities = JSON.parse(this.allIctFacilitiesObj);
			},
		},
		computed: {
			teacherFacilities: function () {
				return this.ict_facilities.find(facility=>{
					return facility.is_for_teachers === 1;
				});
			},
			learnerFacilities: function () {
				return this.ict_facilities.find(facility=>{
					return facility.is_for_teachers === 0;
				});
			},
            adminFacilities: function () {
                return this.ict_facilities.find(facility=>{
                    return facility.is_for_teachers === 2;
                });
            },
            staffLearnerFacilities: function () {
                return this.ict_facilities.find(facility=>{
                    return facility.is_for_teachers === 3;
                });
            },
		}
	}
</script>

<style scoped>

</style>
