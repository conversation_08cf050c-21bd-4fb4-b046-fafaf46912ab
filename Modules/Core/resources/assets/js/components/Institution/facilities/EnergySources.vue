<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Energy Sources</h4>
						<div class="nk-block-des">
							<p>Information about your school's energy sources</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
			<div class="nk-block">
				<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
					<div class="card-inner-group">
						<div class="card-inner p-0">
							<div class="nk-tb-list nk-tb-ulist is-compact">
								<div class="nk-tb-item bg-secondary nk-tb-head">
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">Usage</span></div>
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">Energy Source Type</span></div>
								</div><!-- .nk-tb-item -->
								<div class="nk-tb-item">
									<div class="nk-tb-col">
										<span class="text-dark text-uppercase">Cooking</span>
									</div>
									<div class="nk-tb-col">
										<span v-if="school.energy_sources === null" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">{{ school.energy_sources.cooking.name.toUpperCase() }}</span>
									</div>
								</div><!-- .nk-tb-item -->
								<div class="nk-tb-item">
									<div class="nk-tb-col">
										<span class="text-dark text-uppercase">Lighting</span>
									</div>
									<div class="nk-tb-col">
										<span v-if="school.energy_sources === null" class="text-muted font-italic">Not Set</span>
										<span v-else class="text-dark text-uppercase">{{ school.energy_sources.lighting.name.toUpperCase() }}</span>
									</div>
								</div><!-- .nk-tb-item -->
							</div><!-- .nk-tb-list -->
						</div><!-- .card-inner -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

export default {
    name: "EnergySources",
    props: [
        'schoolObj',
    ],

    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            school: {
                energy_sources: {
                    cooking: {
                        name: ''
                    },
                    lighting: {
                        name: ''
                    }
                },
            },
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
