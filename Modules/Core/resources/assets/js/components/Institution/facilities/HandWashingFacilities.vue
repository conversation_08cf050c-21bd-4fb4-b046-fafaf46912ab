<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Hand Washing Facilities</h4>
						<div class="nk-block-des">
							<p>Information about your Hand Washing Facilities</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
            <success-notifications ref="notifySuccess"></success-notifications>
            <div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="card-inner p-0">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase"></span></div>
                                <div class="nk-tb-col tb-col-md"><span class="sub-text text-white text-center text-uppercase">Method Used</span></div>
                            </div><!-- .nk-tb-item -->
                            <div  class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-uppercase">HAND WASHING METHOD</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <div class="preview-icon-wrap">
                                        <span v-if="school.hand_washing === null" class="text-dark-teal text-uppercase"></span>
                                        <span v-else class="text-dark-teal text-uppercase">{{ school.hand_washing.method.name }}</span>
                                    </div>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                    </div>
                </div>
            </div>
            <div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="card-inner p-0">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase">Facility</span></div>
                                <div class="nk-tb-col tb-col-md"><span class="sub-text text-white text-center text-uppercase">Status</span></div>
                                <!--								<div class="nk-tb-col nk-tb-col-tools text-center text-white text-uppercase">Actions</div>-->
                            </div><!-- .nk-tb-item -->
                            <div v-for="facility in hand_washing_facility_types" class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-uppercase">{{ facility.name.toUpperCase() }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <div class="preview-icon-wrap">
                                        <span v-if="facilityExists(facility)" class="text-dark-teal text-uppercase">Yes</span>
                                        <span v-else class="text-danger text-uppercase">No</span>
                                    </div>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                    </div>
                </div>
            </div>
        </div>
	</div>
</template>

<script>

export default {
    name: "HandWashingFacilities",
    props: [
        'schoolObj',
        'handWashingFacilitiesObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            school: {
                sanitation: [],
                hand_washing: {
                    method: {
                        name: ''
                    }
                }
            },
            hand_washing_facility_types: [],
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
            this.hand_washing_facility_types = this.handWashingFacilitiesObj;
        },

        facilityExists: function (facility) {
            return this.school.sanitation.find(item=>{
                return item.hand_washing_facility_id === facility.id && item.present_in_school;
            }) !== undefined;
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
