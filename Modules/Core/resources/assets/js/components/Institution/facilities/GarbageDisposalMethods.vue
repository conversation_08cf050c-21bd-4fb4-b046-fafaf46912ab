<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Garbage Final Disposal</h4>
						<div class="nk-block-des">
							<p>Information about how you finally dispose garbage</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
			<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
				<div class="card-inner-group">
					<div class="card-inner p-0">
						<div class="nk-tb-list nk-tb-ulist is-compact">
							<div class="nk-tb-item nk-tb-head bg-secondary">
								<div class="nk-tb-col"><span class="sub-text text-white text-uppercase"></span></div>
								<div class="nk-tb-col tb-col-md"><span class="sub-text text-white text-center text-uppercase">Method Used</span></div>
							</div><!-- .nk-tb-item -->
                            <div  class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-uppercase">GARBAGE DISPOSAL METHOD</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <div class="preview-icon-wrap">
                                        <span v-if="school.garbage_disposal_method === null" class="text-dark-teal text-uppercase"></span>
                                        <span v-else class="text-dark-teal text-uppercase">{{ school.garbage_disposal_method.garbage.name }}</span>
                                    </div>
                                </div>
							</div><!-- .nk-tb-item -->
						</div><!-- .nk-tb-list -->
					</div>
				</div>
			</div>
		</div>

	</div>
</template>

<script>

export default {
    name: "GarbageDisposalMethodsSection",
    props: [
        'schoolObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            school: {
                garbage_disposal_method: {
                    garbage: {
                        name: ''
                    }
                },
            },
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
