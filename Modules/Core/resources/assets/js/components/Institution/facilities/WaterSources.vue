<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Main Water Sources</h4>
						<div class="nk-block-des">
							<p>Information about your school's main water sources</p>
						</div>
					</div>
				</div><!-- .nk-block-between -->
			</div>
			<div class="nk-block">
				<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
					<div class="card-inner-group">
						<div class="card-inner p-0">
							<div class="nk-tb-list nk-tb-ulist is-compact">
								<div class="nk-tb-item bg-secondary nk-tb-head">
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">MAIN WATER PURPOSE</span></div>
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">MAIN WATER SOURCE TYPE</span></div>
									<div class="nk-tb-col"><span class="sub-text text-uppercase text-white">DISTANCE TO MAIN WATER SOURCE</span></div>
								</div><!-- .nk-tb-item -->
								<div class="nk-tb-item">
									<div class="nk-tb-col">
										<span class="text-dark text-uppercase">FOR DRINKING</span>
									</div>
									<div class="nk-tb-col">
                                        <span v-if="water_sources.drinking_water_source_id === ''" class="font-italic text-muted">NOT SET</span>
                                        <span v-else class="text-dark">{{ getWaterSourceType(water_sources.drinking_water_source_id).name }}</span>
									</div>
									<div class="nk-tb-col">
                                        <span v-if="water_sources.drinking_water_source_distance_id === ''" class="font-italic text-muted">NOT SET</span>
                                        <span v-else class="text-dark">{{ getWaterSourceDistanceRange(water_sources.drinking_water_source_distance_id).name }}</span>
									</div>
								</div><!-- .nk-tb-item -->
								<div class="nk-tb-item">
									<div class="nk-tb-col">
										<span class="text-dark text-uppercase">FOR OTHER PURPOSES</span>
									</div>
									<div class="nk-tb-col">
                                        <span v-if="water_sources.water_source_other_purposes_id === ''" class="font-italic text-muted">NOT SET</span>
                                        <span v-else class="text-dark">{{ getWaterSourceType(water_sources.water_source_other_purposes_id).name }}</span>
									</div>
									<div class="nk-tb-col">
                                        <span v-if="water_sources.water_source_other_purposes_distance_id === ''" class="font-italic text-muted">NOT SET</span>
                                        <span v-else class="text-dark">{{ getWaterSourceDistanceRange(water_sources.water_source_other_purposes_distance_id).name }}</span>
									</div>

								</div><!-- .nk-tb-item -->
							</div><!-- .nk-tb-list -->
						</div><!-- .card-inner -->
					</div>
				</div>
			</div>
		</div>

	</div>
</template>

<script>
export default {
    name: "WaterSection",
    props: [
        'schoolWaterSourcesObj',
        'waterSourceTypesObj',
        'waterSourceDistanceRangesObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            school: {water_sources: []},
            water_source_types: [],
            water_source_distances: [],
            water_sources: {
                drinking_water_source_id: '',
                water_source_other_purposes_id: '',
                drinking_water_source_distance_id: '',
                water_source_other_purposes_distance_id: '',
            },
        }
    },
    methods: {
        initPlugins: function () {
            this.water_source_types = this.waterSourceTypesObj;
            this.water_source_distances = this.waterSourceDistanceRangesObj;

            if (this.schoolWaterSourcesObj.length) {
                this.water_sources = JSON.parse(this.schoolWaterSourcesObj);
            }
        },
        getWaterSourceType: function (source_id) {
            return this.water_source_types.find(water_source_type => {
                return water_source_type.id === source_id;
            })
        },
        getWaterSourceDistanceRange: function (range_id) {
            return this.water_source_distances.find(water_source_distance_range => {
                return water_source_distance_range.id === range_id;
            })
        },
    },
    computed: {},
}
</script>

<style scoped>

</style>
