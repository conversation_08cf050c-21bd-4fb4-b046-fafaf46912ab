<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Other Facilities</h4>
						<div class="nk-block-des">
							<p>Information about the other facilities</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
			<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
				<div class="card-inner-group">
					<div class="card-inner p-0">
						<div class="nk-tb-list nk-tb-ulist is-compact">
							<div class="nk-tb-item nk-tb-head bg-secondary">
								<div class="nk-tb-col"><span class="sub-text text-white text-uppercase">Facility</span></div>
								<div class="nk-tb-col tb-col-md"><span class="sub-text text-white text-center text-uppercase">Status</span></div>
							</div><!-- .nk-tb-item -->
							<div v-for="facility in other_facility_types" class="nk-tb-item">
								<div class="nk-tb-col">
									<span class="text-secondary text-uppercase">{{ facility.name.toUpperCase() }}</span>
								</div>
                                <div class="nk-tb-col text-center">
                                    <div class="preview-icon-wrap">
                                        <span v-if="facilityExists(facility)" class="text-dark-teal text-uppercase">Yes</span>
                                        <span v-else class="text-danger text-uppercase">No</span>
                                    </div>
                                </div>
							</div><!-- .nk-tb-item -->
						</div><!-- .nk-tb-list -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

export default {
    name: "OtherFacilitiesSection",
    props: [
        'schoolObj',
        'otherFacilityTypesObj',
        'schoolOtherFacilitiesObj'
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            school: {
                other_facilities: [],
            },
            other_facility_types: [],
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
            this.other_facility_types = this.otherFacilityTypesObj;
        },

        facilityExists: function (facility) {
            return this.school.other_facilities.find(item=>{
                return item.infrastructure_type_id === facility.id && item.present_in_school;
            }) !== undefined;
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
