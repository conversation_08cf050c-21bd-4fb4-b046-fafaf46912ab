<template>
	<div class="components-preview wide-sm mx-auto">
		<div v-if="licenseExpired" class="alert alert-fill alert-danger alert-icon mb-5">
			<em class="icon ni ni-cross-circle"></em>
			<strong>License Expired</strong>!
			License {{ licenseObj !== undefined ? licenseObj.license_number : '' }} is expired.
			<a href="/institution/license-applications/apply" class="font-weight-bold text-white">Click here</a> to apply for license renewal.
		</div>
		<div class="nk-block nk-block-lg">
			<div style="border-width: 20px;" class="card card-preview border-secondary rounded-0">
				<div class="card-inner">
					<div class="row mb-5">
						<div class="col-4 align-self-center"></div>
						<div class="col-4 text-center">
							<img class="w-max-80px d-block mx-auto" src="@images/coat_of_arms_ug.svg" alt="MoES Logo">
							Republic of Uganda
						</div>
						<div class="col-4 align-self-center text-right">
							{{ licenseObj !== undefined ? licenseObj.license_number : '' }}
						</div>
					</div>
					<div class="text-center my-5 mb-3">
						<h4 class="text-uppercase">Ministry of Education and Sports</h4>
						<span class="text-red lead">Licensing Certificate for Primary School Institutions</span>
					</div>

					<div class="text-center pb-3">
						<span class="text-uppercase d-blockmx-auto">This is to certify that</span>
					</div>

					<div class="school text-center my-3">
						<h5 class="font-weight-bold">{{ school.school_name }}</h5>
					</div>

					<div class="pb-3 pt-3 mx-4">
						<span class="text-uppercase d-block mx-auto">is licensed and classified as per section 32/45 of the Education Act, 2008 as hereunder: -</span>
					</div>
					<div class="pb-3 mb-5 mx-4">
						<span class="text-dark font-weight-bold">LICENSE No:</span> <span class="text-dark">{{ licenseObj !== undefined ? licenseObj.license_number : '' }}</span><br>
						<span class="text-dark font-weight-bold">DATE LICENSED:</span> <span class="text-dark">{{ licenseObj !== undefined ? formatDate(licenseObj.created_at) : '' }}</span><br>
						<span class="text-dark font-weight-bold">CLASSIFICATION:</span> <span class="text-dark">PRIVATE PRIMARY SCHOOL</span><br>
						<span class="text-dark font-weight-bold">REMARKS:</span> <span class="text-dark">Licensed for two (2) years</span>
					</div>

					<div class="bg-signature py-5">
						<div class="signature text-center mt-4">
							<!-- signature image here -->
						</div>
						<div class="text-center">
							Permanent Secretary, Ministry of Education and Sports.
						</div>
					</div>

					<div class="py-3"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "PrimarySchoolLicense",
		props: ['schoolObj', 'licenseObj'],
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
				edit: false,
				school: {}
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;
				this.school = JSON.parse(this.schoolObj)
			},
			formatDate: function (raw_date) {
				return moment(raw_date).format("D MMMM, YYYY");
			},
		},
		computed: {
			licenseExpired: function () {
				let status = true;

				if (this.licenseObj !== undefined) {
					if (moment().diff(moment(this.licenseObj.expiry_date)) < 0) {
						status = false;
					}
				}

				return status;
			},
		}
	}
</script>

<style scoped>
	.school {
		position: relative;
	}

	.school:after {
		content: '';
		position: absolute;
		bottom: 0px;
		left: 15%;
		width: 70%;
		border-bottom: 2px solid #364a63;
	}

	.bg-signature {
		background-image: url("@images/ps.png");
		background-repeat: no-repeat;
		background-position: 50% -60%;
		background-size: 20%;
		height: 150px;
	}

	.signature {
		position: relative;
	}

	.signature:after {
		content: '';
		position: absolute;
		bottom: 0px;
		left: 30%;
		width: 40%;
		border-bottom: 2px solid #364a63;
	}
</style>
