<template>
    <div>
        <hr class="mb-5 border-dark-teal">
        <div class="nk-block">
            <div class="nk-block-head-content">
                <h4 class="nk-block-title">School Owners</h4>
                <div class="nk-block-des">
                    <p>You have a total of {{ schoolOwners.length }} owners.</p>
                </div>
            </div>
            <notifications ref="notify"></notifications>
            <div class="card card-stretch card-bordered border-dark-teal mt-2">
                <div class="card-inner-group">
                    <div class="card-inner position-relative border-bottom-0 card-tools-toggle">
                        <div class="card-title-group">
                            <div class="card-tools">
								<span v-if="(sole_owner && owners.length < 1) || !sole_owner" data-toggle="modal" data-target="#schoolOwnerModal" data-backdrop="static" class="btn bg-dark-teal">
									<em class="icon ni ni-plus text-white"></em>
									<span class="">Add School Owner</span>
								</span>
                            </div><!-- .card-tools -->
                            <!--                            <div class="card-tools">-->
                            <!--                                <div class="form-inline flex-nowrap gx-3">-->
                            <!--                                    <div style="width: 200px !important" class="form-wrap">-->
                            <!--                                        <select v-model="bulk_action" id="ownerBulkAction" class="form-select-sm">-->
                            <!--                                            <option value="">Bulk Action</option>-->
                            <!--                                            <option value="delete">Delete Selected</option>-->
                            <!--                                        </select>-->
                            <!--                                    </div>-->
                            <!--                                    <div class="btn-wrap">-->
                            <!--										<span class="d-none d-md-block">-->
                            <!--											<button-->
                            <!--                                                @click="bulk_action === '' || selected_owners.length === 0 || (bulk_action==='restore' && inactivePresent === false) ? null : processBulkAction()"-->
                            <!--                                                :class="[bulk_action === '' || selected_owners.length === 0 || (bulk_action==='restore' && inactivePresent === false) ? 'disabled btn-dim btn-outline-light' : 'btn-primary' , 'btn']">Apply</button>-->
                            <!--										</span>-->
                            <!--                                        <span class="d-md-none">-->
                            <!--											<button-->
                            <!--                                                @click="bulk_action === '' || selected_owners.length === 0 || (bulk_action==='restore' && inactivePresent === false) ? null : processBulkAction()"-->
                            <!--                                                :class="[bulk_action === '' || selected_owners.length === 0 || (bulk_action==='restore' && inactivePresent === false) ? 'disabled btn-dim btn-outline-light' : 'btn-primary' , 'btn btn-icon']">-->
                            <!--											<em class="icon ni ni-arrow-right"></em>-->
                            <!--										</button>-->
                            <!--									</span>-->
                            <!--                                    </div>-->
                            <!--                                </div>&lt;!&ndash; .form-inline &ndash;&gt;-->
                            <!--                            </div>&lt;!&ndash; .card-tools &ndash;&gt;-->
                        </div><!-- .card-title-group -->
                    </div><!-- .card-inner -->
                    <div class="card-inner pt-0 pb-3">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleAllOwner()" v-model="select_all_owners"  type="checkbox" class="custom-control-input" id="ownersSelect">
                                        <label class="custom-control-label" for="ownersSelect"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col"><span class="sub-text text-white">NAMES</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">PHONE NUMBER</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">EMAIL ADDRESS</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">CV FILE</span></div>
                                <div class="nk-tb-col"><span class="text-white">ACTION</span></div>
                            </div><!-- .nk-tb-item -->
                            <div v-for="owner in schoolOwners" class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleOneOwner()" v-model="selected_owners" :value="owner.id" type="checkbox" class="custom-control-input" :id="'ownersSelect'+owner.id">
                                        <label class="custom-control-label" :for="'ownersSelect'+owner.id"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col">
                                    <div class="user-card">
                                        <div class="user-avatar">
                                            <img :src="owner.photo_url" style="border-radius: 0" :alt="owner.person.initials">
                                        </div>
                                        <div class="user-name text-uppercase">
                                            <span class="d-block text-dark">{{ owner.person.full_name }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="nk-tb-col">
                                    <span v-if="owner.person.phone_1 === null" class="text-muted font-italic">Not Yet Set</span>
                                    <span v-else class="text-dark">+{{ owner.person.phone_1 }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span v-if="owner.person.email === null" class="text-muted font-italic">Not Yet Set</span>
                                    <span v-else class="text-dark">{{ owner.person.email }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span v-if="owner.cv === null" class="text-muted font-italic">Not Yet Set</span>
                                    <a v-else :href="owner.cv_url" download class="btn btn-sm cursor bg-dark-teal" target="_blank">
                                        <em class="icon ni ni-download text-white"></em>
                                        <span class="">Download CV</span>
                                    </a>
                                </div>
                                <div class="nk-tb-col text-center">
								<span @click="editSchoolOwner(owner)" data-toggle="tooltip" data-placement="top" title="Edit Owner" class="cursor lead text-dark-teal">
									<em class="icon ni ni-edit-fill"></em>
								</span>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                        <div v-if="!schoolOwners.length" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No school owner info to display at the moment...
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
        <!-- School Owner Modal -->
        <div class="modal fade zoom" tabindex="-1" id="schoolOwnerModal">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetSchoolOwner()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="edit ? updateSchoolOwner() : (verify || contact_person_nin === 'no'  ? createSchoolOwner() : (contact_person_nin === 'yes' ? verifyNIN() : verifyPassport()))">
                        <div class="modal-header">
                            <h6 class="modal-title">{{ edit ? 'Edit' : 'Add' }} School Owner</h6>
                        </div>
                        <div class="modal-body">
                            <!--                            <error-notifications ref="notifyErrorNIN"></error-notifications>-->
                            <notifications ref="notifyError"></notifications>
                            <div v-show="!edit || schoolTypeIdObj === 7 || school.school_ownership_id === 2 " class="row g-4">
                                <div class="col-lg-6 d-flex">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label class="form-label">Does this school owner have a NIN?</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 d-flex">
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input :disabled="verify" type="radio" class="custom-control-input" v-model="contact_person_nin" value="yes" id="personWithNIN">
                                            <label class="custom-control-label text-uppercase" for="personWithNIN">YES</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input :disabled="verify" type="radio" class="custom-control-input" v-model="contact_person_nin" value="no" id="personWithoutNIN">
                                            <label class="custom-control-label text-uppercase" for="personWithoutNIN">NO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!verify && !edit" class="row g-4">
                                <div v-show="contact_person_nin === 'yes'" class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <div class="form-text-hint bg-primary-dim">
                                                <span class="overline-title">NIN <span class="text-danger">*</span></span>
                                            </div>
                                            <input :disabled="verify" required v-model="form_owner.id_number" type="text" class="form-control bg-primary-dim text-uppercase" minlength="14" maxlength="14" placeholder="ENTER SCHOOL OWNER NIN">
                                        </div>
                                    </div>
                                </div><!-- .col -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <button v-if="contact_person_nin === 'yes'" :disabled="loading" type="submit" class="btn bg-dark-teal btn-block text-center d-flex">
                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span v-if="loading" class="align-self-center">Verifying NIN...</span>
                                            <span v-if="loading" class="sr-only">Verifying NIN...</span>
                                            <span v-if="!loading" class="align-self-center">VERIFY NIN</span>
                                            <em v-if="!loading" class="icon ni ni-user-fill-c"></em>
                                        </button>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->

                            <div v-show="verify || edit || contact_person_nin === 'no'" class="row g-4">
                                <div v-show="verify && contact_person_nin === 'yes'" class="col-12 col-lg-6">
                                    <div class="table-responsive py-3">
                                        <table class="table table-sm table-hover">
                                            <tr>
                                                <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                    <div class="user-card">
                                                        <div class="w-150px">
                                                            <img id="userAvatar" src="@images/default_male.jpg" class="rounded-0" alt="person photo">
                                                        </div>
                                                    </div><!-- .user-card -->
                                                </td>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                    <span class="">{{ nira_person.national_id }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                    <span class="">{{ nira_person.surname }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                    <span class="">{{ nira_person.given_names }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-uppercase text-dark">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                    <span class="">{{ nira_person.gender }}</span>
                                                </td>
                                            </tr>
                                            <!--                                            <tr>-->
                                            <!--                                                <td class="px-2 align-middle text-uppercase text-dark">-->
                                            <!--                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>-->
                                            <!--                                                    <span class="">{{ nira_person.date_of_birth }}</span>-->
                                            <!--                                                </td>-->
                                            <!--                                            </tr>-->
                                        </table>
                                    </div>
                                </div>

                                <div :class='[contact_person_passport === "no" ? "col-12 col-lg-6 d-flex" : "col-12 col-lg-12 d-flex"]'>
                                    <div class="row g-4 align-self-center">

                                        <div v-show="contact_person_nin === 'no' || edit && form_owner.identity_type_id !== 1" class="col-lg-12">
                                            <div class="form-group">
                                                <label for="schoolOwnerPhoto" class="form-label" @click="seletPhoto()">Owner's Photo</label>
                                                <span class="form-note">Upload the school owner's photo.</span>
                                            </div>
                                            <div class="form-group mb-1">
                                                <input
                                                    :required="!edit && contact_person_nin === 'no'"
                                                    id="schoolOwnerPhoto"
                                                    ref="schoolOwnerPhoto" @change="selectFile"
                                                    accept="image/x-png,image/jpeg"
                                                    data-max-file-size="2M"
                                                    type="file"
                                                    class="dropify"
                                                    data-height="180"
                                                    data-allowed-file-extensions="jpeg jpg png"
                                                    data-default-file="@images/default_male.jpg" />
                                            </div>
                                            <span @click="form_owner.photo === '' ? seletPhoto() : resetPhoto()" class="btn btn-block bg-dark-teal">
                                                <em class="icon ni ni-camera-fill mr-1"></em>
                                                <span v-if="form_owner.photo === ''">Select Photo</span>
                                                <span v-else>Remove Photo</span>
                                            </span>
                                        </div>

                                        <div v-show="contact_person_nin === 'no' || edit" class="col-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="contactPersonFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :disabled="form_owner.identity_type_id === 1" :required="verify && contact_person_nin === 'no'" v-model.trim="form_owner.first_name" id="contactPersonFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="contact_person_nin === 'no' || edit" class="col-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="contactPersonSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :disabled="form_owner.identity_type_id === 1" :required="verify && contact_person_nin === 'no'" v-model.trim="form_owner.surname" id="contactPersonSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="contact_person_nin === 'no' || edit" class="col-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="contactPersonOtherNames" class="form-label">Other Names</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :disabled="form_owner.identity_type_id === 1" v-model.trim="form_owner.other_names" id="contactPersonOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="contact_person_nin === 'no' || edit" class="col-lg-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Gender <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-group">
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" :disabled="form_owner.identity_type_id === 1" v-model.number="form_owner.gender" value="M" id="contactPersonMale">
                                                        <label class="custom-control-label text-uppercase" for="contactPersonMale">Male</label>
                                                    </div>
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" :disabled="form_owner.identity_type_id === 1" v-model.number="form_owner.gender" value="F" id="contactPersonFemale">
                                                        <label class="custom-control-label text-uppercase" for="contactPersonFemale">Female</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div :class='[contact_person_passport === "no" ? "col-lg-12" : "col-6"]'>
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="contact_person_email" class="form-label">Email <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="verify" v-model="form_owner.email" id="contact_person_email" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim">
                                                </div>
                                            </div>
                                        </div><!-- .col -->
                                        <div :class='[contact_person_passport === "no" ? "col-lg-12" : "col-6"]'>
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="userPhone" class="form-label">Phone <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input  id="schoolOwnerPhone" v-model.trim="form_owner.phone_1" autocomplete="off" type="text" placeholder="Enter Phone Number" maxlength="9" class="form-control bg-primary-dim" :required="verify">
                                                </div>
                                                <div class="form-note">(Don't include 0 at the beginning) E.G <code>*********</code></div>
                                            </div>
                                        </div><!-- .col -->

                                        <div v-show="contact_person_nin === 'no' || edit && form_owner.identity_type_id !== 1" class="col-lg-6">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Nationality <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <select :disabled="form_owner.identity_type_id === 1" id="schoolOwnerCountryId" :required="!edit && contact_person_nin === 'no'" class="form-select-sm">
                                                        <option value="">--SELECT--</option>
                                                        <option v-for="country in countries" :value="country.id">{{ country.name }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="contact_person_nin === 'no' || edit && form_owner.identity_type_id !== 1" class="col-lg-6">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Passport <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model.trim="form_owner.id_number" id="contactPersonPassport" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                <label class="form-label" for="schoolOwnerCV">Owner's CV <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="form-control-wrap">
                                                        <div class="custom-file">
                                                            <input :disabled="loading"
                                                                   :required="!edit && verify || contact_person_nin === 'no'"
                                                                   ref="schoolOwnerCV"
                                                                   type="file"
                                                                   :class="[loading ? '' : 'cursor', 'custom-file-input']"
                                                                   id="schoolOwnerCV"
                                                                   @change="checkCVfile()"
                                                                   accept=".pdf">
                                                            <label id="schoolOwnerCVLabel" class="custom-file-label small bg-primary-dim text-muted font-italic" for="schoolOwnerCV">
                                                                <span v-if="edit">Upload New CV</span>
                                                                <span v-else>Upload CV</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div><!-- .row -->
                                </div>

                            </div><!-- .row -->
                            <div v-show="verify || edit || contact_person_nin === 'no'" class="nk-kycfm-action pt-5 row">
                                <div class="col-lg-6">
                                    <button @click="resetSchoolOwner()" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                        <span class="align-self-center text-uppercase">CANCEL</span>
                                    </button>
                                </div>
                                <div class="col-lg-6">
                                    <button :disabled="loading" type="submit" class="btn bg-dark-teal text-center btn-block d-flex">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Saving...</span>
                                        <span v-if="loading" class="sr-only">Loading...</span>
                                        <span v-if="!loading" class="align-self-center text-uppercase">SAVE</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </div>

                        </div>
                    </form>

                </div>
            </div>
        </div>
        <!-- /School Owner Modal -->
    </div>
</template>

<script>
import Notifications from "../Notifications.vue";

export default {
    name: "SchoolOwners",
    props: ['schoolObj','countriesObj','schoolTypeIdObj'],
    mounted() {
        this.initPlugins();
    },
    components: {
        Notifications
    },
    data: function () {
        return {
            api_url: '/institutions/owners',
            sole_owner: false,
            valid_file: false,
            loading: false,
            verify: false,
            filtering: false,
            edit: false,
            contact_person_nin: 'yes',
            contact_person_passport: 'yes',
            bulk_action: '',
            select_all_owners: false,
            photoDropify: null,
            selected_owners: [],
            school: {
                owners: [],
            },
            countries: [],
            school_type_id: '',
            form_owner: {
                first_name: '',
                surname: '',
                other_names: '',
                id_number: '',
                birth_date: '',
                identity_type_id: '',
                gender: 'M',
                country_id: '',
                email: '',
                phone_1: null,
                contact_person_nin: 'yes',
                cv: '',
                cv_url: '',
                photo: '',
                photo_url: '',
            },
            nira_person: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
            owner: {
                person: {
                    id: '',
                    first_name: '',
                    surname: '',
                    other_names: '',
                    phone_1: '',
                    email: '',
                },
                cv: '',
                cv_url: '',
                photo: '',
                photo_url: '',
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.countries = this.countriesObj;

            $('#schoolOwnerCountryId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#schoolOwnerModal'),
                templateSelection: function (data, container) {
                    self.form_owner.country_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            this.photoDropify = $('#schoolOwnerPhoto').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Oops, something wrong appended.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max).'
                }
            });

            this.photoDropify.on('dropify.afterClear', function(event, element){
                self.form_owner.photo = '';
                self.$refs.schoolOwnerPhoto.value=null;
            });


            let owner_phone = document.querySelector('#schoolOwnerPhone');
            let iti_owner_phone = intlTelInput(owner_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            owner_phone.addEventListener('blur', ()=>{
                self.form_owner.phone_1 = iti_owner_phone.getNumber().slice(-9);
                owner_phone.value = iti_owner_phone.getNumber().slice(-9);
            });
            owner_phone.addEventListener('change', ()=>{
                self.form_owner.phone_1 = iti_owner_phone.getNumber().slice(-9);
                owner_phone.value = iti_owner_phone.getNumber().slice(-9);
            });

            this.resetPhoto();
        },
        seletPhoto() {
            $('#schoolOwnerPhoto').click();
        },
        selectFile() {
            this.form_owner.photo = this.$refs.schoolOwnerPhoto.files[0];
        },
        resetPhoto: function () {
            let filedropper = this.photoDropify.data('dropify');
            filedropper.resetPreview();
            filedropper.clearElement();
            filedropper.settings['defaultFile'] = this.form_owner.photo_url === '' ? "@images/default_male.jpg" : this.form_owner.photo_url;
            filedropper.destroy();
            filedropper.init();
            this.loading = false;
        },
        verifyNIN: function () {
            this.loading = true;
            this.contact_person_passport = 'no';
            axios.post('/nira/user-info', {id_number: this.form_owner.id_number.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.nira_person = response.data;
                    this.form_owner.id_number = this.nira_person.national_id;
                    this.form_owner.first_name = this.nira_person.given_names;
                    this.form_owner.surname = this.nira_person.surname;
                    this.form_owner.other_names = this.nira_person.other_names;
                    this.form_owner.gender = this.nira_person.gender;
                    this.form_owner.birth_date = this.nira_person.date_of_birth;
                    this.verify = true;
                    if (this.nira_person.photo !== null) {
                        if (this.nira_person.photo.includes('.png')) {
                            $('#userAvatar').attr('src', '/images/nira-photos/' + this.nira_person.photo);
                        } else {
                            $('#userAvatar').attr('src', 'data:image/png;base64,' + this.nira_person.photo);
                        }
                    }
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        //Verify passport
        verifyPassport: function () {
            this.loading = true;
            this.contact_person_passport = 'yes';
            window.setTimeout(() => {
                this.loading = false;
                this.verify = true;
            }, 1000);
        },
        createSchoolOwner: function () {
            let formData = new FormData();
            this.loading = true;
            formData.append('first_name', this.form_owner.first_name);
            formData.append('surname', this.form_owner.surname);
            formData.append('other_names', this.form_owner.other_names ?? '');
            formData.append('gender', this.form_owner.gender);
            formData.append('birth_date', this.form_owner.birth_date);
            formData.append('phone_1', this.form_owner.phone_1);
            formData.append('email', this.form_owner.email);
            formData.append('country_id', this.form_owner.country_id);
            formData.append('id_number', this.form_owner.id_number);
            formData.append('identity_type_id', this.form_owner.identity_type_id);
            formData.append('contact_person_nin', this.contact_person_nin);
            formData.append('photo', this.form_owner.photo || this.nira_person.photo);
            formData.append('cv', this.form_owner.cv);
            axios.post(this.api_url+'/create', formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response=>{
                    this.school.owners = response.data;
                    this.resetSchoolOwner();
                    this.$refs.notify.messages.push({status: 'success', title: 'Success: ', message:" School Owner Added Successfully"});
                })
                .catch(error=>{
                    console.log(error)
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        editSchoolOwner: function (owner) {
            this.edit = true;
            this.form_owner.id = owner.person.id;
            this.form_owner.first_name = owner.person.first_name;
            this.form_owner.surname = owner.person.surname;
            this.form_owner.other_names = owner.person.other_names;
            this.form_owner.gender = owner.person.gender;
            this.nira_person.national_id = owner.person.id_number;
            this.form_owner.id_number = owner.person.id_number;
            this.form_owner.identity_type_id = owner.person.identity_type_id;
            this.form_owner.cv = owner.cv;
            this.form_owner.cv_url = owner.cv_url;
            this.form_owner.photo = owner.photo;
            this.form_owner.photo_url = owner.photo_url;
            this.form_owner.phone_1 = owner.person.phone_1;
            this.form_owner.email = owner.person.email;
            this.resetCVFile();
            this.resetPhoto();

            $('#schoolOwnerCountryId').val(owner.person.country_id).change();
            $('#schoolOwnerModal').modal({backdrop:'static'});
        },

        updateSchoolOwner: function () {
            let formData = new FormData();
            this.loading = true;
            formData.append('first_name', this.form_owner.first_name);
            formData.append('surname', this.form_owner.surname);
            formData.append('other_names', this.form_owner.other_names ?? '');
            formData.append('gender', this.form_owner.gender);
            formData.append('id_number', this.form_owner.id_number);
            formData.append('identity_type_id', this.form_owner.identity_type_id);
            formData.append('country_id', this.form_owner.country_id);
            formData.append('phone_1', this.form_owner.phone_1);
            formData.append('email', this.form_owner.email);
            formData.append('photo', this.form_owner.photo || this.nira_person.photo);
            formData.append('photo_url', this.form_owner.photo_url);
            formData.append('cv', this.form_owner.cv);
            axios.post(this.api_url+'/update/'+this.form_owner.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response=>{
                    this.school.owners = response.data;
                    this.resetSchoolOwner();
                    this.$refs.notify.messages.push({status: 'success', title: 'Success: ', message:" School Owner Updated Successfully"});
                })
                .catch(error=>{
                    console.log(error)
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        toggleAllOwner: function () {
            this.selected_owners =[];

            if (this.select_all_owners) {
                this.school.owners.forEach(owner=>{
                    this.selected_owners.push(owner.id)
                });
            }
        },
        toggleOneOwner: function () {
            this.select_all_owners = this.selected_owners.length === this.school.owners.length
        },
        checkCVfile: function () {
            // Get the file input element's value
            let fileInput = this.$refs.schoolOwnerCV.files[0];
            // Check file size (max 2MB)
            let fileSizeInMB = fileInput.size / 1024 / 1024;  // Convert size to MB
            if (fileSizeInMB > 2) {
                Swal.fire({
                    icon: 'error',
                    title: 'File too large!',
                    text: 'File size should be less than 2MB.'
                });

                this.resetCVFile();
                return false;
            }
            // Update label with file name
            let fileName = fileInput.name;
            window.setTimeout(() => {
                $('#schoolOwnerCVLabel').text(fileName);
            }, 10);

            this.valid_file = true;
            this.form_owner.cv = fileInput;
            return true;
        },
        resetCVFile: function () {
            this.form_owner.cv = null;
            this.valid_file = false;
            this.loading = false;
            this.$refs.schoolOwnerCV.value = null;
            window.setTimeout(()=>{
                if (this.edit) {
                    $('#schoolOwnerCVLabel').text("Upload New CV")
                } else {
                    $('#schoolOwnerCVLabel').text("Upload CV")
                }

            }, 10);
        },
        formatAddress: function (address) {
            return address.address.replace(/\n/g, "<br>");
        },
        startLoading: function() {
            $.blockUI({
                message: $('#schoolOwnerLoadingMessage'),
                css: {
                    padding:0,
                    margin:0,
                    width:'30%',
                    top:'40%',
                    left:'35%',
                    textAlign:'center',
                    color:'#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor:'wait'
                },
            });
        },
        resetSchoolOwner: function () {
            $('#schoolOwnerModal').modal('hide');
            this.loading = false;
            this.edit = false;
            this.verify = false;
            this.contact_person_nin = 'yes';
            this.contact_person_passport = 'yes';
            this.form_owner.id = '';
            this.form_owner.first_name = '';
            this.form_owner.surname = '';
            this.form_owner.other_names = '';
            this.form_owner.id_number = '';
            this.form_owner.identity_type_id = '';
            this.form_owner.birth_date = '';
            this.form_owner.gender = 'M';
            this.form_owner.email = '';
            this.form_owner.phone_1 = null;
            this.form_owner.cv = '';
            this.form_owner.cv_url = '';
            this.form_owner.photo = '';
            this.form_owner.photo_url = '';
            this.nira_person = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            }
            $('#schoolOwnerCountryId').val('').change();
            this.resetCVFile();
            this.resetPhoto();
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
        },
    },
    computed: {
        schoolOwners: function() {
            return this.school.owners || [];
        }
    }
}
</script>

<style scoped>

</style>
