<template>
	<div class="nk-block">
		<notifications ref="notify"></notifications>
		<div class="card card-stretch card-bordered border-dark-teal">
			<div class="card-aside-wrap">
				<div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg" data-content="userAside" data-toggle-screen="lg" data-toggle-overlay="true">
					<div class="card-inner-group" data-simplebar>
						<div class="card-inner">
							<div class="user-card">
								<div class="user-avatar w-100px" style="height: 1%">
									<img :src="school.logo_url" :alt="school.name+' Logo'">
								</div>
								<div class="user-info">
									<span class="lead-text">{{ school.name }}</span>
									<span class="sub-text">{{ school.emis_number }}</span>
								</div>
								<div class="user-action">
									<a data-toggle="modal" data-target="#updateLogoModal" data-backdrop="static" class="btn btn-round btn-icon btn-sm bg-dark-teal">
										<em class="icon ni ni-camera-fill"></em>
									</a>
								</div>
							</div><!-- .user-card -->
						</div><!-- .card-inner -->
						<div class="card-inner p-0">
							<ul class="link-list-menu nav nav-tabs">
								<li>
									<a data-toggle="tab" href="#basicInfo" class="active">
										<em class="icon ni ni-building-fill"></em><span>School Identification</span>
									</a>
								</li>
								<li>
									<a data-toggle="tab" href="#ownership">
										<em class="icon ni ni-downward-alt-fill"></em><span>Ownership</span>
									</a>
								</li>
								<li>
									<a data-toggle="tab" href="#locationDetails">
										<em class="icon ni ni-location"></em><span>Location Details</span>
									</a>
								</li>
								<li>
									<a data-toggle="tab" href="#operationDetails">
										<em class="icon ni ni-opt-alt-fill"></em><span>Operational Details</span>
									</a>
								</li>
								<li>
									<a data-toggle="tab" href="#healthInfo">
										<em class="icon ni ni-activity-round-fill"></em><span>Health information</span>
									</a>
								</li>
								<li><a data-toggle="tab" href="#proximities">
									<em class="icon ni ni-map-pin"></em><span>Proximities</span></a>
								</li>
								<li>
									<a data-toggle="tab" href="#governance">
										<em class="icon ni ni-shield-fill"></em><span>Governance</span>
									</a>
								</li>
							</ul>
						</div><!-- .card-inner -->
					</div><!-- .card-inner-group -->
				</div><!-- card-aside -->
				<div class="card-inner card-inner-lg">
					<div class="tab-content">
						<div class="tab-pane active" id="basicInfo">
							<div class="nk-block-head nk-block-head-lg">
								<div class="nk-block-between">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Institution Identification</h4>
										<div class="nk-block-des">
											<p>Basic school information.</p>
										</div>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div><!-- .nk-block-head -->
							<div class="nk-block">
								<div class="nk-data data-list">
									<!-- Display Form with Update Button -->
									<div class="data-item py-1" v-if="!showUpdateNameForm">
										<div class="data-col">
											<span class="data-label">Name Of School</span>
											<div class="d-flex align-items-center flex-wrap">
												<div class="d-flex align-items-center mr-3">
													<span class="data-value ">
														{{ school.name }}
														<span v-if="hasPendingNameChange" class="badge badge-gray text-white">
															Under Review
														</span>
													</span>
												</div>
												<div class="d-flex align-items-center">
													<button v-if="!hasPendingNameChange"
															@click="showUpdateNameForm = true"
															type="button"
															class="btn btn-sm btn-primary">
														<em class="ni ni-edit-fill text-white mr-1"></em>Update
													</button>
													<span v-else class="text-muted small"></span>
												</div>
											</div>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<update-school-name
										v-else
										:school="school"
										@success="handleNameUpdateSuccess"
										@error="handleNameUpdateError"
										@cancel="showUpdateNameForm = false"
										@request-submitted="handleRequestSubmitted"
									/>

									<div class="data-item py-1" v-if="!showUpdateNameForm">
										<div class="data-col">
											<span class="data-label">EMIS Number</span>
											<span class="data-value text-dark">{{ school.emis_number }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<div class="data-item py-1" v-if="!showUpdateNameForm">
										<div class="data-col">
											<span class="data-label">Postal Address</span>
											<span v-if="school.postal_address === null || school.postal_address === undefined" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.postal_address.toUpperCase() }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<div class="data-item py-1" v-if="!showUpdateNameForm">
										<div class="data-col">
											<span class="data-label">School Email Address</span>
											<span v-if="school.email === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.email }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<div class="data-item py-1" v-if="!showUpdateNameForm">
										<div class="data-col">
											<span class="data-label">Phone Contact</span>
											<span v-if="school.phone === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.phone }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<!-- Dowclass="data-item py-1"nload Letter Button (after phone contact) -->
									<div class="data-item py-1" v-if="!showUpdateNameForm && !hasPendingNameChange">
										<div class="data-col">
											<button @click="downloadLetter()"
													:disabled="loading"
													type="submit"
													class="btn btn-xs btn-dark-teal d-flex">
												<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
												<span v-if="loading" class="align-self-center">Downloading...</span>
												<span v-if="loading" class="sr-only">Downloading...</span>
												<span v-if="!loading" class="align-self-center">
													<em class="icon ni ni-download text-white mr-1"></em>Download Letter
												</span>
											</button>
										</div>
										<div class="data-col data-col-end"></div>
									</div>

									<!-- Name Change History Section -->
									<div class="mt-5">
										<div class="d-flex justify-content-between align-items-center mb-3">
											<h5 class="mb-0">Name Amendment History</h5>
										</div>
										<!-- Add the NameChangeRequestsTable component -->
										<name-change-requests-table
											v-if="school.id"
											ref="nameChangeRequestsTable"
											:school-id="school.id"
											:refresh-trigger="refreshTrigger"
										/>
									</div>
								</div><!-- data-list -->
							</div><!-- .nk-block-head -->
						</div><!-- .tab-pane -->
						<div class="tab-pane" id="ownership">
							<div class="nk-block-head nk-block-head-lg">
								<div class="nk-block-between">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Ownership</h4>
										<div class="nk-block-des">
											<p>School ownership information.</p>
										</div>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div><!-- .nk-block-head -->
							<div class="nk-block">
								<div class="nk-data data-list">

								<!-- Only show label when not updating -->

									<div class="data-item py-1" v-if="!showUpdateOwnershipForm">
										<div class="data-col">
											<span class="data-label">Ownership Status</span>
											<div class="d-flex align-items-center flex-wrap">
												<div class="d-flex align-items-center mr-3">
													<span class="data-value">
														<span v-if="school.ownership_status === null" class="data-value text-muted font-italic">Not Set</span>
														<span v-else class="data-value text-dark">{{ school.ownership_status.name.toUpperCase() }}</span>

														
													</span>
												</div>
												
											</div>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div>

									<div class="data-col w-15 data-col-end"></div>
									<!-- data-item -->
									<!-- Only show these items when not updating -->
									<template v-if="!showUpdateOwnershipForm">
										<div class="data-item py-1">
											<div class="data-col">
												<span class="data-label">Founding Body</span>
												<span v-if="school.founding_body === null" class="data-value text-muted font-italic">Not Set</span>
												<span v-else class="data-value text-dark">{{ school.founding_body.name.toUpperCase() }}</span>
											</div>
											<div class="data-col w-15 data-col-end"></div>
										</div>

										<div class="data-item py-1">
											<div class="data-col">
												<span class="data-label">Legal Ownership Status</span>
												<span v-if="school.school_ownership_status_id === 1">GOVERNMENT</span>
												<span v-else-if="school.legal_ownership_status !== null && school.school_ownership_status_id === 2">{{ school.legal_ownership_status.name.toUpperCase() }}</span>
												<span v-else class="text-muted font-italic">Not Set</span>
											</div>
											<div class="data-col w-15 data-col-end"></div>
										</div>

										<div class="data-item py-1">
											<div class="data-col">
												<span class="data-label">Year School Founded</span>
												<span v-if="school.year_founded === null" class="data-value text-muted font-italic">Not Set</span>
												<span v-else class="data-value text-dark">{{ school.year_founded }}</span>
											</div>
											<div class="data-col w-15 data-col-end"></div>
										</div>

									</template>
								</div><!-- data-list -->
							</div><!-- .nk-block -->
							<div v-show="school.school_ownership_status_id === 2" class="nk-block">
								<school-owners
									ref="schoolOwners"
									:school-type-id-obj="school.school_type_id"
									:countries-obj="countriesObj"
									:school-obj="schoolObj"
								></school-owners>
							</div><!-- .nk-block -->

						</div><!-- .tab-pane -->
						<div class="tab-pane" id="locationDetails">
							<div class="nk-block-head nk-block-head-lg">
								<div class="nk-block-between">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Location Details</h4>
										<div class="nk-block-des">
											<p>School Location Details.</p>
										</div>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div><!-- .nk-block-head -->
							<div class="nk-block">
								<div class="nk-data data-list">
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">District</span>
											<span v-if="school.district === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.district.name.toUpperCase() }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">County/Municipality</span>
											<span v-if="school.county === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.county.name.toUpperCase() }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Sub-County/Division</span>
											<span v-if="school.sub_county === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.sub_county.name.toUpperCase() }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Parish/Ward</span>
											<span v-if="school.parish === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.parish.name.toUpperCase() }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Physical Address</span>
											<span v-if="school.physical_address === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.physical_address }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Latitude</span>
											<span v-if="school.latitude === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.latitude }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Longitude</span>
											<span v-if="school.longitude === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.longitude }}</span>
										</div>
										<div class="data-col data-col-end"></div>
									</div><!-- data-item -->
								</div>
								<div v-if="googleMapsApi.length" class="card-inner">
									<div id="map"></div>
								</div>
							</div>
						</div><!-- .tab-pane -->
						<div class="tab-pane" id="operationDetails">
							<div class="nk-block-head nk-block-head-lg">
								<div class="nk-block-between">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Operational Details</h4>
										<div class="nk-block-des">
											<p>School operational information.</p>
										</div>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div><!-- .nk-block-head -->
							<div class="nk-block">
								<div class="nk-data data-list">
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">ECCE Center Type</span>
											<span class="data-value text-dark ucap" v-if="school.pre_primary_school.has_daycare_center && school.pre_primary_school.has_nursery_section">BOTH NURSERY & DAY CARE</span>
											<span class="data-value text-dark ucap" v-if="school.pre_primary_school.has_daycare_center && !school.pre_primary_school.has_nursery_section">DAY CARE ONLY</span>
											<span class="data-value text-dark ucap" v-if="!school.pre_primary_school.has_daycare_center && school.pre_primary_school.has_nursery_section">NURSERY ONLY</span>
											<span v-if="!school.pre_primary_school.has_daycare_center && !school.pre_primary_school.has_nursery_section" class="data-value text-muted font-italic">Not set</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.is_attached_to_primary === 1">
										<div class="data-col">
											<span class="data-label">Primary School EMIS No.</span>
											<span v-if="school.primary_school_emis_number === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark ucap">{{ school.primary_school_emis_number }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.is_attached_to_primary === 1">
										<div class="data-col">
											<span class="data-label">Primary School Telephone Contact</span>
											<span v-if="school.primary_school_telephone_contact === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">+256{{ school.primary_school_telephone_contact }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.school_ownership_status_id === 1">
										<div class="data-col">
											<span class="data-label">Registration Status</span>
											<span class="data-value text-dark ucap">Registered/Licenced</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
										<div class="data-col">
											<span class="data-label">Registration Number</span>
											<span v-if="licenceStatus === 'not-set'" class="data-value text-muted font-italic">Not Set</span>
											<span v-if="licenceStatus === 'pending'" class="data-value text-muted font-italic">Pending</span>
											<span v-if="licenceStatus === 'active' && school.licence.type === 'registration'" class="data-value text-dark ucap">{{ school.licence.licence_number }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
										<div class="data-col">
											<span class="data-label">License Number</span>
											<span v-if="licenceStatus === 'not-set'" class="data-value text-muted font-italic">Not Set</span>
											<span v-if="licenceStatus === 'pending'" class="data-value text-muted font-italic">Pending</span>
											<span v-if="licenceStatus === 'active' && school.licence.type === 'licence'" class="data-value text-dark ucap">{{ school.licence.licence_number }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
										<div class="data-col">
											<span class="data-label">Issue Date</span>
											<span v-if="licenceStatus === 'not-set' || licenceStatus === 'pending'" class="data-value text-muted font-italic">Not Set</span>
											<span v-if="licenceStatus === 'active'" class="data-value text-dark ucap">{{ school.licence.start_date }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
										<div class="data-col">
											<span class="data-label">Expiry Date</span>
											<span v-if="licenceStatus === 'not-set' || licenceStatus === 'pending'" class="data-value text-muted font-italic">Not Set</span>
											<span v-if="licenceStatus === 'active'" class="data-value text-dark ucap">{{ school.licence.end_date }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->

									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Sex Composition</span>
											<span class="data-value text-dark ucap" v-if="school.has_male_students && school.has_female_students">MIXED</span>
											<span class="data-value text-dark ucap" v-if="school.has_male_students && !school.has_female_students">MALES ONLY</span>
											<span class="data-value text-dark ucap" v-if="!school.has_male_students && school.has_female_students">FEMALES ONLY</span>
											<span v-if="!school.has_male_students && !school.has_female_students" class="data-value text-muted font-italic">Not set</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Boarding Status</span>
											<span class="data-value text-dark ucap" v-if="school.pre_primary_school.admits_day_scholars_yn && school.pre_primary_school.admits_boarders_yn">PARTLY BOARDING</span>
											<span class="data-value text-dark ucap" v-if="school.pre_primary_school.admits_day_scholars_yn && !school.pre_primary_school.admits_boarders_yn">DAY SCHOOL</span>
											<span class="data-value text-dark ucap" v-if="!school.pre_primary_school.admits_day_scholars_yn && school.pre_primary_school.admits_boarders_yn">FULLY BOARDING</span>
											<span v-if="!school.pre_primary_school.admits_day_scholars_yn && !school.pre_primary_school.admits_boarders_yn" class="data-value text-muted font-italic">Not set</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
									<div v-if="school.school_ownership_status_id === 2" class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Capital For Establishment</span>
											<span v-if="school.capital_for_establishment === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">UGX {{ formatMoney(school.capital_for_establishment) }}</span>
										</div>
										<div class="data-col w-15 data-col-end"></div>
									</div><!-- data-item -->
								</div><!-- data-list -->
							</div><!-- .nk-block -->
						</div><!-- .tab-pane -->
						<div class="tab-pane" id="proximities">
							<div class="nk-block-head nk-block-head-lg">
								<div class="nk-block-between">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Proximities</h4>
										<div class="nk-block-des">
											<p>School proximities to other institutions.</p>
										</div>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div><!-- .nk-block-head -->
							<div class="nk-block">
								<div class="nk-data data-list">
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Distance to DEO/MEO/CEO Office</span>
											<span v-if="school.pre_primary_school.deo_office_distance === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.pre_primary_school.deo_office_distance.name.toUpperCase() }}</span>
										</div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Distance to nearest Pre-Primary School</span>
											<span v-if="school.pre_primary_school.school_distance_range === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.pre_primary_school.school_distance_range.name.toUpperCase() }}</span>
										</div>
									</div><!-- data-item -->
									<div class="data-item py-1">
										<div class="data-col">
											<span class="data-label">Distance to nearest Health Facility</span>
											<span v-if="school.health_facility_distance === null" class="data-value text-muted font-italic">Not Set</span>
											<span v-else class="data-value text-dark">{{ school.health_facility_distance.name.toUpperCase() }}</span>
										</div>
									</div><!-- data-item -->
								</div><!-- data-list -->
							</div><!-- .nk-block -->
							
						</div><!-- .tab-pane -->
						<div class="tab-pane" id="healthInfo">
							<div class="nk-block-between">
								<div class="nk-block-head-content d-flex justify-content-between w-100">
									<div class="nk-block-head-content">
										<h4 class="nk-block-title">Health Information</h4>
									</div>
									<div class="nk-block-head-content align-self-start d-lg-none">
										<a class="toggle cursor btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
									</div>
								</div>
							</div>
							<!-- sexuality education policy -->
							<div class="nk-data data-list">
								<div class="data-item py-1">
									<div class="data-col">
										<span class="data-label">Does your school provide a hot midday meal to learners ?</span>
										<span v-if="school.pre_primary_school.offer_hot_midday_meal_to_learners === true" class="pl-lg-5 ml-lg-5 data-value text-dark">YES</span>
										<span v-else class="pl-lg-5 ml-lg-5 data-value text-dark">NO</span>
									</div>
								</div><!-- data-item -->
								<div v-if="school.meal_types.length" class="data-item py-1">
									<div class="data-col">
										<span class="data-label">Meal Types</span>
										<span class="pl-lg-5 ml-lg-5 data-value text-dark">
											<span v-for="meal_type in school.meal_types" class="d-block">
												- {{ meal_type.name.toUpperCase() }}
											</span>
										</span>
									</div>
								</div><!-- data-item -->
								<div class="data-item py-1">
									<div class="data-col">
										<span class="data-label">Source of Food For Learners</span>
										<span v-if="school.food_sources.length" class="pl-lg-5 ml-lg-5 data-value text-dark">
											<span v-for="food_source in school.food_sources" class="d-block">
												- {{ food_source.name.toUpperCase() }}
											</span>
										</span>
										<span v-else class="pl-lg-5 ml-lg-5 data-value text-dark">NONE</span>
									</div>
								</div><!-- data-item -->
							</div>

							
						</div>
						<div class="tab-pane" id="governance">
							<pre-primary-school-smc-committee
							ref="smcCommittee"
							:countries-obj="countriesObj"
							></pre-primary-school-smc-committee>
						</div><!-- .tab-pane -->
						
					</div><!-- .tab-content -->
				</div>
			</div><!-- .card-aside-wrap -->
		</div><!-- .card -->

		<!-- Logo Update Modal -->
		<div class="modal fade zoom" tabindex="-1" id="updateLogoModal">
			<div class="modal-dialog modal-sm modal-dialog-centered" role="document">
				<form @submit.prevent="updateLogo()">
					<div class="modal-content">
						<a @click="resetLogo()" class="cursor close" data-dismiss="modal" aria-label="Close">
							<em class="icon ni ni-cross"></em>
						</a>
						<div class="modal-header">
							<h5 class="modal-title">Update Logo</h5>
						</div>
						<div class="modal-body">
							<div class="row g-4">
								<div class="col-12">
									<div class="align-self-center form-group">
										<input
										id="schoolLogo"
										ref="logo" @change="selectFile"
										accept="image/png,image/jpeg"
										data-max-file-size="2M"
										type="file"
										class="dropify"
										data-height="180"
										data-allowed-file-extensions="jpeg jpg png"
										data-default-file="/images/school-logos/logo.png" />
									</div>
								</div>
							</div>
							<div class="row mt-2">
								<div class="col-12">
									<span @click="school.logo === '' ? selectLogo() : resetLogo()" class="btn btn-block bg-dark-teal">
										<em class="icon ni ni-camera-fill mr-1"></em>
										<span v-if="school.logo === ''">Select Logo</span>
										<span v-else>Remove Logo</span>
									</span>
								</div>
							</div>
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button @click="resetLogo()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button :disabled="loading" type="submit" class="btn btn-primary d-flex">
								<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="loading" class="align-self-center">Updating...</span>
								<span v-if="loading" class="sr-only">Updating...</span>
								<span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<!-- Logo Update Modal -->

		<!-- Loading Modal -->
		<div style="display:none;" id="schoolLoadingMessage" class="card card-preview">
			<div class="card-inner">
				<div class="d-flex align-items-center">
					<strong>Deleting...</strong>
					<div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
				</div>
			</div>
		</div>
		<!-- /Loading Modal -->

	</div><!-- .nk-block -->
</template>

<script>
	import Notifications from "../Notifications.vue";
	import { Loader } from "@googlemaps/js-api-loader";
	import InstitutionUsers from './users/Index.vue';
	import LicenseStatus from './PrePrimarySchoolLicense.vue';
	import SchoolOwners from './SchoolOwners.vue';
	import PrePrimarySchoolSmcCommittee from './PrePrimarySchoolSmcCommittee.vue';
	import UpdateSchoolName from './UpdateSchoolName.vue';
	import UpdateSchoolOwnership from './UpdateSchoolOwnership.vue';
	import NameChangeRequestsTable from './NameChangeRequestsTable.vue';
	import OwnershipChangeRequestsTable from './OwnershipChangeRequestsTable.vue';
	export default {
		name: "PrePrimarySchoolProfile",
		props: [
			'levelObj',
			'schoolObj',
			'usersObj',
			'googleMapsApi',
			'countriesObj'
		],
		components: {
			Notifications,
			InstitutionUsers,
			LicenseStatus,
			SchoolOwners,
			PrePrimarySchoolSmcCommittee,
			UpdateSchoolName,
			UpdateSchoolOwnership,
			NameChangeRequestsTable,
			OwnershipChangeRequestsTable,
		},
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
				edit: false,
				apiUrl: '/institutions/pre-primary-schools',
				valid_file: false,
				logoDropify: null,
				map: null,
				marker: null,
				school: {
					logo: '',
					emis_number: '',
					district_id: '',
					county_id: '',
					sub_county_id: '',
					parish_id: '',
					school_ownership_status_id: '',
					district: {
						name: '',
					},
					county: {
						name: '',
					},
					sub_county: {
						name: '',
					},
					parish: {
						name: '',
					},
					founding_body: {
						name: '',
					},
					ownership_status: {
						name: '',
					},
					land_owner_type: {
						name: '',
					},
					legal_ownership_status: {
						name: '',
					},
					operational_status: {
						name: '',
					},
					licence: {
						licence_number: '',
						type: '',
						status: '',
					},
					school_gender_category: '',
					has_male_students: '',
					has_female_students: '',
					pre_primary_school: {
						has_daycare_center: '',
						has_nursery_section: '',
						ecce_center_type: '',
						admits_day_scholars_yn: '',
						admits_boarders_yn: '',
						school_boarding_status: '',
						school_distance_range: {
							name: '',
						},
						deo_office_distance: {
							name: '',
						},
						offer_hot_midday_meal_to_learners: '',
					},
					health_facility_distance: {
						name: '',
					},
					registration_status: {
						name: '',
					},
					head_teacher: {
						teacher: {
							full_name: '',
							contacts: [{contact: ''}],
						},
					},
					owners: [],
					licenses: [],
					certificates: [],
					is_attached_to_primary: 0,
					primary_school_emis_number: '',
					primary_school_name: '',
					primary_school_telephone_contact: '',
					registration_status_id: 0,
					license_number: '',
					licence_certificate_expiry_date: '',
					registration_number: '',
					has_adopted_sex_hiv_policy: 0,
					meal_types: [],
					food_sources: [],
					school_type_id: '',
				},
			showUpdateNameForm: false,
			updateNameForm: {
				schoolName: '',
				reason: '',
				description: '',
			},
			changeReasons: [], // Array to hold the reasons
			showUpdateOwnershipForm: false,
			nameChangeRequests: [], // Add this to store the requests
			ownershipChangeRequests: [], // Initialize as an empty array

			refreshTrigger: 0,
			ownershipRefreshCounter: 0
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;

				this.school = this.schoolObj;
				this.countries = this.countriesObj;
				this.$refs.schoolOwners.owners = this.school.owners;
				this.$refs.smcCommittee.smc_committee = this.school.smc_committee;

				this.logoDropify = $('#schoolLogo').dropify({
					messages: {
						'default': '',
						'replace': 'Click to replace',
						'remove': 'Remove',
						'error': 'Ooops, something wrong appended.'
					},
					error: {
						'fileSize': 'The file size is too big (2MB max).'
					}
				});

				this.logoDropify.on('dropify.afterClear', function(event, element){
					self.school.logo = '';
					self.$refs.logo.value=null;
				});

				$('.dropify-clear').click(()=>{
					this.resetLogo();
				});
				this.resetLogo();
			},
			formatMoney: function (raw_money = '') {
				if (typeof raw_money === "string" && raw_money.length) {
					return moneyFormat.to(moneyFormat.from(raw_money.replace(/[^0-9]/g,"")));
				} else if(typeof raw_money === "number") {
					return moneyFormat.to(moneyFormat.from(raw_money.toString().replace(/[^0-9]/g,"")));
				}
				return null
			},
			selectFile() {
				this.school.logo = this.$refs.logo.files[0];
			},
			selectLogo() {
				$('.dropify').click();
			},
			updateLogo: function () {
				let formData = new FormData();
				this.loading = true;
				formData.append('logo', this.school.logo);
				axios.post(this.apiUrl + '/update-logo', formData, {headers: {'Content-Type': 'multipart/form-data'}})
				.then(response=>{
					this.school.logo_url = response.data.logo_url;
					this.school.logo = response.data.logo;
					$('#updateLogoModal').modal('hide');
					this.resetLogo();
					this.$refs.notify.messages.push({status: 'success', title: 'Success: ', message:"Logo updated successfully"});
				})
				.catch(error=>{
					console.log(error)
					this.loading = false;
					if (error.response.data.errors !== undefined) {
						for (let field in error.response.data.errors) {
							this.showError(error.response.data.errors[field]);
						}
					} else {
						this.renderError(error)
					}
				});
			},
			resetLogo: function () {
				let filedropper = this.logoDropify.data('dropify');
				filedropper.resetPreview();
				filedropper.clearElement();
				filedropper.settings['defaultFile'] = this.school.logo_url;
				filedropper.destroy();
				filedropper.init();
				this.loading = false;
			},
			downloadLetter: function () {
				this.loading = true;
				try {
					axios({
						method: 'get',
						url: '/institution/emis-number-certificates/' + this.school.emis_number,
						responseType: 'arraybuffer'
					})
						.then(response=>{
							let blob = new Blob([response.data], { type: 'application/pdf' });
							console.log(blob);
							let a = window.document.createElement('a');
							a.href = window.URL.createObjectURL(blob, {
								type: 'data:application/pdf'
							})
							a.download = this.school.emis_number+'-emis-number-certificate.pdf';
							document.body.appendChild(a);
							a.click();
							window.URL.revokeObjectURL(a.href);
							document.body.removeChild(a);
						})
						.catch(error => {
							// Handle error
							this.renderError(error)
						})
						.finally(() => {
							this.loading = false;
						});
				} catch (error) {
					// Handle error
					this.renderError(error)
				}
			},
			startLoading: function() {
				$.blockUI({
					message: $('#schoolLoadingMessage'),
					css: {
						padding:0,
						margin:0,
						width:'30%',
						top:'40%',
						left:'35%',
						textAlign:'center',
						color:'#364a63',
						wordWrap: 'break-word',
						backgroundColor: '#fff',
						backgroundClip: 'border-box',
						border: '0 solid rgba(0, 0, 0, 0.125)',
						borderRadius: '4px',
						cursor:'wait'
					},
				});
			},
			showError: function (message) {
				let text = '';
				for (let i = 0; i < message.length; i++) {
					text += message[i]+'<br>'
				}
				this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
			},
			renderError: function (error) {
				if (error.response && (error.response.status === 500 || error.response.status === 405)) {
					this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
				} else if (error.response && error.response.status === 401) {
					this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
				} else if (error.response && error.response.status === 404) {
					this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
				} else if (error.response && error.response.status === 422) {
					for (let field in error.response.data.errors) {
						this.showError(error.response.data.errors[field]);
					}
				} else {
					this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
				}
			},
			submitNameUpdate: function () {
			let self = this;

			// Validate required fields
			if (!self.updateNameForm.schoolName || !self.updateNameForm.reason) {
				self.$refs.notifyError.messages.push({
					status: 'error',
					title: 'Validation Error',
					message: 'School name and reason for change are required.'
				});
				return;
			}

			Swal.fire({
				title: 'Are you sure?',
				html: '<div style="margin-bottom: 10px;">' +
					'You are about to change the school name from <strong>' + self.school.name + '</strong> to <strong>' + self.updateNameForm.schoolName + '</strong>.</div>' +
					'<div class="text-info">NOTE: Once approved by admin, this name change will be reflected in the system.</div>',
				icon: 'warning',
				confirmButtonText: 'Yes, Proceed',
				showCancelButton: true,
				cancelButtonColor: '#29384A',
				reverseButtons: true,
			}).then(function (result) {
				if (result.value) {
					self.loading = true;
					axios.post('/institutions/secondary-schools/update-name', {
						new_name: self.updateNameForm.schoolName,
						reason_id: self.updateNameForm.reason, // Send reason_id instead of reason text
						description: self.updateNameForm.description || '',
						old_value: self.school.name,
						school_id: self.school.id  // Add the school_id from the school object
					})
					.then(response => {
						self.loading = false;
						self.showUpdateNameForm = false;

						// Reset the form
						self.updateNameForm.schoolName = '';
						self.updateNameForm.reason = '';
						self.updateNameForm.description = '';

						Swal.fire({
							title: 'Success',
							html: '<div style="margin-bottom: 10px;">' +
								'The school name change request has been submitted successfully.</div>' +
								'<div>The new name will be reflected once approved by admin.</div>',
							icon: 'success',
							confirmButtonText: 'Close',
							showCancelButton: false,
						});
					})
					.catch(error => {
						self.loading = false;
						self.renderError(error);
					});
				}
			});
		},
		cancelNameUpdate: function () {
			this.showUpdateNameForm = false;
			this.updateNameForm.schoolName = '';
			this.updateNameForm.reason = '';
			this.updateNameForm.description = '';
		},
		fetchChangeReasons() {
			axios.get('/institutions/change-reasons')
				.then(response => {
					this.changeReasons = response.data;
				})
				.catch(error => {
					console.error("There was an error fetching the change reasons:", error);
				});
		},
		handleNameUpdateSuccess(response) {
			this.showUpdateNameForm = false;

			// Switch to basicInfo tab
			$('a[href="#basicInfo"]').tab('show');

			Swal.fire({
				title: 'Success',
				html: '<div style="margin-bottom: 10px;">' +
					'The school name change request has been submitted successfully.</div>' +
					'<div>The new name will be reflected once approved by admin.</div>',
				icon: 'success',
				confirmButtonText: 'Close',
				showCancelButton: false,
			}).then(() => {
				// Refresh the change requests
				this.fetchNameChangeRequests();
			});
		},
		handleNameUpdateError(error) {
			this.renderError(error);
		},
		editOwnershipStatus() {
			// Implement the logic to edit ownership status
			console.log('Edit ownership status');
		},
		handleOwnershipUpdateSuccess(response) {
			// Hide the update form
			this.showUpdateOwnershipForm = false;

			// Refresh the school data if needed
			if (response.school) {
				this.school = response.school;
			}
		},
		handleOwnershipRequestSubmitted() {
			// Refresh the table
			this.ownershipRefreshCounter++;

			// Fetch latest requests to update badge status
			this.fetchOwnershipChangeRequests();
		},

		handleOwnershipUpdateError(error) {
			Swal.fire({
				title: 'Error',
				text: error.response?.data?.message || 'Error updating ownership status',
				icon: 'error',
				confirmButtonText: 'Ok'
			});
		},
		handleNameChangeRequestError(error) {
			console.error('Error in name change requests:', error);
			this.$refs.notify.error(error.response?.data?.message || 'Error fetching name change requests');
		},

		fetchOwnershipChangeRequests() {
			axios.get(`/institutions/${this.schoolObj.id}/change-requests-ownership`)
				.then(response => {
					this.ownershipChangeRequests = response.data;
				//  console.log('Ownership Change Requests:', this.ownershipChangeRequests); // Log the array
				})
				.catch(error => {
				//  console.error('Error fetching ownership change requests:', error);
				});
		},
		fetchNameChangeRequests() {
		//   console.log(this.schoolObj);
			axios.get(`/institutions/${this.schoolObj.id}/change-requests`)
				.then(response => {
					this.nameChangeRequests = response.data;
				})
				.catch(error => {
					console.error('Error fetching name change requests:', error);

				});
		},
		handleRequestSubmitted() {
			// Increment the trigger to force a refresh
			this.refreshTrigger++;
		}
	},
	created() {
		this.fetchChangeReasons();
		this.fetchNameChangeRequests(); // Add this to fetch requests on creation
		this.fetchOwnershipChangeRequests();
	},
	computed: {
		founderYears: function () {
			let years = [];

			for (var i = 1900; i <= Number(moment().format('YYYY')); i++) {
				years.push(i);
			}

			return years;
		},
		founders: function () {
			return this.founding_bodies.filter(founding_body=>{
				return founding_body.name.search('GOVERNMENT') === -1;
			})
		},
		legal_owners: function () {
			return this.legal_ownership_statuses.filter(legal_owner=>{
				return legal_owner.name.search('GOVERNMENT') === -1;
			})
		},
		hasPendingNameChange() {
			return this.nameChangeRequests.some(request => request.approval_status === 'PENDING');
		},
		hasPendingOwnershipChange() {
			return this.ownershipChangeRequests.some(request => request.approval_status === 'PENDING');
		},
		licenceStatus: function() {
				if (!this.school.licence) {
					return 'not-set';
				}
				if (this.school.licence.status === 0) {
					return 'pending';
				}
				if (this.school.licence.licence_number && this.school.licence.status !== 0) {
					return 'active';
				}
				return null; // Fallback case
			},
	},   
	watch: {
		'school.id': {
			handler: 'fetchNameChangeRequests',
			immediate: true
		},
		'school.id': {
			handler: 'fetchOwnershipChangeRequests',
			immediate: true
		}

	}
}
</script>

<style scoped>
#map {
	height: 400px;
}
</style>
