<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Download Centre</h3>
                    <div class="nk-block-des text-soft">
                        You have a total of <span class="text-base">
                            {{ export_requests ? export_requests.length : 0 }}
                        </span> export requests.
                    </div>
                </div><!-- .nk-block-head-content -->
                <notifications ref="notify"></notifications>
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a class="cursor btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em
                                class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li>
                                    <button @click="showModal = true" class="btn bg-dark-teal text-white">
                                        <em class="icon ni ni-plus"></em>
                                        <span>New Export Request</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="nk-block-des text-soft mb-1">
                <button @click="refreshTable" class="btn text-white bg-dark-teal flex items-center gap-4">
                    <span>Refresh list</span>
                    <em class="icon ni ni-reload"></em>
                </button>
                <!-- <span class="text-base"> Last refreshed: {{ lastRefresh }}</span> -->
            </div>
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <!-- Table Section -->
                    <div class="card-inner p-0">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase">#</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase">File Name</span>
                                </div>
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase">Date
                                        Requested</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white text-uppercase">Status</span>
                                </div>
                                <div class="nk-tb-col text-right"><span
                                        class="sub-text text-white text-uppercase">Actions</span></div>
                            </div>

                            <div v-for="(exportrequest, index) in export_requests" :key="index" class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-dark tb-lead">{{ index + 1 }}.</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span class="text-dark tb-lead text-uppercase">{{ exportrequest.file_name }}</span>
                                </div>
                                <div class="nk-tb-col px-1 align-middle text-uppercase">
                                    <span class="text-dark tb-lead">{{ exportrequest.status === 'completed' ?
                                        formatDate(exportrequest.date_created) : exportrequest.request_date }}</span>
                                </div>
                                <div class="nk-tb-col px-1 align-middle text-uppercase">
                                    <span :class="getStatusBadgeClass(exportrequest.status)" class="badge">
                                        {{ exportrequest.status ?? "--" }}
                                    </span>
                                </div>
                                <div class="nk-tb-col text-right">
                                    <button v-if="exportrequest.status === 'completed'"
                                        @click="downloadFile(exportrequest.uuid)" class="btn btn-lg text-primary">
                                        <em class="icon ni ni-download"></em>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!export_requests || export_requests.length === 0" class="p-5">
                    <div class="alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em>
                        No Export requests found
                    </div>
                </div>
            </div>

            <!-- Modal Section -->
            <div class="modal fade" :class="{ show: showModal }" tabindex="-1" role="dialog"
                :style="{ display: showModal ? 'block' : 'none' }">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Create New Export Request</h5>
                            <a @click="closeModal" class="close">
                                <em class="icon ni ni-cross"></em>
                            </a>
                        </div>
                        <div class="modal-body">
                            <form @submit.prevent="submitRequest">
                                <div class="form-group mb-4">
                                    <label class="form-label" for="exportRequestType">Export Type</label>
                                    <div class="form-wrap">
                                        <select class="form-select-lg form-select form-control form-control-xl"
                                            id="exportRequestType" v-model="selectedExportType" :disabled="loading"
                                            required>
                                            <option value="">Select Export Type</option>
                                            <option v-for="exportType in export_request_types" :key="exportType.id"
                                                :value="exportType.id">
                                                {{ exportType.name }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group d-flex justify-content-between align-items-center mt-4">
                                    <button type="button" @click="closeModal" class="btn btn-secondary">Cancel</button>
                                    <button type="submit" :disabled="loading" class="btn bg-dark-teal text-white">
                                        <span v-if="loading" class="spinner-border spinner-border-sm mr-1"></span>
                                        {{ loading ? "Requesting..." : "Request Export" }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="showModal" class="modal-backdrop fade show"></div>
        </div>
    </div>
</template>

<script>
import Notifications from '../../Notifications.vue';
import moment from "moment";

export default {
    name: "DownloadCentre",
    props: ['exportrequesttypesobj', 'academicyearidobj', 'exportrequestsobj'],
    components: {
        Notifications
    },
    data() {
        return {
            showModal: false,
            selectedExportType: '',
            loading: false,
            apiUrl: '/institutions/download-centre/',
            academic_year_id: '',
            export_request_types: [],
            export_requests: [],
            lastRefresh: localStorage.getItem("last_refresh") || "Never",
        };
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.academic_year_id = this.academicyearidobj;
            this.export_request_types = this.exportrequesttypesobj;
            this.export_requests = this.exportrequestsobj;
            this.updateLastRefresh();
        },
        getStatusBadgeClass(status) {
            const classes = {
                'completed': 'text-uppercase badge badge-dark-teal',
                'pending': 'text-uppercase badge badge-primary',
                'processing': 'text-uppercase badge badge-primary',
                'failed': 'text-uppercase badge badge-red'
            };
            return classes[status] || 'badge-secondary';
        },
        async submitRequest() {
            if (!this.selectedExportType || !this.academic_year_id) {
                this.$refs.notify.messages.push({ 
                    status: 'error', 
                    title: 'Check Again:', 
                    message: 'You may have some missing fields.'
                });
                this.closeModal();
                return;
            }

            this.loading = true;
            try {
                const response = await axios.post(this.apiUrl + 'export-request', {
                    export_request_type_id: this.selectedExportType,
                    academic_year_id: this.academic_year_id
                });

                this.closeModal();
                this.loading = false;

                if (response?.status === 200 && response?.data?.exportRequests) {
                    this.export_requests = response.data.exportRequests;
                }
            } catch (error) {
                this.loading = false;
                this.closeModal();
                this.renderError(error);
            }
        },
        closeModal() {
            this.showModal = false;
            this.selectedExportType = '';
            this.loading = false;
        },
        downloadFile(uuid) {
            axios.get(this.apiUrl + 'export-requests/' + uuid + '/download', {
                responseType: 'blob'
            })
                .then(response => {
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', response.headers['content-disposition'].split('=')[1]);
                    document.body.appendChild(link);
                    link.click();
                })
                .catch(error => {
                    this.renderError(error);
                });
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({ status: 'error', title: 'System Error:', message: error.response.data.message });
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({ status: 'error', title: 'Permission Error:', message: 'You are not authorised to perform this action' });
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({ status: 'error', title: 'Resource Not Found:', message: 'You are trying to reach a url that does not exist' });
            } else if (error.response && error.response.status === 422) {
                this.$refs.notify.messages.push({ status: 'error', title: 'Something went wrong! ', message: error.response?.data?.message });
            } else {
                this.$refs.notify.messages.push({ status: 'error', title: 'Other Error:', message: error.message });
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i] + '<br>'
            }
            if (this.preview) {
                this.$refs.notifyModal.messages.push({ status: 'error', title: 'Data Error!', message: text });
            } else {
                this.$refs.notify.messages.push({ status: 'error', title: 'Data Error!', message: text });
            }
        },
        updateLastRefresh() {
            const now = new Date().toLocaleTimeString();
            localStorage.setItem("last_refresh", now);
            this.lastRefresh = now;
        },
        async refreshTable() {
            this.updateLastRefresh();
            try {
                const response = await axios.get(this.apiUrl + 'export-requests');

                if (response?.status === 200 && response?.data?.exportRequests) {
                    this.export_requests = response.data.exportRequests;
                }
            } catch (error) {
                this.renderError(error);
            }
        },
        formatDate: function (date) {
            return moment(date).format("MMMM DD, YYYY");
        },
    },
    mounted() {
        this.initPlugins();
    }
};
</script>

<style scoped>
.modal {
    z-index: 1000;
}

.modal-backdrop {
    z-index: 999;
}
</style>