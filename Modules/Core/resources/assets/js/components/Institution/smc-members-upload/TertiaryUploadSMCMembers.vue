<template>
    <div class="col-lg-12">
        <div class="nk-block nk-block-lg">
            <div class="nk-block-head">
                <div class="nk-block-head-content">
                    <h5 class="title nk-block-title">Follow instructions below</h5>
                    <div class="nk-block-des">
                        <p>Import Members by excel</p>
                    </div>
                </div><!-- .nk-block-head-content -->
            </div>
            <div class="card card-preview">
                <div style="height: calc(100vh - 145px);" class="card-inner overflow-auto scrollbar-dark-teal" data-simplebar>
                    <div class="row">
                        <div class="col-12 d-flex flex-column">
                            <span class="align-self-start fs-16 font-weight-bold mr-lg-2">
                              Step 1:
                            </span>
                            <span>Download and the Excel template below</span>
                            <button @click="downloadTemplate()" class="btn bg-dark-teal align-self-start mt-2">
                                <em class="icon ni ni-file-download"></em><span>Download Excel Template</span>
                            </button>
                        </div>
                    </div>
                    <div class="row mt-5">
                        <div class="col-12 d-flex flex-column">
                            <span class="align-self-start fs-16 font-weight-bold mr-lg-2">
                              Step 2:
                            </span>
                            <span>Fill out the downloaded template with the corresponding information</span>
                        </div>
                    </div>
                    <div class="row mt-5">
                        <div class="col-12 d-flex flex-column">
                            <span class="align-self-start fs-16 font-weight-bold mr-lg-2">
                              Step 3:
                            </span>
                            <span>Upload the completed Excel Template you filled from Step 2 then click upload</span>
                            <form @submit.prevent="uploadFilledTemplate()">
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div style="margin-top: 1px !important;" class="custom-file mr-0 mr-lg-2">
                                            <input :disabled="loading" ref="filledTemplate" type="file" :class="[loading ? '' : 'cursor', 'custom-file-input']" id="filledTemplate" @change="checkFile()" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" required>
                                            <label id="filledTemplateLabel" class="custom-file-label" for="filledTemplate">Upload Filled Excel Template</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <button type="submit" :disabled="loading" class="btn bg-dark-teal">
                                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span v-if="loading" class="align-self-center">Uploading...</span>
                                            <span v-if="loading" class="sr-only">Uploading...</span>
                                            <span v-if="!loading" class="align-self-center">Upload</span><em v-if="!loading" class="ni ni-upload ml-2"></em>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
	export default {
		name: "TertiaryUploadSMCMembers",
		props: [],
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
                saving_loading: false,
                valid_file: false,
                filled_template: '',
                smc_committee: [],
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;
			},
			downloadTemplate: function () {
				axios({
					method: 'get',
					url: '/institutions/smc-members-download-attendance-template',
					responseType: 'arraybuffer'
				})
				.then(response=>{
					let blob = new Blob([response.data], { type: 'application/octet-stream' });
					let a = window.document.createElement('a');
					a.href = window.URL.createObjectURL(blob, {
						type: 'data:attachment/xlsx'
					})
					a.download = 'School-management-committee-members.xlsx';
					document.body.appendChild(a);
					a.click();
					document.body.removeChild(a);
				})
				.catch(error=>{
					console.log(error)
					new Noty({
						type: "error",
						text: error.response.data.message
					}).show();
				});
			},
            checkFile: function () {
				let fileExt = this.$refs.filledTemplate.value;
				fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

				if (fileExt.toLowerCase() !== ".xlsx") {
					new Noty({
						type: "error",
						text: "Invalid file selected, only XLSX Excel files allowed!"
					}).show();

					this.resetFile();
					return false;
				}
				this.filled_template = this.$refs.filledTemplate.files[0];

				return true;
			},
			resetFile: function () {
				this.filled_template = '';
			    this.loading = false;
				this.$refs.filledTemplate.value = null;
				window.setTimeout(()=>{$('#filledTemplateLabel').text("Upload Filled Excel Template")}, 10);
			},
			uploadFilledTemplate: function () {
				this.loading = true;
				let formData = new FormData();
				formData.append('filled_template', this.filled_template);

				axios.post('/institutions/upload-tertiary-school-members-template', formData, {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				})
				.then(response=>{
                    console.clear();
					this.smc_committee = response.data;
                    this.$emit('fresh-data', response.data);
                    this.resetSchoolSmcCommitteeMember();
					this.resetFile();
					new Noty({
						type: "success",
						text: "SMC Members Uploaded Successfully"
					}).show();
				})
				.catch(error=>{
					this.loading = false;
					console.log(error);
					new Noty({
						type: "error",
						text: error.response.data.message
					}).show();
				});
			},
            resetSchoolSmcCommitteeMember: function () {
                $('#uploadSmcCommitteeMemberModal').modal('hide');
                this.loading = false;
                this.edit = false;
                this.member = {
                    id: '',
                    name: '',
                    nin: '',
                };
            },
		},
		computed: {

		}
	}
</script>

<style scoped>

</style>
