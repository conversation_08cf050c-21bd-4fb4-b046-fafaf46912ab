<template>
	<div>
		<div class="nk-block-head nk-block-head-sm">
			<div class="nk-block-between">
				<div class="nk-block-head-content">
					<h3 class="nk-block-title page-title">Participation In Extra-curricular Sports Activities</h3>
					<div class="nk-block-des text-soft">
						<p>Learners Participation In Extra-curricular Sports Activities Report</p>
					</div>
				</div><!-- .nk-block-head-content -->
			</div><!-- .nk-block-between -->
		</div><!-- .nk-block-head -->
		<div class="nk-block">
			<div class="card card-stretch card-bordered border-dark-teal">
				<div class="card-inner-group">

                    <div class="w-100">

                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                <tr class="bg-secondary">
                                    <td rowspan="4" class="text-white text-uppercase border-1 align-middle text-center px-1">
                                        <span class="">ACTIVITY</span>
                                    </td>
                                </tr>
                                <tr class="bg-secondary">
                                    <td :colspan="2*extra_curricular_activity_levels.length" class="text-white border-1 text-uppercase align-middle text-center px-1">
                                        <span class="">Level OF Participation</span>
                                    </td>
                                </tr>
                                <tr class="bg-secondary-dim">
                                    <td v-for="level in extra_curricular_activity_levels" colspan="2" class="text-dark border-secondary border-1 text-uppercase align-middle text-center px-1">
                                        <span class="">{{ level.name }}</span>
                                    </td>
                                </tr>
                                <tr class="bg-secondary-dim">
                                    <td v-for="label in levelGenderLabels" class="text-dark border-secondary border-1 text-uppercase align-middle text-center px-1">
                                        <span class="">{{ label }}</span>
                                    </td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="activity in extra_curricular_activities">
                                    <th class="text-uppercase text-dark border-secondary align-middle px-1 border-1">
                                        <span class="">{{ activity.name }}</span>
                                    </th>
                                    <td v-for="total in levelGenderTotals(activity)" class="text-uppercase text-dark border-secondary align-middle text-center px-1 border-1">
                                        <span class="">{{ total }}</span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

				</div><!-- .card-inner-group -->
			</div><!-- .card -->
		</div><!-- .nk-block -->
	</div>
</template>

<script>
export default {
    name: "ExtraCurricularActivityParticipation",
    props: [
        'extraCurricularActivitiesObj',
        'extraCurricularActivityLevelsObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            extra_curricular_activities: [],
            extra_curricular_activity_levels: [],
        }
    },
    methods: {
        initPlugins: function () {
            this.extra_curricular_activities = this.extraCurricularActivitiesObj;
            this.extra_curricular_activity_levels = this.extraCurricularActivityLevelsObj;
        },
        levelGenderTotals: function (activity) {
            let labels = [];

            this.extra_curricular_activity_levels.forEach(level=>{
                let maleCount = activity.learner_cocurricular_activities.filter(entry=>{
                    return entry.person.gender === "M" && entry.performing_level_id === level.id;
                }).length;

                let femaleCount = activity.learner_cocurricular_activities.filter(entry=>{
                    return entry.person.gender === "F" && entry.performing_level_id === level.id;
                }).length;

                labels.push(maleCount);
                labels.push(femaleCount);
            });

            return labels;
        },
    },
    computed: {
        levelGenderLabels: function () {
            let labels = [];

            this.extra_curricular_activity_levels.forEach(()=>{
                labels.push("M");
                labels.push("F");
            });

            return labels;
        },
    },
}
</script>

<style scoped>

</style>
