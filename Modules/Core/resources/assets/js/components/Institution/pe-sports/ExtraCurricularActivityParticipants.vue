<template>
	<div>
		<div class="nk-block-head nk-block-head-sm">
			<div class="nk-block-between">
				<div class="nk-block-head-content">
					<h3 class="nk-block-title page-title">{{ extra_curricular_activity.activity_name }}</h3>
					<div class="nk-block-des text-soft">
						<p>Event has a total of {{ participants.total }} participants.</p>
					</div>
				</div><!-- .nk-block-head-content -->
				<div class="nk-block-head-content">
					<button data-toggle="modal" data-target="#participantsModal" data-backdrop="static" type="button" class="btn bg-dark-teal">
						<em class="icon ni ni-edit-fill text-white mr-1"></em>Edit Participants
					</button>
				</div><!-- .nk-block-head-content -->
			</div><!-- .nk-block-between -->
		</div><!-- .nk-block-head -->

		<div class="nk-block">
			<div class="card card-stretch card-bordered border-dark-teal">
				<div class="card-inner-group">
					<div class="card-inner p-0">
						<div class="nk-tb-list nk-tb-ulist is-compact">
							<div class="nk-tb-item nk-tb-head bg-secondary">
								<div class="nk-tb-col"><span class="sub-text ucap text-white">PARTICIPANTS</span></div>
								<div class="nk-tb-col nk-tb-col-tools"><span class="sub-text ucap text-white">GENDER</span></div>
								<div class="nk-tb-col nk-tb-col-tools text-center"><span class="sub-text ucap text-white">CLASS</span></div>
								<div class="nk-tb-col nk-tb-col-tools text-center"><span class="sub-text ucap text-white">ACTIONS</span></div>
							</div><!-- .nk-tb-item -->
							<div v-for="participant in participants.data" class="nk-tb-item">
								<div class="nk-tb-col">
									<div class="user-card">
										<div class="user-avatar">
											<img :src="participant.learner.person.photo_url" style="border-radius: 0" :alt="participant.learner.full_name">
										</div>
										<div class="user-name text-uppercase">
											<a :href="'/institution/learners/profile/'+participant.learner.emis_id.toLowerCase()" class="tb-lead cursor text-dark">{{ participant.learner.full_name }}</a>
										</div>
									</div>
								</div>
								<div class="nk-tb-col nk-tb-col-tools">
									<span v-if="participant.learner.gender === 0" class="text-dark ucap tb-lead">MALE</span>
									<span v-if="participant.learner.gender === 1" class="text-dark ucap tb-lead">FEMALE</span>
								</div>
								<div class="nk-tb-col nk-tb-col-tools text-center">
									<span class="text-dark ucap tb-lead">{{ participant.learner.education_grade.name.toUpperCase() }}</span>
								</div>
								<div class="nk-tb-col nk-tb-col-tools text-center">
									<a @click="removeParticipants(participant)" data-toggle="tooltip" data-placement="top" title="Remove Participant" class="cursor lead text-danger">
										<em class="icon ni ni-trash-alt"></em>
									</a>
								</div>
							</div><!-- .nk-tb-item -->
						</div><!-- .nk-tb-list -->
					</div><!-- .card-inner -->
					<div v-if="participants.data.length" class="card-inner d-flex flex-row">
						<nav>
							<ul class="pagination">
								<li :class="getLinkClasses(link)" v-for="link in participants.links">
									<a @click="loadParticipants(link)" class="page-link cursor" v-html="link.label"></a>
								</li>
							</ul>
						</nav>
						<div class="d-flex ml-4">
							<span class="align-self-center">
								Showing <span class="text-primary">{{ participants.from }}</span> to <span class="text-primary">{{ participants.to }}</span> of <span class="text-primary">{{ participants.total }}</span>
							</span>
						</div>
					</div><!-- .card-inner -->
				</div><!-- .card-inner-group -->
			</div><!-- .card -->
		</div><!-- .nk-block -->

		<div v-if="!participants.data.length" class="card card-stretch">
			<div class="card-inner-group">
				<div class="card-body">
					<div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
						<em class="icon ni ni-alert-circle"></em> There are no participants to display at the moment.
					</div>
				</div>
			</div>
		</div>
		<!-- Participants Modal -->
		<div class="modal fade zoom" tabindex="-1" id="participantsModal">
			<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
				<div class="modal-content">
					<a @click="resetParticipants()" class="cursor close" data-dismiss="modal" aria-label="Close">
						<em class="icon ni ni-cross"></em>
					</a>
					<form @submit.prevent="updateParticipants()">
						<div class="modal-header">
							<h5 class="modal-title">Add Participants</h5>
						</div>
						<div class="modal-body">
							<div class="row mb-2">
								<div class="col-12 d-flex justify-content-center">
									<span>You have selected <span class="text-dark-teal">{{ selected_learners.length }}</span> participants</span>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-4">
									<div class="form-group">
										<div class="form-control-wrap">
											<select :disabled="loading" id="filterEducationGradeId">
												<option value="">--All Classes--</option>
												<option v-for="classe in education_grades" :value="classe.id">{{ classe.name.toUpperCase() }}</option>
											</select>
										</div>
									</div>
								</div>
								<div class="col-lg-4">
									<div class="form-group">
										<div class="form-control-wrap">
											<select :disabled="loading" id="filterGender">
												<option value="">--All Genders--</option>
												<option value="0">MALE</option>
												<option value="1">FEMALE</option>
											</select>
										</div>
									</div>
								</div>
								<div class="col-lg-4">
									<div class="form-control-wrap">
										<div class="input-group">
											<input :disabled="loading" v-model.trim="filter.name" type="text" class="form-control bg-primary-dim" placeholder="Learner Name">
											<div v-if="filter.name.length !== 0 || filter.gender !== '' || filter.education_grade_id !== ''" class="input-group-append">
												<button @click.prevent="resetFilter()" class="btn bg-dark-teal px-2 text-white" type="button">
													<em class="icon ni ni-cross"></em>
												</button>
											</div>
										</div>
										<div class="form-icon form-icon-right">
											<em class="icon ni ni-search"></em>
										</div>

									</div>
								</div>
							</div>
							<div style="overflow-x: hidden !important;" class="scrollbar-dark-teal mt-4 overflow-auto h-max-400px" data-simplebar>
								<div class="row">
									<div v-for="admission in availableParticipants" class="col-lg-4 mb-2">
										<div class="custom-control custom-control-sm custom-checkbox">
											<input :disabled="loading" :value="admission.learner.id" v-model="selected_learners" type="checkbox" class="custom-control-input custom-control-input-dark-teal" :id="'participant'+admission.learner.id">
											<label class="custom-control-label custom-control-label-dark-teal" :for="'participant'+admission.learner.id">{{ admission.learner.full_name }}</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button @click="resetParticipants()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
								<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="loading" class="align-self-center">Loading...</span>
								<span v-if="loading" class="sr-only">Loading...</span>
								<span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- /Participants Modal -->

		<!-- Loading Modal -->
		<div style="display:none;" id="participantsLoadingMessage" class="card card-preview">
			<div class="card-inner">
				<div class="d-flex align-items-center">
					<strong>Deleting...</strong>
					<div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
				</div>
			</div>
		</div>
		<!-- /Loading Modal -->
	</div>
</template>

<script>
	export default {
		name: "Participants",
		props: ['educationGradesObj', 'learnerIdsObj', 'admissionsObj', 'participantsObj', 'extraCurricularActivityObj'],
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
                apiUrl: '/institutions/extra-curricular-activities',
				loading: false,
				filtering: false,
				select_all_participants: false,
				selected_participants: [],
				selected_learners: [],
				learner_ids: [],
				admissions: [],
				education_grades: [],
				participants: {
					total: 0,
					data: [],
				},
				extra_curricular_activity: {
					id: '',
					activity_name: '',
				},
				filter: {
					education_grade_id: '',
					gender: '',
					name: '',
				},
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;

				$('#filterEducationGradeId').select2({
					minimumResultsForSearch: 0,
					dropdownParent: $('#participantsModal'),
					containerCssClass: 'bg-primary-dim',
					templateSelection: function (data, container) {
						self.filter.education_grade_id = data.id.length > 0 ? Number(data.id) : "";
						return data.text;
					},
				});

				$('#filterGender').select2({
					minimumResultsForSearch: 0,
					dropdownParent: $('#participantsModal'),
					containerCssClass: 'bg-primary-dim',
					templateSelection: function (data, container) {
						self.filter.gender = data.id.length > 0 ? Number(data.id) : "";
						return data.text;
					},
				});

				this.extra_curricular_activity = JSON.parse(this.extraCurricularActivityObj);
				this.admissions = JSON.parse(this.admissionsObj);
				this.learner_ids = JSON.parse(this.learnerIdsObj);
				this.participants = JSON.parse(this.participantsObj);
				this.education_grades = JSON.parse(this.educationGradesObj);
				this.selected_learners = this.learner_ids;
			},
			resetParticipants: function () {
				this.loading = false;
				$('#participantsModal').modal('hide');
				$(".dual-listbox__buttons").children().last().click();
				this.selected_learners = this.learner_ids;
			},
			updateParticipants: function () {
				this.loading = true;
				axios.post(this.apiUrl + '/'+this.extra_curricular_activity.id+'/participants', {selected_learners: this.selected_learners})
				.then(response => {
					this.learner_ids = response.data.learner_ids;
					this.participants = response.data.participants;
					this.resetParticipants();
					new Noty({
						type: "success",
						text: "Participants Updated Successfully"
					}).show();
				})
				.catch(error => {
					console.log(error)
					this.loading = false;
					new Noty({
						type: "error",
						text: error.response.data.message
					}).show();
				});
			},
			loadParticipants: function (link) {
				if (link.url !== null && this.participants.current_page !== Number(link.url.substring(link.url.indexOf("=")+1))) {
					this.loading = true;
					axios.get(this.apiUrl + '/'+this.extra_curricular_activity.id+'/participants?page='+link.url.substring(link.url.indexOf("=")+1))
					.then(response=>{
						this.loading = false;
						this.participants = response.data;
					})
					.catch(error=>{
						console.log(error)
						this.loading = false;
						new Noty({
							type: "error",
							text: error.response.data.message
						}).show();
					});
				}
			},
			removeParticipants: function (participant) {
				let self = this;
				Swal.fire({
					title: 'Are you sure?',
					text: "You are about to remove "+participant.learner.full_name+"'s participation!",
					icon: 'warning',
					showCancelButton: true,
					confirmButtonText: 'Yes, Remove!',
				}).then(function (result) {
					if (result.value) {
						self.startLoading();
						axios.delete(this.apiUrl + '/'+participant.school_extra_curricular_id+'/participants/'+participant.learner_id)
						.then(response=>{
							self.learner_ids = response.data.learner_ids;
							self.participants = response.data.participants;
							self.resetParticipants();
							$.unblockUI();
							new Noty({
								type: "success",
								text: "Participant Removed Successfully"
							}).show();
						})
						.catch(error=>{
							console.log(error)
							self.loading = false;
							$.unblockUI();
							new Noty({
								type: "error",
								text: error.response.data.message
							}).show();
						});
					}
				});
			},
			resetFilter: function () {
				this.filter.name = '';
				$('#filterEducationGradeId').val('').change();
				$('#filterGender').val('').change();
			},
			getLinkClasses: function (link) {
				let css = "page-item";

				if (link.active) {
					css += " active-dark-teal disabled";
				}

				if (link.url === null) {
					css += " disabled";
				}

				return css;
			},
			startLoading: function() {
				$.blockUI({
					message: $('#participantsLoadingMessage'),
					css: {
						padding:0,
						margin:0,
						width:'30%',
						top:'40%',
						left:'35%',
						textAlign:'center',
						color:'#364a63',
						wordWrap: 'break-word',
						backgroundColor: '#fff',
						backgroundClip: 'border-box',
						border: '0 solid rgba(0, 0, 0, 0.125)',
						borderRadius: '4px',
						cursor:'wait'
					},
				});
			},
		},
		computed: {
			availableParticipants: function () {
				let available = [];

				if (this.filter.education_grade_id === '' && this.filter.gender === '' && this.filter.name.toUpperCase() === '') {
					available = this.admissions;
				} else {
					if (this.filter.education_grade_id !== '' && this.filter.gender !== '' && this.filter.name.toUpperCase() !== '') {
						let search_term = new RegExp(this.filter.name, 'gi');
						available = this.admissions.filter(admission => {
							return admission.learner.full_name.search(search_term) >= 0
							&& admission.learner.education_grade_id === this.filter.education_grade_id
							&& admission.learner.gender === this.filter.gender;
						});
					} else if (this.filter.education_grade_id !== '' && this.filter.gender !== '') {
						available = this.admissions.filter(admission => {
							return admission.learner.education_grade_id === this.filter.education_grade_id
							&& admission.learner.gender === this.filter.gender;
						});
					} else if (this.filter.education_grade_id !== '' && this.filter.name.toUpperCase() !== '') {
						let search_term = new RegExp(this.filter.name, 'gi');
						available = this.admissions.filter(admission => {
							return admission.learner.full_name.search(search_term) >= 0
							&& admission.learner.education_grade_id === this.filter.education_grade_id;
						});
					} else if (this.filter.gender !== '' && this.filter.name.toUpperCase() !== '') {
						let search_term = new RegExp(this.filter.name, 'gi');
						available = this.admissions.filter(admission => {
							return admission.learner.full_name.search(search_term) >= 0
							&& admission.learner.gender === this.filter.gender;
						});
					} else if (this.filter.name.toUpperCase() !== '') {
						let search_term = new RegExp(this.filter.name, 'gi');
						available = this.admissions.filter(admission => {
							return admission.learner.full_name.search(search_term) >= 0;
						});
					} else if (this.filter.education_grade_id !== '') {
						available = this.admissions.filter(admission => {
							return admission.learner.education_grade_id === this.filter.education_grade_id;
						});
					} else if (this.filter.gender !== '') {
						available = this.admissions.filter(admission => {
							return admission.learner.gender === this.filter.gender;
						});
					}
				}

				return available;
			},
		}
	}
</script>

<style scoped>

</style>
