<template>
	<div class="components-preview mx-auto">
		<div class="nk-block nk-block-lg">
			<div class="nk-block-head">
				<div class="nk-block-between">
					<div class="nk-block-head-content">
						<h4 class="title nk-block-title">Extra-curricular Activities</h4>
						<div class="nk-block-des">
							<p>Report about Extra-curricular Activities</p>
						</div>
					</div><!-- .nk-block-head-content -->
				</div><!-- .nk-block-between -->
			</div>
			<div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
				<div class="card-inner-group">
					<div class="card-inner p-0">
                        <div class="table-responsive">
                            <table class="table border border-dark-teal">
                                <thead class="bg-secondary">
                                <tr>
                                    <th class="text-white align-middle text-uppercase w-45">Extra-curricular Activity</th>
                                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                                </tr>
                                </thead>
                                <tbody class="border-top-0 border-secondary">
                                    <tr v-for="facility in practical_skills_types">
                                        <td class="align-middle border-left border-secondary">{{ facility.name.toUpperCase() }}</td>
                                        <td class="align-middle border-left border-secondary text-center" >
                                            <div class="preview-icon-wrap">
                                                <span v-if="facilityExists(facility)" class="text-dark-teal text-uppercase">Yes</span>
                                                <span v-else class="text-danger text-uppercase">No</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div><!-- .nk-tb-list -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

	export default {
		name: "practicalSkills",
		props: [
		    'schoolObj',
            'surveyObj',
            'practicalSkillsObj'
        ],
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				loading: false,
                practical_skills_types: [],
                school: {
                    practical_skills: [],
                },
                practical_skills: [],
			}
		},
		methods: {
			initPlugins: function () {
                this.school = this.schoolObj;
                this.practical_skills_types = this.practicalSkillsObj;
			},
            facilityExists: function (facility) {
                return this.school.practical_skills.find(item=>{
                    return item.practical_skill_id === facility.id && item.present_in_school;
                }) !== undefined;
            },
		},
		computed: {}
	}
</script>

<style scoped>

</style>
