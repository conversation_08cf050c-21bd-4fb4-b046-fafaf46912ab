<template>
    <div class="components-preview mx-auto">
        <div class="nk-block nk-block-lg">
            <div class="nk-block-head">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h4 class="title nk-block-title">Sports Equipment</h4>
                        <div class="nk-block-des">
                            <p>Information about your school's sports equipment</p>
                        </div>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div>
            <div class="nk-block">
                <div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
                    <div class="table-responsive">
                        <div  class="nk-tb-item">
                            <div class="nk-tb-col">
                                <span class="text-secondary text-uppercase">NO OF PLAY GROUNDS:</span>
                            </div>
                            <div class="nk-tb-col text-center">
                                <div class="">
                                    <span v-if="play_ground.sports_play_ground === null" class="text-dark-teal text-uppercase">Not Set</span>
                                    <span v-else class="text-dark-teal text-uppercase">{{ play_ground.sports_play_ground.no_of_play_grounds }}</span>
                                </div>
                            </div>
                            <div class="nk-tb-col">
                                <span class="text-secondary text-uppercase">SIZE OF PLAY GROUNDS:</span>
                            </div>
                            <div class="nk-tb-col text-center">
                                <div class="">
                                    <span v-if="play_ground.sports_play_ground === null" class="text-dark-teal text-uppercase">Not Set</span>
                                    <span v-else class="text-dark-teal text-uppercase">{{ play_ground.sports_play_ground.size_of_play_grounds }}</span>
                                </div>
                            </div>
                        </div><!-- .nk-tb-item -->

                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                            <tr>
                                <th class="text-white align-middle text-uppercase w-45">Sports Equipment</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                            </tr>
                            </thead>
                            <tbody class="border-top-0 border-dark-teal">
                                <tr v-for="item in equipment">
                                    <td class="align-middle">{{ item.name.toUpperCase() }}</td>
                                    <td class="align-middle border-left text-center" >
                                        <span>{{ getEquipmentQuantity(item.id) }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "PrePrimarySportsEquipment",
    props: ['equipmentObj', 'reportObj','playGroundObj'],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            report: [],
            play_ground: {
                sports_play_ground: {
                    no_of_play_grounds: '',
                    size_of_play_grounds: '',
                }
            },
            equipment: [],
        }
    },
    methods: {
        initPlugins: function () {
            this.report = JSON.parse(this.reportObj);
            this.play_ground = JSON.parse(this.playGroundObj);
            this.equipment = JSON.parse(this.equipmentObj);
        },
        getEquipmentQuantity: function (materialId, gradeId) {
            let qtyReport = this.report.find(materialReport=>{
                return materialReport.education_grade_id === gradeId && materialReport.equipment_id === materialId;
            })

            if (qtyReport === undefined) {
                return 0;
            } else {
                return qtyReport.quantity;
            }
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
