<template>
    <div class="components-preview mx-auto">
        <div class="nk-block nk-block-lg">
            <div class="nk-block-head">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h6 class="nk-block-title text-uppercase">Sports Activities</h6>
                        <div class="nk-block-des">
                            <p>Does this institution offer/engage in these activities below?</p>
                        </div>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div>
            <div class="table-responsive">
                <table class="table border border-dark-teal">
                    <thead class="bg-secondary">
                    <tr>
                        <th class="text-white align-middle text-uppercase w-45">Activity</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                    </tr>
                    </thead>
                    <tbody class="border-top-0 border-secondary">
                        <tr v-for="activity in sports_activities">
                            <td class="align-middle border-left border-secondary">{{ activity.name.toUpperCase() }}</td>
                            <td class="align-middle border-left border-secondary text-center" >
                                <div class="preview-icon-wrap">
                                    <span v-if="activityExists(activity)" class="text-dark-teal text-uppercase">Yes</span>
                                    <span v-else class="text-danger text-uppercase">No</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div><!-- .nk-tb-list -->
    </div>
</template>

<script>

export default {
    name: "SportsActivities",
    props: [
        'schoolObj',
        'sportsActivitiesObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            api_url: '/institutions/surveys/sports-activity',
            school: {
                sports_activities: [],
            },
            survey: {
                current_section_id: '',
                sections: [{name:'', is_complete_yn:false}],
                section_items: [{name:'', section_id:'', is_complete_yn:false}],
                survey: {name: ''},
            },
            sports_activities: [],
            activity: {
                sports_activity_id: '',
                name: '',
                present_in_school: '',
            },

        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
            this.sports_activities = this.sportsActivitiesObj;
        },
        activityExists: function (activity) {
            return this.school.sports_activities.find(item=>{
                return item.sports_activity_id === activity.id && item.present_in_school;
            }) !== undefined;
        },
    },
    computed: {}
}
</script>

<style scoped>

</style>
