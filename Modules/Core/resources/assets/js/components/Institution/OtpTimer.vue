<template>
	<div class="d-inline">
		<span v-if="running">The code expires in <span class="countdown text-dark lead"></span></span>
		<span v-else>Your email verification code has expired.</span>
	</div> 
</template>

<script>
	export default {
		name: "OtpTimer",
		data: function () {
			return {
				running: true,
				timer: null
			}
		},
		methods: {
			restartTimer: function (minutes = 10) {
				this.$emit('expired', false);
				clearInterval(this.timer);
				this.running = true;
				this.startTimer(minutes);
			},
			startTimer: function (minutes = 10) {
				let duration = moment.duration({'minutes':minutes});
				let timestamp = new Date();
				let interval = 1;
				this.timer = setInterval(()=>{
					timestamp = new Date(timestamp.getTime() + interval * 1000);
					duration = moment.duration(duration.asSeconds() - interval, 'seconds');
					let min = duration.minutes();
					let sec = duration.seconds();

					sec -= 1;
					if (min < 0) return this.resetTimer();
					if (min < 10 && min.length != 2) min = '0' + min;
					if (sec < 0 && min != 0) {
						min -= 1;
						sec = 59;
					} else if (sec < 10 && length.sec != 2) sec = '0' + sec;

					$('.countdown').text(min + ' minutes :' + sec +' seconds');
					if (this.running && (min == 0 && sec == 0))
					{
						clearInterval(this.timer);
						$('.countdown').text('00 minutes :' + '00 seconds');
						this.running = false;
						this.$emit('expired', true);
					}
				}, 1000);
			},
		},
		computed: {
			
		}
	}
</script>

<style scoped>
	
</style>