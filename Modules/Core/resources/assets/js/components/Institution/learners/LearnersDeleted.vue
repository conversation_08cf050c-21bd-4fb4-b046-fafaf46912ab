<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <notifications ref="notifyError"></notifications>
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Manage Flagged {{ learnerLabel }}s To Be Deleted</h3>
                    <div class="nk-block-des text-soft">
                        <p>You have a total of {{ flagged_requests.total }} flagged {{ learnerLabel.toLowerCase() }}s to be deleted.</p>
                    </div>
                </div><!-- .nk-block-head-content -->

<!--                <div class="nk-block-head-content">-->
<!--                    <div class="toggle-wrap nk-block-tools-toggle">-->
<!--                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>-->
<!--                        <div class="toggle-expand-content" data-content="pageMenu">-->
<!--                            <ul class="nk-block-tools g-3">-->
<!--                                <li><a @click="exportLearners()" class="cursor btn btn-md btn-secondary text-white"><em class="icon ni ni-download-cloud"></em><span>Export</span></a></li>-->
<!--                            </ul>-->
<!--                        </div>-->
<!--                    </div>&lt;!&ndash; .toggle-wrap &ndash;&gt;-->
<!--                </div>&lt;!&ndash; .nk-block-head-content &ndash;&gt;-->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="p-3">
                        <div class="row">
                            <div v-show="schoolTypeIdObj <= 6" class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_education_grade_id" class="form-select-sm">
                                        <option v-if="schoolTypeIdObj > 3 && schoolTypeIdObj !== 7" value="">SELECT YEAR OF STUDY</option>
                                        <option v-else value="">SELECT CLASS</option>
                                        <option v-for="classe in classes" :value="classe.id">{{ classe.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
<!--                            <div v-show="schoolTypeIdObj === 7" class="col-lg-3">-->
<!--                                <div class="form-wrap">-->
<!--                                    <select id="filterLearnerCurriculumId" class="form-select-sm">-->
<!--                                        <option value="">SELECT CURRICULUM</option>-->
<!--                                        <option v-for="curriculum in school.international_curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div v-show="schoolTypeIdObj === 7" class="col-lg-3">-->
<!--                                <div class="form-wrap">-->
<!--                                    <select id="filterLearnerEducationGradeId" class="form-select-sm">-->
<!--                                        <option value="">SELECT GRADE</option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_gender" class="form-select-sm">
                                        <option value="">SELECT GENDER</option>
                                        <option value="M">MALE</option>
                                        <option value="F">FEMALE</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_learner_nin_status" class="form-select-sm">
                                        <option value="">LEARNER NIN STATUS</option>
                                        <option value="verified">VERIFIED</option>
                                        <option value="missing">MISSING</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_approval_status" class="form-select-sm">
                                        <option value="">APPROVAL STATUS</option>
                                        <option value="PENDING">PENDING</option>
                                        <option value="APPROVED">APPROVED</option>
                                        <option value="REJECTED">REJECTED</option>
                                    </select>
                                    <!-- <select id="filter_parent_nin_status" class="form-select-sm">
                                        <option value="">PARENT NIN STATUS</option>
                                        <option value="verified">VERIFIED</option>
                                        <option value="missing">MISSING</option>
                                    </select> -->
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select v-model="filter.country_id" id="filter_country_id" class="form-select-sm">
                                        <option value="">NATIONALITY</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <div class="input-group">
                                        <input v-model.trim="filter.lin" type="text" class="form-control text-uppercase" placeholder="LIN">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-wrap">
                                    <div class="input-group">
                                        <input v-model.trim="filter.search_term" type="text" class="form-control text-uppercase" placeholder="Learner Name">
                                        <div class="input-group-append">
                                            <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                <em class="icon ni ni-cross"></em>
                                            </button>
                                            <button @click.prevent="filterLearners()" class="btn rounded-right text-white bg-dark-teal" type="button">
                                                <em class="icon ni ni-filter mr-1"></em>Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div v-if="loading" class="text-center py-5">
                        <div class="py-5"></div>
                        <div class="py-3"></div>
                        <div class="spinner-border" style="width: 2rem; height: 2rem;" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="py-3"></div>
                        <div class="py-5"></div>
                    </div>
                    <div v-if="!loading" class="card-inner p-0">
                        <div class="table-responsive">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-left-0 text-uppercase text-center border-1">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleAllAdmissions()" v-model="select_all_learners" type="checkbox" class="custom-control-input" id="uid">
                                            <label class="custom-control-label" for="uid"></label>
                                        </div>
                                    </th>
                                    <th @click="sortLearners('sort_name')" class="nk-tb-col px-1 text-white border-white cursor text-uppercase border-1">
                                        <!-- <em v-if="filter.sort_name === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em> -->
                                        <!-- <em v-if="filter.sort_name === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em> -->
                                        <span class="sub-text ucap text-white">{{ learnerLabel }}</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">LIN</span>
                                    </th>
                                    <th @click="sortLearners('sort_gender')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_gender === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_gender === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">Sex</span>
                                    </th>
                                    <th @click="sortLearners('sort_education_grade_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_education_grade_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_education_grade_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">{{ gradeLabel }}</span>
                                    </th>

                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Date Flagged</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Date Approved</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Status</span>
                                    </th>
                                    <!-- <th @click="sortLearners('sort_country_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_country_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_country_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">Nationality</span>
                                    </th> -->
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                        <span class="sub-text ucap text-white">ACTION</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="border-bottom">
                                <tr v-for="request in flagged_requests.data" class="nk-tb-item">
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleOneLearner(request.id)" v-model="selected_learners" :value="request.id" type="checkbox" class="custom-control-input" :id="'uid'+request.id">
                                            <label class="custom-control-label" :for="'uid'+request.id"></label>
                                        </div>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="'/institution/learners/profile/'+request.learner.encrypted_lin" target="_blank" class="tb-lead cursor text-dark-teal">
                                            <div class="user-card">
                                                <div v-if="true" class="user-avatar">
                                                    <img :src="request.learner.person.photo_url" style="border-radius: 0" :alt="request.learner.person.initials">
                                                </div>
                                                <div class="user-name text-uppercase">
                                                    <span v-if="true" class="text-dark-teal">{{ request.learner.person.full_name }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ request.learner.lin }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="request.learner.person !== null" class="text-dark tb-lead">{{ request.learner.person.gender }}</span>
                                    </td>

                                    <td v-if="schoolTypeIdObj <= 6" class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ request.learner|educ_grade }}</span>
                                    </td>
                                    <td v-if="schoolTypeIdObj === 7" class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="request.learner.international_education_grade !== null" class="text-dark tb-lead">{{ request.learner.international_education_grade.name }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="true" class="text-dark tb-lead">{{ formatDate(request.date_created)  }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="request.date_approved !== null" class="text-dark tb-lead">{{ formatDate(request.date_approved)  }}</span>
                                        <span v-else class="text-muted font-italic">NOT SET</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="request.approval_status === 'APPROVED'"> <span class="badge badge-dark-teal">Approved</span></span>
                                        <span v-if="request.approval_status === 'PENDING'"> <span class="badge badge-primary">Pending</span></span>
                                        <span v-if="request.approval_status === 'REJECTED'" data-toggle="tooltip" data-placement="top" class="cursor"><span class="badge badge-danger">Rejected</span></span>
                                    </td>

                                    <!-- <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="true" class="text-dark tb-lead">{{ learner.person.country.name.toUpperCase()  }}</span>
                                    </td> -->
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="'/institution/data-update/flagged-learner-view-application/'+request.id" target="_blank" data-toggle="tooltip" data-placement="top" title="View Application" class="cursor lead mr-1 text-primary">
                                            <em class="icon ni ni-eye-fill"></em>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-if="!flagged_requests.data.length && !loading" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There are no flagged {{ learnerLabel.toLowerCase() }}s to display.
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <li :class="[flagged_requests.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="flagged_requests.current_page > 1 ? loadLearners(1) : null" :class="[flagged_requests.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <li :class="[flagged_requests.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="flagged_requests.current_page > 1 ? loadLearners(flagged_requests.current_page-1) : null" :class="[flagged_requests.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadLearners(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[flagged_requests.current_page === flagged_requests.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="flagged_requests.current_page < flagged_requests.last_page ? loadLearners(flagged_requests.current_page+1) : null" :class="[flagged_requests.current_page === flagged_requests.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[flagged_requests.current_page === flagged_requests.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="flagged_requests.current_page < flagged_requests.last_page ? loadLearners(flagged_requests.last_page) : null" :class="[flagged_requests.current_page === flagged_requests.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center mr-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPerPage" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing <span class="text-primary">{{ flagged_requests.from }}</span> to <span class="text-primary">{{ flagged_requests.to }}</span> of <span class="text-primary">{{ flagged_requests.total }}</span>
                            </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
<!--        <div v-if="!flagged_requests.data.length" class="card card-stretch">-->
<!--            <div class="card-inner-group">-->
<!--                <div class="card-body">-->
<!--                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">-->
<!--                        <em class="icon ni ni-alert-circle"></em> There are no {{ learnerLabel.toLowerCase() }}s to display for academic year {{ selectedYear }}.-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import learnerMixin from "../../../mixins/learnerMixin";

export default {
    name: "LearnersDeleted",
    mixins:[learnerMixin],
    props: ['countriesObj', 'flaggedRequestsObj', 'educationGradesObj', 'schoolTypeIdObj','schoolObj'],
    mounted() {
        this.initPlugins();
    },
    components: {
        Notifications
    },
    data: function () {
        return {
            select_all_learners: false,
            selected_learners: [],
            loading: false,
            filtering: false,
            api_url: '/institutions/learners/flagged-deleted/',
            bulk_action: '',
            edit: false,
            classes: [],
            countries: [],
            school: {},
            flagged_requests: {
                total: 0,
                data: [],
                links: [],
            },
            filter: {
                learner_nin_status: '',
                approval_status: '',
                //parent_nin_status: '',
                gender: '',
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_education_grade_id: '',
                country_id: '',
                sort_name: 'asc',
                sort_nin_status: '',
                sort_gender: '',
                sort_education_grade_id: '',
                sort_country_id: '',
                search_term: '',
                lin: '',
                per_page: '',
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.flagged_requests = this.flaggedRequestsObj;
            this.classes = JSON.parse(this.educationGradesObj);
            this.countries = JSON.parse(this.countriesObj);

            $('#bulk_action').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });

            $('#filter_education_grade_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id;
                    return data.text;
                },
            });

            $('#filterLearnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_curriculum_id = data.id;
                    self.loadEducationGrades();
                    return data.text;
                },
            });

            $('#filterLearnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_education_grade_id = data.id;
                    return data.text;
                },
            });

            $('#filter_country_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.country_id = data.id;
                    return data.text;
                },
            });

            $('#filter_gender').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#filter_learner_nin_status').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.learner_nin_status = data.id;
                    return data.text;
                },
            });

            $('#filter_approval_status').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.approval_status = data.id;
                    return data.text;
                },
            });

            // $('#filter_parent_nin_status').select2({
            //     minimumResultsForSearch: Infinity,
            //     templateSelection: function (data, container) {
            //         self.filter.parent_nin_status = data.id;
            //         return data.text;
            //     },
            // });

            $('#filterPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    self.loadLearners(1, self.filtering);
                    return data.text;
                },
            });
        },
        loadEducationGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#filterLearnerEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("SELECT GRADE", "", false, false);
            select.append(newOption).trigger('change');
            self.filter.inter_sch_education_grade_id = "";

            //load new options
            if (self.filter.inter_sch_curriculum_id !== "") {
                self.grades = self.school.international_curriculums.find(curriculum=>{
                    return curriculum.id === self.filter.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        toggleAllAdmissions: function () {
            this.selected_learners =[];

            if (this.select_all_learners) {
                this.flagged_requests.data.forEach(learner=>{
                    this.selected_learners.push(learner.person_id)
                });
            }
        },
        toggleOneLearner: function (id) {
            this.select_all_learners = this.selected_learners.length === this.flagged_requests.length
        },

        sortLearners: function (section) {
            if (section === 'sort_name') {
                this.filter.sort_name = this.filter.sort_name.length ? (this.filter.sort_name === 'asc' ? this.filter.sort_name = 'desc': this.filter.sort_name = 'asc') : this.filter.sort_name = 'asc';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_nin_status') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = this.filter.sort_nin_status.length ? (this.filter.sort_nin_status === 'asc' ? this.filter.sort_nin_status = 'desc': this.filter.sort_nin_status = 'asc') : this.filter.sort_nin_status = 'asc';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_gender') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = this.filter.sort_gender.length ? (this.filter.sort_gender === 'asc' ? this.filter.sort_gender = 'desc': this.filter.sort_gender = 'asc') : this.filter.sort_gender = 'asc';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_education_grade_id') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = this.filter.sort_education_grade_id.length ? (this.filter.sort_education_grade_id === 'asc' ? this.filter.sort_education_grade_id = 'desc': this.filter.sort_education_grade_id = 'asc') : this.filter.sort_education_grade_id = 'asc';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_country_id') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = this.filter.sort_country_id.length ? (this.filter.sort_country_id === 'asc' ? this.filter.sort_country_id = 'desc': this.filter.sort_country_id = 'asc') : this.filter.sort_country_id = 'asc';
            }

            this.loading = true;
            axios.post(this.api_url+'filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.flagged_requests = response.data;
                    this.loading = false;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        filterLearners: function () {
            this.loading = true;
            axios.post(this.api_url+'filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.flagged_requests = response.data;
                    this.loading = false;
                    this.filtering = true;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetFilter: function () {
            this.loading = true;
            axios.get(this.api_url+'?year='+this.selectedYear)
                .then(response=>{
                    this.flagged_requests = response.data;
                    this.loading = false;
                    this.filter.search_term = '';
                    this.filter.lin = '';
                    $('#filter_gender').val('').change();
                    $('#filter_education_grade_id').val('').change();
                    $('#filter_country_id').val('').change();
                    $('#filter_learner_nin_status').val('').change();
                    $('#filter_approval_status').val('').change();
                    //$('#filter_parent_nin_status').val('').change();
                    this.filtering = false;
                    $('#filterPerPage').val(15).change();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error)
                });
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'filter?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.flagged_requests = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },

        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },

        exportLearners: function () {
            axios.post(this.api_url+'export-excel',this.filter)
                .then(()=>{
                    this.$refs.notifyError.messages.push({status: 'success', title: 'Success', message:"Export has started, we will notify you through your email when the excel file is ready."});
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        exportLearnersPdf: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                window.location.href = 'export-flagged_requests-pdf'+'?year='+urlParams.get('year');
            } else {
                window.location.href = 'export-flagged_requests-pdf';
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedYear: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                return urlParams.get('year');
            } else {
                return moment().format("YYYY");
            }
        },
        getPaginationLinks: function () {
            let arr = this.flagged_requests.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        learnerLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Student";
            } else {
                return "Learner";
            }
        },
        gradeLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Year Of Study";
            } else {
                return "Class";
            }
        },
    }
}
</script>

<style scoped>

</style>
