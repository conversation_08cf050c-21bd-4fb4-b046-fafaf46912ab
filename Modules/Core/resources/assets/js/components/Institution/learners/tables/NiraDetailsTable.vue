<template>
    <div class="tab-pane" id="niraDetails">
        <div v-if="hasNIRADetails" class="nk-block">
            <div class="nk-block-head nk-block-head-lg">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h5 class="nk-block-title">Personal Information</h5>
                        <div class="nk-block-des">
                            <p>NIRA Basic details.</p>
                        </div>
                    </div>

                    <div class="nk-block-head-content align-self-start d-lg-none">
                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside">
                            <em class="icon ni ni-menu-alt-r"></em>
                        </a>
                    </div>
                </div>
            </div><!-- .nk-block-head -->

            <div class="nk-block">
                <div class="nk-data">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <tbody>
                            <tr>
                                <td class="text-uppercase text-muted">Full Name</td>
                                <td class="text-uppercase text-dark">{{ fullName }}</td>
                            </tr>
                            <tr>
                                <td class="text-uppercase text-muted">Date Of Birth</td>
                                <td class="text-uppercase text-dark">{{ learner.person.nira_person.date_of_birth }}</td>
                            </tr>
                            <tr>
                                <td class="text-uppercase text-muted">Sex</td>
                                <td class="text-uppercase text-dark">{{ learner.person.nira_person.gender }}</td>
                            </tr>
                            <tr>
                                <td class="text-uppercase text-muted">NIN</td>
                                <td class="text-uppercase text-dark">{{ learner.person.nira_person.national_id }}</td>
                            </tr>
                            <tr>
                                <td class="text-uppercase text-muted">Nationality</td>
                                <td v-if="learner.person.nira_person.nationality" class="text-uppercase text-dark">
                                    {{ learner.person.nira_person.nationality.toUpperCase() }}
                                </td>
                                <td v-else class="text-uppercase text-muted">Not Set</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div><!-- .nk-block -->
        </div><!-- .nk-block -->

        <div v-else class="alert alert-secondary alert-icon">
            <em class="icon ni ni-alert-circle"></em> This learner has no NIRA details attached
        </div>
    </div>
</template>

<script>
export default {
    name: "NiraDetailsTable",
    props: {
        learner: {
            type: Object,
            required: true
        }
    },
    computed: {
        // Check if NIRA details are available
        hasNIRADetails() {
            return this.learner.person.nira_person !== null && this.learner.person.nira_person !== undefined;
        },
        // Compute the full name from surname and given names
        fullName() {
            const { surname, given_names } = this.learner.person.nira_person;
            return `${surname} ${given_names}`.toUpperCase();
        }
    }
};
</script>

<style scoped>
</style>
