<template>
    <div v-if="shouldDisplay" class="tab-pane" id="unebEquatedDetails">
        <div class="nk-block">
            <div class="nk-block">
                <div class="nk-data">
                    <div class="table-responsive">
                        <!-- Uneb Details -->
                        <table v-if="learner.uneb_equated_data !== null" class="table border border-dark-teal">
                            <thead class="bg-secondary">
                            <tr>
                                <th class="text-white align-middle w-45" rowspan="2">Learner UNEB Equated Details</th>
                            </tr>
                            <tr>
                                <th class="py-2"></th>
                            </tr>
                            </thead>
                            <tbody class="border-top-0 border-dark-teal">
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Full Name</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span v-if="learner.uneb_equated_data.name !== null">{{ learner.uneb_equated_data.name }}</span>
                                    <span v-else class="text-uppercase text-muted">MISSING</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Exam Level</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span>{{ learner.uneb_equated_data.exam_level }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Equated Code</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span>{{ learner.uneb_equated_data.equated_code }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Equated Year</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span>{{ learner.uneb_equated_data.equated_year }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Exam Year</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span>{{ learner.uneb_equated_data.exam_year }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="align-middle text-dark border-secondary bg-secondary-dim">Code Type</td>
                                <td class="align-middle border-left text-dark border-secondary">
                                    <span>{{ learner.uneb_equated_data.code_type }}</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div><!-- .nk-block -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
export default {
    name: "UnebEquatedCodeDetailsTable",
    props: {
        learner: {
            type: Object,
            required: true
        },
        schoolTypeId: {
            required: true
        }
    },
    computed: {
        // Determine if the component should be displayed
        shouldDisplay() {
            return this.schoolTypeId === 3 && this.learner.uneb_equated_data !== null;
        }
    }
};
</script>

<style scoped>
</style>
