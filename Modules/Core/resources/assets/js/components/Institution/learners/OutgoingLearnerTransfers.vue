<template>
    <div class="card-inner-group">
        <div class="card-inner position-relative card-tools-toggle">
            <div class="card-title-group">
                <div class="card-tools">
                    <form @submit.prevent="loadTransfers(1, true)">
                        <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                            <div v-show="schoolTypeIdObj <= 6" style="width: 200px !important" class="form-wrap">
                                <select id="filterOutgoingEducationGradeId" class="form-select-sm">
                                    <option value="">ALL CLASSES</option>
                                    <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                <select id="filterOutgoingLearnerCurriculumId" class="form-select-sm">
                                    <option value="">SELECT CURRICULUM</option>
                                    <option v-for="curriculum in curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                <select id="filterOutgoingInterEducationGradeId" class="form-select-sm">
                                    <option value="">SELECT GRADE</option>
                                </select>
                            </div>
                            <div style="width: 180px !important" class="form-wrap">
                                <select id="filterOutgoingGender" class="form-select-sm">
                                    <option value="">ALL GENDERS</option>
                                    <option value="M">MALE</option>
                                    <option value="F">FEMALE</option>
                                </select>
                            </div>
                            <div class="form-wrap">
                                <div class="input-group">
                                    <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                    <div class="input-group-append">
                                        <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                            <em class="icon ni ni-cross"></em>
                                        </button>
                                        <button class="btn rounded-right bg-dark-teal" type="submit">
                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div><!-- .card-tools -->
            </div><!-- .card-title-group -->
        </div><!-- .card-inner -->

        <div class="card-inner p-0">
            <div class="nk-tb-list nk-tb-ulist is-compact">
                <div class="nk-tb-item nk-tb-head">
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Learner</span>
                    </div>
                    <div class="nk-tb-col bg-secondary text-center ucap">
                        <span class="sub-text text-white">Class</span>
                    </div>
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Current School</span>
                    </div>
                    <div class="nk-tb-col w-20 bg-secondary ucap">
                        <span class="sub-text text-white">Transfer Reason</span>
                    </div>
                    <div class="nk-tb-col w-15 bg-secondary text-center ucap">
                        <span class="sub-text text-white">Transfer Date</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
            <div v-if="outgoing_transfers.data.length" class="nk-tb-list nk-tb-ulist is-compact">
                <div v-for="enrolment in outgoing_transfers.data" class="nk-tb-item">
                    <div class="nk-tb-col w-25">
                        <div class="user-card">
                            <div class="user-avatar">
                                <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.full_name">
                            </div>
                            <div class="user-name text-uppercase">
                                <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" class="tb-lead cursor text-dark">{{ enrolment.learner.person.full_name }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="nk-tb-col text-center">
                        <span v-if="schoolTypeIdObj <= 6 && enrolment.education_grade !== null" class="text-dark">{{ enrolment.education_grade.name.toUpperCase() }}</span>
                        <span v-if="schoolTypeIdObj === 7 && enrolment.international_education_grade !== null" class="text-dark">{{ enrolment.international_education_grade.name.toUpperCase() }}</span>
                    </div>
                    <div class="nk-tb-col w-25">
                        <span class="text-dark">{{ enrolment.school.name }}</span>
                        <span class="font-italic text-muted">({{ enrolment.school.district.name.toUpperCase() }})</span>
                    </div>
                    <div class="nk-tb-col w-20">
                        <span v-if="enrolment.transfer !== null" class="text-dark">{{ enrolment.transfer.transfer_reason.name }}</span>
                    </div>
                    <div class="nk-tb-col w-15 text-center">
                        <span v-if="enrolment.transfer !== null" class="text-dark">{{ formatDate(enrolment.transfer.date_created) }}</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
        </div><!-- .card-inner -->

        <div v-if="!outgoing_transfers.data.length" class="card-inner p-0">
            <div class="card-body">
                <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                    <em class="icon ni ni-alert-circle"></em> There are no outgoing learner transfers to display at the moment.
                </div>
            </div>
        </div>

        <div v-if="outgoing_transfers.data.length" class="card-inner d-flex flex-row justify-content-between">
            <nav>
                <ul class="pagination">
                    <li :class="[outgoing_transfers.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="outgoing_transfers.current_page > 1 ? loadTransfers(1) : null" :class="[outgoing_transfers.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                        </a>
                    </li>
                    <li :class="[outgoing_transfers.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="outgoing_transfers.current_page > 1 ? loadTransfers(outgoing_transfers.current_page-1) : null" :class="[outgoing_transfers.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                        </a>
                    </li>
                    <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                        <a @click="loadTransfers(link.label)" class="page-link cursor" v-html="link.label"></a>
                    </li>
                    <li :class="[outgoing_transfers.current_page === outgoing_transfers.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="outgoing_transfers.current_page < outgoing_transfers.last_page ? loadTransfers(outgoing_transfers.current_page+1) : null" :class="[outgoing_transfers.current_page === outgoing_transfers.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                        </a>
                    </li>
                    <li :class="[outgoing_transfers.current_page === outgoing_transfers.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="outgoing_transfers.current_page < outgoing_transfers.last_page ? loadTransfers(outgoing_transfers.last_page) : null" :class="[outgoing_transfers.current_page === outgoing_transfers.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="d-flex ml-4">
                <span class="align-self-center">
                    Showing <span class="text-primary">{{ outgoing_transfers.from }}</span> to <span class="text-primary">{{ outgoing_transfers.to }}</span> of <span class="text-primary">{{ outgoing_transfers.total }}</span>
                </span>
            </div>
        </div><!-- .card-inner -->
    </div><!-- .card-inner-group -->
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";

export default {
    name: "OutgoingLearnerTransfers",
    props: [
        "educationGradesObj",
        "learnerTransferReasonsObj",
        "outgoingTransfersObj",
        "schoolTypeIdObj",
        "curriculumsObj"
    ],
    components: {
        Notifications
    },
    data: function () {
        return {
            api_url: '/institutions/learner/transfers',
            select_all_outgoing_transfers: false,
            loading: false,
            filtering: false,
            loading_message: '',
            outgoing_transfers: {
                data: [],
                links: [],
                total: 0,
            },
            selected_outgoing_transfers: [],
            education_grades: [],
            curriculums: [],
            filter: {
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                gender: '',
                name: '',
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.outgoing_transfers = this.outgoingTransfersObj;
            this.education_grades = this.educationGradesObj;
            this.curriculums = this.curriculumsObj;

            $('#filterOutgoingEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            //For international schools
            $('#filterOutgoingLearnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_curriculum_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadGrades();
                    return data.text;
                },
            });

            $('#filterOutgoingInterEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#filterOutgoingGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });
        },
        formatDate: function (date) {
            return moment(date).format("MMMM DD, YYYY");
        },
        loadGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#filterOutgoingInterEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("SELECT GRADE", "", false, false);
            select.append(newOption).trigger('change');
            self.filter.inter_sch_education_grade_id = "";

            //load new options
            if (self.filter.inter_sch_curriculum_id !== "") {
                self.grades = self.curriculums.find(curriculum=>{
                    return curriculum.id === self.filter.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        toggleAllTransfers: function () {
            this.selected_outgoing_transfers =[];

            if (this.select_all_outgoing_transfers) {
                this.outgoing_transfers.data.forEach(admin=>{
                    this.selected_outgoing_transfers.push(admin.id)
                });
            }
        },
        toggleOneTransfers: function (id) {
            this.select_all_outgoing_transfers = this.selected_outgoing_transfers.length === this.outgoing_transfers.data.length
        },
        loadTransfers: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/outgoing?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.outgoing_transfers = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                gender: '',
                name: '',
            };
            $('#filterOutgoingGender').val('').change();
            $('#filterOutgoingEducationGradeId').val('').change();
            $('#filterOutgoingLearnerCurriculumId').val('').change();
            $('#filterOutgoingInterEducationGradeId').val('').change();
            this.loadTransfers(1, false);
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            console.log(error.response)
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.outgoing_transfers.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    },
}
</script>

<style scoped>

</style>
