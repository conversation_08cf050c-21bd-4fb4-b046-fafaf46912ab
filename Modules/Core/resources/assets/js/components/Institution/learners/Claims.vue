<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Learner Claims</h3>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <!--								<li>-->
                                <!--									<a class="cursor btn btn-secondary text-white">-->
                                <!--										<em class="icon ni ni-download-cloud"></em><span>Export</span>-->
                                <!--									</a>-->
                                <!--								</li>-->
                                <li>
                                    <a :href="'/institution/learners/claim-form'" target="_blank" class="cursor btn bg-dark-teal">
                                        <span class="text-white">Submit Claim</span><em class="icon ni ni-arrow-right-circle-fill text-white"></em>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <ul class="nav nav-tabs">
                        <li class="nav-item px-2">
                            <a id="IncomingClaimsTabHandle" class="nav-link active" data-toggle="tab" href="#IncomingClaimsTab">
                                <span>Incoming Learner Claims</span>
                            </a>
                        </li>
                        <li class="nav-item px-2">
                            <a class="nav-link" data-toggle="tab" href="#OutgoingClaimsTab">
                                <span>Outgoing Learner Claims</span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active" id="IncomingClaimsTab">
                            <incoming-learner-claims
                                :education-grades-obj="educationGradesObj"
                                :incoming-claims-obj="incoming_claims"
                            ></incoming-learner-claims>
                        </div>
                        <div class="tab-pane" id="OutgoingClaimsTab">
                            <outgoing-learner-claims
                                :education-grades-obj="educationGradesObj"
                                :outgoing-claims-obj="outgoing_claims"
                            ></outgoing-learner-claims>
                        </div>
                    </div>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>

</template>

<script>
export default {
    name: "LearnerClaims",
    props: [
        "countriesObj",
        "districtsObj",
        "educationGradesObj",
        "learnerTransferReasonsObj",
        "incomingClaimsObj",
        "outgoingClaimsObj"
    ],
    data: function () {
        return {
            incoming_claims: {
                data: [],
                links: [],
                total: 0,
            },
            outgoing_claims: {
                data: [],
                links: [],
                total: 0,
            },
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.incoming_claims = this.incomingClaimsObj;
            this.outgoing_claims = this.outgoingClaimsObj;
        },
        newTransferRequest: function () {
            $('#IncomingClaimsTabHandle').click();
            $('#incomingClaimsModal').modal({backdrop: "static"});
        }
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
