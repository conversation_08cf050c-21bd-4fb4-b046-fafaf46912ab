<template>

    <div>
        <notifications ref="notify"></notifications>
        <div class="card-inner position-relative card-tools-toggle">
            <div class="card-title-group">
                <div class="card-tools">
                    <form @submit.prevent="loadLearners(1, true)">
                        <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                            <div style="width: 200px !important" class="form-wrap">
                                <select id="filterALevelEducationGradeId" class="form-select-sm">
                                    <option value="">ALL CLASSES</option>
                                    <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div style="width: 200px !important" class="form-wrap">
                                <select id="filterALevelGender" class="form-select-sm">
                                    <option value="">ALL GENDERS</option>
                                    <option value="M">MALE</option>
                                    <option value="F">FEMALE</option>
                                </select>
                            </div>
                            <div style="width: 300px !important" class="form-wrap">
                                <div class="input-group">
                                    <input v-model.trim="filter.lin" type="text" class="form-control text-uppercase" placeholder="LIN">
                                </div>
                            </div>
                            <div style="width: 300px !important" class="form-wrap">
                                <div class="input-group">
                                    <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                    <div class="input-group-append">
                                        <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                            <em class="icon ni ni-cross"></em>
                                        </button>
                                        <button class="btn rounded-right bg-dark-teal" type="submit">
                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div><!-- .card-tools -->
            </div><!-- .card-title-group -->
        </div><!-- .card-inner -->

        <div class="card-inner p-0">
            <div class="table-responsive">
                <table class="nk-tb-list nk-tb-ulist is-compact">
                    <thead>
                    <tr class="nk-tb-item nk-tb-head bg-secondary">
                        <th class="nk-tb-col px-2 text-white border-white cursor text-uppercase border-1">
                            <span class="sub-text ucap text-white">Learner</span>
                        </th>
                        <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                            <span class="sub-text ucap text-white">LIN</span>
                        </th>
                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                            <span class="sub-text ucap text-white">Sex</span>
                        </th>
                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                            <span class="sub-text ucap text-white">Class</span>
                        </th>
                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                            <span class="sub-text ucap text-white">Index Numbers</span>
                        </th>
                        <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                            <span class="sub-text ucap text-white">Action</span>
                        </th>
                    </tr>
                    </thead>
                    <tbody class="border-bottom">
                    <tr v-for="learner in learners.data" class="nk-tb-item">
                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <a :href="'/institution/learners/profile/'+learner.encrypted_lin" target="_blank" class="tb-lead cursor text-dark-teal">
                                <div class="user-card">
                                    <div class="user-avatar">
                                        <img :src="learner.person.photo_url" style="border-radius: 0" :alt="learner.person.initials">
                                    </div>
                                    <div class="user-name text-uppercase">
                                        <span class="text-dark-teal">{{ learner.person.full_name }}</span>
                                    </div>
                                </div>
                            </a>
                        </td>
                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <span class="text-dark tb-lead">{{ learner.lin }}</span>
                        </td>
                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <span class="text-dark tb-lead">{{ learner.person.gender }}</span>
                        </td>

                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <span class="text-dark tb-lead">{{ learner.education_grade.name.toUpperCase() }}</span>
                        </td>
                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <span v-for="index in learner.index_numbers" class="text-dark tb-lead">{{index.level}} : {{index.index_number}} - {{index.exam_year}}</span>
                        </td>
                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                            <a @click="editDetailsRequestModal(learner)" target="_blank" data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                <em class="icon ni ni-edit-fill"></em>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div><!-- .card-inner -->

        <div v-if="!learners.data.length" class="card-inner p-0">
            <div class="card-body">
                <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                    <em class="icon ni ni-alert-circle"></em> There are no learners with missing index numbers to display at the moment.
                </div>
            </div>
        </div>

        <div v-if="learners.data.length" class="card-inner d-flex flex-row justify-content-between">
            <nav>
                <ul class="pagination">
                    <li :class="[learners.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="learners.current_page > 1 ? loadLearners(1) : null" :class="[learners.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                        </a>
                    </li>
                    <li :class="[learners.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="learners.current_page > 1 ? loadLearners(learners.current_page-1) : null" :class="[learners.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                        </a>
                    </li>
                    <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                        <a @click="loadLearners(link.label)" class="page-link cursor" v-html="link.label"></a>
                    </li>
                    <li :class="[learners.current_page === learners.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="learners.current_page < learners.last_page ? loadLearners(learners.current_page+1) : null" :class="[learners.current_page === learners.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                        </a>
                    </li>
                    <li :class="[learners.current_page === learners.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="learners.current_page < learners.last_page ? loadLearners(learners.last_page) : null" :class="[learners.current_page === learners.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="d-flex ml-4">
                <span class="align-self-center">
                    Showing <span class="text-primary">{{ learners.from }}</span> to <span class="text-primary">{{ learners.to }}</span> of <span class="text-primary">{{ learners.total }}</span>
                </span>
            </div>
        </div><!-- .card-inner -->

        <!-- Transfer Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateALevelIndexNumberModal">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetLearner()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Add Learner Index Number</h5>
                    </div>
                    <form @submit.prevent="index_no_verify ? updateIndexNumber() : verifyUnebLearner()">
                        <div class="modal-body">
                            <div class="alert alert-info alert-icon">
                                <em class="icon ni ni-alert-circle"></em>
                                <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                                <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select Level</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Select Exam Year</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Enter Index Number</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 4:</span> Verify Learner Information If It Matches</h6>
                                <h6 class="small mb-2"><span class="text-danger">Step 5:</span> Click Submit to complete the process.</h6>
                            </div>

                            <div class="row mb-2">
                                <div class="col-12 ml-2">
                                    <error-notifications ref="notifyError"></error-notifications>
                                    <div class="table-responsive align-self-start">
                                        <table class="table table-sm">
                                            <thead>
                                            <tr>
                                                <th style="border: none">
                                                    <h6>EMIS Details</h6>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                    <div class="form-group d-flex flex-column justify-content-center">
                                                        <div class="w-150px mx-auto">
                                                            <img v-if="learner.person.photo_url !== null" id="learnerPhoto" :src="learner.person.photo_url" :alt="learner.person.full_name" class="rounded-0">
                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'F'" id="learnerPhoto" src="/images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'M'" id="learnerPhoto" src="/images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                    <span class="">{{ learner.person.full_name }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">LIN</h6>
                                                    <span class="">{{ learner.lin }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">CURRENT CLASS</h6>
                                                    <span v-if="learner.education_grade !== null" class="">{{ learner.education_grade.name.toUpperCase() }}</span>
                                                </td>
                                            </tr>
                                            <tr v-if="school_type_id >= 3">
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                    <span v-for="indexNumber in learner.index_numbers" class="">{{ indexNumber.level }} : {{ indexNumber.index_number }} - {{ indexNumber.exam_year }}</span>
                                                    <span v-if="learner.index_numbers === null || learner.index_numbers === undefined || learner.index_numbers === ''" class="text-sm text-muted">Not Set</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                    <span v-if="learner.person.gender === 'F'" class="">FEMALE</span>
                                                    <span v-if="learner.person.gender === 'M'" class="">MALE</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                    <span class="">{{ formatDate(learner.person.birth_date) }}</span>
                                                </td>
                                            </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div v-show="index_no_verify" class="col-12 ml-2">
                                    <hr class="border-dark-teal my-1">
                                    <div class="table-responsive align-self-start">
                                        <table class="table table-sm">
                                            <thead>
                                            <tr>
                                                <th style="border: none">
                                                    <h6>Uneb Details</h6>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                    <div class="form-group d-flex flex-column justify-content-center">
                                                        <div class="w-150px mx-auto">
                                                            <img v-if="learner.person.photo_url !== null" id="learnerPhoto" :src="learner.person.photo_url" :alt="learner.person.full_name" class="rounded-0">
                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'F'" id="learnerPhoto" src="/images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'M'" id="learnerPhoto" src="/images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                    <span class="">{{ uneb_learner.name }}</span>
                                                </td>
                                            </tr>
                                            <tr v-if="school_type_id >= 3">
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                    <span class="">{{ uneb_learner.index_number }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">EXAM YEAR</h6>
                                                    <span class="">{{ uneb_learner.exam_year }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">EXAM LEVEL</h6>
                                                    <span class="">{{ uneb_learner.exam_level }}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                    <span v-if="uneb_learner.gender === 'F'" class="">FEMALE</span>
                                                    <span v-if="uneb_learner.gender === 'M'" class="">MALE</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                    <span class="">{{ formatDate(uneb_learner.date_of_birth) }}</span>
                                                </td>
                                            </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <hr class="border-dark-teal my-1">
                                    <div class="row">
                                        <div class="col-lg-6 mt-3">
                                            <div class="form-group">
                                                <label class="form-label" for="learnerUCELevel">Learner Level <span class="text-danger">*</span></label>
                                                <select :disabled="index_no_verify" required="" id="learnerUCELevel" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
<!--                                                    <option value="PLE">PLE</option>-->
                                                    <option value="UCE">UCE</option>
<!--                                                    <option value="UACE">UACE</option>-->
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-3">
                                            <div class="form-group">
                                                <label class="form-label" for="learnerALevelExamYearId">Learner Exam Year <span class="text-danger">*</span></label>
                                                <select :disabled="index_no_verify" required="" id="learnerALevelExamYearId" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option v-for="year in academicYears" :value="year">{{ year }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12 mt-3">
                                            <div class="form-group">
                                                <label class="form-label" for="learner_index_number">UCE Index Number <span class="text-danger">*</span></label>
                                                <div class="form-control-wrap">
                                                    <input :disabled="index_no_verify" required="" v-model.trim="form_learner.index_number" minlength="9" maxlength="9" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="eg. U0123/012" id="learner_index_number" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer" style="display: block">
                            <div class="row">
                                <div class="col-12 d-flex flex-lg-row justify-content-center">
                                    <button @click="resetLearner()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light mr-2">
                                        <span>Reset</span>
                                        <em class="ni ni-cross ml-2"></em>
                                    </button>
                                    <button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                                        <span v-if="loading && !index_no_verify" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                        <span v-if="loading && !index_no_verify" class="align-self-center">Fetching Data, Please wait...</span>
                                        <span v-if="loading && !index_no_verify" class="sr-only">Fetching Data, Please wait...</span>
                                        <span v-if="!loading && !index_no_verify" class="align-self-center">Verify</span>

                                        <span v-if="loading && index_no_verify" class="align-self-center">Submitting, Please wait...</span>
                                        <span v-if="loading && index_no_verify" class="sr-only">Submitting, Please wait...</span>
                                        <span v-if="!loading && index_no_verify" class="align-self-center">Submit</span>
                                        <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- /Modal -->
    </div><!-- .card-inner-group -->

</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";

export default {
    name: "IndexNumbersALevel",
    props: [
        "schoolTypeIdObj",
        "educationGradesObj",
        "learnersALevelObj",
    ],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/index-number',
            loading: false,
            index_no_verify: false,
            filtering: false,
            edit: false,
            loading_message: '',
            //photoDropify: null,
            learners: {
                data: [],
                links: [],
                total: 0,
            },
            learner: {
                current_education_grade_id: '',
                lin: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                    gender: 'M',
                    photo: null,
                    birth_date: moment().format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                },
                index_numbers: []
            },
            exam_years: [],
            form_learner: {
                level: '',
                index_number: '',
                exam_year: '',
                learner_id: '',
            },
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            },
            school_type_id: '',
            education_grades: [],
            filter: {
                education_grade_id: '',
                gender: '',
                lin: '',
                name: '',
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school_type_id = this.schoolTypeIdObj;
            this.learners = this.learnersALevelObj;
            this.education_grades = this.educationGradesObj;

            $('#filterALevelEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#filterALevelGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#learnerALevelExamYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateALevelIndexNumberModal'),
                templateSelection: function (data, container) {
                    self.form_learner.exam_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerUCELevel').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateALevelIndexNumberModal'),
                templateSelection: function (data, container) {
                    self.form_learner.level = data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                $('#learnerALevelExamYearId').val('').change();
                $('#learnerUCELevel').val('').change();
            }, 50);
        },
        editDetailsRequestModal: function (learner) {
            this.edit = true;
            this.form_learner.learner_id = learner.person_id;
            this.learner.lin = learner.lin;
            this.learner.education_grade.name = learner.education_grade.name;
            this.learner.index_numbers = learner.index_numbers;
            this.learner.person.photo_url = learner.person.photo_url;
            this.learner.person.full_name = learner.person.full_name;
            this.learner.person.gender = learner.person.gender;
            this.learner.person.birth_date = moment(learner.person.birth_date).format('D MMMM, YYYY');
            $('#updateALevelIndexNumberModal').modal({backdrop: "static"});
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },

        verifyUnebLearner: function () {
            this.loading = true;
            let apiUrl = '';
            apiUrl = '/uneb/uce-learner-info';

            axios.post(apiUrl, {index_number: this.form_learner.index_number.toUpperCase(), exam_year: this.form_learner.exam_year})
                .then(response => {
                    this.loading = false;
                    this.$parent.pending = false;
                    this.uneb_learner = response.data;
                    this.learner.first_name = this.uneb_learner.first_name;
                    this.learner.surname = this.uneb_learner.surname;
                    this.learner.gender = this.uneb_learner.gender;
                    this.learner.birth_date = this.uneb_learner.date_of_birth ? moment(this.uneb_learner.date_of_birth).format('D MMMM, YYYY').toUpperCase():'';
                    $("#learnerBirthDate").datepicker('update', moment(this.uneb_learner.date_of_birth).toDate())
                    //this.uganda = this.uneb_learner.nationality === "UGANDA";
                    // let ug = this.countries.find(c=>{
                    //     return c.name.toUpperCase() === 'UGANDA';
                    // })
                    // $('#learnerCountryId').val(ug.id).change();
                    this.index_no_verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        updateIndexNumber: function () {
            let self = this;
            //this.loading = true;

            Swal.fire({
                title: 'Are you sure?',
                html: '<div style="margin-bottom: 10px;">' +
                        'You want to update this learner index number?</div>',
                icon: 'warning',
                confirmButtonText: 'Yes, Proceed!',
                showCancelButton: true,
                cancelButtonColor: '#29384A',
                reverseButtons: true,

            }).then(function (result) {
                if (result.value) {
                    //self.startLoading('Flagging Learner');
                    axios.post(self.api_url+'/update/', self.form_learner)
                        .then(response=>{
                            Swal.fire({
                                title: 'Success',
                                html: '<div style="margin-bottom: 10px;">' +
                                     'Learner Index Number Saved Successfully.</div>',
                                icon: 'success',
                                confirmButtonText: 'Close',
                                showCancelButton: false,
                            })
                            self.learners = response.data;
                            $('#updateALevelIndexNumberModal').modal('hide');
                            self.resetLearner();
                        })
                        .catch(error=>{
                            self.loading = false;
                            self.renderError(error);
                        });
                }
            });
            // this.loading = true;
            // axios.post(this.api_url+'/update/', this.form_learner)
            //     .then(response=>{
            //         this.$refs.notify.messages.push({status: 'success', title: 'Success:', message:"Learner Index Number Saved Successful"});
            //         this.learners = response.data;
            //         $('#updateALevelIndexNumberModal').modal('hide');
            //         this.resetLearner();
            //     })
            //     .catch(error=>{
            //         this.loading = false;
            //         this.renderError(error);
            //     });
        },
        resetLearner: function () {
            this.loading = false;
            this.index_no_verify = false;
            this.filtering = false;
            this.edit = false;
            this.form_learner.level = '';
            this.form_learner.index_number = '';
            this.form_learner.exam_year = '';
            // this.form_learner = {
            //     level: '',
            //     index_number: '',
            //     exam_year: '',
            //     learner_id: '',
            // };
            this.uneb_learner = {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            };

            window.setTimeout(()=>{
                $('#learnerALevelExamYearId').val('').change();
                $('#learnerUCELevel').val('').change();
            }, 50);
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                gender: '',
                lin: '',
                name: '',
            };
            $('#filterALevelGender').val('').change();
            $('#filterALevelEducationGradeId').val('').change();
            this.loadLearners(1, false);
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/a_level/'+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.learners = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error: ', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error: ', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found: ', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error: ', message:error.message});
            }
        },
    },
    computed: {
        academicYears: function () {
            let years = [];

            for (let i = Number(moment().format("YYYY"))-1; i >= 2015; i--) {
                years.push(i);
            }

            return years;
        },
        getPaginationLinks: function () {
            let arr = this.learners.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    },
}
</script>

<style scoped>

</style>
