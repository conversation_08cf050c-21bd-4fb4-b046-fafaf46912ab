<template>
    <div>
<!--        <ul class="nav nav-tabs">-->
<!--            <li class="nav-item">-->
<!--                <a id="PendingPromotionsTabHandle" class="nav-link active" data-toggle="tab" href="#PendingPromotionsTab">-->
<!--                    <h6>PENDING PROMOTIONS</h6>-->
<!--                </a>-->
<!--            </li>-->
<!--            &lt;!&ndash; <li class="nav-item px-2">-->
<!--                <a class="nav-link" data-toggle="tab" href="#CompletedPromotionsTab">-->
<!--                    <h6>COMPLETED PROMOTIONS</h6>-->
<!--                </a>-->
<!--            </li> &ndash;&gt;-->
<!--        </ul>-->
        <div class="tab-content">
            <div class="tab-pane active" id="PendingPromotionsTab">
                <school-learners-pending-promotions
                    :education-grades-obj="educationGradesObj"
                    :school-type-id-obj="schoolTypeIdObj"
                    :curriculums-obj="curriculumsObj"
                ></school-learners-pending-promotions>
<!--                :pending-promotions-obj="pendingPromotionsObj"-->
            </div>
            <!-- <div class="tab-pane" id="CompletedPromotionsTab">
                <school-learners-completed-promotions
                    :education-grades-obj="educationGradesObj"
                    :school-type-id-obj="schoolTypeIdObj"
                    :curriculums-obj="curriculumsObj"
                ></school-learners-completed-promotions> -->
<!--                :completed-promotions-obj="completedPromotionsObj"-->
            <!-- </div> -->
        </div>
<!--        <div v-if="school_type_id === 7" class="tab-content">-->
<!--            <div class="tab-pane active" id="PendingPromotionsTab">-->
<!--                <school-learners-pending-promotions-->
<!--                    :education-grades-obj="educationGradesObj"-->
<!--                    :school-type-id-obj="schoolTypeIdObj"-->
<!--                ></school-learners-pending-promotions>-->
<!--            </div>-->
<!--            <div class="tab-pane" id="CompletedPromotionsTab">-->
<!--                <school-learners-completed-promotions-->
<!--                    :education-grades-obj="educationGradesObj"-->
<!--                    :school-type-id-obj="schoolTypeIdObj"-->
<!--                ></school-learners-completed-promotions>-->
<!--            </div>-->
<!--        </div>-->
    </div>
</template>

<script>
export default {
    name: "Promotions",
    props: [
        'pendingPromotionsObj','completedPromotionsObj','educationGradesObj', 'schoolTypeIdObj','curriculumsObj'
    ],
    data: function () {
        return {
            pending_promotions: {
                data: [],
                links: [],
                total: 0,
            },
            completed_promotions: {
                data: [],
                links: [],
                total: 0,
            },
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.pending_promotions = this.pendingPromotionsObj;
            this.completed_promotions = this.completedPromotionsObj;
        },
        newTransferRequest: function () {
            $('#PendingPromotionsTabHandle').click();
            $('#pendingPromotionsModal').modal({backdrop: "static"});
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
