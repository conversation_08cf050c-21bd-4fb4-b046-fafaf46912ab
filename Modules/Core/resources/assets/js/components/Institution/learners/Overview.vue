<template>
    <div class="components-preview mx-auto">
        <div class="nk-block nk-block-lg">
            <div class="nk-block-head">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h4 class="title nk-block-title">Overview</h4>
                        <div class="nk-block-des">
                            <p>Information about your school's {{ learnerLabel.toLowerCase() }}s reports</p>
                        </div>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div>
            <div class="nk-block">
                <div class="card card-inner card-inner-lg card-stretch card-bordered border-dark-teal">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="table-responsive">
                                <!--                        <button class="btn btn-md bg-dark-teal mb-2" style="float: right">Download As</button>-->
                                <h5 class="">{{ learnerLabel }}s By Sex</h5>

                                <table class="table border border-dark-teal">
                                    <thead class="bg-secondary">
                                    <tr>
                                        <th class="text-white align-middle text-uppercase w-45" rowspan="2">Sex</th>
                                    </tr>
                                    <tr>
                                        <th class="py-2 text-center text-white text-uppercase border-left border-white">Number Of {{ learnerLabel }}s</th>
                                    </tr>
                                    </thead>
                                    <tbody class="border-top-0 border-dark-teal">
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">FEMALE</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{ female_learners }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">MALE</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{ male_learners }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-dark border-secondary bg-secondary-dim">TOTAL</td>
                                        <td class="align-middle border-left text-dark border-secondary text-center">
                                            <span>{{ male_learners+female_learners }}</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div v-if="schoolTypeIdObj > 3 && schoolTypeIdObj !== 7" class="col-lg-6">
                            <h5 class="">{{ learnerLabel }}s By Course Durations</h5>

                            <div class="table-responsive">
                                <table class="table table-sm table-hover table-bordered">
                                    <thead>
                                    <tr class="bg-secondary">
                                        <th class="text-uppercase border-secondary text-white">Duration of course study</th>
                                        <th class="text-uppercase border-secondary text-white text-center">MALE</th>
                                        <th class="text-uppercase border-secondary text-white text-center">FEMALE</th>
                                    </tr>
                                    </thead>
                                    <tbody class="border-secondary border-top">
                                    <tr v-for="duration in course_durations">
                                        <td class="align-middle text-uppercase text-dark border-secondary">
                                            <span class="">{{ duration.name }}</span>
                                        </td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ duration.male_count }}</span>
                                        </td>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ duration.female_count }}</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                    <tbody class="border-secondary border-top">
                                    <tr>
                                        <th class="align-middle text-uppercase text-dark border-secondary">
                                            <span class="">Total</span>
                                        </th>
                                        <th class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ getTotalMale() }}</span>
                                        </th>
                                        <th class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ getTotalFemale() }}</span>
                                        </th>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div v-else class="col-lg-6">
                            <div class="table-responsive">
                                <!--                        <button class="btn btn-md bg-dark-teal mb-2" style="float: right">Download As</button>-->
                                <h5 v-if="schoolTypeIdObj <= 6" class="">{{ learnerLabel }}s By Sex Per Class</h5>
                                <h5 v-if="schoolTypeIdObj === 7" class="">{{ learnerLabel }}s By Sex Per Curriculum</h5>
                                <table class="table table-sm table-hover table-bordered">
                                    <thead>
                                    <tr class="bg-secondary">
                                        <th v-if="schoolTypeIdObj <= 6" class="py-2 text-uppercase border-secondary text-white">CLASS</th>
                                        <th v-if="schoolTypeIdObj === 7" class="py-2 text-uppercase border-secondary text-white">CURRICULUM</th>
                                        <th class="py-2 text-uppercase border-secondary text-white">SEX</th>
                                        <th class="py-2 text-uppercase border-secondary text-white text-center">TOTAL</th>
                                    </tr>
                                    </thead>
                                    <tbody class="border-secondary border-top" v-for="education_grade in grades">
                                    <tr>
                                        <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                            <span class="">{{ education_grade.name }}</span>
                                        </th>
                                        <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                            <span class="">MALE</span>
                                        </th>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ education_grade.male_count }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                            <span class="">FEMALE</span>
                                        </th>
                                        <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                            <span class="">{{ education_grade.female_count }}</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

<!--                <div class="card card-inner card-inner-lg">-->
<!--                    <div class="table-responsive">-->
<!--                        &lt;!&ndash;                        <button class="btn btn-md bg-dark-teal mb-2" style="float: right">Download As</button>&ndash;&gt;-->
<!--                        <h5 class="">Learners By Sex, Class & Special Needs</h5>-->
<!--                        <table class="table border border-dark-teal">-->
<!--                            <thead class="bg-secondary">-->
<!--                            <tr>-->
<!--                                <th class="text-white align-middle text-uppercase w-45" rowspan="3">Disability Type</th>-->
<!--                                <th class="text-white align-middle text-uppercase text-center border-left border-white py-2" :colspan="grades.length + 6">CLASS</th>-->
<!--                            </tr>-->
<!--                            <tr>-->
<!--                                <th class="py-2 text-center text-white text-uppercase border-left border-white" colspan="2" v-for="grade in grades">{{ grade.name.toUpperCase() }}</th>-->
<!--                            </tr>-->
<!--                            <tr>-->
<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center">F</th>-->
<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>-->

<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>-->
<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>-->

<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>-->
<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>-->

<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">F</th>-->
<!--                                <th style="height: 40px; vertical-align: middle;" class="bg-secondary-dim border-secondary text-center border-left border-secondary">M</th>-->
<!--                            </tr>-->
<!--                            </thead>-->
<!--                            <tbody class="border-top-0 border-dark-teal">-->
<!--                            <tr v-for="type in disabilities">-->
<!--                                <td class="align-middle">{{ type.name.toUpperCase() }}</td>-->
<!--                                <td  class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                                <td class="align-middle border-left border-secondary text-center">-->
<!--                                    <span>0</span>-->
<!--                                </td>-->
<!--                            </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->
<!--                </div>-->
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Overview",
    props: [
        'maleLearnersObj',
        'femaleLearnersObj',
        'gradesObj',
        'disabilitiesObj',
        'courseDurationsObj',
        'schoolTypeIdObj',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            grades: [],
            disabilities: [],
            course_durations: [],
            male_learners: '',
            female_learners: '',
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.male_learners = JSON.parse(this.maleLearnersObj);
            this.female_learners = JSON.parse(this.femaleLearnersObj);
            this.grades = JSON.parse(this.gradesObj);
            //this.disabilities = JSON.parse(this.disabilitiesObj);
            this.course_durations = JSON.parse(this.courseDurationsObj);
        },
        getTotalMale: function () {
            let total = 0;

            this.course_durations.forEach(duration=>{
                total += duration.male_count
            });

            return total;
        },
        getTotalFemale: function () {
            let total = 0;

            this.course_durations.forEach(duration=>{
                total += duration.female_count
            });

            return total;
        },
    },
    computed: {
        learnerLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Student";
            } else {
                return "Learner";
            }
        }
    }
}
</script>

<style scoped>

</style>
