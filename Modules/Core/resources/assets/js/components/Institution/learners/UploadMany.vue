<template>
	<div class="nk-block nk-block-lg">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">Import Learners</h5>
                    <div class="nk-block-des text-soft">
                        <p>You can import data at once using the template provided below.</p>
                    </div>
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->

		<div class="card card-preview">
			<div class="card-inner">
				<div class="row">
					<div class="col-12 d-flex flex-column">
						<span class="align-self-start fs-16 font-weight-bold mr-lg-2">
							Step 1:
						</span>
						<span>Download and fill out the Excel Template then proceed to Step 2</span>
						<button @click="downloadTemplate()" class="btn btn-lg bg-dark-teal align-self-start mt-2">
							<em class="icon ni ni-file-download"></em><span>Download Excel Template</span>
						</button>
					</div>
                </div>
                    <div class="row mt-5">
					<div class="col-12 d-flex flex-column">
						<span class="align-self-start fs-16 font-weight-bold mr-lg-2">
							Step 2:
						</span>
						<span>Upload the Excel Template you filled from Step 1 then click upload</span>
						<form @submit.prevent="uploadFilledTemplate()">
							<div class="mt-2 d-flex flex-lg-row flex-column">
								<div style="margin-top: 1px !important;" class="custom-file mr-0 mr-lg-2">
									<input :disabled="loading" ref="filledTemplate" type="file" :class="[loading ? '' : 'cursor', 'custom-file-input']" id="filledTemplate" @change="checkfile()" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" required>
									<label id="filledTemplateLabel" class="custom-file-label" for="filledTemplate">Upload Filled Excel Template</label>
								</div>
								<button type="submit" :disabled="!valid_file || loading" class="btn btn-lg btn-primary mt-2 mt-lg-0">
									<span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
									<span v-if="loading" class="align-self-center">Uploading...</span>
									<span v-if="loading" class="sr-only">Uploading...</span>
									<span v-if="!loading" class="align-self-center">Upload</span><em v-if="!loading" class="ni ni-upload ml-2"></em>
								</button>
							</div>
						</form>
					</div>
					<div v-if="uploadPercentage > 0 && uploadPercentage < 100" class="col-12">
						<div class="progress my-4">
							<div class="progress-bar progress-bar-striped progress-bar-animated" :style="'width: '+uploadPercentage+'%;'"></div>
						</div>
					</div>
					<div v-if="learners.length" class="col-12 mt-4 d-flex flex-column">
						<div v-if="errorsPresent.length" class="alert alert-fill alert-danger alert-dismissible alert-icon mb-2">
							<em class="icon ni ni-cross-circle"></em>
							<strong>Some Data Had Errors</strong>! All entries with errors won't be saved.
							<button class="close" data-dismiss="alert"></button>
						</div>
						<span class="align-self-start fs-16 font-weight-bold mr-lg-2">
							Step 3:
						</span>
						<span>Cross check your data and select the learners you want to save.</span>
						<div class="mt-2 nk-tb-list nk-tb-ulist is-compact">
							<div class="nk-tb-item nk-tb-head bg-secondary">
								<div class="nk-tb-col nk-tb-col-check">
									<div class="custom-control custom-control-sm custom-checkbox notext">
										<input :disabled="deleting_loading || saving_loading" @change="toggleAllLearners()" v-model="select_all_learners" type="checkbox" class="custom-control-input" id="allLearners">
										<label class="custom-control-label" for="allLearners"></label>
									</div>
								</div>
								<div class="nk-tb-col"><span class="sub-text ucap text-white">Learner</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Gender</span></div>
								<div class="nk-tb-col"><span class="sub-text ucap text-white">Birth Date</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Class</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Nationality</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">NIN</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Passport Number</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Refugee ID</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">UNEB Index No.</span></div>
								<div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Seating Year.</span></div>
							</div><!-- .nk-tb-item -->
                            <div v-for="learner in learners" class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input :disabled="deleting_loading || saving_loading" @change="toggleOneLearner()" v-model="selected_learners" :value="learner.id" :class="[learner.passed_validation === 0 ? 'custom-control-input-danger' : '' , 'custom-control-input']" type="checkbox" :id="'learner'+learner.id">
                                        <label :class="[learner.passed_validation === 0 ? 'custom-control-label-danger' : '' , 'custom-control-label']" :for="'learner'+learner.id"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']">{{ learner.full_name }}</span>
                                    <button @click="showErrors(learner.validation_errors)" v-if="learner.passed_validation === 0" class="btn btn-danger btn-xs ml-2">View Errors</button>
                                </div>
                                <div class="nk-tb-col text-center">
									<span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']">
										{{ learner.gender === 0 ? 'Male' : (learner.gender === 1 ? 'Female' : '') }}
									</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.birth_date !== null">{{ formatDate(learner.birth_date) }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.education_grade !== null">{{ learner.education_grade.name.toUpperCase() }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.country !== null">{{ learner.country.title.toUpperCase()  }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.nin !== null">{{ learner.nin }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.passport_number !== null">{{ learner.passport_number }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.refugee_number !== null">{{ learner.refugee_number }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.index_number !== null">{{ learner.index_number }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span :class="[learner.passed_validation === 0 ? 'text-danger' : 'text-dark', 'text-uppercase']" v-if="learner.index_number !== null">{{ learner.index_number }}</span>
                                </div>
                            </div><!-- .nk-tb-item -->
						</div><!-- .nk-tb-list -->
						<div class="mt-2 d-flex flex-column flex-lg-row">
                            <button @click="validLearners ? saveLearners() : null" :disabled="!validLearners || saving_loading" class="btn btn-primary">
                                <span v-if="saving_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="saving_loading" class="align-self-center">Saving...</span>
                                <span v-if="saving_loading" class="sr-only">Saving...</span>
                                <span v-if="!saving_loading" class="align-self-center">Save Learners</span>
                            </button>
							<button @click="selected_learners.length > 0 ? deleteSelected() : null" :disabled="selected_learners.length === 0 || deleting_loading" class="btn btn-danger mt-3 mt-lg-0 ml-lg-2">
								<span v-if="deleting_loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
								<span v-if="deleting_loading" class="align-self-center">Deleting...</span>
								<span v-if="deleting_loading" class="sr-only">Deleting...</span>
								<span v-if="!deleting_loading" class="align-self-center">Delete Selected</span>
							</button>
						</div>
						<!-- Modal Content Code -->
						<div class="modal fade zoom" tabindex="-1" id="errorsModal">
							<div class="modal-dialog modal-sm" role="document">
								<div class="modal-content">
									<a @click="clearErrors()" class="close cursor" data-dismiss="modal" aria-label="Close">
										<em class="icon ni ni-cross text-white"></em>
									</a>
									<div class="modal-header bg-danger">
										<h5 class="modal-title text-white">Errors</h5>
									</div>
									<div class="modal-body pb-5">
										<ul v-if="formattedErrors.length" class="list-unstyled">
											<li v-for="error in formattedErrors">
												<em class="ni ni-alert-circle-fill text-danger mr-1"></em>{{ error }}
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "UploadMany",
		props: '',
		mounted() {
			this.initPlugins();
		},
		data: function () {
			return {
				select_all_learners: false,
				loading: false,
				saving_loading: false,
				deleting_loading: false,
				edit: false,
				valid_file: false,
				filled_template: '',
			    uploadPercentage: 0,
			    selected_learners: [],
			    learners: [],
			    errors: {},
			    birth_dates: [],
			}
		},
		methods: {
			initPlugins: function () {
				let self = this;
			},
			toggleAllLearners: function () {
				this.selected_learners =[];

				if (this.select_all_learners) {
					this.learners.forEach(learner=>{
						this.selected_learners.push(learner.id)
					});
				}
			},
			toggleOneLearner: function (id) {
				this.select_all_learners = this.selected_learners.length === this.learners.length
			},

            formatDate: function (raw_date) {
                return moment(raw_date).format("D MMMM, YYYY");
            },
            downloadTemplate: function () {
                axios({
                    method: 'get',
                    url: '/learners/download-primary-school-template?year='+this.selectedYear,
                    responseType: 'arraybuffer'
                })
                .then(response=>{
                    let blob = new Blob([response.data], { type: 'application/octet-stream' });
                    let a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(blob, {
                        type: 'data:attachment/xlsx'
                    })
                    a.download = 'Learners_upload_template.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                })
                .catch(error=>{
                    console.log(error)
                    new Noty({
                        type: "error",
                        text: error.response.data.message
                    }).show();
                });
            },
			checkfile: function () {
				let validExts = [".xlsx", ".xls", ".csv"];
				let fileExt = this.$refs.filledTemplate.value;
				fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

				if (validExts.indexOf(fileExt) < 0) {
					new Noty({
						type: "error",
						text: "Invalid file selected, valid files are of " + validExts.toString() + " types."
					}).show();

					this.resetFile();
					return false;
				}

				this.valid_file = true;
				this.filled_template = this.$refs.filledTemplate.files[0];

				return true;
			},
			resetFile: function () {
				this.filled_template = '';
			    this.uploadPercentage = 0;
			    this.valid_file = false;
			    this.loading = false;
				this.$refs.filledTemplate.value = null;
				window.setTimeout(()=>{$('#filledTemplateLabel').text("Upload Filled Excel Template")}, 10);
			},
            uploadFilledTemplate: function () {
                this.loading = true;
                let formData = new FormData();
                formData.append('filled_template', this.filled_template);

                axios.post('/learners/upload-primary-school-template?year='+this.selectedYear, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    onUploadProgress: function( progressEvent ) {
                        this.uploadPercentage = parseInt(Math.round( ( progressEvent.loaded / progressEvent.total ) * 100));
                    }.bind(this)
                })
                .then(response=>{
                    console.clear();
                    this.resetFile();
                    this.learners = response.data;
                    new Noty({
                        type: "success",
                        text: "Learners Uploaded successfully"
                    }).show();
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    new Noty({
                        type: "error",
                        text: error.response.data.message
                    }).show();
                    // this.resetFile();
                });
            },
			showErrors: function (errors) {
				this.errors = JSON.parse(errors);
				$("#errorsModal").modal({backdrop: "static"});
			},
			clearErrors: function () {
				this.errors = {};
				$("#errorsModal").modal("hide");
			},
            saveLearners: function () {
                this.saving_loading = true;
                axios.post('/learners/save-learner-uploads?year='+this.selectedYear,{uploads: this.selected_learners})
                    .then(response=>{
                        this.saving_loading = false;
                        this.learners = response.data;
                        new Noty({
                            type: "success",
                            text: "Learners saved successfully"
                        }).show();
                        window.location.href = "/institution/learners/all";
                        this.selected_learners = [];
                        this.select_all_learners = false;
                    })
                    .catch(error=>{
                        this.saving_loading = false;
                        console.log(error);
                    })
            },
			deleteSelected: function () {
				this.deleting_loading = true;
				axios.post('/learners/delete-selected-uploads?year='+this.selectedYear, {selected_learners: this.selected_learners})
				.then(response=>{
					this.deleting_loading = false;
					this.learners = response.data;
					new Noty({
						type: "success",
						text: this.selected_learners.length + " record(s) deleted successfully"
					}).show();
					this.birth_dates = [];
					this.selected_learners = [];
					this.select_all_learners = false;
					window.setTimeout(()=>{ this.initDatePicker() }, 10);
				})
				.catch(error=>{
					this.deleting_loading = false;
					console.log(error);
				})
			},
		},
		computed: {
            validLearners: function () {
                let response = true;

                if (!this.selected_learners.length) {
                    response = false;
                } else {
                    this.selected_learners.forEach(learner_id=>{
                        let invalid = this.learners.find(learner=>{return learner.id === learner_id && learner.passed_validation === 0});

                        if (invalid !== undefined) {
                            response = false;
                        }
                    });
                }

                return response;
            },
			errorsPresent: function () {
				return this.learners.filter(learner=>{
					return learner.passed_validation === 0;
				});
			},
			dateMissing: function () {
				return this.cleanData.length > 0 && this.birth_dates.length < this.cleanData.length;
			},
			cleanData: function () {
				return this.learners.filter(learner=>{
					return learner.passed_validation === 1;
				});
			},
			formattedErrors: function () {
				let arr = [];
				Object.values(this.errors).forEach(attribute => {
					attribute.forEach(error=>{
						arr.push(error);
					});
				});
				return arr;
			},
			selectedYear: function () {
				let queryString = window.location.search;
				let urlParams = new URLSearchParams(queryString);

				if (urlParams.has('year')) {
					return urlParams.get('year');
				} else {
					return moment().format("YYYY");
				}
			}
		}
	}
</script>

<style scoped>
	.custom-file-label {
		height: 44px !important;
		padding: 10px 1rem !important;
	}

	.custom-file-label::after {
		height: 100% !important;
	    padding: 10px 1rem !important;
	}

	.custom-control-input-danger:checked ~
	.custom-control-label-danger::before,
	.custom-control-input-danger:not(:disabled):active ~
	.custom-control-label-danger::before {
		background-color: #e85347;
		border-color: #e85347;
	}
</style>
