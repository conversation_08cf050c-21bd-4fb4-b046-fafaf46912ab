<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Learner Transition</h3>
                    <div class="nk-block-des">
                        <nav class="nk-block-des">
                            <ul class="breadcrumb breadcrumb-arrow">
                                <li class="breadcrumb-item">
                                    <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item active text-soft">
                                    Add New Learner
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <!--								<li>-->
                                <!--									<a class="cursor btn btn-secondary text-white">-->
                                <!--										<em class="icon ni ni-download-cloud"></em><span>Export</span>-->
                                <!--									</a>-->
                                <!--								</li>-->
                                <li>
                                    <a @click="admissionRequestModal()" class="cursor btn bg-dark-teal">
                                        <span class="text-white">Add New Learner</span><em class="icon ni ni-plus-circle-fill text-white"></em>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <div class="card-inner-group">
                        <div class="card-inner position-relative card-tools-toggle">
                            <div class="card-title-group">
                                <div class="card-tools">
                                    <form @submit.prevent="loadLearners(1, true)">
                                        <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                            <div style="width: 200px !important" class="form-wrap">
                                                <select id="filterIncomingEducationGradeId" class="form-select-sm">
                                                    <option value="">ALL CLASSES</option>
                                                    <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                                </select>
                                            </div>
                                            <div style="width: 200px !important" class="form-wrap">
                                                <select id="filterIncomingGender" class="form-select-sm">
                                                    <option value="">ALL GENDERS</option>
                                                    <option value="M">MALE</option>
                                                    <option value="F">FEMALE</option>
                                                </select>
                                            </div>
                                            <div style="width: 300px !important" class="form-wrap">
                                                <div class="input-group">
                                                    <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                                    <div class="input-group-append">
                                                        <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                            <em class="icon ni ni-cross"></em>
                                                        </button>
                                                        <button class="btn rounded-right bg-dark-teal" type="submit">
                                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div><!-- .card-tools -->
                            </div><!-- .card-title-group -->
                        </div><!-- .card-inner -->

                        <div class="card-inner p-0">
                            <div class="table-responsive">
                                <table class="nk-tb-list nk-tb-ulist is-compact">
                                    <thead>
                                    <tr class="nk-tb-item nk-tb-head bg-secondary">
                                        <th class="nk-tb-col px-2 text-white border-white cursor text-uppercase border-1">
                                            <span class="sub-text ucap text-white">Learner</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                            <span class="sub-text ucap text-white">LIN</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                            <span class="sub-text ucap text-white">Sex</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                            <span class="sub-text ucap text-white">Class</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                            <span class="sub-text ucap text-white">Previous School</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                            <span class="sub-text ucap text-white">Transition Date</span>
                                        </th>
                                        <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                            <span class="sub-text ucap text-white">Action</span>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody class="border-bottom">
                                    <tr v-for="enrolment in learner_admissions.data" class="nk-tb-item">
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" target="_blank" class="tb-lead cursor text-dark-teal">
                                                <div class="user-card">
                                                    <div class="user-avatar">
                                                        <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.initials">
                                                    </div>
                                                    <div class="user-name text-uppercase">
                                                        <span class="text-dark-teal">{{ enrolment.learner.person.full_name }}</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ enrolment.learner.lin }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ enrolment.learner.person.gender }}</span>
                                        </td>

                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ enrolment.learner.education_grade.name.toUpperCase() }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                    <span v-if="enrolment.learner.transition !== null" class="text-dark tb-lead">
                                        {{ enrolment.learner.transition.school.name }}
                                        <span class="font-italic text-muted">({{ enrolment.learner.transition.school.district.name.toUpperCase() }})</span>
                                    </span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span v-if="enrolment.learner.transition !== null" class="text-dark tb-lead">{{ formatDate(enrolment.learner.transition.date_created) }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" target="_blank" data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                                <em class="icon ni ni-eye-fill"></em>
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div><!-- .card-inner -->

                        <div v-if="!learner_admissions.data.length" class="card-inner p-0">
                            <div class="card-body">
                                <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                    <em class="icon ni ni-alert-circle"></em> There are no learner transitions to display at the moment.
                                </div>
                            </div>
                        </div>

                        <div v-if="learner_admissions.data.length" class="card-inner d-flex flex-row justify-content-between">
                            <nav>
                                <ul class="pagination">
                                    <li :class="[learner_admissions.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                        <a @click="learner_admissions.current_page > 1 ? loadLearners(1) : null" :class="[learner_admissions.current_page === 1 ? '' : 'cursor', 'page-link']">
                                            <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                        </a>
                                    </li>
                                    <li :class="[learner_admissions.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                        <a @click="learner_admissions.current_page > 1 ? loadLearners(learner_admissions.current_page-1) : null" :class="[learner_admissions.current_page === 1 ? '' : 'cursor', 'page-link']">
                                            <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                        </a>
                                    </li>
                                    <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                        <a @click="loadLearners(link.label)" class="page-link cursor" v-html="link.label"></a>
                                    </li>
                                    <li :class="[learner_admissions.current_page === learner_admissions.last_page ? 'disabled' : '', getLinkClasses()]">
                                        <a @click="learner_admissions.current_page < learner_admissions.last_page ? loadLearners(learner_admissions.current_page+1) : null" :class="[learner_admissions.current_page === learner_admissions.last_page ? '' : 'cursor', 'page-link']">
                                            <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                        </a>
                                    </li>
                                    <li :class="[learner_admissions.current_page === learner_admissions.last_page ? 'disabled' : '', getLinkClasses()]">
                                        <a @click="learner_admissions.current_page < learner_admissions.last_page ? loadLearners(learner_admissions.last_page) : null" :class="[learner_admissions.current_page === learner_admissions.last_page ? '' : 'cursor', 'page-link']">
                                            <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                            <div class="d-flex ml-4">
                <span class="align-self-center">
                    Showing <span class="text-primary">{{ learner_admissions.from }}</span> to <span class="text-primary">{{ learner_admissions.to }}</span> of <span class="text-primary">{{ learner_admissions.total }}</span>
                </span>
                            </div>
                        </div><!-- .card-inner -->

                        <!-- Transfer Modal -->
                        <div class="modal fade zoom" tabindex="-1" id="learnerAdmissionModal">
                            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <a @click="resetSearchLearner()" class="cursor close" data-dismiss="modal" aria-label="Close">
                                        <em class="icon ni ni-cross"></em>
                                    </a>
                                    <div class="modal-header">
                                        <h5 class="modal-title">Add New Learner</h5>
                                    </div>
                                    <form @submit.prevent="verify ? (!index_no_verify && school_type_id === 3 ? verifyUnebLearner() : admitLearner(learner)) : getLearnerDetails()">
                                        <div class="modal-body">
                                            <notifications ref="notify"></notifications>
                                            <error-notifications ref="notifyError"></error-notifications>
                                            <div class="alert alert-info alert-icon">
                                                <em class="icon ni ni-alert-circle"></em>
                                                <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                                                <h6 v-if="!verify"  class="small mb-2"><span class="text-danger">Step 1:</span> Enter NIN or LIN registered in EMIS and click proceed.</h6>
                                                <h6 v-if="verify && !index_no_verify" class="small mb-2"><span class="text-danger">Step 2:</span> Verify EMIS Learner Information</h6>
                                                <h6 class="small mb-2" v-if="verify && !index_no_verify && schoolTypeIdObj !== 2"><span class="text-danger">Step 3:</span> Enter Learner UNEB Details</h6>
                                                <h6 class="small mb-2" v-if="verify && !index_no_verify && schoolTypeIdObj !== 2"><span class="text-danger">Step 4:</span> Verify Learner UNEB Details and compare</h6>
<!--                                                <h6 class="small mb-2"><span class="text-danger">Step 5:</span> Tick checkbox if information matches</h6>-->
                                                <h6 v-if="verify && index_no_verify" class="small mb-2"><span class="text-danger">Step {{ schoolTypeIdObj !== 2 ? '5': '3 '}}:</span> Click Submit to complete the process if the information matches.</h6>
                                            </div>

                                            <div v-show="!verify" :class="[!verify ? 'mb-2' : '']">
                                                <div class="row">
                                                    <div class="col-lg-12 mt-2">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learner_lin">Learner's NIN or LIN <span class="text-danger">*</span></label>
                                                            <div class="form-control-wrap">
                                                                <input v-model.trim="form_learner.id_number" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter Learners NIN or LIN" id="learner_lin" maxlength="14" minlength="14" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div v-show="verify" class="col-12 ml-2">
                                                    <div class="table-responsive align-self-start">
                                                        <table class="table table-sm">
                                                            <thead>
                                                            <tr>
                                                                <th style="border: none">
                                                                    <h6>EMIS Details</h6>
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr>
                                                                <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                                    <div class="form-group d-flex flex-column justify-content-center">
                                                                        <div class="w-150px mx-auto">
                                                                            <img v-if="learner.person.photo_url !== null" id="learnerPhoto" :src="learner.person.photo_url" :alt="learner.person.full_name" class="rounded-0">
                                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'F'" id="learnerPhoto" src="@images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'M'" id="learnerPhoto" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                                    <span class="">{{ learner.person.full_name }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">LIN</h6>
                                                                    <span class="">{{ learner.lin }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr v-if="school_type_id >= 3">
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                                    <span v-for="indexNumber in learner.index_numbers" class="">{{ indexNumber.level }} : {{ indexNumber.index_number }} - {{ indexNumber.exam_year }}</span>
                                                                    <span v-if="learner.index_numbers === null || learner.index_numbers === undefined || learner.index_numbers === ''" class="text-sm text-muted">Not Set</span>
                                                                </td>
                                                            </tr>
                                                            <!--                                        <tr>-->
                                                            <!--                                            <td class="px-2 align-middle text-dark text-uppercase">-->
                                                            <!--                                                <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>-->
                                                            <!--                                                <span class="">{{ form_learner.index_number }} : {{ form_learner.level }} - : {{ form_learner.exam_year }}</span>-->
                                                            <!--                                            </td>-->
                                                            <!--                                        </tr>-->
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">PREVIOUS CLASS</h6>
                                                                    <span v-if="learner.education_grade !== null" class="">{{ learner.education_grade.name.toUpperCase() }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                    <span v-if="learner.person.gender === 'F'" class="">FEMALE</span>
                                                                    <span v-if="learner.person.gender === 'M'" class="">MALE</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                    <span class="">{{ formatDate(learner.person.birth_date) }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">PREVIOUS SCHOOL</h6>
                                                                    <span class="">{{ learner.school.name }} - {{ learner.school.district.name.toUpperCase() }}</span>
                                                                </td>
                                                            </tr>

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div v-show="index_no_verify" class="col-12 ml-2">
                                                    <hr class="border-dark-teal my-1">
                                                    <div class="table-responsive align-self-start">
                                                        <table class="table table-sm">
                                                            <thead>
                                                            <tr>
                                                                <th style="border: none">
                                                                    <h6>Uneb Details</h6>
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr>
                                                                <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                                    <div class="form-group d-flex flex-column justify-content-center">
                                                                        <div class="w-150px mx-auto">
                                                                            <img v-if="learner.person.photo_url !== null" id="learnerPhoto" :src="learner.person.photo_url" :alt="learner.person.full_name" class="rounded-0">
                                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'F'" id="learnerPhoto" src="@images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                                            <img v-if="learner.person.photo_url === null && learner.person.gender === 'M'" id="learnerPhoto" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                                    <span class="">{{ uneb_learner.name }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr v-if="school_type_id >= 3">
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                                    <span class="">{{ uneb_learner.index_number }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">EXAM YEAR</h6>
                                                                    <span class="">{{ uneb_learner.exam_year }}</span>
                                                                </td>
                                                            </tr>
<!--                                                            <tr v-if="uneb_learner.exam_year !== 2024">-->
<!--                                                                <td class="px-2 align-middle text-dark text-uppercase">-->
<!--                                                                    <h6 class="overline-title mb-0 text-dark-teal">EXAM LEVEL</h6>-->
<!--                                                                    <span class="">{{ uneb_learner.exam_level }}</span>-->
<!--                                                                </td>-->
<!--                                                            </tr>-->
                                                            <tr>
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                    <span v-if="uneb_learner.gender === 'F'" class="">FEMALE</span>
                                                                    <span v-if="uneb_learner.gender === 'M'" class="">MALE</span>
                                                                </td>
                                                            </tr>
                                                            <tr v-if="uneb_learner.exam_year !== 2024">
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                    <span class="">{{ formatDate(uneb_learner.date_of_birth) }}</span>
                                                                </td>
                                                            </tr>
                                                            <tr v-if="uneb_learner.exam_year === 2024">
                                                                <td class="px-2 align-middle text-dark text-uppercase">
                                                                    <h6 class="overline-title mb-0 text-dark-teal">AGE</h6>
                                                                    <span class="">{{ uneb_learner.age }}</span>
                                                                </td>
                                                            </tr>

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div v-show="verify && school_type_id === 3" class="col-12">
                                                    <hr class="border-dark-teal my-1">
                                                    <div class="row">
                                                        <div class="col-lg-6 mt-3">
                                                            <div v-show="learner.school.school_type_id === 2" class="form-group">
                                                                <label class="form-label" for="learnerLevelPle">Learner Level <span class="text-danger">*</span></label>
                                                                <select :disabled="index_no_verify" :required="verify && learner.school.school_type_id === 2 && school_type_id === 3" id="learnerLevelPle" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option value="PLE">PLE</option>
<!--                                                                    <option value="UACE">UACE</option>-->
                                                                </select>
                                                            </div>
                                                            <div v-show="learner.school.school_type_id === 3" class="form-group">
                                                                <label class="form-label" for="learnerLevelUce">Exam Level <span class="text-danger">*</span></label>
                                                                <select :disabled="index_no_verify" :required="verify && learner.school.school_type_id === 3 && school_type_id === 3" id="learnerLevelUce" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option value="UCE">UCE</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6 mt-3">
                                                            <div class="form-group">
                                                                <label class="form-label" for="learnerExamYearId">Exam Year <span class="text-danger">*</span></label>
                                                                <select :disabled="index_no_verify" :required="verify && school_type_id === 3" id="learnerExamYearId" class="form-select-sm">
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="year in academicYears" :value="year">{{ year }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="learner.school.school_type_id === 2" class="row">
                                                        <div class="col-lg-12 mt-3">
                                                            <div class="form-group">
                                                                <label class="form-label" for="learner_index_number">PLE Index Number <span class="text-danger">*</span></label>
                                                                <div class="form-control-wrap">
                                                                    <input :disabled="index_no_verify" :required="verify && learner.school.school_type_id === 2 && school_type_id === 3" v-model.trim="form_learner.index_number" minlength="10" maxlength="10" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="eg. 012345/012"  id="learner_index_number" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="learner.school.school_type_id === 3" class="row">
                                                        <div class="col-lg-12 mt-3">
                                                            <div class="form-group">
                                                                <label class="form-label" for="learner_index_number">UCE Index Number <span class="text-danger">*</span></label>
                                                                <div class="form-control-wrap">
                                                                    <input :disabled="index_no_verify" :required="verify && learner.school.school_type_id === 2 && school_type_id === 3" v-model.trim="form_learner.index_number" minlength="9" maxlength="10" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="eg. U0123/012" id="learner_index_number" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
<!--                                        <div class="col-12">-->
<!--                                            <div class="custom-control custom-control-xs custom-checkbox">-->
<!--                                                <input :required="verify && index_no_verify" type="checkbox" class="custom-control-input" id="tc-agree">-->
<!--                                                <label class="custom-control-label" for="tc-agree">I Here By Acknowledge That the above learner information is true and matches the information I provided.</label>-->
<!--                                            </div>-->
<!--                                        </div>&lt;!&ndash; .col &ndash;&gt;-->
                                        <div class="modal-footer" style="display: block">
                                            <div class="row">
                                                <div class="col-12 d-flex flex-lg-row justify-content-center">
                                                    <button @click="resetSearchLearner()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light mr-2">
                                                        <span>Reset</span>
                                                        <em class="ni ni-cross ml-2"></em>
                                                    </button>
                                                    <button :disabled="loading " type="submit" class="btn bg-dark-teal d-flex">
                                                        <span v-if="loading && !verify && !index_no_verify" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                                        <span v-if="loading && !verify && !index_no_verify" class="align-self-center">Fetching Data, Please wait...</span>
                                                        <span v-if="loading && !verify && !index_no_verify" class="sr-only">Fetching Data, Please wait...</span>
                                                        <span v-if="!loading && !verify && !index_no_verify" class="align-self-center">Proceed</span>

                                                        <span v-if="loading && verify && !index_no_verify && school_type_id === 3" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                                        <span v-if="loading && verify && !index_no_verify && school_type_id === 3" class="align-self-center">Fetching Data, Please wait...</span>
                                                        <span v-if="loading && verify && !index_no_verify && school_type_id === 3" class="sr-only">Fetching Data, Please wait...</span>
                                                        <span v-if="!loading && verify && !index_no_verify && school_type_id === 3" class="align-self-center">Verify</span>

                                                        <span v-if="loading && verify && index_no_verify && school_type_id === 3" class="align-self-center">Submitting, Please wait...</span>
                                                        <span v-if="loading && verify && index_no_verify && school_type_id === 3" class="sr-only">Submitting, Please wait...</span>
                                                        <span v-if="!loading && verify && index_no_verify && school_type_id === 3" class="align-self-center">Submit</span>

                                                        <span v-if="loading && verify && !index_no_verify && school_type_id !== 3" class="align-self-center">Submitting, Please wait...</span>
                                                        <span v-if="loading && verify && !index_no_verify && school_type_id !== 3" class="sr-only">Submitting, Please wait...</span>
                                                        <span v-if="!loading && verify && !index_no_verify && school_type_id !== 3" class="align-self-center">Submit</span>
                                                        <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <!-- /Transfer Modal -->
                    </div><!-- .card-inner-group -->
                </div><!-- .nk-block -->
            </div><!-- .card-inner-group -->
        </div>
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";

export default {
    name: "TransitionForm",
    props: [
        "schoolTypeIdObj",
        "educationGradesObj",
        "countriesObj",
        "learnerAdmissionsObj",
    ],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/transitions',
            verify: false,
            index_no_verify: false,
            loading: false,
            filtering: false,
            nin_learner_loading: false,
            nin_loading: false,
            nin_verify: false,
            loading_message: '',
            //photoDropify: null,
            learner_admissions: {
                data: [],
                links: [],
                total: 0,
            },
            learner: {
                current_education_grade_id: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    gender: 'M',
                    // birth_date: moment().format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                },
                index_numbers: []
            },
            exam_years: [],
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            },
            form_learner: {
                level: '',
                index_number: '',
                exam_year: '',
                id_number: '',
            },
            transfer: {
                education_grade_id: '',
            },
            school_type_id: '',
            education_grades: [],
            uganda: true,
            filter: {
                education_grade_id: '',
                gender: '',
                name: '',
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school_type_id = this.schoolTypeIdObj;
            this.learner_admissions = this.learnerAdmissionsObj;
            this.education_grades = this.educationGradesObj;

            $('#filterIncomingEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#filterIncomingGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#learnerExamYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.exam_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerLevelPle').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.level = data.id;
                    //let isPleSelected = data.text === 'PLE';
                    //$('#learnerLevelPle').prop('disabled', isPleSelected);
                    return data.text;
                },
            });

            $('#learnerLevelUce').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.level = data.id;
                    //let isUceSelected = data.text === 'UCE';
                    //$('#learnerLevelUce').prop('disabled', isUceSelected);
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                $('#learnerExamYearId').val('').change();
                $('#learnerLevelPle').val('').change();
                $('#learnerLevelUce').val('').change();
            }, 50);
        },
        admissionRequestModal: function () {
            $('#learnerAdmissionModal').modal({backdrop: "static"});
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        getLearnerDetails: function () {
            if (this.form_learner.id_number === '') {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error:', message:"Enter NIN or LIN to proceed!!!"});
            } else {
                this.loading = true;
                axios.post(this.api_url + '/verify/'+this.form_learner.id_number.toLowerCase(), this.form_learner)
                    .then(response => {
                        this.loading = false;
                        this.learner = response.data;
                        this.verify = true;
                    })
                    .catch(error => {
                        this.loading = false;
                        this.renderError(error);
                    });
            }
        },
        verifyUnebLearner: function () {
            this.loading = true;
            let apiUrl = '';
            if (this.form_learner.level === 'UCE') {
                apiUrl = '/uneb/uce-learner-info';
            } else {
                apiUrl = '/uneb/ple-learner-info';
            }
            axios.post(apiUrl, {index_number: this.form_learner.index_number.toUpperCase(), exam_year: this.form_learner.exam_year})
                .then(response => {
                    this.loading = false;
                    this.$parent.pending = false;
                    this.uneb_learner = response.data;
                    this.learner.first_name = this.uneb_learner.first_name;
                    this.learner.surname = this.uneb_learner.surname;
                    this.learner.gender = this.uneb_learner.gender;
                    this.learner.birth_date = this.uneb_learner.date_of_birth ? moment(this.uneb_learner.date_of_birth).format('D MMMM, YYYY').toUpperCase():'';
                    $("#learnerBirthDate").datepicker('update', moment(this.uneb_learner.date_of_birth).toDate())
                    //this.uganda = this.uneb_learner.nationality === "UGANDA";
                    // let ug = this.countries.find(c=>{
                    //     return c.name.toUpperCase() === 'UGANDA';
                    // })
                    // $('#learnerCountryId').val(ug.id).change();
                    this.index_no_verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        admitLearner: function (learner) {
            let self = this;
            //this.loading = true;

            Swal.fire({
                title: 'Are you sure?',
                html: '<div style="margin-bottom: 10px;">' +
                        'You want to proceed to transition this learner?</div>',
                icon: 'warning',
                confirmButtonText: 'Yes, Proceed!',
                showCancelButton: true,
                cancelButtonColor: '#29384A',
                reverseButtons: true,

            }).then(function (result) {
                if (result.value) {
                    //self.startLoading('Flagging Learner');
                    axios.post(self.api_url+'/transfer/'+learner.person_id, self.form_learner)
                        .then(response=>{
                            Swal.fire({
                                title: 'Success',
                                html: '<div style="margin-bottom: 10px;">' +
                                     'Learner Transition Successfull.</div>',
                                icon: 'success',
                                confirmButtonText: 'Close',
                                showCancelButton: false,
                            })
                            self.learner_admissions = response.data;
                            $('#learnerAdmissionModal').modal('hide');
                            self.resetSearchLearner();
                        })
                        .catch(error=>{
                            self.loading = false;
                            self.renderError(error);
                        });
                }
            });

            // axios.post(this.api_url+'/transfer/'+learner.person_id, this.form_learner)
            //     .then(response=>{
            //         this.$refs.notify.messages.push({status: 'success', title: 'Success:', message:"Learner Transition Successful"});
            //         this.learner_admissions = response.data;
            //         $('#learnerAdmissionModal').modal('hide');
            //         this.resetSearchLearner();
            //     })
            //     .catch(error=>{
            //         this.loading = false;
            //         this.renderError(error);
            //     });
        },
        // parentVerifyNIN() {
        //     if (this.uganda && this.form_learner.parent_nin.length === 14 && !this.nin_loading && !this.nin_verify) {
        //         this.nin_loading = true;
        //         axios.post('/nira/user-info', {id_number: this.form_learner.parent_nin.toUpperCase()})
        //             .then(response => {
        //                 this.nin_loading = false;
        //                 this.uganda = true;
        //                 this.nira_parent = response.data;
        //                 this.nin_verify = true;
        //                 if (this.nira_parent.photo !== null) {
        //                     if (this.nira_parent.photo.includes('.png')) {
        //                         $('#parentPersonPhoto').attr('src', '/images/nira-photos/' + this.nira_parent.photo.toLowerCase());
        //                     } else {
        //                         $('#parentPersonPhoto').attr('src', 'data:image/png;base64,' + this.nira_parent.photo);
        //                     }
        //                 } else {
        //                     $('#parentPersonPhoto').attr('src', this.nira_parent.gender === 'M' ? '@images/default_male.jpg' : '@images/default_female.jpg');
        //                 }
        //             })
        //             .catch(error => {
        //                 this.nin_loading = false;
        //                 this.renderError(error);
        //             });
        //     }
        // },
        // verifyPassport() {
        //     if (this.uganda && this.form_learner.parent_passport.length && !this.nin_loading) {
        //         this.nin_loading = true;
        //         window.setTimeout(()=>{
        //             this.nin_loading = false;
        //             this.uganda = false;
        //             this.nin_verify = true;
        //         }, 1000)
        //     }
        // },
        resetSearchLearner: function () {
            this.verify = false;
            this.index_no_verify = false;
            this.nin_verify = false;
            this.loading = false;
            this.uganda =  true;
            this.nin_learner_loading = false;
            this.nin_loading = false;
            this.filtering = false;
            this.learner = {
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                },
                index_numbers:[]
            };
            this.form_learner = {
                id_number: '',
                level: '',
                index_number: '',
                exam_year: '',
                gender: 'M',
                // birth_date: moment().format("D MMMM, YYYY"),
            };
            this.uneb_learner = {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            };

            window.setTimeout(()=>{
                $('#learnerExamYearId').val('').change();
                $('#learnerLevelPle').val('').change();
                $('#learnerLevelUce').val('').change();
                //$('#learnerBirthDate').datepicker('setDate', moment().toDate());
            }, 50);
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                gender: '',
                name: '',
            };
            $('#filterIncomingGender').val('').change();
            $('#filterIncomingEducationGradeId').val('').change();
            this.loadLearners(1, false);
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.learner_admissions = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        academicYears: function () {
            let years = [];

            for (let i = Number(moment().format("YYYY"))-1; i >= 2003; i--) {
                years.push(i);
            }

            return years;
        },
        getPaginationLinks: function () {
            let arr = this.learner_admissions.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    },
}
</script>

<style scoped>

</style>
