<template>
    <div>
        <div class="card-inner-group px-3">
            <div class="card-inner p-0">
                <div v-if="learner.learner_disabilities.length" class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text ucap text-white">Special Need</span></div>
                    </div><!-- .nk-tb-item -->
                    <div v-for="disability in learner.learner_disabilities" class="nk-tb-item">
                        <div class="nk-tb-col text-dark">
                            <span>{{ disability.disability_type.name.toUpperCase() }}</span>
                        </div>
                    </div><!-- .nk-tb-item -->
                </div><!-- .nk-tb-list -->
                <div v-else class="py-3">
                    <div class="alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em> <strong>{{ learner.person.full_name }}</strong> has no special needs.
                    </div>
                </div>
            </div><!-- .card-inner -->
        </div><!-- .card-inner-group -->

    </div>
</template>

<script>
export default {
    name: "Disabilities",
    props: ['learnerObj'],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            learner: {
                id:'',
                person: {
                    full_name:'',
                },
                learner_disabilities:[]
            },
        }
    },
    methods: {

        initPlugins: function () {
            let self = this;
            this.learner = this.learnerObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
