<template>
    <div>
        <div class="modal fade zoom" tabindex="-1" id="missingClassLearnerModal"  data-bs-backdrop="static" data-bs-keyboard="false"  data-backdrop="static" aria-labelledby="staticBackdropLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Update Learners Missing Classes</h5>


                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>
                        <div class="example-alert">
                            <div class="alert alert-warning alert-icon">
                                <em class="icon ni ni-alert-circle"></em>Note: Select 10 learners first and click submit changes to continue.
                            </div>
                        </div>

                        <form @submit.prevent="submitData">
                            <div  style="overflow: auto;max-height: 55vh;">
                            <table class="table border-secondary">
                                <thead>
                                    <tr class="bg-gray-100">
                                    <th>NAMES</th>
                                    <th>LIN</th>
                                    <th>SEX</th>
                                    <!-- <th>2022</th> -->
                                    <!-- <th>2023</th> -->
                                    <th>2024</th>
                                    <th>Reporting Status</th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="clearners in school_classless_learner" :key="clearners.person_id">
                                    <td :style="removedLearners.includes(clearners.person_id)?'text-decoration: line-through;':''">
                                        <div><a :style="removedLearners.includes(clearners.person_id)?'color: #aaa;':''"  target="_blank" :href="'/institution/learners/profile/' + clearners.learner.encrypted_lin">{{ clearners.learner.person.full_name }} <em class="icon ni ni-arrow-up-right"></em></a></div>
                                        <!-- <span class="text-gray text-sm">{{  formatBirthDate(clearners.learner.person.birth_date)}}</span> -->
                                        <!-- <span class="text-gray text-sm"><a href=''>{{ clearners.learner.lin }}</a></span> -->
                                    </td>
                                    <td :style="removedLearners.includes(clearners.person_id)?'text-decoration: line-through;':''">
                                        <div :style="removedLearners.includes(clearners.person_id)?'color: #aaa;':''">{{ clearners.learner.lin }}</div>
                                        <!-- <span class="text-gray text-sm">{{  formatBirthDate(clearners.learner.person.birth_date)}}</span> -->
                                        <!-- <span class="text-gray text-sm"><a href=''>{{ clearners.learner.lin }}</a></span> -->
                                    </td>
                                    <td :style="removedLearners.includes(clearners.person_id)?'text-decoration: line-through;':''">
                                        <span :style="removedLearners.includes(clearners.person_id)?'color: #aaa;':''" class="text-gray text-sm">{{  clearners.learner.person.gender}}</span>
                                    </td>
                                    <!-- <td>
                                        <div class="custom-select2">
                                            <select @change="selectClass($event,clearners.person_id,'2022')" :name="'learner_selected_class_2022['+clearners.person_id+']'" class="form-select-sm" :requiredx="!removedLearners.includes(clearners.person_id)" :disabled="removedLearners.includes(clearners.person_id)  || loading">
                                                <option :selected="selectedClass['2022'][clearners.person_id] == null"></option>
                                                <option value="0" :selected="selectedClass['2022'][clearners.person_id] == '0'">N/A</option>
                                                <option v-for="l_class in school.school_type.classes" :key="l_class.id" :value="l_class.id" :selected="selectedClass['2022'][clearners.person_id] == l_class.id">{{ l_class.name }}</option>
                                            </select>
                                        </div>
                                    </td> -->
                                    <!-- <td>
                                        <div class="custom-select2">
                                            <select @change="selectClass($event,clearners.person_id,'2023')" :name="'learner_selected_class_2023['+clearners.person_id+']'" class="form-select-sm" :requiredx="!removedLearners.includes(clearners.person_id)" :disabled="removedLearners.includes(clearners.person_id) || loading || notValidSelection(clearners.person_id)">
                                                <option v-if="selectedClass['2022'][clearners.person_id] != '0'" value="0" :selected="selectedClass['2023'][clearners.person_id] == '0'">N/A</option>
                                                <option v-for="l_class in filtered_classes(clearners.person_id)" :key="l_class.id" :value="l_class.id" :selected="selectedClass['2023'][clearners.person_id] == l_class.id">{{ l_class.name }}</option>
                                            </select>
                                        </div>
                                    </td> -->
                                    <td>
                                        <div class="custom-select2">
                                            <select @change="selectClass($event, clearners.person_id, '2024')" :name="'learner_selected_class_2024[' + clearners.person_id + ']'" class="form-select-sm" :required="!removedLearners.includes(clearners.person_id) && (selectedStatus[clearners.person_id] || selectedStatus[clearners.person_id] === 0)" :disabled="shouldDisableSelect(clearners.person_id) || removedLearners.includes(clearners.person_id) || loading">
                                                <option :selected="selectedClass['2024'][clearners.person_id] == null"></option>
                                                <option value="0" :selected="selectedClass['2024'][clearners.person_id] == '0'">N/A</option>
                                                <option v-for="l_class in school.school_type.classes" :key="l_class.id" :value="l_class.id" :selected="selectedClass['2024'][clearners.person_id] == l_class.id">{{ l_class.name }}</option>
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                            <select id="reportingStatusId" @change="selectStatus($event,clearners.person_id)" :name="'reportingStatus['+clearners.person_id+']'" class="form-select-sm" :required="!removedLearners.includes(clearners.person_id) && (selectedClass['2024'][clearners.person_id] || selectedClass['2024'][clearners.person_id] === 0)" :disabled="shouldDisableSelect(clearners.person_id) || removedLearners.includes(clearners.person_id) || loading">
                                                <option :selected="selectedStatus[clearners.person_id] == null"></option>
                                                <option value="1">STILL IN SCHOOL</option>
                                                <option value="2">LEFT SCHOOL</option>
                                            </select>
                                        </div>
                                    </td>
                                    <!-- <td>
                                        <div class="custom-select2">
                                            <select @change="selectClass($event,clearners.person_id,'2024')" :name="'learner_selected_class_2024['+clearners.person_id+']'" class="form-select-sm" :requiredx="!removedLearners.includes(clearners.person_id)" :disabled="shouldDisableSelect(clearners.person_id) || removedLearners.includes(clearners.person_id)  || loading">
                                                <option :selected="selectedClass['2024'][clearners.person_id] == null"></option>
                                                <option value="0" :selected="selectedClass['2024'][clearners.person_id] == '0'">N/A</option>
                                                <option v-for="l_class in school.school_type.classes" :key="l_class.id" :value="l_class.id" :selected="selectedClass['2024'][clearners.person_id] == l_class.id">{{ l_class.name }}</option>
                                            </select>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                            <select id="reportingStatusId" @change="selectStatus($event,clearners.person_id)" :name="'reportingStatus['+clearners.person_id+']'" class="form-select-sm" :requiredx="!removedLearners.includes(clearners.person_id)" :disabled="shouldDisableSelect(clearners.person_id) || removedLearners.includes(clearners.person_id)  || loading">
                                                <option :selected="selectedStatus[clearners.person_id] == null"></option>
                                                <option value="1">STILL IN SCHOOL</option>
                                                <option value="2">LEFT SCHOOL</option>
                                            </select>
                                        </div>
                                    </td> -->
                                    <td nowrap v-if="canDelete(clearners.person_id)">
                                        <a v-if="!shouldDisableSelect(clearners.person_id) && !removedLearners.includes(clearners.person_id)" href="#" @click.prevent="removeLearner(clearners.person_id)"><em class="ni ni-trash-alt ml-1 text-red"></em> <span  class="text-sm">Delete</span></a>
                                        <a v-if="removedLearners.includes(clearners.person_id)" href="#" @click.prevent="undoRemoveLearner(clearners.person_id)"><em class="ni ni-undo ml-1"></em> Undo</a>
                                    </td>
                                    <td v-else>
                                        <a href="#" @click.prevent="resetLearner(clearners.person_id)"><em class="ni text-green ni-cross-circle ml-1"></em> <span  class="text-sm">Reset</span></a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row ml-2 mt-4 d-flex-row justify-content-between">

                    <div class="pt-2 ml-1">
                       <span v-if="school_classless_learner.length">{{ school_classless_learner.length - removedLearners.length }} learners </span>
                       <span v-if="school_classless_learner.length && removedLearners.length"> | </span>
                        <span v-if="removedLearners.length"><a class="pa-1" href="" @click.prevent="removedLearners.splice(0)">{{ removedLearners.length }} deleted. Undo all</a></span>
                    </div>
                    <div class="mr-2">
                        <button type="submit" class="btn bg-dark-teal d-flex" :disabled="loading || noDataChange">
                            <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span v-if="loading" class="align-self-center">Submitting, Please wait...</span>
                            <span v-if="loading" class="sr-only">Submitting, Please wait...</span>
                            <span v-if="!loading" class="align-self-center">Submit Changes</span>
                            <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>

                        </button>
                    </div>

                     </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";



export default {
    name: "MissingClassLearnersForm",
    props: ["mainSchoolObj"],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            selectedClass:{
                //'2022':{},
                //'2023':{},
                '2024':{},

            },
            removedLearners:[],
            learner_selected_class:[],
            school: {
                classless_learners:[],
                school_type:{
                    classes:[]
                },
            },
            // clearners:{person_id:''},
            api_url: '/institutions/learners/save-classless-learners',
            loading: false,
            filtering: false,
            loading_message: '',
            selectedStatus: {},
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.mainSchoolObj;

            $('#missingClassLearnerModal').modal({
                backdrop: 'static',
                keyboard: false
            });

        },

        notValidSelection: function(learner){
            if(this.selectedClass['2022'][learner]){
                return false;//is valid
            }
            return true;
        },
        formatBirthDate: function(date){
            return moment(date).format("D MMMM, YYYY");
        },
        selectClass: function(evt, learner, year){
            if(evt.target.value==''){
                this.$delete(this.selectedClass[year], learner);
            } else {
                // this.selectedClass[year][learner] = evt.target.value;
                // this.selectedClass[year] = Object.assign({}, this.selectedClass[year], {learner: evt.target.value })
                // this.someObject = Object.assign({}, this.someObject, { a: 1, b: 2 })
                this.$set(this.selectedClass[year], learner, evt.target.value);
            }
        },
        selectStatus: function(evt, learner){
            if(evt.target.value==''){
                this.$delete(this.selectedStatus, learner);
            } else {
                // this.selectedClass[year][learner] = evt.target.value;
                // this.selectedClass[year] = Object.assign({}, this.selectedClass[year], {learner: evt.target.value })
                // this.someObject = Object.assign({}, this.someObject, { a: 1, b: 2 })
                this.$set(this.selectedStatus, learner, evt.target.value);
            }
        },
        undoRemoveLearner:function(learner){
            this.removedLearners.splice(this.removedLearners.indexOf(learner), 1);

        },
        removeLearner:function(learner){
            if(this.removedLearners.indexOf(learner)==-1){
                this.removedLearners.push(learner);
                //this.$delete(this.selectedClass['2022'], learner);
                //this.$delete(this.selectedClass['2023'], learner);
                this.$delete(this.selectedClass['2024'], learner);
                this.$delete(this.selectedStatus, learner);
            }
        },
        submitData: function(submitEvent){
            let self = this;
            this.$refs.notify.messages.splice(0);

            this.loading = true;
            axios.post(self.api_url, [self.selectedClass, self.removedLearners, self.selectedStatus])
                .then(response => {
                    this.loading = false;
                    if(this.school.classless_learners.length == response.data.classless_learners.length){
                        return;
                    }
                    this.school = response.data;
                    this.removedLearners.splice(0);
                    this.selectedClass = {'2022':{}, '2023':{}, '2024':{}};//.splice(0);
                    this.selectedStatus = {};
                    if(this.school.classless_learners.length == 0){
                        $('#missingClassLearnerModal').modal('hide');
                    }
                    setTimeout(()=>{
                        this.$refs.notify.messages.push({
                        status: "success",
                        title: "Success",
                        message: "information updated",
                    });
                    }, 500)
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });


        },

        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },

        canDelete:function(learner){
            //if(this.selectedClass['2022'][learner])return false;
            //if(this.selectedClass['2023'][learner])return false;
            if(this.selectedClass['2024'][learner])return false;
            if(this.selectedStatus[learner])return false;
            return true;
        },
        resetLearner:function(learner){
            //this.$delete(this.selectedClass['2022'], learner);
            //this.$delete(this.selectedClass['2023'], learner);
            this.$delete(this.selectedClass['2024'], learner);
            this.$delete(this.selectedStatus, learner);

        },
        filtered_classes:function(learner){
            return this.school.school_type.classes.filter((v)=>{
                // console.log(v.id, learner);
                // console.log(this.selectedClass['2022'][learner])]
                let _2022class = Number.parseInt(this.selectedClass['2022'][learner]);
                if(_2022class>=0){
                    // console.log(v.id, learner);
                    // console.log(this.selectedClass['2022'][learner], this.selectedClass['2022'][learner] + 1 )
                    if((_2022class + 1 ) == v.id ) return true;
                    if((_2022class - 1 ) == v.id ) return true;
                    if(_2022class == v.id ) return true;
                    if(_2022class == 0 ) return true;
                    // if((this.selectedClass['2022'][learner] - 1 ) == v.id ) return true;
                    // if((this.selectedClass['2022'][learner] ) == v.id ) return true;
                    // console.log('fail');
                }
                return false;
            });
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                //this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        noDataChange:function(){
            return this.removedLearners.length == 0 && JSON.stringify(this.selectedClass['2024']) == '{}' && JSON.stringify(this.selectedStatus) == '{}';
            //return this.removedLearners.length == 0 && JSON.stringify(this.selectedClass['2022']) == '{}' && JSON.stringify(this.selectedClass['2023']) == '{}' && JSON.stringify(this.selectedClass['2024']) == '{}';
        },
        shouldDisableSelect() {
            return (personId) => {
                // Count the number of selected learner records
                const selectedCount = Object.values(this.selectedClass['2024']).filter(id => id !== null && id !== '0').length;
                // Disable select if the selected count is 20 or more
                return selectedCount >= 10 && !this.selectedClass['2024'][personId];
            };
        },
        school_classless_learner:function(){
            return this.school.classless_learners;
        }
    },
}
</script>

<style scoped>
    th{
        padding: 8px 16px!important;
        font-size: 13px!important;
        text-transform: uppercase;
    }
    .table td:first-child, .table th:first-child{
        padding-left: 4px!important;
    }
    #missingClassLearnerModal .modal-content .close{
        top: 26px!important;
        right: 16px!important;
    }
</style>
