<template>
    <div>
        <div class="card-inner-group px-3">
            <div class="card-inner p-0">
                <div v-if="learner.learner_talents.length" class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text ucap text-white">Talents</span></div>
                        <!--						<div class="nk-tb-col tb-col-md text-center"><span class="sub-text ucap text-white">Actions</span></div>-->
                    </div><!-- .nk-tb-item -->
                    <div v-for="learner in learner.learner_talents" class="nk-tb-item">
                        <div class="nk-tb-col text-dark">
                            <span>{{ learner.talent.name.toUpperCase() }}</span>
                        </div>
                    </div><!-- .nk-tb-item -->
                </div><!-- .nk-tb-list -->
                <div v-else class="py-3">
                    <div class="alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em> <strong>{{ learner.person.full_name }}</strong> has no talents.
                    </div>
                </div>
            </div><!-- .card-inner -->
        </div><!-- .card-inner-group -->
    </div>
</template>

<script>
export default {
    name: "Talents",
    props: ['learnerObj'],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            learner: {
                id:'',
                person: {
                    full_name:'',
                },
                learner_talents:[]
            },

        }
    },
    methods: {

        initPlugins: function () {
            let self = this;
            this.learner = this.learnerObj;
        },
    },
    computed: {

    }
}
</script>

<style scoped>

</style>
