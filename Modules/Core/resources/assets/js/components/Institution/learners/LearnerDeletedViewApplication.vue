<template>
    <div class="w-100">
        <div class="nk-block-head-sub">
            <a class="back-to" :href="'/institution/data-update/flagged-learners-for-deleting'"><em class="icon ni ni-arrow-left"></em><span>Back</span></a>
        </div>
        <nav class="nk-block-des mb-1">
            <ul class="breadcrumb breadcrumb-arrow">
                <li class="breadcrumb-item">
                    <a href="/institution/dashboard" class="text-primary">Dashboard</a>
                </li>
                <li class="breadcrumb-item">
                    <a :href="'/institution/data-update/flagged-learners-for-deleting'" class="text-primary">
                         Flagged Learners
                    </a>
                </li>
                <li class="breadcrumb-item active text-soft">
                    Application Details
                </li>
            </ul>
        </nav>
        <div class="nk-block">
            <div class="card card-stretch">
                <notifications ref="notify"></notifications>
                <div class="card-inner-group">
                    <div class="card-body card-bordered border-dark-teal">
                        <div class="card-inner card-inner-xl" style="padding: 3%">
                            <div class="entry">
                                <h4>Learner Flagged Application</h4>
                                <p>Below are the application details</p>
                                <!-- <h6 class="style-head"><span class="text-gray mr-2">Application ID:</span>#{{ application.reference_number }}</h6> -->
                                <h6 class="style-head"><span class="text-gray mr-2">Date Flagged:</span>{{ formatApplicationDate(application.date_created) }}</h6>
                                <h6 class="style-head"><span class="text-gray mr-2">Date Approved:</span>
                                    <span v-if="application.date_approved !== null">{{ formatApplicationDate(application.date_approved) }}</span>
                                    <span v-else class="text-muted font-italic">Not SET</span>
                                </h6>
                                <h6 class="style-head">
                                    <span class="text-gray mr-2">Approved By:</span>
                                    <span v-if="application.user !== undefined && application.approval_status !== 'PENDING' && application.date_approved !== null">{{ application.user.full_name }} | ESO</span>
                                    <span v-else class="text-muted font-italic">Not SET</span>
                                </h6>
                                <h6 class="style-head"><span class="text-gray mr-2">Approval Status:</span>
                                    <span v-if="application.approval_status === 'PENDING'" class="text-uppercase badge badge-primary">Pending</span>
                                    <span v-if="application.approval_status === 'APPROVED'" class="text-uppercase badge badge-dark-teal">Approved</span>
                                    <span v-if="application.approval_status === 'REJECTED'" class="text-uppercase badge badge-red">Rejected</span>
                                </h6>
                                
                                <div class="nk-divider divider md border-dark-teal"></div>
                                <div class="preview-block">
                                    <div class="row gy-4">
                                        <div class="col-sm-6">
                                            <span class="preview-title-lg overline-title">Learner Details</span>
                                            <div v-if="application.learner !== null && application.learner !== undefined" class="row mt-1">
                                                <div class="col-lg-4"></div>
                                                <div class="col-lg-8">
                                                    <div class="d-flex justify-content-start">
                                                        <div class="user-avatar lg sq bg-white justify-content-start">
                                                            <img class="py-3 rounded-0" :src="application.learner.person.photo_url" :alt="application.learner.person.full_name">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-4">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">NAMES:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.learner !== null && application.learner !== undefined" class="style-head ucap">{{ application.learner.person.full_name }}</h6>
                                                </div>
                                            </div>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">LIN:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.learner !== null && application.learner !== undefined" class="style-head ucap">{{ application.learner.lin }}</h6>
                                                </div>
                                            </div>

                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">SEX:</span></h6>
                                                </div>
                                                <div v-if="application.learner !== null && application.learner !== undefined" class="col-lg-8">
                                                    <h6 v-if="application.learner.person.gender === 'M'">MALE</h6>
                                                    <h6 v-if="application.learner.person.gender === 'F'">FEMALE</h6>
                                                </div>
                                            </div>

                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">DATE OF BIRTH:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.learner !== null && application.learner !== undefined">{{ formatLearnerDate(application.learner.person.birth_date) }}</h6>
                                                </div>
                                            </div>
                                        
                                        </div>


                                        <div class="col-sm-6">
                                            <span class="preview-title-lg overline-title">WHY THIS LEANER NEEDS TO BE DELETED?</span>
                                            <div class="row">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Reason:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.reason !== undefined" class="style-head">{{ application.reason.name }}</h6>
                                                </div>
                                            </div>

                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Description:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head">{{ application.description }}</h6>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <!-- <div v-if="schools.length && application.status !== 1" class="nk-divider divider md"></div>
                                <div v-if="schools.length && application.status !== 1" class="mt-2">
                                    <span class="preview-title-lg overline-title">Institutions with SIMILAR names</span>
                                    <div class="table-responsive">
                                        <table class="nk-tb-list nk-tb-ulist is-compact">
                                            <tr class="nk-tb-item nk-tb-head bg-secondary">
                                                <th class="nk-tb-col text-white">INSTITUTION</th>
                                                <th class="nk-tb-col text-white">EMIS NUMBER</th>
                                                <th class="nk-tb-col text-white">OWNERSHIP</th>
                                                <th class="nk-tb-col text-white">PHONE</th>
                                                <th class="nk-tb-col text-white text-center">ACTIONS</th>
                                            </tr>
                                            <tr v-for="school in schools" :key="school.id" class="nk-tb-item">
                                                <td class="nk-tb-col text-dark">
                                                    {{school.name}}
                                                </td>
                                                <td class="nk-tb-col text-dark">
                                                    {{school.emis_number}}
                                                </td>
                                                <td class="nk-tb-col text-dark">
                                                    {{school.ownership_status.name}}
                                                </td>
                                                <td class="nk-tb-col text-dark">
                                                    {{ school.phone === null ? '' : '+' + school.phone }}
                                                </td>
                                                <td class="nk-tb-col text-dark text-center">
                                                    <a :href="'/admin/manage-institutions/view-'+application.institution_level.name+'-institution/'+school.emis_number" target="_blank" class="badge badge-dark-teal cursor">
                                                        VIEW
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <div v-if="permission === 'recommend' && application.deo_date_approved === null" class="form-group mt-5">
                                    <p class="lead">By Clicking Recommend, I hereby confirm that I have verified the institution details and I take full responsibility of this action.</p>
                                </div>
                                <div v-if="permission === 'endorse' && application.moes_desk_officer_date_approved === null && applicationStatus === 'Pending'" class="form-group mt-5">
                                    <p class="lead">By Clicking Endorse, I hereby confirm that I have verified the institution details and I take full responsibility of this action.</p>
                                </div>
                                <div v-if="permission === 'approve' && application.moes_staff_date_approved === null && applicationStatus === 'Endorsed'" class="form-group mt-5">
                                    <p class="lead">By Clicking Approve, I hereby confirm that I have verified the institution details and I take full responsibility of this action.</p>
                                </div>

                                <span @click="recommendApplication()" v-if="permission === 'recommend' && application.deo_date_approved === null" class="btn bg-dark-teal btn-lg mr-2">
                                    <em class="icon ni ni-check-circle-cut mr-1"></em>RECOMMEND
                                </span>
                                <span @click="endorseApplication()" v-if="permission === 'endorse' && application.moes_desk_officer_date_approved === null && applicationStatus === 'Pending'" class="btn bg-dark-teal btn-lg mr-2">
                                    <em class="icon ni ni-check-circle-cut mr-1"></em>ENDORSE
                                </span>
                                <span @click="approveApplication()" v-if="permission === 'approve' && application.moes_staff_date_approved === null && applicationStatus === 'Endorsed'" class="btn bg-dark-teal btn-lg mr-2">
                                    <em class="icon ni ni-check-circle-cut mr-1"></em>APPROVE
                                </span>


                                <span @click="rejectApplication()" v-if="permission === 'recommend' && application.deo_date_approved === null" class="btn btn-danger btn-lg">
                                    <em class="icon ni ni-cross-circle mr-1"></em>REJECT
                                </span>
                                <span @click="rejectApplication()" v-if="permission === 'endorse' && application.moes_desk_officer_date_approved === null && applicationStatus === 'Pending'" class="btn btn-danger btn-lg">
                                    <em class="icon ni ni-cross-circle mr-1"></em>REJECT
                                </span>
                                <span @click="rejectApplication()" v-if="permission === 'approve' && application.moes_staff_date_approved === null && applicationStatus === 'Endorsed'" class="btn btn-danger btn-lg">
                                    <em class="icon ni ni-cross-circle mr-1"></em>REJECT
                                </span>
                            </div>-->
                            <div v-if="application.approval_status === 'REJECTED'" class="nk-divider divider md"></div> 

                            <div v-if="application.approval_status === 'REJECTED' && application.approved_by !== null && application.reject_reason !== null" class="nk-block">
                                <div class="nk-block-head nk-block-head-sm nk-block-between">
                                    <h6 class="title">Reason To Why This Delete Request Was Rejected.</h6>
                                </div><!-- .nk-block-head -->
                                <div class="bq-note">
                                    <div class="bq-note-item">
                                        <div class="bq-note-text">
                                            <p>{{ application.reject_reason }}</p>
                                        </div>
                                    </div><!-- .bq-note-item -->
                                </div><!-- .bq-note -->
                            </div>

                        </div><!-- .card-inner -->
                    </div>
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->

        <!-- Rejection Reason Modal -->
        <div class="modal fade zoom" tabindex="-1" id="rejectionReasonModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a @click="resetApplication()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form @submit.prevent="commitRejectReason()">
                        <div class="modal-header">
                            <h5 class="modal-title">Reject EMIS Number Application</h5>
                        </div>
                        <div class="modal-body">
                            <notifications ref="modalNotify"></notifications>
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-group">
                                            <label class="form-label">Reject Reason <span class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <textarea v-model="reason" class="form-control no-resize" id="default-textarea"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- .col -->
                            </div><!-- .row -->
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetApplication()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>CANCEL</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Rejecting Application...</span>
                                <span v-if="loading" class="sr-only">Rejecting Application...</span>
                                <span v-if="!loading" class="align-self-center">REJECT</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- /Rejection Reason Modal -->
    </div>
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
export default {
    name: "LearnerDeletedViewApplication",
    props: ['applicationObj', 'schoolsObj', 'permission'],
    components: {Notifications},
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            api_url: '/admin/emis-number-applications',
            loading: false,
            reasoning: false,
            application: {
                reference_number: '',
                school_name: '',
                emis_number: '',
                phone: '',
                email: '',
                masked_contact_person_nin: '',
                contact_person_phone: '',
                contact_person_email: '',
                year_founded: '',
                status: 0,
                deo_date_approved: null,
                has_male_students: false,
                has_female_students: false,
                admits_day_scholars_yn: false,
                admits_boarders_yn: false,
                institution_level: {name: '',display_name: ''},
                diploma_awarding_school_type: {name: ''},
                certificate_awarding_school_type: {name: ''},
                region: {name: ''},
                district: {name: ''},
                county: {name: ''},
                local_government: {name: ''},
                sub_county: {name: ''},
                parish: {name: ''},
                school_ownership_status: {name: ''},
                founding_body: {name: ''},
                contact_person: {
                    gender: 'M'
                },
                gender: 'M',
                deo: {
                    full_name: '',
                    photo_url: '',
                },
                deo_reject_reason: '',
                moes_staff: {
                    full_name: '',
                    photo_url: '',
                },
                moes_staff_reject_reason: '',
                moes_staff_date_approved: null,
                moes_desk_officer: {
                    full_name: '',
                    photo_url: '',
                },
                moes_desk_officer_reject_reason: '',
                moes_desk_officer_date_approved: null,
            },
            reason: '',
            schools: [],
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.application = this.applicationObj;
            this.schools = this.schoolsObj;
        },
        formatApplicationDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY hh:mma");
            // if (application.date_approved !== null & application.date_created !== null) {
            //     return moment(application.date_approved).format("D MMMM, YYYY hh:mma");
            // }
            // if (application.date_created !== null && application.date_approved === null) {
            //     return moment(application.date_created).format("D MMMM, YYYY hh:mma");
            // }
        },
        formatLearnerDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        downloadLetter: function () {
            this.loading = true;
            try {
                axios({
                    method: 'get',
                    url: '/emis-number-certificates/' + this.application.emis_number,
                    responseType: 'arraybuffer'
                })
                    .then(response=>{
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        console.log(blob);
                        let a = window.document.createElement('a');
                        a.href = window.URL.createObjectURL(blob, {
                            type: 'data:application/pdf'
                        })
                        a.download = this.application.emis_number+'-emis-number-certificate.pdf';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(a.href);
                        document.body.removeChild(a);
                    })
                    .catch(error => {
                        // Handle error
                        this.renderError(error)
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            } catch (error) {
                // Handle error
                this.renderError(error)
            }
        },
        // downloadLetter: function () {
        //     axios({
        //         method: 'get',
        //         url: '/emis-number-certificates/'+this.application.emis_number.toLowerCase()+'-emis-number-certificate.pdf?x='+moment().unix(),
        //         responseType: 'arraybuffer'
        //     })
        //         .then(response=>{
        //             let blob = new Blob([response.data], { type: 'application/pdf' });
        //             let a = window.document.createElement('a');
        //             a.href = window.URL.createObjectURL(blob, {
        //                 type: 'data:application/pdf'
        //             })
        //             a.download = this.application.emis_number.toLowerCase()+'-emis-number-certificate.pdf';
        //             document.body.appendChild(a);
        //             a.click();
        //             window.URL.revokeObjectURL(a.href);
        //             document.body.removeChild(a);
        //         })
        //         .catch(error=>{
        //             // this.renderError(error)
        //         });
        // },
        recommendApplication: function() {
            let self = this;
            if (!this.loading) {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You are about to recommend this EMIS Number application "+self.application.reference_number,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'YES, RECOMMEND!'
                }).then(function (result) {
                    if (result.value) {
                        self.loading = true;
                        axios.post(self.api_url+'/'+self.application.school_type_id+"/recommend/"+self.application.reference_number)
                            .then(response=>{
                                Swal.fire('RECOMMENDED!', 'This Application has been recommended for Approval.', 'success');
                                self.loading = false;
                                self.application = response.data;
                            })
                            .catch(errors=>{
                                self.loading = false;
                                self.renderError(errors);
                            })
                    }
                });
            }
        },
        approveApplication: function() {
            let self = this;
            if (!this.loading) {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You are about to approve this EMIS Number application "+self.application.reference_number,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'YES, APPROVE!'
                }).then(function (result) {
                    if (result.value) {
                        self.loading = true;
                        axios.post(self.api_url+'/'+self.application.reference_number.toLowerCase().trim()+"/approve")
                            .then(response=>{
                                Swal.fire('APPROVED!', 'This Application has been approved for a new EMIS Number.', 'success');
                                self.loading = false;
                                self.application = response.data;
                                $("html, body").animate({ scrollTop: 0 }, "slow");
                            })
                            .catch(errors=>{
                                self.loading = false;
                                self.renderError(errors);
                            })
                    }
                });
            }
        },
        endorseApplication: function() {
            let self = this;
            if (!this.loading) {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You are about to endorse this EMIS Number application "+self.application.reference_number,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'YES, ENDORSE!'
                }).then(function (result) {
                    if (result.value) {
                        self.loading = true;
                        axios.post(self.api_url+'/'+self.application.reference_number.toLowerCase().trim()+"/endorse")
                            .then(response=>{
                                Swal.fire('ENDORSED!', 'This Application has been endorsed for a new EMIS Number.', 'success');
                                self.loading = false;
                                self.application = response.data;
                                $("html, body").animate({ scrollTop: 0 }, "slow");
                            })
                            .catch(errors=>{
                                self.loading = false;
                                self.renderError(errors);
                            })
                    }
                });
            }
        },
        commitRejectReason: function () {
            this.loading = true;
            axios.post(this.api_url+'/'+this.application.reference_number.toLowerCase().trim()+"/reject", {reason: this.reason})
                .then(response=>{
                    Swal.fire('REJECTED!', 'This Application has been rejected successfully.', 'success');
                    this.loading = false;
                    this.application = response.data;
                    this.resetApplication();
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                })
                .catch(errors=>{
                    this.loading = false;
                    this.renderError(errors);
                });
        },
        rejectApplication: function () {
            this.reasoning = true;
            $('#rejectionReasonModal').modal({backdrop: "static"});
        },
        resetApplication: function () {
            $('#rejectionReasonModal').modal('hide');
            this.reasoning = false;
            this.reason = '';
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                if (this.reasoning) {
                    this.$refs.modalNotify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                } else {
                    this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                }
            } else if (error.response && error.response.status === 401) {
                if (this.reasoning) {
                    this.$refs.modalNotify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                } else {
                    this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                }
            } else if (error.response && error.response.status === 404) {
                if (this.reasoning) {
                    this.$refs.modalNotify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                } else {
                    this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                }
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                if (this.reasoning) {
                    this.$refs.modalNotify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                } else {
                    this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                }
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            if (this.reasoning) {
                this.$refs.modalNotify.messages.push({status: 'error', title: 'Data Error!', message: text});
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        
    }
}
</script>

<style scoped>

</style>
