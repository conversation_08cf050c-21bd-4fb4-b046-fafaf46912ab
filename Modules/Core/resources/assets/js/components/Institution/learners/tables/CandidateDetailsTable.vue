<template>
    <table v-if="candidates.length > 0" class="table border border-dark-teal">
        <thead class="bg-secondary">
        <tr>
            <th class="align-middle text-white border-secondary">Level</th>
            <th class="align-middle border-left text-white border-secondary" colspan="2">Index Number</th>
            <th class="align-middle border-left text-white border-secondary">Exam Year</th>
            <th class="align-middle border-left text-white border-secondary" colspan="2">Full Name</th>
            <th class="align-middle border-left text-white border-secondary">Age</th>
            <th class="align-middle border-left text-white border-secondary" colspan="2">Sex</th>
        </tr>
        </thead>
        <tbody class="border-top-0 border-dark-teal">
        <tr v-for="candidate in candidates" :key="candidate.index_number">
            <td class="align-middle text-dark border-secondary bg-secondary-dim">{{ candidate.exam_level.name }}</td>
            <td class="align-middle border-left text-dark border-secondary bg-secondary-dim" colspan="2">{{ candidate.index_number }}</td>
            <td class="align-middle border-left text-dark border-secondary bg-secondary-dim">{{ candidate.exam_year }}</td>
            <td class="align-middle border-left text-dark border-secondary bg-secondary-dim" colspan="2">{{ candidate.name }}</td>
            <td class="align-middle border-left text-dark border-secondary bg-secondary-dim">
                <span v-if="candidate.age !== null">{{ candidate.age }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
            <td class="align-middle border-left text-dark border-secondary bg-secondary-dim" colspan="2">
                <span v-if="candidate.gender !== null">{{ candidate.gender }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        </tbody>
    </table>
</template>

<script>
export default {
    name: "CandidateDetailsTable",
    props: {
        candidates: {
            type: Array,
            required: true
        }
    }
};
</script>

<style scoped>
</style>
