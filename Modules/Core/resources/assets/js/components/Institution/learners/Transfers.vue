<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Learner Transfers</h3>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <!--								<li>-->
                                <!--									<a class="cursor btn btn-secondary text-white">-->
                                <!--										<em class="icon ni ni-download-cloud"></em><span>Export</span>-->
                                <!--									</a>-->
                                <!--								</li>-->
                                <li>
                                    <a @click="newTransferRequest()" class="cursor btn bg-dark-teal">
                                        <span class="text-white">Transfer Learner</span><em class="icon ni ni-plus text-white"></em>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <ul class="nav nav-tabs">
                        <li class="nav-item px-2">
                            <a id="IncomingTransfersTabHandle" class="nav-link active" data-toggle="tab" href="#IncomingTransfersTab">
                                <span>Incoming Learner Transfers</span>
                            </a>
                        </li>
                        <li class="nav-item px-2">
                            <a class="nav-link" data-toggle="tab" href="#OutgoingTransfersTab">
                                <span>Outgoing Learner Transfers</span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active" id="IncomingTransfersTab">
                            <incoming-learner-transfers
                                :education-grades-obj="educationGradesObj"
                                :learner-transfer-reasons-obj="learnerTransferReasonsObj"
                                :countries-obj="countriesObj"
                                :incoming-transfers-obj="incoming_transfers"
                                :school-type-id-obj="schoolTypeIdObj"
                                :curriculums-obj="curriculumsObj"
                            ></incoming-learner-transfers>
                        </div>
                        <div class="tab-pane" id="OutgoingTransfersTab">
                            <outgoing-learner-transfers
                                :education-grades-obj="educationGradesObj"
                                :learner-transfer-reasons-obj="learnerTransferReasonsObj"
                                :outgoing-transfers-obj="outgoing_transfers"
                                :school-type-id-obj="schoolTypeIdObj"
                                :curriculums-obj="curriculumsObj"
                            ></outgoing-learner-transfers>
                        </div>
                    </div>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>

</template>

<script>
export default {
    name: "LearnerTransfers",
    props: [
        "countriesObj",
        "educationGradesObj",
        "learnerTransferReasonsObj",
        "incomingTransfersObj",
        "outgoingTransfersObj",
        "curriculumsObj",
        "schoolTypeIdObj"
    ],
    data: function () {
        return {
            incoming_transfers: {
                data: [],
                links: [],
                total: 0,
            },
            outgoing_transfers: {
                data: [],
                links: [],
                total: 0,
            },
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.incoming_transfers = this.incomingTransfersObj;
            this.outgoing_transfers = this.outgoingTransfersObj;
        },
        newTransferRequest: function () {
            $('#IncomingTransfersTabHandle').click();
            $('#incomingTransfersModal').modal({backdrop: "static"});
        }
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
