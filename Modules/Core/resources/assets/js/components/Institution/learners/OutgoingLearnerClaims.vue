<template>
    <div class="card-inner-group">
        <div class="card-inner position-relative card-tools-toggle">
            <div class="card-title-group">
                <div class="card-tools">
                    <form @submit.prevent="loadClaims(1, true)">
                        <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                            <div style="width: 200px !important" class="form-wrap">
                                <select id="filterOutgoingEducationGradeId" class="form-select-sm">
                                    <option value="">ALL CLASSES</option>
                                    <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                </select>
                            </div>
                            <div style="width: 200px !important" class="form-wrap">
                                <select id="filterOutgoingGender" class="form-select-sm">
                                    <option value="">ALL GENDERS</option>
                                    <option value="M">MALE</option>
                                    <option value="F">FEMALE</option>
                                </select>
                            </div>
                            <div class="form-wrap">
                                <div class="input-group">
                                    <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                    <div class="input-group-append">
                                        <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                            <em class="icon ni ni-cross"></em>
                                        </button>
                                        <button class="btn rounded-right bg-dark-teal" type="submit">
                                            <em class="icon ni ni-filter mr-1"></em>Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div><!-- .card-tools -->
            </div><!-- .card-title-group -->
        </div><!-- .card-inner -->

        <div class="card-inner p-0">
            <div class="nk-tb-list nk-tb-ulist is-compact">
                <div class="nk-tb-item nk-tb-head">
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Learner</span>
                    </div>
                    <div class="nk-tb-col bg-secondary text-center ucap">
                        <span class="sub-text text-white">Sex</span>
                    </div>
                    <div class="nk-tb-col bg-secondary text-center ucap">
                        <span class="sub-text text-white">Class</span>
                    </div>
                    <div class="nk-tb-col w-25 bg-secondary ucap">
                        <span class="sub-text text-white">Current School</span>
                    </div>
                    <div class="nk-tb-col w-15 bg-secondary text-center ucap">
                        <span class="sub-text text-white">Claim Date</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
            <div v-if="out_going_claims.data.length" class="nk-tb-list nk-tb-ulist is-compact">
                <div v-for="enrolment in out_going_claims.data" class="nk-tb-item">
                    <div class="nk-tb-col w-25">
                        <div class="user-card">
                            <div class="user-avatar">
                                <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.full_name">
                            </div>
                            <div class="user-name text-uppercase">
                                <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" class="tb-lead cursor text-dark">{{ enrolment.learner.person.full_name }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="nk-tb-col text-center">
                        <span class="text-dark">{{ enrolment.learner.person.gender }}</span>
                    </div>
                    <div class="nk-tb-col text-center">
                        <span class="text-dark">{{ enrolment.education_grade.name.toUpperCase() }}</span>
                    </div>
                    <div class="nk-tb-col w-25">
                        <span class="text-dark">{{ enrolment.school.name }}</span>
                        <span class="font-italic text-muted">({{ enrolment.school.district.name.toUpperCase() }})</span>
                    </div>
                    <div class="nk-tb-col w-15 text-center">
                        <span v-if="enrolment.claim !== null" class="text-dark">{{ formatDate(enrolment.claim.date_created) }}</span>
                    </div>
                </div><!-- .nk-tb-item -->
            </div><!-- .nk-tb-list -->
        </div><!-- .card-inner -->

        <div v-if="!out_going_claims.data.length" class="card-inner p-0">
            <div class="card-body">
                <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                    <em class="icon ni ni-alert-circle"></em> There are no outgoing learner claims to display at the moment.
                </div>
            </div>
        </div>

        <div v-if="out_going_claims.data.length" class="card-inner d-flex flex-row justify-content-between">
            <nav>
                <ul class="pagination">
                    <li :class="[out_going_claims.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="out_going_claims.current_page > 1 ? loadClaims(1) : null" :class="[out_going_claims.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                        </a>
                    </li>
                    <li :class="[out_going_claims.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                        <a @click="out_going_claims.current_page > 1 ? loadClaims(out_going_claims.current_page-1) : null" :class="[out_going_claims.current_page === 1 ? '' : 'cursor', 'page-link']">
                            <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                        </a>
                    </li>
                    <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                        <a @click="loadClaims(link.label)" class="page-link cursor" v-html="link.label"></a>
                    </li>
                    <li :class="[out_going_claims.current_page === out_going_claims.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="out_going_claims.current_page < out_going_claims.last_page ? loadClaims(out_going_claims.current_page+1) : null" :class="[out_going_claims.current_page === out_going_claims.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                        </a>
                    </li>
                    <li :class="[out_going_claims.current_page === out_going_claims.last_page ? 'disabled' : '', getLinkClasses()]">
                        <a @click="out_going_claims.current_page < out_going_claims.last_page ? loadClaims(out_going_claims.last_page) : null" :class="[out_going_claims.current_page === out_going_claims.last_page ? '' : 'cursor', 'page-link']">
                            <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="d-flex ml-4">
                <span class="align-self-center">
                    Showing <span class="text-primary">{{ out_going_claims.from }}</span> to <span class="text-primary">{{ out_going_claims.to }}</span> of <span class="text-primary">{{ out_going_claims.total }}</span>
                </span>
            </div>
        </div><!-- .card-inner -->
    </div><!-- .card-inner-group -->
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";

export default {
    name: "OutgoingLearnerClaims",
    props: ["educationGradesObj", "outgoingClaimsObj"],
    components: {
        Notifications
    },
    data: function () {
        return {
            api_url: '/institutions/learner/claims',
            select_all_out_going_claims: false,
            loading: false,
            filtering: false,
            loading_message: '',
            out_going_claims: {
                data: [],
                links: [],
                total: 0,
            },
            selected_out_going_claims: [],
            education_grades: [],
            filter: {
                education_grade_id: '',
                gender: '',
                name: '',
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.out_going_claims = this.outgoingClaimsObj;
            this.education_grades = this.educationGradesObj;

            $('#filterOutgoingEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#filterOutgoingGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });
        },
        formatDate: function (date) {
            return moment(date).format("MMMM DD, YYYY");
        },
        toggleAllClaims: function () {
            this.selected_out_going_claims =[];

            if (this.select_all_out_going_claims) {
                this.out_going_claims.data.forEach(admin=>{
                    this.selected_out_going_claims.push(admin.id)
                });
            }
        },
        toggleOneClaims: function (id) {
            this.select_all_out_going_claims = this.selected_out_going_claims.length === this.out_going_claims.data.length
        },
        loadClaims: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/outgoing?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.out_going_claims = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                gender: '',
                name: '',
            };
            $('#filterOutgoingGender').val('').change();
            $('#filterOutgoingEducationGradeId').val('').change();
            this.loadClaims(1, false);
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            console.log(error.response)
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.out_going_claims.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    },
}
</script>

<style scoped>

</style>
