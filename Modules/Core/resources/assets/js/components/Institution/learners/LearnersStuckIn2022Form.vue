<template>
    <div>
        <div class="modal fade zoom" tabindex="-1" id="missingClassLearnerModal"  data-bs-backdrop="static" data-bs-keyboard="false"  data-backdrop="static" aria-labelledby="staticBackdropLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Update Learners Stuck In 2022</h5>


                    </div>
                    <div class="modal-body">
                        <notifications ref="notify"></notifications>

                        <div class="example-alert mb-3">
                            <div class="alert alert-warning alert-icon">
                                <em class="icon ni ni-alert-circle"></em>Note: Select 10 learners first and click submit changes to continue.
                            </div>
                        </div>

                        <form @submit.prevent="submitData">
                            <div  style="overflow: auto;max-height: 55vh;">
                                <table class="table border-secondary">
                                    <thead>
                                            <tr class="bg-gray-100">
                                            <th>NAMES</th>
                                            <th>LIN</th>
                                            <th>SEX</th>
                                            <th>2024</th>
                                            <th>Reporting Status</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="slearner in learners_stuck" :key="slearner.person_id">
                                            <td :style="removedLearners.includes(slearner.person_id)?'text-decoration: line-through;':''">
                                                <div v-if="slearner.person !== null"><a :style="removedLearners.includes(slearner.person_id)?'color: #aaa;':''"  target="_blank" :href="'/institution/learners/profile/' +slearner.learner.encrypted_lin">{{ slearner.learner.person.full_name }} <em class="icon ni ni-arrow-up-right"></em></a></div>
                                                <!-- <span class="text-gray text-sm">{{  formatBirthDate(clearners.learner.person.birth_date)}}</span> -->
                                                <!-- <span class="text-gray text-sm"><a href=''>{{ clearners.learner.lin }}</a></span> -->
                                            </td>
                                            <td :style="removedLearners.includes(slearner.person_id)?'text-decoration: line-through;':''">
                                                <div :style="removedLearners.includes(slearner.person_id)?'color: #aaa;':''">{{ slearner.learner.lin }}</div>
                                                <!-- <span class="text-gray text-sm">{{  formatBirthDate(clearners.learner.person.birth_date)}}</span> -->
                                                <!-- <span class="text-gray text-sm"><a href=''>{{ clearners.learner.lin }}</a></span> -->
                                            </td>
                                            <td :style="removedLearners.includes(slearner.person_id)?'text-decoration: line-through;':''">
                                                <span v-if="slearner.person !== null" :style="removedLearners.includes(slearner.person_id)?'color: #aaa;':''" class="text-gray text-sm">{{  slearner.learner.person.gender}}</span>
                                            </td>
                                            <td>
                                                <div class="custom-select2">
                                                    <select @change="selectClass($event, slearner.person_id, '2024')" :name="'learner_selected_class_2024[' + slearner.person_id + ']'" class="form-select-sm" :required="!removedLearners.includes(slearner.person_id) && (selectedStatus[slearner.person_id] || selectedStatus[slearner.person_id] === 0)" :disabled="shouldDisableSelect(slearner.person_id) || removedLearners.includes(slearner.person_id) || loading">
                                                        <option :selected="selectedClass['2024'][slearner.person_id] == null"></option>
                                                        <option value="0" :selected="selectedClass['2024'][slearner.person_id] == '0'">N/A</option>
                                                        <option v-for="l_class in school.school_type.classes" :key="l_class.id" :value="l_class.id" :selected="selectedClass['2024'][slearner.person_id] == l_class.id">{{ l_class.name }}</option>
                                                    </select>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="">
                                                    <select id="reportingStatusId" @change="selectStatus($event,slearner.person_id)" :name="'reportingStatus['+slearner.person_id+']'" class="form-select-sm" :required="!removedLearners.includes(slearner.person_id) && (selectedClass['2024'][slearner.person_id] || selectedClass['2024'][slearner.person_id] === 0)" :disabled="shouldDisableSelect(slearner.person_id) || removedLearners.includes(slearner.person_id) || loading">
                                                        <option :selected="selectedStatus[slearner.person_id] == null"></option>
                                                        <option value="1">STILL IN SCHOOL</option>
                                                        <option value="2">LEFT SCHOOL</option>
                                                    </select>
                                                </div>
                                            </td>
                                            <td nowrap v-if="canDelete(slearner.person_id)">
                                                <!-- <a v-if="!shouldDisableSelect(slearner.person_id) && !removedLearners.includes(slearner.person_id)" href="#" @click.prevent="removeLearner(slearner.person_id)"><em class="ni ni-trash-alt ml-1 text-red"></em> <span class="text-sm">Delete</span></a> -->
                                                <a v-if="removedLearners.includes(slearner.person_id)" href="#" @click.prevent="undoRemoveLearner(slearner.person_id)"><em class="ni ni-undo ml-1"></em> Undo</a>
                                            </td>
                                            <td v-else>
                                                <a href="#" @click.prevent="resetLearner(slearner.person_id)"><em class="ni text-green ni-cross-circle ml-1"></em> <span class="text-sm">Reset</span></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="row ml-2 mt-4 d-flex-row justify-content-between">
                                <div class="pt-2 ml-1">
                                    <span v-if="learners_stuck.length">{{ learners_stuck.length - removedLearners.length }} learners </span>
                                    <span v-if="learners_stuck.length && removedLearners.length"> | </span>
                                    <span v-if="removedLearners.length"><a class="pa-1" href="" @click.prevent="removedLearners.splice(0)">{{ removedLearners.length }} deleted. Undo all</a></span>
                                </div>

                                <!-- <nav>
                                    <ul class="pagination">
                                        <li :class="[learners_stuck.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                            <a @click="learners_stuck.current_page > 1 ? loadLearners(1) : null" :class="[learners_stuck.current_page === 1 ? '' : 'cursor', 'page-link']">
                                                <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                            </a>
                                        </li>
                                        <li :class="[learners_stuck.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                            <a @click="learners_stuck.current_page > 1 ? loadLearners(learners_stuck.current_page-1) : null" :class="[learners_stuck.current_page === 1 ? '' : 'cursor', 'page-link']">
                                                <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                            </a>
                                        </li>
                                        <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                            <a @click="loadLearners(link.label)" class="page-link cursor" v-html="link.label"></a>
                                        </li>
                                        <li :class="[learners_stuck.current_page === learners_stuck.last_page ? 'disabled' : '', getLinkClasses()]">
                                            <a @click="learners_stuck.current_page < learners_stuck.last_page ? loadLearners(learners_stuck.current_page+1) : null" :class="[learners_stuck.current_page === learners_stuck.last_page ? '' : 'cursor', 'page-link']">
                                                <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                            </a>
                                        </li>
                                        <li :class="[learners_stuck.current_page === learners_stuck.last_page ? 'disabled' : '', getLinkClasses()]">
                                            <a @click="learners_stuck.current_page < learners_stuck.last_page ? loadLearners(learners_stuck.last_page) : null" :class="[learners_stuck.current_page === learners_stuck.last_page ? '' : 'cursor', 'page-link']">
                                                <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                            </a>
                                        </li>
                                    </ul>
                                </nav> -->
                                <!-- <div class="d-flex flex-row ml-4">
                                    <span class="align-self-center mr-1">Show</span>
                                    <div class="form-wrap align-self-center">
                                        <select id="filterPerPage" class="form-select-sm">
                                            <option value="15">15</option>
                                            <option value="30">30</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                </div> -->
                                <!-- <div class="d-flex ml-4">
                                    <span class="align-self-center">
                                        Showing <span class="text-primary">{{ learners_stuck.from }}</span> to <span class="text-primary">{{ learners_stuck.to }}</span> of <span class="text-primary">{{ learners_stuck.total }}</span>
                                    </span>
                                </div> -->

                                <div class="mr-2">
                                    <button type="submit" class="btn bg-dark-teal d-flex" :disabled="loading || noDataChange">
                                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span v-if="loading" class="align-self-center">Submitting, Please wait...</span>
                                        <span v-if="loading" class="sr-only">Submitting, Please wait...</span>
                                        <span v-if="!loading" class="align-self-center">Submit Changes</span>
                                        <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>

                                    </button>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";



export default {
    name: "LearnersStuckIn2022Form",
    props: ["mainSchoolObj"],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            selectedClass:{
                '2024':{},

            },
            removedLearners:[],
            learner_selected_class:[],
            school: {
                learners_stuck: [],
                school_type:{
                    classes:[]
                },
            },
            api_url: '/institutions/learners/save-learners-stuck',
            loading: false,
            filtering: false,
            loading_message: '',
            selectedStatus: {},
            filter: {
                class_id: '',
                per_page: '',

            }
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.school = this.mainSchoolObj;

            $('#missingClassLearnerModal').modal({
                backdrop: 'static',
                keyboard: false
            });

            $('#filterClassId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#missingClassLearnerModal'),
                templateSelection: function (data, container) {
                    self.filter.class_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#filterPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    self.loadLearners(1, self.filtering);
                    return data.text;
                },
            });
        },

        notValidSelection: function(learner){
            if(this.selectedClass['2022'][learner]){
                return false;//is valid
            }
            return true;
        },
        formatBirthDate: function(date){
            return moment(date).format("D MMMM, YYYY");
        },
        selectClass: function(evt, learner, year){
            if(evt.target.value==''){
                this.$delete(this.selectedClass[year], learner);
            } else {
                this.$set(this.selectedClass[year], learner, evt.target.value);
            }
        },
        selectStatus: function(evt, learner){
            if(evt.target.value==''){
                this.$delete(this.selectedStatus, learner);
            } else {
                this.$set(this.selectedStatus, learner, evt.target.value);
            }
        },
        undoRemoveLearner:function(learner){
            this.removedLearners.splice(this.removedLearners.indexOf(learner), 1);

        },
        removeLearner:function(learner){
            if(this.removedLearners.indexOf(learner)==-1){
                this.removedLearners.push(learner);
                this.$delete(this.selectedClass['2024'], learner);
                this.$delete(this.selectedStatus, learner);
            }
            // console.log(this.removedLearners);
        },
        submitData: function(submitEvent){
            let self = this;
            this.$refs.notify.messages.splice(0);

            this.loading = true;
            axios.post(self.api_url+'/update', [self.selectedClass, self.removedLearners, self.selectedStatus])
                .then(response => {
                    this.loading = false;
                    if(this.school.learners_stuck.length == response.data.learners_stuck.length){
                        return;
                    }
                    this.school = response.data;
                    this.removedLearners.splice(0);
                    this.selectedClass = {'2024':{}};//.splice(0);
                    this.selectedStatus = {};
                    if(this.school.learners_stuck.length == 0){
                        $('#missingClassLearnerModal').modal('hide');
                    }
                    setTimeout(()=>{
                        this.$refs.notify.messages.push({
                        status: "success",
                        title: "Success",
                        message: "information updated",
                    });
                    }, 500)
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });


        },

        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        canDelete:function(learner){
            if(this.selectedClass['2024'][learner])return false;
            if(this.selectedStatus[learner])return false;
            return true;
        },
        resetLearner:function(learner){
            this.$delete(this.selectedClass['2024'], learner);
            this.$delete(this.selectedStatus, learner);

        },
        filtered_classes:function(learner){
            return this.school.school_type.classes.filter((v)=>{
                let _2022class = Number.parseInt(this.selectedClass['2022'][learner]);
                if(_2022class>=0){
                    // console.log(v.id, learner);
                    // console.log(this.selectedClass['2022'][learner], this.selectedClass['2022'][learner] + 1 )
                    if((_2022class + 1 ) == v.id ) return true;
                    if((_2022class - 1 ) == v.id ) return true;
                    if(_2022class == v.id ) return true;
                    if(_2022class == 0 ) return true;
                    // if((this.selectedClass['2022'][learner] - 1 ) == v.id ) return true;
                    // if((this.selectedClass['2022'][learner] ) == v.id ) return true;
                    // console.log('fail');
                }
                return false;
            });
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/filter?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.school.learners_stuck = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        noDataChange:function(){
            return this.removedLearners.length == 0 && JSON.stringify(this.selectedClass['2024']) == '{}' && JSON.stringify(this.selectedStatus) == '{}';
        },

        shouldDisableSelect() {
            return (personId) => {
                // Count the number of selected learner records
                const selectedCount = Object.values(this.selectedClass['2024']).filter(id => id !== null && id !== '0').length;
                // Disable select if the selected count is 20 or more
                return selectedCount >= 10 && !this.selectedClass['2024'][personId];
            };
        },

        learners_stuck:function(){
            return this.school.learners_stuck;
        },
        // getPaginationLinks: function () {
        //     let arr = this.school.learners_stuck.links;
        //     arr.pop();
        //     arr.shift();
        //     return arr;
        // },
    },
}
</script>

<style scoped>
    th{
        padding: 8px 16px!important;
        font-size: 13px!important;
        text-transform: uppercase;
    }
    .table td:first-child, .table th:first-child{
        padding-left: 4px!important;
    }
    #missingClassLearnerModal .modal-content .close{
        top: 26px!important;
        right: 16px!important;
    }
</style>
