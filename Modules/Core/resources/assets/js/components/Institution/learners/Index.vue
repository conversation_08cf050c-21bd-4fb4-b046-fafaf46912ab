<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Manage {{ learnerLabel }}s</h3>
                    <div class="nk-block-des text-soft">
                        <p>You have a total of {{ learners.total }} {{ learnerLabel.toLowerCase() }}s.</p>
                    </div>
                </div><!-- .nk-block-head-content -->

                <!--                <div class="nk-block-head-content">-->
                <!--                    <div class="toggle-wrap nk-block-tools-toggle">-->
                <!--                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>-->
                <!--                        <div class="toggle-expand-content" data-content="pageMenu">-->
                <!--                            <ul class="nk-block-tools g-3">-->
                <!--                                <li><a @click="exportLearners()" class="cursor btn btn-md btn-secondary text-white"><em class="icon ni ni-download-cloud"></em><span>Export</span></a></li>-->
                <!--                            </ul>-->
                <!--                        </div>-->
                <!--                    </div>&lt;!&ndash; .toggle-wrap &ndash;&gt;-->
                <!--                </div>&lt;!&ndash; .nk-block-head-content &ndash;&gt;-->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="p-3">
                        <div class="row">
                            <div v-show="schoolTypeIdObj <= 6" class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_education_grade_id" :disabled="loading" class="form-select-sm">
                                        <option v-if="schoolTypeIdObj > 3 && schoolTypeIdObj !== 7" value="">SELECT YEAR OF STUDY</option>
                                        <option v-else value="">SELECT CLASS</option>
                                        <option v-for="classe in classes" :value="classe.id">{{ classe.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                            <!--                            <div v-show="schoolTypeIdObj === 7" class="col-lg-3">-->
                            <!--                                <div class="form-wrap">-->
                            <!--                                    <select id="filterLearnerCurriculumId" class="form-select-sm">-->
                            <!--                                        <option value="">SELECT CURRICULUM</option>-->
                            <!--                                        <option v-for="curriculum in school.international_curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>-->
                            <!--                                    </select>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--                            <div v-show="schoolTypeIdObj === 7" class="col-lg-3">-->
                            <!--                                <div class="form-wrap">-->
                            <!--                                    <select id="filterLearnerEducationGradeId" class="form-select-sm">-->
                            <!--                                        <option value="">SELECT GRADE</option>-->
                            <!--                                    </select>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_gender" :disabled="loading" class="form-select-sm">
                                        <option value="">SELECT GENDER</option>
                                        <option value="M">MALE</option>
                                        <option value="F">FEMALE</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_learner_nin_status" :disabled="loading" class="form-select-sm">
                                        <option value="">LEARNER NIN STATUS</option>
                                        <option value="verified">VERIFIED</option>
                                        <option value="missing">MISSING</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select id="filter_approval_status" :disabled="loading" class="form-select-sm">
                                        <option value="">SELECT STATUS</option>
                                        <option value="false">ACTIVE</option>
                                        <option value="true">FLAGGED</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <select v-model="filter.country_id" id="filter_country_id" :disabled="loading" class="form-select-sm">
                                        <option value="">NATIONALITY</option>
                                        <option v-for="country in countries" :value="country.id">{{ country.name.toUpperCase() }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-wrap">
                                    <div class="input-group">
                                        <input v-model.trim="filter.lin" :disabled="loading" type="text" class="form-control text-uppercase" placeholder="LIN">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-wrap">
                                    <div class="input-group">
                                        <input v-model.trim="filter.search_term" :disabled="loading" type="text" class="form-control text-uppercase" placeholder="Learner Name">
                                        <div class="input-group-append">
                                            <button @click.prevent="resetFilter()" :disabled="loading"  v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                <em class="icon ni ni-cross"></em>
                                            </button>
                                            <button @click.prevent="filterLearners()" :disabled="loading"  class="btn rounded-right text-white bg-dark-teal" type="button">
                                                <em class="icon ni ni-filter mr-1"></em>Apply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div v-if="loading" class="text-center py-5">
                        <div class="py-5"></div>
                        <div class="py-3"></div>
                        <div class="spinner-border" style="width: 2rem; height: 2rem;" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="py-3"></div>
                        <div class="py-5"></div>
                    </div>
                    <div v-if="!loading" class="card-inner p-0">
                        <div class="table-responsive">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-left-0 text-uppercase text-center border-1">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleAllAdmissions()" v-model="select_all_learners" type="checkbox" class="custom-control-input" id="uid">
                                            <label class="custom-control-label" for="uid"></label>
                                        </div>
                                    </th>
                                    <th @click="sortLearners('sort_name')" class="nk-tb-col px-1 text-white border-white cursor text-uppercase border-1">
                                        <em v-if="filter.sort_name === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_name === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">{{ learnerLabel }} Name</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">LIN</span>
                                    </th>
                                    <th @click="sortLearners('sort_gender')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_gender === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_gender === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">Sex</span>
                                    </th>
                                    <th @click="sortLearners('sort_education_grade_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_education_grade_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_education_grade_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">{{ gradeLabel }}</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">{{ learnerLabel.toUpperCase() }} NIN</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">STATUS</span>
                                    </th>
                                    <th @click="sortLearners('sort_country_id')" class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <em v-if="filter.sort_country_id === 'asc'" class="icon ni ni-sort-up-fill text-white lead float-left mr-1"></em>
                                        <em v-if="filter.sort_country_id === 'desc'" class="icon ni ni-sort-down-fill text-white lead float-left mr-1"></em>
                                        <span class="sub-text ucap text-white">Nationality</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                        <span class="sub-text ucap text-white">ACTIONS</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="border-bottom">
                                <tr v-for="enrolment in learners.data" class="nk-tb-item">
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleOneLearner(enrolment.learner.person_id)" v-model="selected_learners" :value="enrolment.learner.person_id" type="checkbox" class="custom-control-input" :id="'uid'+enrolment.learner.person_id">
                                            <label class="custom-control-label" :for="'uid'+enrolment.learner.person_id"></label>
                                        </div>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" class="tb-lead cursor text-dark-teal">
                                            <div class="user-card">
                                                <div v-if="true" class="user-avatar">
                                                    <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.initials">
                                                </div>
                                                <div class="user-name text-uppercase">
                                                    <span v-if="true" class="text-dark-teal">{{ enrolment.learner.person.full_name }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ enrolment.learner.lin }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="enrolment.learner.person !== null" class="text-dark tb-lead">{{ enrolment.learner.person.gender }}</span>
                                    </td>

                                    <td v-if="schoolTypeIdObj <= 6" class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ enrolment|educ_grade }}</span>
                                    </td>
                                    <td v-if="schoolTypeIdObj === 7" class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="enrolment.international_education_grade !== null" class="text-dark tb-lead">{{ enrolment.international_education_grade.name }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="enrolment.learner.person.country.code !== 'UGA'" class="text-muted font-italic">
                                            N/A
                                        </span>
                                        <span v-else-if="learnerNINVerified(enrolment)" class=""><em class="icon text-dark-teal ni ni-check-circle"></em> <span class="text-dark">YES</span></span>
                                        <span v-else class=""><em class="icon text-danger ni ni-cross-circle"></em> <span class="text-dark">NO</span></span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="enrolment.learner.is_flagged_yn" class=""><span class="badge badge-danger">Flagged</span></span>
                                        <span v-else class=""><span class="badge badge-dark-teal">Active</span></span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span v-if="true" class="text-dark tb-lead">{{ enrolment.learner.person.country.name.toUpperCase()  }}</span>
                                    </td>
                                    <!-- <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a  data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                            <em class="icon ni ni-eye-fill"></em>
                                        </a>
                                    </td> -->
                                    <td class="nk-tb-col align-middle text-center">
                                        <ul class="">
                                            <li>
                                                <div class="drodown">
                                                    <a href="#" class="dropdown-toggle btn btn-sm btn-icon btn-trigger" data-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                    <div class="dropdown-menu dropdown-menu-right drop-show">
                                                        <ul class="link-list-opt no-bdr">
                                                            <li><a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" class="cursor lead text-primary" target="_blank"><em class="icon ni ni-eye"></em><span>View Profile</span></a></li>
                                                            <li v-if="enrolment.learner.is_flagged_yn === false && enrolment.academic_year_id === activeAcademicYearId"><a :href="'/institution/learners/profile/' + enrolment.learner.encrypted_lin + '?edit=true'" class="cursor lead text-primary" target="_blank"><em class="icon ni ni-edit"></em><span>Edit</span></a></li>
                                                            <li v-if="enrolment.learner.is_flagged_yn === false && enrolment.academic_year_id === activeAcademicYearId"><a @click="flagLearnerViewModal(enrolment)" class="cursor lead text-danger"><em class="icon ni ni-flag"></em><span>Flag | Delete</span></a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-if="!learners.data.length && !loading" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There are no {{ learnerLabel.toLowerCase() }}s to display for academic year.
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <!-- First Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === 1) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page > 1) ? loadLearners(1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === 1) ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <!-- Previous Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === 1) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page > 1) ? loadLearners(learners.current_page-1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === 1) ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <!-- Pagination Links -->
                                <li v-if="learners.data.length > 0" v-for="link in getPaginationLinks" :class="[getLinkClasses(link), loading ? 'disabled' : '']">
                                    <a @click="!loading ? loadLearners(link.label) : null" :class="['page-link', loading ? 'disabled' : 'cursor']" v-html="link.label"></a>
                                </li>
                                <!-- Next Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === learners.last_page) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page < learners.last_page) ? loadLearners(learners.current_page+1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === learners.last_page) ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <!-- Last Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === learners.last_page) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page < learners.last_page) ? loadLearners(learners.last_page) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === learners.last_page) ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center mr-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPerPage" :disabled="!learners.data.length || loading" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="70">70</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing <span class="text-primary">{{ learners.from }}</span> to <span class="text-primary">{{ learners.to }}</span> of <span class="text-primary">{{ learners.total }}</span>
                            </span>
                        </div>
                    </div><!-- .card-inner -->


                    <!-- Delete learner modal -->
                    <div class="modal fade zoom" tabindex="-1" id="flagLearnerModal">
                        <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a @click="resetLearnerDelete()" class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <form @submit.prevent="deleteLearner(enrolment)">
                                    <div class="modal-header">
                                        <h6 class="modal-title">Delete Details Of {{ enrolment.learner.person.full_name}}</h6>
                                    </div>
                                    <div class="modal-body">
                                        <!--                            <error-notifications ref="notifyErrorNIN"></error-notifications>-->
                                        <error-notifications ref="notifyError"></error-notifications>

                                        <div class="row g-4">
                                            <div class="col-12">
                                                <div class="row g-4 align-self-center">
                                                    <div class="col-lg-12">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Select reason for deleting learner <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <select id="learnerDeleteReasonId" class="form-select-sm" required>
                                                                    <option value="">--SELECT--</option>
                                                                    <option v-for="reason in deletion_reasons" :value="reason.id">{{ reason.name.toUpperCase() }}</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div><!-- .row -->
                                                <div class="row g-4 align-self-center">
                                                    <div class="col-lg-12">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label class="form-label">Explain your reason <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <textarea v-model.trim="form_delete_learner.description" required maxlength="100" type="text" placeholder="Briefly describe why you would want this learner to be deleted in not more than 100 characters!" class="form-control form-text" autocomplete="off"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div><!-- .row -->
                                            </div>

                                        </div><!-- .row -->
                                        <div class="nk-kycfm-action pt-5 row">
                                            <div class="col-lg-6">
                                                <button @click="resetLearnerDelete()" :disabled="loading" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                                    <span class="align-self-center text-uppercase">CANCEL</span>
                                                </button>
                                            </div>
                                            <div class="col-lg-6">
                                                <button :disabled="loading" type="submit" class="btn bg-dark-teal text-center btn-block d-flex">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Submitting...</span>
                                                    <span v-if="loading" class="sr-only">Loading...</span>
                                                    <span v-if="!loading" class="align-self-center text-uppercase">Submit For Approval</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </div>

                                    </div>
                                </form>

                            </div>
                        </div>
                    </div>

                    <!-- Loading Modal -->
                    <div style="display:none;" id="loadingMessage" class="card card-preview">
                        <div class="card-inner">
                            <div class="d-flex align-items-center">
                                <strong>{{ loading_message }}...</strong>
                                <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
                            </div>
                        </div>
                    </div>
                    <!-- /Loading Modal -->

                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
        <!--        <div v-if="!learners.data.length" class="card card-stretch">-->
        <!--            <div class="card-inner-group">-->
        <!--                <div class="card-body">-->
        <!--                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">-->
        <!--                        <em class="icon ni ni-alert-circle"></em> There are no {{ learnerLabel.toLowerCase() }}s to display for academic year {{ selectedYear }}.-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</template>

<script>
import SuccessNotifications from "../../Notifications.vue";
import ErrorNotifications from "../../Notifications.vue";
import learnerMixin from "../../../mixins/learnerMixin";

export default {
    name: "Index",
    mixins:[learnerMixin],
    props: ['countriesObj', 'learnersObj', 'educationGradesObj', 'schoolTypeIdObj','schoolObj','deletionReasonsObj','academicYearIdObj'],
    mounted() {
        this.initPlugins();
    },
    components: {
        SuccessNotifications,
        ErrorNotifications
    },
    data: function () {
        return {
            select_all_learners: false,
            selected_learners: [],
            loading_message: "",
            loading: false,
            filtering: false,
            filterPerPage: false,
            api_url: '/institutions/learners/',
            bulk_action: '',
            edit: false,
            classes: [],
            countries: [],
            deletion_reasons: [],
            school: {},
            activeAcademicYearId: '',
            learners: {
                total: 0,
                data: [],
                links: [],
            },
            filter: {
                learner_nin_status: '',
                parent_nin_status: '',
                approval_status: '',
                gender: '',
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_education_grade_id: '',
                country_id: '',
                sort_name: 'asc',
                sort_nin_status: '',
                sort_gender: '',
                sort_education_grade_id: '',
                sort_country_id: '',
                search_term: '',
                lin: '',
                per_page: '',
            },
            form_delete_learner: {
                reason_id: '',
                description: '',
            },
            enrolment: {
                learner: {
                    person: {
                        first_name: '',
                        surname: '',
                        other_names: '',
                        full_name: '',
                        current_id: {
                            identity_type: {
                                name: ''
                            },
                        },
                    },
                    encrypted_lin: '',
                    decrypted_lin: '',
                    is_flagged_yn: ''
                },
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            this.school = this.schoolObj;
            this.learners = this.learnersObj;
            this.classes = JSON.parse(this.educationGradesObj);
            this.countries = JSON.parse(this.countriesObj);
            this.deletion_reasons = this.deletionReasonsObj;
            this.activeAcademicYearId = Number(this.academicYearIdObj);

            $('#bulk_action').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });

            $('#filter_education_grade_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id;
                    return data.text;
                },
            });

            $('#filterLearnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_curriculum_id = data.id;
                    self.loadEducationGrades();
                    return data.text;
                },
            });

            $('#filterLearnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_education_grade_id = data.id;
                    return data.text;
                },
            });

            $('#filter_country_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.country_id = data.id;
                    return data.text;
                },
            });

            $('#filter_gender').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#filter_learner_nin_status').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.learner_nin_status = data.id;
                    return data.text;
                },
            });

            $('#filter_approval_status').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.approval_status = data.id;
                    return data.text;
                },
            });

            // $('#filter_parent_nin_status').select2({
            //     minimumResultsForSearch: Infinity,
            //     templateSelection: function (data, container) {
            //         self.filter.parent_nin_status = data.id;
            //         return data.text;
            //     },
            // });

            $('#filterPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    if (self.filterPerPage) {
                        self.loadLearners(1, self.filtering);
                    }
                    self.filterPerPage = true;
                    return data.text;
                },
            });

            $('#learnerDeleteReasonId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#flagLearnerModal'),
                templateSelection: function (data, container) {
                    self.form_delete_learner.reason_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
        },
        loadEducationGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#filterLearnerEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("SELECT GRADE", "", false, false);
            select.append(newOption).trigger('change');
            self.filter.inter_sch_education_grade_id = "";

            //load new options
            if (self.filter.inter_sch_curriculum_id !== "") {
                self.grades = self.school.international_curriculums.find(curriculum=>{
                    return curriculum.id === self.filter.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        toggleAllAdmissions: function () {
            this.selected_learners =[];

            if (this.select_all_learners) {
                this.learners.data.forEach(enrolment=>{
                    this.selected_learners.push(enrolment.learner.person_id)
                });
            }
        },
        toggleOneLearner: function (id) {
            this.select_all_learners = this.selected_learners.length === this.learners.length
        },
        learnerNINVerified: function (enrolment) {
            return enrolment.learner.person.current_id !== null && enrolment.learner.person.current_id.identity_type_id === 1 && enrolment.learner.person.current_id.verification_status !== 3;
        },
        parentNINVerified: function (enrolment) {
            return enrolment.learner.parents !== null && enrolment.learner.parents.length && enrolment.learner.parents.filter(parent=>{return parent.current_id !== null && parent.current_id.identity_type_id === 1 && parent.current_id.verification_status !== 3;}).length
        },
        sortLearners: function (section) {
            if (section === 'sort_name') {
                this.filter.sort_name = this.filter.sort_name.length ? (this.filter.sort_name === 'asc' ? this.filter.sort_name = 'desc': this.filter.sort_name = 'asc') : this.filter.sort_name = 'asc';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_nin_status') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = this.filter.sort_nin_status.length ? (this.filter.sort_nin_status === 'asc' ? this.filter.sort_nin_status = 'desc': this.filter.sort_nin_status = 'asc') : this.filter.sort_nin_status = 'asc';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_gender') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = this.filter.sort_gender.length ? (this.filter.sort_gender === 'asc' ? this.filter.sort_gender = 'desc': this.filter.sort_gender = 'asc') : this.filter.sort_gender = 'asc';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_education_grade_id') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = this.filter.sort_education_grade_id.length ? (this.filter.sort_education_grade_id === 'asc' ? this.filter.sort_education_grade_id = 'desc': this.filter.sort_education_grade_id = 'asc') : this.filter.sort_education_grade_id = 'asc';
                this.filter.sort_country_id = '';
            }

            if (section === 'sort_country_id') {
                this.filter.sort_name = '';
                this.filter.sort_nin_status = '';
                this.filter.sort_gender = '';
                this.filter.sort_education_grade_id = '';
                this.filter.sort_country_id = this.filter.sort_country_id.length ? (this.filter.sort_country_id === 'asc' ? this.filter.sort_country_id = 'desc': this.filter.sort_country_id = 'asc') : this.filter.sort_country_id = 'asc';
            }

            this.loading = true;
            axios.post(this.api_url+'filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.learners = response.data;
                    this.loading = false;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        filterLearners: function () {
            this.loading = true;
            axios.post(this.api_url+'filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.learners = response.data;
                    this.loading = false;
                    this.filtering = true;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            Object.assign(this.learners, {
                data: [],
                current_page: 1,
                last_page: 1,
                from: null,
                to: null,
                total: 0
            });
            this.filter.lin = '';
            this.filter.search_term = '';
            $('#filter_gender').val('').change();
            $('#filter_education_grade_id').val('').change();
            $('#filter_country_id').val('').change();
            $('#filter_learner_nin_status').val('').change();
            $('#filter_approval_status').val('').change();
            $('#filterPerPage').val(15).change();
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'filter?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.learners = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        flagLearnerViewModal: function(enrolment) {
            this.enrolment.learner.person.full_name = enrolment.learner.person.full_name;
            this.enrolment.learner.lin = enrolment.learner.lin;

            $('#flagLearnerModal').modal({backdrop: "static"});
        },
        deleteLearner: function () {
            let self = this;

            Swal.fire({
                title: 'Are you sure?',
                html: '<div style="margin-bottom: 10px;">' +
                    'You are about to flag details of <strong>' + self.enrolment.learner.person.full_name + '</strong> for deletion.</div>' +
                    '<div class="text-danger">NOTE: Once approved and deleted, the change is irreversible.</div>',
                icon: 'warning',
                confirmButtonText: 'Yes, Proceed',
                showCancelButton: true,
                cancelButtonColor: '#29384A',
                reverseButtons: true,

            }).then(function (result) {
                if (result.value) {
                    //self.startLoading('Flagging Learner');
                    axios.post(self.api_url+'delete/'+self.enrolment.learner.lin, self.form_delete_learner)
                        .then((response)=>{
                            Swal.fire({
                                title: 'Success',
                                html: '<div style="margin-bottom: 10px;">' +
                                    '<strong>' + self.enrolment.learner.person.full_name + '</strong> has been successfully flagged for deleteing.</div>' +
                                    '<div>Once approved by admin, the record will be deleted permanently</div>',
                                icon: 'success',
                                confirmButtonText: 'Close',
                                showCancelButton: false,
                            })
                            self.learners = response.data;
                            self.resetLearnerDelete();
                        })
                        .catch(error=>{
                            $.unblockUI();
                            self.renderError(error)
                        });
                }
            });
        },
        resetLearnerDelete: function () {
            $('#flagLearnerModal').modal('hide');
            this.loading = false;
            this.form_delete_learner.description = '';

            this.$refs.notifyError.messages = [];
            // this.$refs.notifySuccess.messages = [];
            window.setTimeout(()=>{
                $('#learnerDeleteReasonId').val('').change();
            }, 50);
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },

        exportLearners: function () {
            axios.post(this.api_url+'export-excel',this.filter)
                .then(()=>{
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success', message:"Export has started, we will notify you through your email when the excel file is ready."});
                })
                .catch(error=>{
                    this.loading = false;
                    this.renderError(error)
                });
        },
        exportLearnersPdf: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                window.location.href = 'export-learners-pdf'+'?year='+urlParams.get('year');
            } else {
                window.location.href = 'export-learners-pdf';
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedYear: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                return urlParams.get('year');
            } else {
                return moment().format("YYYY");
            }
        },
        getPaginationLinks: function () {
            let arr = this.learners.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        learnerLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Student";
            } else {
                return "Learner";
            }
        },
        gradeLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Year Of Study";
            } else {
                return "Class";
            }
        },
    }
}
</script>

<style>
.swal2-popup {
    height: 420px;
    width: 600px;
    overflow-y: auto; /* Add scroll if content exceeds height */
}
.drop-show {
    transform: translate3d(-141px, -52px, 0px) !important;
}
</style>
