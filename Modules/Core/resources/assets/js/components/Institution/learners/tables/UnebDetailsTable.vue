<template>
    <table class="table border border-dark-teal mt-3">
        <thead class="bg-secondary">
        <tr>
            <th class="text-white align-middle w-45" rowspan="2">{{ title }}</th>
        </tr>
        <tr>
            <th class="py-2"></th>
        </tr>
        </thead>
        <tbody class="border-top-0 border-dark-teal">
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">Full Name</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span v-if="data.name !== null">{{ data.name }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">Date Of Birth</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span v-if="data.date_of_birth !== null">{{ formatDate(data.date_of_birth) }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">Sex</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span v-if="data.gender !== null">{{ data.gender }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">Nationality</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span v-if="data.nationality !== null">{{ data.nationality.toUpperCase() }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">District</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span v-if="data.district !== null">{{ data.district.toUpperCase() }}</span>
                <span v-else class="text-uppercase text-muted">MISSING</span>
            </td>
        </tr>
        <tr>
            <td class="align-middle text-dark border-secondary bg-secondary-dim">Index Number</td>
            <td class="align-middle border-left text-dark border-secondary">
                <span>{{ data.exam_level }}: {{ data.index_number }} - {{ data.exam_year }}</span>
            </td>
        </tr>
        </tbody>
    </table>
</template>

<script>
export default {
    name: "UnebDetailsTable",
    props: {
        data: {
            type: Object,
            required: true
        },
        title: {
            type: String,
            required: true
        }
    },
    methods: {
        formatDate(date) {
            return new Date(date).toLocaleDateString();
        }
    }
};
</script>
