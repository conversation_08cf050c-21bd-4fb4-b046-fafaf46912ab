<template>
    <div>
        <success-notifications ref="notifySuccess"></success-notifications>
        <div class="nk-block-head-content float-right mb-3">
            <button v-if="learner.school.school_type_id >= 4 && learner.school.school_type_id !== 7" :disabled="learner.is_flagged_yn" data-toggle="modal" data-target="#updateParentInfoModal" data-backdrop="static" type="button" class="btn btn-sm mt-4 btn-primary">
                <em class="icon ni ni-plus-circle-fill text-white mr-1"></em>ADD NEXT OF KIN
            </button>
            <button v-else data-toggle="modal" data-target="#updateParentInfoModal" data-backdrop="static" :disabled="learner.is_flagged_yn" type="button" class="btn btn-sm mt-4 btn-primary">
                <em class="icon ni ni-plus-circle-fill text-white mr-1"></em>ADD PARENT/GUARDIAN
            </button>
        </div>
        <div class="card-inner-group">
            <div class="card-inner p-0">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col">
                            <span v-if="learner.school.school_type_id >= 4 && learner.school.school_type_id !== 7" class="sub-text ucap text-white">NEXT OF KIN</span>
                            <span v-else class="sub-text ucap text-white">Parent/Guardian</span>
                        </div>
                        <div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Sex</span></div>
                        <div class="nk-tb-col text-center"><span class="sub-text ucap text-white">Relationship</span></div>
                        <div class="nk-tb-col"><span class="sub-text ucap text-white">Contacts</span></div>
                        <div class="nk-tb-col"><span class="sub-text ucap text-white">Identity document</span></div>
                        <div class="nk-tb-col text-center"></div>
                    </div><!-- .nk-tb-item -->
                    <div v-for="parent in learner.parents" class="nk-tb-item">
                        <div class="nk-tb-col">
                            <div class="user-card">
                                <div class="user-avatar">
                                    <img :src="parent.photo_url" style="border-radius: 0" :alt="parent.initials">
                                </div>
                                <div class="user-info text-dark">
                                    <span class="tb-lead">{{ parent.full_name.toUpperCase() }} <span class="dot dot-success d-md-none ml-1"></span></span>
                                    <!--									<span v-if="parent.next_of_kin === 1">Next of Kin</span>-->
                                </div>
                            </div>
                        </div>
                        <div class="nk-tb-col text-dark text-center">
                            <span v-if="parent.gender === 'M'">MALE</span>
                            <span v-if="parent.gender === 'F'">FEMALE</span>
                        </div>
                        <div class="nk-tb-col text-dark text-center">
                            <span v-if="parent.pivot.is_parent_yn === true">{{ parent.gender === 'M' ? 'FATHER' : 'MOTHER' }}</span>
                            <span v-if="parent.pivot.is_parent_yn === false">GUARDIAN</span>
                        </div>
                        <div class="nk-tb-col text-dark">
                            <span v-if="parent.phone_1" class="d-block"><em class="icon mr-1 text-primary ni ni-call-fill"></em>+{{ parent.phone_1 }}</span>
                            <span v-if="parent.phone_2" class="d-block"><em class="icon mr-1 text-primary ni ni-call-fill"></em>+{{ parent.phone_2 }}</span>
                            <span v-if="parent.email" class="d-block"><em class="icon mr-1 text-primary ni ni-mail-fill"></em>{{ parent.email }}</span>
                        </div>
                        <div class="nk-tb-col text-dark">
                            <span v-if="parent.id_number !== null && parent.id_number !== ''" class="d-block">
                                {{ parent.masked_id_number }}
                                <!-- {{ parent.id_number }} -->
                            </span>
                        </div>
                        <div v-if="learner.parents.length > 1" class="nk-tb-col text-dark text-center">
                            <span @click="deleteParent(parent)" class="cursor" title="Remove"><em class="icon ni ni-trash-fill text-danger"></em></span>
                        </div>
                    </div><!-- .nk-tb-item -->
                </div><!-- .nk-tb-list -->
                <div v-if="!learner.parents.length" class="py-3">
                    <div class="alert alert-secondary alert-icon">
                        <em class="icon ni ni-alert-circle"></em> <strong>{{ learner.person.full_name }}</strong> has no parents or guardians yet.
                    </div>
                </div>
            </div><!-- .card-inner -->
        </div><!-- .card-inner-group -->

        <!-- modal add parent -->
        <div class="modal fade zoom" tabindex="-1" id="updateParentInfoModal">
            <form @submit.prevent="submitForm()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetParentInfo()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 v-if="learner.school.school_type_id >= 4 && learner.school.school_type_id !== 7" class="modal-title">Add Next Of Kin</h5>
                            <h5 v-else class="modal-title">Add Parent/Guardian</h5>
                        </div>
                        <div class="modal-body">
                            <error-notifications ref="notifyError"></error-notifications>
                            <div class="row">
                                <div class="col-lg-12 col-md-12 pb-4 overflow-auto scrollbar-dark-teal">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label v-if="learner.school.school_type_id >= 4 && learner.school.school_type_id !== 7" class="form-label">Does this next of kin have a NIN?</label>
                                                    <label v-else class="form-label">Does this parent/guardian have a NIN?</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <div class="custom-control custom-control-inline custom-radio">
                                                    <input :disabled="parent_verify" type="radio" class="custom-control-input" v-model="parent_has_nin" value="yes" id="parentWithNIN">
                                                    <label class="custom-control-label text-uppercase" for="parentWithNIN">YES</label>
                                                </div>
                                                <div class="custom-control custom-control-inline custom-radio">
                                                    <input :disabled="parent_verify" type="radio" class="custom-control-input" v-model="parent_has_nin" value="no" id="parentWithoutNIN">
                                                    <label class="custom-control-label text-uppercase" for="parentWithoutNIN">NO</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="uganda && parent_has_nin === 'yes'" class="row mt-2">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentNIN" class="form-label">Enter NIN</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input :required="parent_has_nin === 'yes'" v-model.trim="form_parent.parent_nin" id="learnerParentNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                        <div class="input-group-append">
                                                            <button @click="parentVerifyNIN()" v-if="parent_has_nin === 'yes'" :disabled="saveLoading || loading" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                <span v-if="loading" class="sr-only">Verifying...</span>
                                                                <span v-if="!loading" class="">Verify NIN</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="uganda && parent_verify && parent_has_nin === 'yes' && nira_parent.national_id !== ''" class="row">
                                        <div class="table-responsive py-3">
                                            <table class="table table-sm table-hover">
                                                <tr>
                                                    <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                        <div class="user-card">
                                                            <div class="w-150px">
                                                                <img id="learnerParentPhoto" src="@images/default_male.jpg" class="rounded-0" alt="contact person photo">
                                                            </div>
                                                        </div><!-- .user-card -->
                                                    </td>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                        <span class=""> {{ nira_parent.national_id }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                        <span class="">{{ nira_parent.surname }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                        <span class="">{{ nira_parent.given_names }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                        <span class="">{{ nira_parent.gender }}</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="px-2 align-middle text-uppercase text-dark">
                                                        <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                        <span class="">{{ nira_parent.date_of_birth }}</span>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div v-show="parent_has_nin === 'no'" class="row mt-2">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentPassport" class="form-label">Enter Passport</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <div class="input-group">
                                                        <input v-model.trim="form_parent.parent_passport" id="learnerParentPassport" maxlength="9" type="text" placeholder="eg. *********" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off">
                                                        <div class="input-group-append">
                                                            <button @click="parentVerifyPassport()" v-if="parent_has_nin === 'no'" :disabled="saveLoading || loading" class="btn rounded-right text-white bg-dark-teal" type="submit">
                                                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                                <span v-if="loading" class="align-self-center">Verifying...</span>
                                                                <span v-if="loading" class="sr-only">Verifying...</span>
                                                                <span v-if="!loading" class="">Verify Passport</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-show="parent_verify && parent_has_nin === 'no'" class="row mt-3">
                                        <div class="col-lg-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="parent_verify && parent_has_nin === 'no'" v-model.trim="form_parent.parent_first_name" id="learnerParentFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-lg-0">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="parent_verify &&  parent_has_nin === 'no'" v-model.trim="form_parent.parent_surname" id="learnerParentSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="learnerParentOtherNames" class="form-label">Other Names</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model.trim="form_parent.parent_other_names" id="learnerParentOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>

                                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">Gender <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-group">
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model.number="form_parent.parent_gender" value="M" id="learnerParentMale">
                                                        <label class="custom-control-label text-uppercase" for="learnerParentMale">Male</label>
                                                    </div>
                                                    <div class="custom-control custom-control-inline custom-radio">
                                                        <input type="radio" class="custom-control-input" v-model.number="form_parent.parent_gender" value="F" id="learnerParentFemale">
                                                        <label class="custom-control-label text-uppercase" for="learnerParentFemale">Female</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <label class="form-label" for="learnerParentRelationship">Relationship <span class="text-danger">*</span></label>
                                                <select :required="parent_verify" id="learnerParentRelationship" class="form-select-sm">
                                                    <option value="">--SELECT--</option>
                                                    <option value="parent">PARENT</option>
                                                    <option value="guardian">GUARDIAN</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="parentPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input :required="parent_verify" v-model="form_parent.parent_phone_1" id="parentPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="parentPhoneTwo" class="form-label">Phone Number 2</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model="form_parent.parent_phone_2" id="parentPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div v-show="parent_verify" class="col-lg-6 mt-lg-2 mt-3">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label for="parentEmail" class="form-label">Email Address</label>
                                                </div>
                                                <div class="form-control-group">
                                                    <input v-model="form_parent.parent_email" id="parentEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                        <div class="d-flex p-3 border-top border-dark-teal justify-content-center">
                            <button @click="resetParentInfo()" :disabled="saveLoading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="parent_verify === false || saveLoading" type="submit" class="btn bg-dark-teal d-flex">
                                <span v-if="saveLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="saveLoading" class="align-self-center">Saving...</span>
                                <span v-if="saveLoading" class="sr-only">Saving...</span>
                                <span v-if="!saveLoading" class="align-self-center">Update</span><em v-if="!saveLoading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

    </div>

</template>

<script>
import ErrorNotifications from "../../Notifications.vue";
import SuccessNotifications from "../../Notifications.vue";

export default {
    name: "Guardians",
    props: ['learnerObj'],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            api_url: '/institutions/learners/',
            loading: false,
            saveLoading: false,
            deleteLoading: false,
            parent_verify: false,
            parent_has_nin: 'yes',
            uganda: true,
            parent: {
                id: '',
                learner_id: '',
                passport: '',
                parent_type: 0,
                deceased: 0,
                person: {
                    first_name: '',
                    surname: '',
                    other_names: '',
                    gender: 0,
                    phone_1: '',
                    phone_2: '',
                    email: '',
                    id_number: '',
                    country_id: '',
                    country: {
                        name: '',
                    }
                },
            },
            learner: {
                person: {
                    id:'',
                    full_name:'',
                    country_id: '',
                },
                school: {
                    school_type_id: '',
                },
                parents:[]
            },
            country: 'UGANDA',
            form_parent: {
                parent_nin: '',
                parent_passport: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_relationship: '',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
                photo: null,
                birth_date: moment().subtract(4, 'years').format("D MMMM, YYYY"),
                country_id: 221,
                photo_url: '',
            },
            nira_parent: {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            $('#learnerParentRelationship').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#updateParentInfoModal'),
                templateSelection: function (data, container) {
                    self.form_parent.parent_relationship = data.id;
                    return data.text;
                },
            });

            this.learner = this.learnerObj;

            //Set phone number 1 flag
            let parentPhone1 = document.querySelector('#parentPhoneOne');
            let iti_person_phone_1 = intlTelInput(parentPhone1, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone1.addEventListener('blur', ()=>{
                self.form_parent.parent_phone_1 = iti_person_phone_1.getNumber();
                parentPhone1.value = iti_person_phone_1.getNumber();
            });
            parentPhone1.addEventListener('change', ()=>{
                self.form_parent.parent_phone_1 = iti_person_phone_1.getNumber();
                parentPhone1.value = iti_person_phone_1.getNumber();
            });

            //Set phone number 2 flag
            let parentPhone2 = document.querySelector('#parentPhoneTwo');
            let iti_person_phone_2 = intlTelInput(parentPhone2, {
                initialCountry: 'ug',
                separateDialCode: false,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. '+selectedCountryPlaceholder;
                },
            });
            parentPhone2.addEventListener('blur', ()=>{
                self.form_parent.parent_phone_2 = iti_person_phone_2.getNumber();
                parentPhone2.value = iti_person_phone_2.getNumber();
            });
            parentPhone2.addEventListener('change', ()=>{
                self.form_parent.parent_phone_2 = iti_person_phone_2.getNumber();
                parentPhone2.value = iti_person_phone_2.getNumber();
            });
        },
        submitForm() {
            return this.parent_verify ? this.createParent() : (this.parent_has_nin === 'yes' ? this.parentVerifyNIN() : this.parentVerifyPassport());
        },
        parentVerifyNIN: function () {
            this.loading = true;
            axios.post('/nira/user-info', {id_number: this.form_parent.parent_nin.toUpperCase()})
                .then(response => {
                    this.loading = false;
                    this.nira_parent = response.data;
                    this.form_parent.parent_first_name = this.nira_parent.given_names;
                    this.form_parent.parent_surname = this.nira_parent.surname;
                    this.form_parent.parent_gender = this.nira_parent.gender;
                    if (this.nira_parent.photo !== null) {
                        if (this.nira_parent.photo.includes('.png')) {
                            $('#learnerParentPhoto').attr('src', '/images/nira-photos/' + this.nira_parent.photo);
                        } else {
                            $('#learnerParentPhoto').attr('src', 'data:image/png;base64,' + this.nira_parent.photo);
                        }
                    }
                    this.parent_verify = true;
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },
        parentVerifyPassport: function () {
            this.loading = true;
            window.setTimeout(() => {
                this.loading = false;
                this.parent_verify = true;
            }, 1000);
        },

        createParent: function () {
            this.saveLoading = true;
            let formData = new FormData();
            let self = this;

            if (this.nira_parent) {
                let checkParent = this.learner.parents.filter(parent => {
                    return parent.pivot.is_parent_yn;
                });

                if (checkParent.length === 2 && this.form_parent.parent_relationship === 'parent') {
                    this.saveLoading = false;
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error!',
                        message: 'Learner already has both parents!'
                    });
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                    return false;
                }

                if (checkParent.length === 1 && checkParent[0].gender === this.nira_parent.gender && this.form_parent.parent_relationship === 'parent') {
                    if (this.nira_parent.gender === 'M') {
                        this.saveLoading = false;
                        this.$refs.notifyError.messages.push({
                            status: 'error',
                            title: 'Error!',
                            message: 'Learner cannot have two fathers!'
                        })
                        $("html, body").animate({ scrollTop: 0 }, "slow");
                        return false;
                    }
                    if (this.nira_parent.gender === 'F') {
                        this.saveLoading = false;
                        this.$refs.notifyError.messages.push({
                            status: 'error',
                            title: 'Error: ',
                            message: 'Learner cannot have two mothers!'
                        });
                        $("html, body").animate({ scrollTop: 0 }, "slow");
                        return false;
                    }
                }

                const checkNin = obj => obj.id_number === this.nira_parent.national_id;
                if (checkParent.some(checkNin)) {
                    this.saveLoading = false;
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error: ',
                        message: 'This NIN has already been added, try a different one..!'
                    });
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                    return false;
                }

                let checkGuardian = this.learner.parents.filter(guardian => {
                    return guardian.pivot.is_parent_yn === false;
                });

                const checkGuardianNin = obj => obj.id_number === this.nira_parent.national_id;
                if (checkGuardian.some(checkGuardianNin) && this.form_parent.parent_nin !== null) {
                    this.saveLoading = false;
                    this.$refs.notifyError.messages.push({
                        status: 'error',
                        title: 'Error: ',
                        message: 'This NIN has already been added, try a different one..!'
                    });
                    $("html, body").animate({ scrollTop: 0 }, "slow");
                    return false;
                }
            }
            // Save parent details
            self.saveLoading = true;
            formData.append('parent_nin', (self.uganda ? self.form_parent.parent_nin : ''));
            formData.append('parent_nin_verify', self.parent_verify);
            formData.append('parent_passport', (self.parent_has_nin === 'no' ? self.form_parent.parent_passport : ''));
            formData.append('parent_first_name', self.form_parent.parent_first_name);
            formData.append('parent_surname', self.form_parent.parent_surname);
            formData.append('parent_other_names', self.form_parent.parent_other_names);
            formData.append('parent_gender', self.form_parent.parent_gender);
            formData.append('parent_relationship', self.form_parent.parent_relationship);
            formData.append('parent_phone_1', self.form_parent.parent_phone_1);
            formData.append('parent_phone_2', self.form_parent.parent_phone_2);
            formData.append('parent_email', self.form_parent.parent_email);

            axios.post(self.api_url + 'create-parent/' + this.learner.person.id, formData, {headers: {'Content-Type': 'multipart/form-data'}})
                .then(response => {
                    self.learner = response.data;
                    self.$refs.notifySuccess.messages.push({
                        status: 'success',
                        title: 'Success: ',
                        message: 'Parent/Guardian Added Successfully'
                    });
                    self.resetParentInfo();
                    self.clearNiraParent();
                })
                .catch(error => {
                    self.saveLoading = false;
                    self.renderError(error);
                });
        },
        //Delete parent details
        deleteParent: function (parent) {
            let self = this;
            if (!this.deleteLoading) {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You are about to remove "+parent.full_name,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'YES, REMOVE!'
                }).then(function (result) {
                    if (result.value) {
                        self.deleteLoading = true;
                        let parent_name = parent.full_name
                        axios.post(self.api_url+"delete-parent", {parent_id: parent.id, learner_id: self.learner.person_id})
                            .then(response=>{
                                Swal.fire('DELETED!', 'Parent/Guardian removed successfully.', 'success');
                                self.deleteLoading = false;
                                self.learner.parents = response.data;
                            })
                            .catch(errors=>{
                                self.deleteLoading = false;
                                self.renderError(errors);
                            })
                    }
                });
            }
        },
        clearNiraParent: function () {
            this.nira_parent = {
                national_id: '',
                surname: '',
                given_names: '',
                date_of_birth: '',
                gender: '',
                nationality: '',
                photo: null,
                alive: true,
            };
        },
        resetParentInfo: function () {
            $('#updateParentInfoModal').modal('hide');
            this.saveLoading = false;
            this.parent_verify = false;
            this.parent_has_nin = 'yes';
            this.uganda = true;

            this.form_parent = {
                parent_nin: '',
                parent_passport: '',
                parent_first_name: '',
                parent_surname: '',
                parent_other_names: '',
                parent_gender: 'M',
                parent_phone_1: '',
                parent_phone_2: '',
                parent_email: '',
            };
            window.setTimeout(()=>{
                $('#learnerParentRelationship').val('').change();
            }, 50);
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                let text = '';
                for (let field in error.response.data.errors) {
                    for (let i = 0; i < error.response.data.errors[field].length; i++) {
                        text += '<br>'+ error.response.data.errors[field][i];
                    }
                }
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
}
</script>

<style scoped>

</style>
