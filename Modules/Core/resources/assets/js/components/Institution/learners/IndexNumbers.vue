<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Add Learners Index Numbers</h3>
                    <nav class="nk-block-des">
                        <ul class="breadcrumb breadcrumb-arrow">
                            <li class="breadcrumb-item">
                                <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active text-soft">
                                Add Learners Index Numbers
                            </li>
                        </ul>
                    </nav>
                </div><!-- .nk-block-head-content -->

            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <ul class="nav nav-tabs">
                        <li class="nav-item px-2">
                            <a class="nav-link active" data-toggle="tab" href="#OLevelTab">
                                <span>O'LEVEL LEARNERS</span>
                            </a>
                        </li>
                        <li class="nav-item px-2">
                            <a class="nav-link" data-toggle="tab" href="#ALevelTab">
                                <span>A'LEVEL LEARNERS</span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active" id="OLevelTab">
                            <school-learners-o-level-index-numbers
                                :school-type-id-obj="schoolTypeIdObj"
                                :education-grades-obj="educationGradesObj"
                                :learners-o-level-obj="learners_o_level"
                            ></school-learners-o-level-index-numbers>
                        </div>
                        <div class="tab-pane" id="ALevelTab">
                            <school-learners-a-level-index-numbers
                                :school-type-id-obj="schoolTypeIdObj"
                                :education-grades-obj="educationGradesObj"
                                :learners-a-level-obj="learners_a_level"
                            ></school-learners-a-level-index-numbers>
                        </div>
                    </div>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>

</template>

<script>

export default {
    name: "IndexNumbers",
    props: [
        "schoolTypeIdObj",
        "educationGradesObj",
        "learnersOLevelObj",
        "learnersALevelObj",
    ],
    data: function () {
        return {
            learners_o_level: {
                data: [],
                links: [],
                total: 0,
            },
            learners_a_level: {
                data: [],
                links: [],
                total: 0,
            },
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.learners_o_level = this.learnersOLevelObj;
            this.learners_a_level = this.learnersALevelObj;
            console.log(this.learners_o_level);
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
