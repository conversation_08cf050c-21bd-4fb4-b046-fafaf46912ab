<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Expected Enrolment</h3>
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-bordered card-stretch border-dark-teal">
                <div class="card-inner-group">
                    <div class="card-inner">

                        <div class="card card-bordered card-stretch">
                            <div class="card-inner-group">
                                <div class="card-inner p-0">

                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">Updated</span></div>
                                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">Period</span></div>
                                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">Male</span></div>
                                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">Female</span></div>
                                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">Total</span></div>
                                            <div class="nk-tb-col text-uppercase text-center"><span class="sub-text text-white">Action</span></div>
                                        </div>

                                        <div v-for="expected_enrolment in expected_enrolments" :key="expected_enrolment.id" class="nk-tb-item text-dark">
                                            <div class="nk-tb-col nk-tb-col-check">{{ formatDate(expected_enrolment.date_updated) }}</div>
                                            <div class="nk-tb-col nk-tb-col-check bg-lighter text-center">{{ computeTerm(expected_enrolment) }}</div>
                                            <div class="nk-tb-col nk-tb-col-check text-right text-center">{{ computeMaleTotal(expected_enrolment.enrolment_data) }}</div>
                                            <div class="nk-tb-col nk-tb-col-check text-right text-center">{{ computeFemaleTotal(expected_enrolment.enrolment_data) }}</div>
                                            <div class="nk-tb-col nk-tb-col-check text-right strong text-center">{{ expected_enrolment.total_enrolment }}</div>
                                            <div class="nk-tb-col nk-tb-col-check pr-3 text-center">
                                                <a class="btn btn-sm btn-primary cursor" href="#" @click="editData(expected_enrolment)">
                                                    <span>Update</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->

        <div id="formcontainer" ref="formcontainer"></div>
    </div>

</template>

<script>
import LearnerSummaryForm from '../LearnerSummaryForm.vue';
import Vue from 'vue'

export default {
    name: "LearnerTransfers",
    props: [
        "expectedEnrolmentsObj",
        "classesObj",
        "termsObj"
    ],
    data: function () {
        return {
            expected_enrolments:[],
            incoming_transfers: {
                data: [],
                links: [],
                total: 0,
            },
            outgoing_transfers: {
                data: [],
                links: [],
                total: 0,
            },
        }
    },
    mounted() {
        this.initPlugins()
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.expected_enrolments = this.expectedEnrolmentsObj.data;
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY hh:mma");
        },
        editData:function(expected_enrolment){
            var ComponentClass = Vue.extend(LearnerSummaryForm);
            var instance = new ComponentClass({
                propsData: {
                    classesObj: JSON.stringify(this.classesObj),
                    termsObj:JSON.stringify(this.termsObj),
                    expectedEnrolmentObj:expected_enrolment,
                    learnersCountObj:null}
            })
            // return;
            instance.$mount() // pass nothing
            this.$refs.formcontainer.innerHTML = "";
            this.$refs.formcontainer.appendChild(instance.$el)
        },
        computeTerm(expected_enrolment){
            let computedTerm = ""
            this.termsObj.forEach((termObj, idx)=>{
                // console.log("termObj", termObj)
                if(expected_enrolment.term_id == termObj.id){
                    computedTerm = termObj.name;
                }
            });
            return computedTerm;
        },
        computeMaleTotal(enrolment_data){
            let total = 0;
            try{
                let dataObj = JSON.parse(enrolment_data);
                for(let data in dataObj){
                    data = dataObj[data];
                    total += data['m'];
                }
            }catch(e){

            }
            return total;
        },
        computeFemaleTotal(enrolment_data){
            let total = 0;
            try{
                let dataObj = JSON.parse(enrolment_data);
                for(let data in dataObj){
                    data = dataObj[data];
                    total += data['f'];
                }
            }catch(e){

            }
            return total;
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
