<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Search Learner's LIN</h3>
                    <div class="nk-block-des">
                        <nav class="nk-block-des">
                            <ul class="breadcrumb breadcrumb-arrow">
                                <li class="breadcrumb-item">
                                    <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item active text-soft">
                                    Search LIN
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <notifications ref="notify"></notifications>
                    <error-notifications ref="notifyError"></error-notifications>
                    <div v-if="!verify" class="alert alert-info alert-icon">
                        <em class="icon ni ni-alert-circle"></em>
                        <h6 class="small">Please enter the details below to search for any learner's LIN.</h6>
                        <!--                                <h6 class="small mb-2">Please enter the details below to search for any learner's LIN. Note that the Parent ID Number MUST be the one the learner registered with in EMIS.</h6>-->
                    </div>
                    <form @submit.prevent="getLearnerDetails(learner)">
                        <div v-show="!verify" :class="[!verify ? 'mb-4' : '']">
                            <div class="row">
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="firstName">First Name <span class="text-danger">*</span></label>
                                        <input required v-model.trim="form_learner.first_name" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter Learner First Name" id="firstName" autocomplete="off">
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="surname">Surname <span class="text-danger">*</span></label>
                                        <input required v-model.trim="form_learner.surname" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter Learner Surname" id="surname" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label class="form-label">Sex <span class="text-danger">*</span></label>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-control-inline custom-radio">
                                                <input type="radio" class="custom-control-input" v-model="form_learner.gender" value="M" id="learnerMale">
                                                <label class="custom-control-label text-uppercase" for="learnerMale">Male</label>
                                            </div>
                                            <div class="custom-control custom-control-inline custom-radio">
                                                <input type="radio" class="custom-control-input" v-model="form_learner.gender" value="F" id="learnerFemale">
                                                <label class="custom-control-label text-uppercase" for="learnerFemale">Female</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="learnerBirthDate">Date Of Birth <span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <div class="form-icon form-icon-left">
                                                <em class="icon ni ni-calendar"></em>
                                            </div>
                                            <input required v-model.trim="form_learner.birth_date" placeholder="eg. 14 APRIL, 2022" id="learnerBirthDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <label class="form-label">Class (Current class in that school)<span class="text-danger">*</span></label>
                                        <select required id="learnerEducationGradeId" class="form-select-sm">
                                            <option value="">--SELECT--</option>
                                            <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                                <!--                                        <div class="col-6 mt-3">-->
                                <!--                                            <div class="form-group">-->
                                <!--                                                <label class="form-label" for="parentNIN">Parent ID Number <span class="text-danger">*</span></label>-->
                                <!--                                                <input required v-model.trim="form_learner.parent_nin" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter Parent NIN." id="parentNIN" autocomplete="off">-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <div class="col-lg-6 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="emisNo">School EMIS Number <span class="text-danger">*</span></label>
                                        <input required v-model.trim="form_learner.school_emis_no" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="Enter School EMIS No." id="emisNo" autocomplete="off">
                                    </div>
                                    <a href="/emis/search" target="_blank" class="form-label d-block text-dark-teal mt-3">
                                        To Find School EMIS Number <span style="text-decoration: underline" class="text-uppercase">click here</span> and search
                                    </a>
                                </div>
                            </div>
                            <!--                                    <div class="row">-->
                            <!--                                        -->
                            <!--                                        <div class="col-6 mt-5">-->
                            <!--                                            -->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                        </div>
                        <div v-if="verify" class="row mb-4">
                            <div class="col-12 ml-2">
                                <div class="table-responsive align-self-start">
                                    <table class="table table-sm">
                                        <thead>
                                        <tr>
                                            <th style="border: none">
                                                <h6>Learner Details</h6>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td rowspan="6" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                <div class="form-group d-flex flex-column justify-content-center">
                                                    <div class="w-150px mx-auto">
                                                        <img v-if="learner.person.photo_url !== null" id="learnerPhoto" :src="learner.person.photo_url" :alt="learner.person.full_name" class="rounded-0">
                                                        <img v-if="learner.person.photo_url === null && learner.person.gender === 'F'" id="learnerPhoto" src="@images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                        <img v-if="learner.person.photo_url === null && learner.person.gender === 'M'" id="learnerPhoto" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                <span class="">{{ learner.person.full_name }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">LIN</h6>
                                                <span class="">{{ learner.lin }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">CLASS</h6>
                                                <span v-if="learner.education_grade !== null" class="">{{ learner.education_grade.name.toUpperCase() }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                <span v-if="learner.person.gender === 'F'" class="">FEMALE</span>
                                                <span v-if="learner.person.gender === 'M'" class="">MALE</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                <span class="">{{ formatDate(learner.person.birth_date) }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                <h6 class="overline-title mb-0 text-dark-teal">SCHOOL</h6>
                                                <span class="">{{ learner.school.name }} - {{ learner.school.district.name.toUpperCase() }}</span>
                                            </td>
                                        </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12 d-flex flex-lg-row justify-content-center">
                                <button @click="resetSearchLearner()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light mr-2">
                                    <span>Reset</span>
                                    <em class="ni ni-cross ml-2"></em>
                                </button>
                                <button v-if="!verify"  :disabled="loading || (verify && !nin_verify && uganda)" type="submit" class="btn bg-dark-teal d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                    <span v-if="loading && !verify" class="align-self-center">Fetching Data, Please wait...</span>
                                    <span v-if="loading && !verify" class="sr-only">Fetching Data...</span>
                                    <span v-if="!loading && !verify" class="align-self-center">Search</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";

export default {
    name: "SearchLinPrimaryForm",
    props: ["educationGradesObj"],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/search-lin',
            verify: false,
            loading: false,
            filtering: false,
            nin_learner_loading: false,
            nin_loading: false,
            nin_verify: false,
            loading_message: '',
            //photoDropify: null,
            learner: {
                current_education_grade_id: '',
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    gender: 'M',
                    birth_date: moment().format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                },
                parents:[],
            },
            education_grades: [],
            form_learner: {
                first_name: '',
                surname: '',
                gender: 'M',
                birth_date: moment().format("D MMMM, YYYY"),
                education_grade_id: '',
                // parent_nin: '',
                school_emis_no: '',
            },
            uganda: true,
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.education_grades = this.educationGradesObj;

            $('#learnerBirthDate').datepicker({
                format: 'd MM, yyyy',
                autoclose: true,
            }).on('hide', e=>{
                self.form_learner.birth_date = moment(e.date).format('D MMMM, YYYY');
            });

            $('#learnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_learner.education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                $('#firstName').val('').change();
            }, 50);
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        getLearnerDetails: function () {
            this.loading = true;
            axios.post(this.api_url+'/verify', this.form_learner)
                .then(response => {
                    this.loading = false;
                    this.learner = response.data;
                    this.verify = true
                })
                .catch(error => {
                    this.loading = false;
                    this.renderError(error);
                });
        },

        resetSearchLearner: function () {
            this.verify = false;
            this.nin_verify = false;
            this.loading = false;
            this.uganda =  true;
            this.nin_learner_loading = false;
            this.nin_loading = false;
            this.filtering = false;
            this.learner = {
                school: {
                    name: '',
                    district: {
                        name: '',
                    },
                },
                education_grade: {
                    name: '',
                },
                person: {
                    full_name: '',
                },
                parents:[],
            };
            this.form_learner = {
                first_name: '',
                surname: '',
                birth_date: moment().format("D MMMM, YYYY"),
                education_grade_id: '',
                gender: 'M',
                // parent_nin: '',
                school_emis_no: '',
            };

            window.setTimeout(()=>{
                $('#learnerEducationGradeId').val('').change();
                $('#learnerBirthDate').datepicker('setDate', moment(this.form_learner.birth_date).toDate());
            }, 50);
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {

    },
}
</script>

<style scoped>

</style>
