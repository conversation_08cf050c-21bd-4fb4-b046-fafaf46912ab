<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <notifications ref="notifySuc<PERSON>"></notifications>
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h6 class="nk-block-title">Promote {{ learnerLabel }} To Next Academic Year</h6>
                    <nav class="nk-block-des">
                        <ul class="breadcrumb breadcrumb-arrow">
                            <li class="breadcrumb-item">
                                <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a :href="'/institution/learners/promotions'" class="text-primary">Learner Promotions</a>
                            </li>
                            <li class="breadcrumb-item active text-soft">
                                Manage
                            </li>
                        </ul>
                    </nav>

                </div><!-- .nk-block-head-content -->

                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li class="nk-block-tools-opt">
                                    <button :disabled="loading || selected_learners.length === 0" @click="promoteLearnersModal" type="button" class="btn btn-primary">
                                        <em class="icon ni ni-arrow-to-up"></em>
                                        <span>Promote</span>
                                    </button>
                                </li>
                                <li class="nk-block-tools-opt">
<!--                                    <button @click="exportSchools()" :disabled="exportLoading" type="submit" class="cursor btn btn-md btn-secondary text-white d-flex">-->
<!--                                        <span v-if="exportLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>-->
<!--                                        <span v-if="exportLoading" class="align-self-center">Processing...</span>-->
<!--                                        <span v-if="exportLoading" class="sr-only">Processing...</span>-->
<!--                                        <em v-if="!exportLoading" class="icon ni ni-download-cloud"></em>-->
<!--                                        <span v-if="!exportLoading" class="align-self-center">Export</span>-->
<!--                                    </button>-->
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner-group">
                    <div class="card-inner position-relative card-tools-toggle">
                        <div class="alert alert-info alert-icon">
                            <em class="icon ni ni-alert-circle"></em>
                            <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                            <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select {{ classTextLabel }}, then click the Apply button to load the learners' information.</h6>
                            <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Select the learner(s) you wish to promote by ticking the checkboxes in the table.</h6>
                            <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Click the Promote button to continue.</h6>
                        </div>
                        <div class="card-title-group">
                            <div class="card-tools">
                                <div class="d-flex flex-lg-row gx-3">
                                    <div v-show="schoolTypeIdObj <= 6" style="width: 200px !important" class="form-wrap">
                                        <select id="filter_education_grade_id" :disabled="loading" class="form-select-sm">
                                            <option v-if="schoolTypeIdObj > 3" value="">SELECT YEAR OF STUDY</option>
                                            <option v-else value="">SELECT CLASS</option>
                                            <option v-for="grade in education_grades" :value="grade.id">{{ grade.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                    <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                        <select id="filterLearnerCurriculumId" :disabled="loading" class="form-select-sm">
                                            <option value="">SELECT CURRICULUM</option>
                                            <option v-for="curriculum in curriculums" :value="curriculum.id">{{ curriculum.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                    <div v-show="schoolTypeIdObj === 7" style="width: 200px !important" class="form-wrap">
                                        <select id="filterLearnerEducationGradeId" :disabled="loading" class="form-select-sm">
                                            <option value="">SELECT GRADE</option>
                                        </select>
                                    </div>
                                    <div style="width: 200px !important" class="form-wrap">
                                        <select id="filter_gender" :disabled="loading" class="form-select-sm">
                                            <option value="">SELECT GENDER</option>
                                            <option value="M">MALE</option>
                                            <option value="F">FEMALE</option>
                                        </select>
                                    </div>
                                    <div class="form-wrap">
                                        <input v-model.trim="filter.lin" :disabled="loading" type="text" class="form-control text-uppercase" placeholder="LIN">
                                    </div>
                                    <div class="form-wrap">
                                        <div class="input-group">
                                            <input v-model.trim="filter.search_term" :disabled="loading" type="text" class="form-control text-uppercase" placeholder="LEARNER NAME">
                                            <div class="input-group-append">
                                                <button :disabled="loading" @click.prevent="resetFilterPending()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                    <em class="icon ni ni-cross"></em>
                                                </button>
                                                <button :disabled="filter.education_grade_id === '' && filter.inter_sch_education_grade_id === '' || loading" @click.prevent="filterLearnersPending()" class="btn rounded-right text-white bg-dark-teal" type="button">
                                                    <em class="icon ni ni-filter mr-1"></em>Apply
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- .card-tools -->
                        </div><!-- .card-title-group -->
                    </div><!-- .card-inner -->
                    <div v-if="loading" class="text-center py-5">
                        <div class="py-5"></div>
                        <div class="py-3"></div>
                        <div class="spinner-border" style="width: 2rem; height: 2rem;" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="py-3"></div>
                        <div class="py-5"></div>
                    </div>
                    <div v-if="!loading" class="card-inner p-0">
                        <div class="table-responsive-lg">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-left-0 text-uppercase text-center border-1">
                                        <div class="custom-control custom-control-sm custom-checkbox notext">
                                            <input @change="toggleAllPendingLearners()" v-model="select_all_pending_learners" type="checkbox" class="custom-control-input" id="uid">
                                            <label class="custom-control-label" for="uid"></label>
                                        </div>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase border-1">
                                        <span class="sub-text ucap text-white">NAMES</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">LIN</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Sex</span>
                                    </th>

                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span v-if="schoolTypeIdObj <= 3 || schoolTypeIdObj === 7" class="sub-text ucap text-white">Class</span>
                                        <span v-if="schoolTypeIdObj >= 4 && schoolTypeIdObj !== 7" class="sub-text ucap text-white">Year</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Promotion Status</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                        <span class="sub-text ucap text-white">ACTIONS</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody v-if="learners.data !== undefined" class="border-bottom">
                                    <tr v-for="enrolment in learners.data" class="nk-tb-item">
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <div class="custom-control custom-control-sm custom-checkbox notext">
                                                <input @change="toggleOnePendingLearner(enrolment.learner_id)" v-model="selected_learners" :value="enrolment.learner_id" type="checkbox" class="custom-control-input" :id="'uid'+enrolment.learner_id">
                                                <label class="custom-control-label" :for="'uid'+enrolment.learner_id"></label>
                                            </div>
                                        </td>
                                        <td class="nk-tb-col px-1 text-uppercase">
                                            <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" target="_blank" class="tb-lead cursor text-dark-teal">
                                                <div class="user-card">
                                                    <div class="user-avatar">
                                                        <img :src="enrolment.learner.person.photo_url" style="border-radius: 0" :alt="enrolment.learner.person.initials">
                                                    </div>
                                                    <div class="user-name text-uppercase">
                                                        <span class="text-dark-teal">{{ enrolment.learner.person.full_name }}</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ enrolment.learner.lin }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span class="text-dark tb-lead">{{ enrolment.learner.person.gender }}</span>
                                        </td>

                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span v-if="enrolment.learner.education_grade !== null && schoolTypeIdObj <= 6" class="text-dark tb-lead">{{ enrolment.learner.education_grade.name.toUpperCase() }}</span>
                                            <span v-if="enrolment.learner.international_education_grade !== null && schoolTypeIdObj === 7" class="text-dark tb-lead">{{ enrolment.learner.international_education_grade.name.toUpperCase() }}</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <span v-if="enrolment.is_enrolment_active_yn === false" class="badge badge-secondary">Pending</span>
                                        </td>
                                        <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                            <a :href="'/institution/learners/profile/'+enrolment.learner.encrypted_lin" data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                                <em class="icon ni ni-eye-fill"></em>
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-if="!learners.data.length && !loading" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There are no {{ learnerLabel.toLowerCase() }} to display at the moment.
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <!-- First Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === 1) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page > 1) ? loadLearnersPending(1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === 1) ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <!-- Previous Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === 1) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page > 1) ? loadLearnersPending(learners.current_page-1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === 1) ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <!-- Pagination Links -->
                                <li v-if="learners.data.length > 0" v-for="link in getPaginationLinks" :class="[getLinkClasses(link), loading ? 'disabled' : '']">
                                    <a @click="!loading ? loadLearnersPending(link.label) : null" :class="['page-link', loading ? 'disabled' : 'cursor']" v-html="link.label"></a>
                                </li>
                                <!-- Next Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === learners.last_page) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page < learners.last_page) ? loadLearnersPending(learners.current_page+1) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === learners.last_page) ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <!-- Last Page -->
                                <li :class="[(loading || learners.data.length === 0 || learners.current_page === learners.last_page) ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="(learners.data.length > 0 && learners.current_page < learners.last_page) ? loadLearnersPending(learners.last_page) : null"
                                       :class="[(learners.data.length === 0 || learners.current_page === learners.last_page) ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center mr-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPendingPromotionsPerPage" :disabled="!learners.data.length || loading" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing <span class="text-primary">{{ learners.from }}</span> to <span class="text-primary">{{ learners.to }}</span> of <span class="text-primary">{{ learners.total }}</span>
                            </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
<!--        <div v-if="!learners.data.length && !loading" class="card card-stretch">-->
<!--            <div class="card-inner-group">-->
<!--                <div class="card-body">-->
<!--                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">-->
<!--                        <em class="icon ni ni-alert-circle"></em> There are no {{ learnerLabel.toLowerCase() }} to display at the moment.-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="modal fade zoom" tabindex="-1" id="updatePendingPromotionStatusModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Promote Learner</h5>
                    </div>
                    <div class="modal-body">
                        <notifications ref="notifyError"></notifications>
                        <notifications ref="notify"></notifications>
                        <div class="alert alert-warning alert-icon">
                            <em class="icon ni ni-alert-circle"></em>
                            <h5 class="mb-2"><span class="text-danger">Notice</span></h5>
                            <h6 class="small mb-2" style="line-height: 1.6;"><span class="text-danger">*</span>Please confirm the learner's current and next class and ensure all information is correct before submitting. Once submitted this action <b>CANNOT</b> be undone.</h6>
                        </div>
                        <form @submit.prevent="updateLearnerPendingPromotionStatus">
                            <div class="row mt-3">
                                <div class="col-lg-6 mt-lg-0 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="learnerCurrentClass">Current Class<span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                                <input
                                                disabled id="learnerCurrentClass"
                                                type="text"
                                                :value="getCurrentClass"
                                                class="form-control bg-primary-dim text-uppercase">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-lg-0 mt-3">
                                    <div class="form-group">
                                        <label class="form-label" for="learnerNextClass">Next Class<span class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input
                                                disabled
                                                id="learnerNextClass"
                                                type="text"
                                                :value="getNextClass"
                                                class="form-control bg-primary-dim text-uppercase">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mt-3">
                                <label class="form-label" for="formPromotionStatusId">Promotion Status <span class="text-danger">*</span></label>
                                <div class="form-wrap w-auto">
                                    <select required id="formPromotionStatusId">
                                        <option value="">-- SELECT --</option>
                                        <option v-for="(status, idx) in promotion_statuses" :key="idx" :value="status.id">{{ status.name }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group mt-3">
                                <label class="form-label" for="formReportingStatusId">Reporting Status <span class="text-danger">*</span></label>
                                <div class="form-wrap w-auto">
                                    <select required id="formReportingStatusId">
                                        <option value="">-- SELECT --</option>
                                        <option v-for="(status, idx) in reporting_statuses" :key="idx" :value="status.id">{{ status.name }}</option>
                                    </select>
                                </div>
                            </div>
<!--                            <div class="form-group mt-3">-->
<!--                                <label class="form-label" for="effectDate">Since <span class="text-danger">*</span></label>-->
<!--                                <div class="form-control-wrap">-->
<!--                                    <div class="form-icon form-icon-left">-->
<!--                                        <em class="icon ni ni-calendar"></em>-->
<!--                                    </div>-->
<!--                                    <input required v-model.trim="promotionStatus.effect_date" id="effectDate" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="modal-footer px-0 pb-0 d-flex justify-content-between">
                                <div class="example-alert mb-2">
                                    <div class="alert alert-dark-teal alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> You are about to update Promotion Status of {{ promotionPreview }}.
                                    </div>
                                </div>
                                <button @click="resetPendingPromotionDetails" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>CANCEL</span>
                                </button>
                                <button :disabled="loading || promotionStatus.status === ''" type="submit" class="btn bg-dark-teal d-flex">
                                    <span v-if="loading" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                    <span v-if="loading" class="align-self-center">Updating...</span>
                                    <span v-if="loading" class="sr-only">Updating...</span>
                                    <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Notifications from "../../Notifications.vue";
import moment from "moment/moment";

export default {
    name: "Index",
    props: ['pendingPromotionsObj','educationGradesObj','curriculumsObj','schoolTypeIdObj'],
    mounted() {
        this.initPlugins();
        this.loadPromotionStatuses();
        this.loadReportingStatuses();
    },
    components: {
        Notifications
    },
    data: function () {
        return {
            select_all_pending_learners: false,
            selected_learners: [],
            loading: false,
            filtering: false,
            filterPerPage: false,
            exportLoading: false,
            api_url: '/institutions/learners/promotions',
            bulk_action: '',
            learners: {
                total: 0,
                data: [],
                links: [],
            },
            education_grades: [],
            curriculums: [],
            promotion_statuses: [],
            reporting_statuses: [],
            filter: {
                pending_promotion: true,
                gender: '',
                education_grade_id: '',
                inter_sch_curriculum_id: '',
                inter_sch_education_grade_id: '',
                sort_name: 'asc',
                sort_gender: '',
                sort_education_grade_id: '',
                search_term: '',
                lin: '',
                per_page: '',
            },
            promotionStatus: {
                pending_promotion: true,
                promotion_status: '',
                reporting_status: '',
                //effect_date: moment().format("D MMMM, YYYY"),
                selected_learners: [],
            },
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        initPlugins: function () {
            let self = this;
            //this.learners = this.pendingPromotionsObj;
            this.education_grades = this.educationGradesObj;
            this.curriculums = this.curriculumsObj;

            $('#bulk_action').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.bulk_action = data.id;
                    return data.text;
                },
            });

            $('#filter_education_grade_id').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id;
                    return data.text;
                },
            });

            $('#filterLearnerCurriculumId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_curriculum_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadGrades();
                    return data.text;
                },
            });

            $('#filterLearnerEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.inter_sch_education_grade_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#filter_gender').select2({
                minimumResultsForSearch: Infinity,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#formPromotionStatusId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.promotionStatus.promotion_status = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#formReportingStatusId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.promotionStatus.reporting_status = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#filterPendingPromotionsPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data, container) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    if (self.filterPerPage) {
                        self.loadLearnersPending(1, self.filtering);
                    }
                    self.filterPerPage = true;
                    return data.text;
                },
            });
        },

        loadGrades: function () {
            let self = this;
            // clean grades
            this.grades = [];
            let select = $("#filterLearnerEducationGradeId");
            select.empty().trigger('change');
            let newOption = new Option("SELECT GRADE", "", false, false);
            select.append(newOption).trigger('change');
            self.filter.inter_sch_education_grade_id = "";

            //load new options
            if (self.filter.inter_sch_curriculum_id !== "") {
                self.grades = self.curriculums.find(curriculum=>{
                    return curriculum.id === self.filter.inter_sch_curriculum_id
                }).grades;

                self.grades.forEach(grade=>{
                    let gradeOption = new Option(grade.name, grade.id, false, false);
                    select.append(gradeOption).trigger('change');
                });
            }
        },
        promoteLearnersModal() {
            this.filterPromotionStatusOption();
            $('#updatePendingPromotionStatusModal').modal({backdrop:"static"});
        },
        filterPromotionStatusOption(){
            let enrolment = this.learners.data.find(learner=>{
                return learner.learner_id === this.selected_learners[0]
            })

            if(enrolment === undefined) return;

            const isCandidateClass = enrolment?.learner?.education_grade?.is_candidate_class_yn === true;

            const excludeStatus = isCandidateClass ? "PROMOTED" : "COMPLETED";

            const updatedStatus = this.promotion_statuses.filter((status) => status.name !== excludeStatus)

                this.$nextTick(() => {
                $('#formPromotionStatusId').empty();
                updatedStatus.forEach(status => {
                    $('#formPromotionStatusId').append(
                        $('<option></option>').val(status.id).text(status.name)
                    );
                });
            });
        },
        loadPromotionStatuses() {
            axios.get('/lists/promotion-statuses')
                .then(response=>{
                    this.promotion_statuses = response.data;
                })
                .catch(error=>{
                    this.renderError(error)
                });
        },
        loadReportingStatuses() {
            axios.get('/lists/reporting-statuses')
                .then(response=>{
                    this.reporting_statuses = response.data;
                })
                .catch(error=>{
                    this.renderError(error)
                });
        },
        resetPendingPromotionDetails() {
            $('#updatePendingPromotionStatusModal').modal('hide');
            $('#formPromotionStatusId').val('').prop('disabled', false).change();
            $('#formReportingStatusId').val('').change();
            this.selected_learners = [];
            $('#filterPendingPromotionsPerPage').val(15).change();
        },

        toggleAllPendingLearners: function () {
            this.selected_learners =[];

            if (this.select_all_pending_learners) {
                this.learners.data.forEach(enrolment=>{
                    this.selected_learners.push(enrolment.learner_id)
                });
            }
        },
        toggleOnePendingLearner: function (id) {
            this.select_all_pending_learners = this.selected_learners.length === this.learners.length
        },

        filterLearnersPending: function () {
            this.loading = true;
            axios.post(this.api_url+'/filter?year='+this.selectedYear, this.filter)
                .then(response=>{
                    this.learners = response.data;
                    this.loading = false;
                    this.filtering = true;
                })
                .catch(error=>{
                    this.loading = false;
                    console.log(error);
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetFilterPending: function () {
            Object.assign(this.learners, {
                data: [],
                current_page: 1,
                last_page: 1,
                from: null,
                to: null,
                total: 0
            });
            this.filter.pending_promotion = false;
            this.filter.lin = '';
            this.filter.search_term = '';
            $('#filter_gender').val('').change();
            $('#filter_education_grade_id').val('').change();
            this.filtering = false;
            this.loading = false;
        },
        updateLearnerPendingPromotionStatus() {
            this.loading = true;
            let payload = {};
            if (this.filtering) {
                payload = {...this.filter, ...this.promotionStatus}
            } else {
                payload = this.promotionStatus
            }
            axios.post(this.api_url+'/update-promotion-status?year='+this.selectedYear, payload)
                .then(response=>{
                    this.$refs.notifySuccess.messages.push({status: 'success', title: 'Success: ', message:"Learner Promotion Successful"});
                    this.learners = response.data;
                    this.loading = false;
                    this.resetPendingPromotionDetails();
                })
                .catch(error=>{
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        loadLearnersPending: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/filter?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.learners = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        selectedYear: function () {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);

            if (urlParams.has('year')) {
                return urlParams.get('year');
            } else {
                return moment().format("YYYY");
            }
        },
        getPaginationLinks: function () {
            let arr = this.learners.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        learnerLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Students";
            } else {
                return "Learners";
            }
        },
        gradeLabel: function () {
            if (this.schoolTypeIdObj > 3 && this.schoolTypeIdObj !== 7) {
                return "Year Of Study";
            } else {
                return "Class";
            }
        },
        classTextLabel() {
            if (this.schoolTypeIdObj !== 7) {
                return "Class";
            } else {
                return "Curriculum and Grade";
            }
        },
        promotionPreview() {
            if (this.selected_learners.length === 1) {
                let enrolment = this.learners.data.find(learner=>{
                    return learner.learner_id === this.selected_learners[0]
                })

                if (enrolment !== undefined) {
                    return enrolment.learner.person.full_name
                }

                return '';
            } else if (this.selected_learners.length > 1) {
                return this.selected_learners.length+' '+this.learnerLabel;
            }

            return '';
        },
        getCurrentClass() {
            if (!this.selected_learners?.length || !this.educationGradesObj?.length) return;

            const enrolment = this.learners.data.find(learner =>
                learner.learner_id === this.selected_learners[0]
            );

            if (!enrolment) return;

            return this.schoolTypeIdObj === 7 ? enrolment.learner.international_education_grade.name : enrolment.learner.education_grade.name;
        },
        getNextClass() {
            if (!this.selected_learners?.length || !this.educationGradesObj?.length) return;

            const enrolment = this.learners.data.find(learner =>
                learner.learner_id === this.selected_learners[0]
            );

            if (!enrolment) return;

            const { learner } = enrolment;

            const { promotion_status } = this.promotionStatus;

            const isCandidateClass = learner?.education_grade?.is_candidate_class_yn === true;

            const currentGrade = this.schoolTypeIdObj === 7 ? learner.international_education_grade : learner.education_grade;

            if (!currentGrade) return;

            if (promotion_status === 2) return currentGrade.name;

            if (promotion_status === 1 && !isCandidateClass) {
                const nextClass = this.educationGradesObj.find(
                    grade => grade[this.schoolTypeIdObj === 7 ? 'id' : 'grade_rank'] === currentGrade[this.schoolTypeIdObj === 7 ? 'id' : 'grade_rank'] + 1
                );
                return nextClass?.name;
            }
        },
    },
    watch: {
        selected_learners(list) {
            this.select_all_pending_learners = list.length > 0 && list.length === this.learners.data.length
            this.promotionStatus.selected_learners = list;
        }
    }
}
</script>

<style scoped>

</style>
