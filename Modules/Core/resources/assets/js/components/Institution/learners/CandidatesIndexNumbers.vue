<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Registered Candidates</h3>
                    <nav class="nk-block-des">
                        <ul class="breadcrumb breadcrumb-arrow">
                            <li class="breadcrumb-item">
                                <a :href="'/institution/dashboard'" class="text-primary">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active text-soft">
                                Registered Candidates
                            </li>
                        </ul>
                    </nav>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li>
                                    <a @click="newRegistrationRequest()" class="cursor btn bg-dark-teal">
                                        <span class="text-white">Register Candidate</span><em class="icon ni ni-plus-circle-fill text-white"></em>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->

            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <div class="card-inner">
                    <notifications ref="notify"></notifications>
                    <div class="card-inner position-relative card-tools-toggle">
                        <div class="card-title-group">
                            <div class="card-tools">
                                <form @submit.prevent="loadLearners(1, true)">
                                    <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                        <div style="width: 200px !important" class="form-wrap">
                                            <select id="filterOLevelEducationGradeId" class="form-select-sm">
                                                <option value="">ALL CLASSES</option>
                                                <option v-for="education_grade in education_grades" :value="education_grade.id">{{ education_grade.name.toUpperCase() }}</option>
                                            </select>
                                        </div>
                                        <div style="width: 200px !important" class="form-wrap">
                                            <select id="filterOLevelGender" class="form-select-sm">
                                                <option value="">ALL GENDERS</option>
                                                <option value="M">MALE</option>
                                                <option value="F">FEMALE</option>
                                            </select>
                                        </div>
                                        <div style="width: 300px !important" class="form-wrap">
                                            <div class="input-group">
                                                <input v-model.trim="filter.lin" type="text" class="form-control text-uppercase" placeholder="LIN">
                                            </div>
                                        </div>
                                        <div style="width: 300px !important" class="form-wrap">
                                            <div class="input-group">
                                                <input v-model.trim="filter.name" type="text" class="form-control" placeholder="Learner Name">
                                                <div class="input-group-append">
                                                    <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                        <em class="icon ni ni-cross"></em>
                                                    </button>
                                                    <button class="btn rounded-right bg-dark-teal" type="submit">
                                                        <em class="icon ni ni-filter mr-1"></em>Apply
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div><!-- .card-tools -->
                        </div><!-- .card-title-group -->
                    </div><!-- .card-inner -->

                    <div class="card-inner p-0">
                        <div class="table-responsive">
                            <table class="nk-tb-list nk-tb-ulist is-compact">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head bg-secondary">
                                    <th class="nk-tb-col px-2 text-white border-white cursor text-uppercase border-1">
                                        <span class="sub-text ucap text-white">Learner</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white  text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">LIN</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Sex</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Class</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white cursor text-uppercase text-center border-1">
                                        <span class="sub-text ucap text-white">Index Number</span>
                                    </th>
                                    <th class="nk-tb-col px-1 align-middle text-white border-white border-right-0 text-uppercase border-1 text-center">
                                        <span class="sub-text ucap text-white">Action</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="border-bottom">
                                <tr v-for="candidate in candidates.data" class="nk-tb-item">
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="'/institution/learners/profile/'+candidate.learner.encrypted_lin" target="_blank" class="tb-lead cursor text-dark-teal">
                                            <div class="user-card">
                                                <div class="user-avatar">
                                                    <img :src="candidate.person.photo_url" style="border-radius: 0" :alt="candidate.person.initials">
                                                </div>
                                                <div class="user-name text-uppercase">
                                                    <span class="text-dark-teal">{{ candidate.person.full_name }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ candidate.learner.lin }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ candidate.person.gender }}</span>
                                    </td>

                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ candidate.education_grade.name.toUpperCase() }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <span class="text-dark tb-lead">{{ candidate.exam_level.name }} : {{ candidate.index_number }} - {{ candidate.exam_year }}</span>
                                    </td>
                                    <td class="nk-tb-col px-1 align-middle text-uppercase text-center">
                                        <a :href="'/institution/learners/profile/'+candidate.learner.encrypted_lin" target="_blank" data-toggle="tooltip" data-placement="top" title="View Details" class="cursor lead mr-1 text-primary">
                                            <em class="icon ni ni-eye-fill"></em>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div><!-- .card-inner -->

                    <div v-if="candidates && candidates.data && candidates.data.length === 0" class="card-inner p-0">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There is no data to display at the moment.
                            </div>
                        </div>
                    </div>

                    <div v-if="candidates.data.length" class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <li :class="[candidates.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="candidates.current_page > 1 ? loadLearners(1) : null" :class="[candidates.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <li :class="[candidates.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="candidates.current_page > 1 ? loadLearners(candidates.current_page-1) : null" :class="[candidates.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadLearners(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[candidates.current_page === candidates.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="candidates.current_page < candidates.last_page ? loadLearners(candidates.current_page+1) : null" :class="[candidates.current_page === candidates.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[candidates.current_page === candidates.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="candidates.current_page < candidates.last_page ? loadLearners(candidates.last_page) : null" :class="[candidates.current_page === candidates.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing <span class="text-primary">{{ candidates.from }}</span> to <span class="text-primary">{{ candidates.to }}</span> of <span class="text-primary">{{ candidates.total }}</span>
                            </span>
                        </div>
                    </div><!-- .card-inner -->

                    <!-- Transfer Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="registerCandidateModal">
                        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a @click="resetLearner()" class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <div class="modal-header">
                                    <h5 class="modal-title">Register Candidate</h5>
                                </div>

                                <form @submit.prevent="(index_no_verify && lin_verify) ? registerCandidate() : (lin_verify ? verifyUnebLearner() : verifyLearnerLIN())">
                                    <div class="modal-body">
                                        <div class="alert alert-info alert-icon">
                                            <em class="icon ni ni-alert-circle"></em>
                                            <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
                                            <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Enter LIN & Click Verify Button</h6>
                                            <h6 class="small mb-2"><span class="text-danger">Step 2:</span> Select Level</h6>
                                            <h6 class="small mb-2"><span class="text-danger">Step 3:</span> Select Exam Year</h6>
                                            <h6 class="small mb-2"><span class="text-danger">Step 4:</span> Enter Index Number</h6>
                                            <h6 class="small mb-2"><span class="text-danger">Step 5:</span> Verify UNEB Information</h6>
                                            <h6 class="small mb-2"><span class="text-danger">Step 6:</span> Click Submit to complete the process.</h6>
                                        </div>
                                        <error-notifications ref="notifyError"></error-notifications>
                                        <notifications ref="notifyVerify"></notifications>
                                        <div v-if="!lin_verify" :class="[!lin_verify ? 'mb-5' : '','row']">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="learner_lin">Learner Identification Number (LIN) <span class="text-danger">*</span></label>
                                                    <div class="form-control-wrap">
                                                        <input required v-model.trim="form_learner.lin" type="text" class="text-uppercase form-control bg-primary-dim text-center" placeholder="Enter Learner LIN" id="learner_lin" autocomplete="off">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div v-show="lin_verify" class="col-12 ml-2">
                                                <div class="table-responsive align-self-start">
                                                    <table class="table table-sm">
                                                        <thead>
                                                        <tr>
                                                            <th style="border: none">
                                                                <h6>EMIS Details</h6>
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr>
                                                            <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                                <div class="form-group d-flex flex-column justify-content-center">
                                                                    <div class="w-150px mx-auto">
                                                                        <img v-if="candidate.person.photo_url !== null" :src="candidate.person.photo_url" :alt="candidate.person.full_name" class="rounded-0">
                                                                        <img v-if="candidate.person.photo_url === null && candidate.person.gender === 'F'" src="@images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                                        <img v-if="candidate.person.photo_url === null && candidate.person.gender === 'M'" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                                <span class="">{{ candidate.person.full_name }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">LIN</h6>
                                                                <span class="">{{ candidate.lin }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">CURRENT CLASS</h6>
                                                                <span v-if="candidate.education_grade !== null" class="">{{ candidate.education_grade.name.toUpperCase() }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                <span v-if="candidate.person.gender === 'F'" class="">FEMALE</span>
                                                                <span v-if="candidate.person.gender === 'M'" class="">MALE</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                <span class="">{{ formatDate(candidate.person.birth_date) }}</span>
                                                            </td>
                                                        </tr>

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div v-show="index_no_verify && lin_verify" class="col-12 ml-2">
                                                <hr class="border-dark-teal my-1">
                                                <div class="table-responsive align-self-start">
                                                    <table class="table table-sm">
                                                        <thead>
                                                        <tr>
                                                            <th style="border: none">
                                                                <h6>UNEB Details</h6>
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr>
                                                            <td rowspan="8" class="px-0 align-middle text-dark text-uppercase w-220px">
                                                                <div class="form-group d-flex flex-column justify-content-center">
                                                                    <div class="w-150px mx-auto">
                                                                        <img v-if="candidate.person.photo_url !== null" :src="candidate.person.photo_url" :alt="candidate.person.full_name" class="rounded-0">
                                                                        <img v-if="candidate.person.photo_url === null && candidate.person.gender === 'F'" src="@images/default_female.jpg" class="rounded-0" alt="learner photo">
                                                                        <img v-if="candidate.person.photo_url === null && candidate.person.gender === 'M'" src="@images/default_male.jpg" class="rounded-0" alt="learner photo">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">Names</h6>
                                                                <span class="">{{ uneb_learner.name }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr v-if="school_type_id >= 3">
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">INDEX NUMBER</h6>
                                                                <span class="">{{ uneb_learner.index_number }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">EXAM YEAR</h6>
                                                                <span class="">{{ uneb_learner.exam_year }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">EXAM LEVEL</h6>
                                                                <span class="">{{ uneb_learner.exam_level }}</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                                <span v-if="uneb_learner.gender === 'F'" class="">FEMALE</span>
                                                                <span v-if="uneb_learner.gender === 'M'" class="">MALE</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">AGE</h6>
                                                                <span class="">{{ uneb_learner.age }}</span>
                                                            </td>
                                                        </tr>
                                                        <!-- <tr>
                                                            <td class="px-2 align-middle text-dark text-uppercase">
                                                                <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                                <span class="">{{ formatDate(uneb_learner.date_of_birth) }}</span>
                                                            </td>
                                                        </tr> -->

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div v-show="lin_verify" class="col-12">
                                                <hr class="border-dark-teal my-1">
                                                <div class="row">
                                                    <div class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learnerExamLevelId">Candidate Level <span class="text-danger">*</span></label>
                                                            <select v-model="form_learner.exam_level_id"   :disabled="index_no_verify && lin_verify" :required="!index_no_verify && lin_verify" id="learnerExamLevelId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option v-for="level in exam_levels" :value="level.id" :key="level.id" :disabled="shouldDisableOptionExamLevel(level.name)">{{ level.name }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learnerExamYearId">Candidate Exam Year <span class="text-danger">*</span></label>
                                                            <select :disabled="index_no_verify && lin_verify" :required="!index_no_verify && lin_verify" id="learnerExamYearId" class="form-select-sm">
                                                                <option value="">--SELECT--</option>
                                                                <option value="2024">2024</option>
<!--                                                                <option :value="currentYear">{{ currentYear }}</option>-->
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div v-show="school_type_id === 2"  class="col-lg-12 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learnerPleIndexNumber">PLE Index Number <span class="text-danger">*</span></label>
                                                            <div class="form-control-wrap">
                                                                <input :disabled="index_no_verify" :required="lin_verify && school_type_id === 2" v-model.trim="form_learner.index_number" minlength="10" maxlength="10" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="eg. 012345/012"  id="learnerPleIndexNumber" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-show="school_type_id === 3"  class="col-lg-12 mt-3">
                                                        <div class="form-group">
                                                            <label class="form-label" for="learnerUceIndexNumber">Index Number <span class="text-danger">*</span></label>
                                                            <div class="form-control-wrap">
                                                                <input :disabled="index_no_verify" :required="lin_verify && school_type_id === 3" v-model.trim="form_learner.index_number" minlength="9" maxlength="9" type="text" class="text-uppercase form-control bg-primary-dim" placeholder="eg. U0123/012"  id="learnerUceIndexNumber" autocomplete="off">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer" style="display: block">
                                        <div class="row">
                                            <div class="col-12 d-flex flex-lg-row justify-content-center">
                                                <button @click="resetLearner()" :disabled="loading" type="button" class="btn btn-dim btn-outline-light mr-2">
                                                    <span>Reset</span>
                                                    <em class="ni ni-cross ml-2"></em>
                                                </button>
                                                <button :disabled="loading" type="submit" class="btn bg-dark-teal d-flex">
                                                    <span v-if="loading && !lin_verify" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                                    <span v-if="loading && !lin_verify" class="align-self-center">Fetching Learner, Please wait...</span>
                                                    <span v-if="loading && !lin_verify" class="sr-only">Fetching Learner, Please wait...</span>
                                                    <span v-if="!loading && !lin_verify" class="align-self-center">Verify LIN</span>

                                                    <span v-if="loading && lin_verify && !index_no_verify" class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                                                    <span v-if="loading && lin_verify && !index_no_verify" class="align-self-center">Fetching Details, Please wait...</span>
                                                    <span v-if="loading && lin_verify && !index_no_verify" class="sr-only">Fetching Details, Please wait...</span>
                                                    <span v-if="!loading && lin_verify && !index_no_verify" class="align-self-center">Verify UNEB Details</span>

                                                    <span v-if="loading && lin_verify && index_no_verify" class="align-self-center">Submitting, Please wait...</span>
                                                    <span v-if="loading && lin_verify && index_no_verify" class="sr-only">Submitting, Please wait...</span>
                                                    <span v-if="!loading && lin_verify && index_no_verify" class="align-self-center">Submit</span>
                                                    <em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- /Transfer Modal -->
                </div><!-- .card-inner-group -->
            </div>
        </div>
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import moment from "moment";
import ErrorNotifications from "../../Notifications.vue";

export default {
    name: "CandidatesIndexNumbers",
    props: [
        "schoolTypeIdObj",
        "educationGradesObj",
        "examLevelsObj",
        "candidatesObj",
    ],
    components: {
        Notifications,
        ErrorNotifications
    },
    data: function () {
        return {
            api_url: '/institutions/learners/index-number',
            loading: false,
            index_no_verify: false,
            lin_verify: false,
            filtering: false,
            loading_message: '',
            //photoDropify: null,
            candidates: {
                data: [],
                links: [],
                total: 0,
            },
            candidate: {
                person_id: '',
                learner: {
                    lin: ''
                },
                education_grade: {
                    name: '',
                },
                exam_level: {
                    name: '',
                },
                person: {
                    full_name: '',
                    gender: 'M',
                    photo: null,
                    birth_date: moment().format("D MMMM, YYYY"),
                    country_id: 221,
                    country: {
                        name: ''
                    },
                },
            },
            exam_years: [],
            form_learner: {
                exam_level_id: '',
                index_number: '',
                exam_year: '',
                learner_id: '',
                current_education_grade_id: '',
                lin: '',
                primary_candidate: 'YES',
            },
            uneb_learner: {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                age: '',
                name: '',
                exam_level: '',
                exam_year: '',
            },
            school_type_id: '',
            education_grades: [],
            exam_levels: [],
            uganda: true,
            filter: {
                education_grade_id: '',
                gender: '',
                lin: '',
                name: '',
            },
        }
    },
    mounted() {
        this.initPlugins();
    },
    methods: {
        initPlugins: function () {
            let self = this;

            this.school_type_id = this.schoolTypeIdObj;
            this.candidates = this.candidatesObj;
            this.education_grades = this.educationGradesObj;
            this.exam_levels = this.examLevelsObj;

            $('#filterOLevelEducationGradeId').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.education_grade_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });
            $('#filterOLevelGender').select2({
                minimumResultsForSearch: 0,
                templateSelection: function (data, container) {
                    self.filter.gender = data.id;
                    return data.text;
                },
            });

            $('#learnerExamYearId').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#registerCandidateModal'),
                templateSelection: function (data, container) {
                    self.form_learner.exam_year = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            $('#learnerExamLevelId').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'bg-primary-dim',
                dropdownParent: $('#registerCandidateModal'),
                templateSelection: function (data, container) {
                    self.form_learner.exam_level_id = data.id !== "" ? Number(data.id) : data.id;
                    return data.text;
                },
            });

            window.setTimeout(()=>{
                $('#learnerExamYearId').val('').change();
                $('#learnerExamLevelId').val('').change();
            }, 50);
        },
        newRegistrationRequest: function () {
            $('#registerCandidateModal').modal({backdrop: "static"});
        },
        formatDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        /** Verify EMIS Learner Details **/
        verifyLearnerLIN: function () {
            let lin_format = /^[A-Z]{1}[0-9]{2}[FM][0-9]{4}[A-Z]{1}[0-9]{5}$/;
            if (lin_format.test(this.form_learner.lin.toUpperCase()) === false) {
                this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:"Wrong LIN format"});
            } else {
                this.loading = true;
                axios.get(this.api_url+'/verify/'+this.form_learner.lin.toLowerCase())
                    .then(response=>{
                        this.loading = false;
                        this.lin_verify = true;
                        this.candidate = response.data;
                        this.$refs.notifyVerify.messages.push({status: 'success', title: 'Success: ', message:"LIN Verified Successfully"});
                    })
                    .catch(error=>{
                        this.loading = false;
                        this.renderError(error);
                    });
            }
        },
        /** Verify UNEB Details **/
        verifyUnebLearner: function () {
            this.loading = true;
            const examTypeMap = {
                1: 'ple',
                2: 'uce',
                3: 'uace'
            };
            const examType = examTypeMap[this.form_learner.exam_level_id];

            if (examType) {
                const apiUrl = `/uneb/${examType}-candidate-info`;
                axios.post(apiUrl, {index_number: this.form_learner.index_number.toUpperCase(), exam_year: this.form_learner.exam_year})
                    .then(response => {
                        this.loading = false;
                        this.$parent.pending = false;
                        this.uneb_learner = response.data;
                        this.candidate.first_name = this.uneb_learner.first_name;
                        this.candidate.surname = this.uneb_learner.surname;
                        this.candidate.gender = this.uneb_learner.gender;
                        this.candidate.birth_date = this.uneb_learner.date_of_birth ? moment(this.uneb_learner.date_of_birth).format('D MMMM, YYYY').toUpperCase():'';
                        $("#learnerBirthDate").datepicker('update', moment(this.uneb_learner.date_of_birth).toDate())
                        //this.uganda = this.uneb_learner.nationality === "UGANDA";
                        // let ug = this.countries.find(c=>{
                        //     return c.name.toUpperCase() === 'UGANDA';
                        // })
                        // $('#learnerCountryId').val(ug.id).change();
                        this.index_no_verify = true;
                        this.$refs.notifyVerify.messages.push({status: 'success', title: 'Success: ', message:"UNEB Details Verified Successfully"});
                    })
                    .catch(error => {
                        this.loading = false;
                        this.renderError(error);
                    });
                } else {
                    console.error("Invalid exam level");
                }
        },
        /** Register Candidate **/
        registerCandidate: function () {
            let self = this;

            Swal.fire({
                title: 'Are you sure?',
                html: '<div style="margin-bottom: 10px;">' +
                    'You want to register this candidate?</div>',
                icon: 'warning',
                confirmButtonText: 'Yes, Proceed!',
                showCancelButton: true,
                cancelButtonColor: '#29384A',
                reverseButtons: true,

            }).then(function (result) {
                if (result.value) {
                    self.form_learner.current_education_grade_id = self.candidate.current_education_grade_id;
                    self.form_learner.learner_id = self.candidate.person_id;
                    axios.post(self.api_url+'/register-candidate', self.form_learner)
                        .then(response=>{
                            Swal.fire({
                                title: 'Success',
                                html: '<div style="margin-bottom: 10px;">' +
                                    'Candidate Registered Successfully.</div>',
                                icon: 'success',
                                confirmButtonText: 'Close',
                                showCancelButton: false,
                            })
                            self.candidates = response.data;
                            $('#registerCandidateModal').modal('hide');
                            self.resetLearner();
                        })
                        .catch(error=>{
                            self.loading = false;
                            self.renderError(error);
                        });
                }
            });
        },
        resetLearner: function () {
            this.loading = false;
            this.index_no_verify = false;
            this.lin_verify = false;
            this.filtering = false;
            this.edit = false;
            this.form_learner = {
                exam_level_id: '',
                index_number: '',
                exam_year: '',
                learner_id: '',
                current_education_grade_id: '',
            };
            this.uneb_learner = {
                id: '',
                nationality: '',
                centre_number: '',
                district: '',
                funding_type: '',
                index_number: '',
                gender: '',
                date_of_birth: '',
                name: '',
                exam_level: '',
                exam_year: '',
            };

            window.setTimeout(()=>{
                $('#learnerExamYearId').val('').change();
                $('#learnerExamLevelId').val('').change();
            }, 50);
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                education_grade_id: '',
                gender: '',
                lin: '',
                name: '',
            };
            $('#filterOLevelGender').val('').change();
            $('#filterOLevelEducationGradeId').val('').change();
            this.loadLearners(1, false);
        },
        loadLearners: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'/all-candidates/'+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.candidates = response.data;
                        this.filtering = filtering;
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        startLoading: function(message) {
            if (message.length) {
                this.loading_message = message;
                $.blockUI({
                    message: $('#loadingMessage'),
                    css: {
                        padding:0,
                        margin:0,
                        width:'30%',
                        top:'40%',
                        left:'35%',
                        textAlign:'center',
                        color:'#364a63',
                        wordWrap: 'break-word',
                        backgroundColor: '#fff',
                        backgroundClip: 'border-box',
                        border: '0 solid rgba(0, 0, 0, 0.125)',
                        borderRadius: '4px',
                        cursor:'wait'
                    },
                });
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
        },
        renderError: function (error) {
            const notifyError = (title, message) => {
                this.$refs.notifyError.messages.push({ status: "error", title, message });
            };

            if (error.response) {
                const { status, data } = error.response;
                switch (status) {
                    case 500:
                    case 405:
                        notifyError("System Error: ", data.message);
                        break;
                    case 401:
                        notifyError("Permission Error: ", "You are not authorised to perform this action");
                        break;
                    case 404:
                        notifyError("Resource Not Found: ", "You are trying to reach a URL that does not exist");
                        break;
                    case 422:
                        for (let field in data.errors) {
                            this.showError(data.errors[field]);
                        }
                        break;
                    default:
                        notifyError("Other Error: ", error.message);
                }
            } else {
                notifyError("Other Error: ", error.message);
            }
        },
    },
    computed: {
        // currentYear: function () {
        //     return Number(moment().format("YYYY"));
        // },

        getPaginationLinks: function () {
            let arr = this.candidates.links;
            arr.pop();
            arr.shift();
            return arr;
        },
        shouldDisableOptionExamLevel: function() {
            return (name) => {
                if (this.school_type_id === 2) {
                    // Disable UCE and UACE if school_type is primary
                    return name === 'UCE' || name === 'UACE';
                } else if (this.school_type_id === 3) {
                    // Disable PLE if school_type_id is secondary
                    return name === 'PLE';
                }
                return false;
            }
        }
    },
}
</script>

<style scoped>

</style>
