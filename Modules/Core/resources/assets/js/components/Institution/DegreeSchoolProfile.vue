<template>
    <div class="nk-block">
        <error-notifications ref="notifyError"></error-notifications>
        <success-notifications ref="notify"></success-notifications>
        <div class="card card-stretch card-bordered border-dark-teal">
            <div class="card-aside-wrap">
                <div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg"
                    data-content="userAside" data-toggle-screen="lg" data-toggle-overlay="true">
                    <div class="card-inner-group" data-simplebar>
                        <div class="card-inner">
                            <div class="user-card">
                                <div class="user-avatar w-100px" style="height: 1%">
                                    <img :src="school.logo_url" :alt="school.name + ' Logo'">
                                </div>
                                <div class="user-info">
                                    <span class="lead-text">{{ school.name }}</span>
                                    <span class="sub-text">{{ school.email }}</span>
                                </div>
                                <div class="user-action">
                                    <a data-toggle="modal" data-target="#updateLogoModal" data-backdrop="static"
                                        class="btn btn-round btn-icon btn-sm bg-dark-teal">
                                        <em class="icon ni ni-camera-fill"></em>
                                    </a>
                                </div>
                            </div><!-- .user-card -->
                        </div><!-- .card-inner -->
                        <div class="card-inner p-0">
                            <ul class="link-list-menu nav nav-tabs">
                                <li>
                                    <a data-toggle="tab" href="#basicInfo" class="active">
                                        <em class="icon ni ni-building-fill"></em><span>Institution
                                            Identification</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#courses">
                                        <em class="icon ni ni-code"></em><span>Courses</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#ownership">
                                        <em class="icon ni ni-downward-alt-fill"></em><span>Ownership</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#locationDetails">
                                        <em class="icon ni ni-location"></em><span>Location Details</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#operationDetails">
                                        <em class="icon ni ni-opt-alt-fill"></em><span>Operational Details</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#campuses">
                                        <em class="icon ni ni-home-fill"></em><span>Campuses</span>
                                    </a>
                                </li>
                                <li>
                                    <a data-toggle="tab" href="#healthInfo">
                                        <em class="icon ni ni-activity-round-fill"></em><span>Health information</span>
                                    </a>
                                </li>
                            </ul>
                        </div><!-- .card-inner -->
                    </div><!-- .card-inner-group -->
                </div><!-- card-aside -->
                <div class="card-inner card-inner-lg">
                    <div class="tab-content">
                        <div class="tab-pane active" id="basicInfo">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Institution Identification</h4>
                                        <div class="nk-block-des">
                                            <p>Basic school information.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <div class="nk-data data-list">
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Name Of Institution</span>
                                            <span class="data-value text-dark">{{ school.name }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">EMIS Number</span>
                                            <span class="data-value text-dark">{{ school.emis_number }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->

                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Postal Address</span>
                                            <span
                                                v-if="school.postal_address === null || school.postal_address === undefined"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{
                                                school.postal_address.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Institution Email Address</span>
                                            <span v-if="school.email === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.email }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Institution Telephone Contact</span>
                                            <span v-if="school.phone === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">+{{ school.phone }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Website</span>
                                            <span v-if="school.website === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.website }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div>
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Land Area</span>
                                            <span v-if="school.school_land_area === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.school_land_area }}
                                                Acres</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                </div><!-- data-list -->
                            </div><!-- .nk-block -->
                        </div><!-- .tab-pane -->


                        <div class="tab-pane" id="courses">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Manage ACMIS Courses</h4>
                                        <div class="nk-block-des">
                                            <p>Courses information.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <table class="table table-ulogs">
                                    <thead class="thead-light">
                                        <tr>
                                            <th class="tb-col-os"><span class="text-dark">Course Name</span></th>
                                            <th class="tb-col-ip text-center"><span class="text-dark">Code</span></th>
                                            <th class="tb-col-time text-center"><span class="text-dark">Duration In
                                                    Years</span></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="course in courses">
                                            <td class="tb-col-os">{{ course.name }}</td>
                                            <td class="tb-col-ip"><span class="sub-text text-dark text-center">{{
                                                course.code }}</span></td>
                                            <td class="tb-col-time"><span class="sub-text text-dark text-center">{{
                                                course.duration_in_years }}</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div><!-- .nk-block -->
                            <!-- owners -->
                        </div><!-- .tab-pane -->

                        <div class="tab-pane" id="ownership">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Ownership</h4>
                                        <div class="nk-block-des">
                                            <p>School ownership information.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <div class="nk-data data-list">
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Institution Type</span>
                                            <span v-if="school.degree_school.school_type === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{
                                                school.degree_school.school_type.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Ownership Status</span>
                                            <span v-if="school.ownership_status === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{
                                                school.ownership_status.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Founder</span>
                                            <span v-if="school.founding_body === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{
                                                school.founding_body.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Main Funding Source</span>
                                            <span v-if="school.funding_source === null"
                                                class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{
                                                school.funding_source.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Institution Owner</span>
                                            <span v-if="school.founding_body === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.founding_body.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Legal Ownership Status</span>
                                            <span class="data-value text-dark ucap" v-if="school.school_ownership_status_id === 1">GOVERNMENT</span>
                                            <span class="data-value text-dark ucap" v-else-if="school.legal_ownership_status !== null && school.school_ownership_status_id === 2">
                                                {{ school.legal_ownership_status.name.toUpperCase() }}
                                            </span>
                                            <span v-else class="text-muted font-italic">Not Set</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Year Institution Founded</span>
                                            <span v-if="school.year_founded === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.year_founded }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->


                                </div><!-- data-list -->
                            </div><!-- .nk-block -->
                            <!-- owners -->
                        </div><!-- .tab-pane -->
                        <div class="tab-pane" id="locationDetails">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">GPS Details</h4>
                                        <div class="nk-block-des">
                                            <p>Institution GPS Details.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <div class="nk-data data-list">
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">District</span>
                                            <span v-if="school.district === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.district.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">County/Municipality</span>
                                            <span v-if="school.county === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.county.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Sub-County/Division</span>
                                            <span v-if="school.sub_county === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.sub_county.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Parish/Ward</span>
                                            <span v-if="school.parish === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.parish.name.toUpperCase() }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Physical Address</span>
                                            <span v-if="school.physical_address === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.physical_address }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Latitude</span>
                                            <span v-if="school.latitude === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.latitude }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Longitude</span>
                                            <span v-if="school.longitude === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.longitude }}</span>
                                        </div>
                                        <div class="data-col data-col-end"></div>
                                    </div><!-- data-item -->
                                </div>
                                <div v-if="googleMapsApi.length" class="card-inner">
                                    <div id="map"></div>
                                </div>
                            </div>
                        </div><!-- .tab-pane -->
                        <!-- <div class="tab-pane" id="contactDetails"></div> --><!-- .tab-pane -->
                        <div class="tab-pane" id="operationDetails">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Operational Details</h4>
                                        <div class="nk-block-des">
                                            <p>Institution operational information.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <div class="nk-data data-list">
                                    <div class="data-item py-1" v-if="school.school_ownership_status_id === 2">
                                        <div class="data-col">
                                            <span class="data-label">Registration Status</span>
                                            <span class="data-value text-dark" v-if="school.registration_status_id === 1">REGISTERED</span>
                                            <span class="data-value text-dark" v-if="school.registration_status_id === 2">LICENSED</span>
                                            <span class="data-value text-dark" v-if="school.registration_status_id === 0">NOT LICENSED</span>
                                            <span v-if="school.registration_status_id === null" class="data-value text-muted font-italic">Not Set</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1"
                                        v-if="school.registration_status_id === 1 && school.school_ownership_status_id === 2">
                                        <div class="data-col">
                                            <span class="data-label">Registration Number</span>
                                            <span class="data-value text-dark" v-if="school.registration_number !== null">{{ school.registration_number }}</span>
                                            <span v-else class="data-value text-muted font-italic">Not Set</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1"
                                        v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                                        <div class="data-col">
                                            <span class="data-label">License Number</span>
                                            <span class="data-value text-dark" v-if="school.license_number !== null">{{ school.license_number }}</span>
                                            <span v-else class="data-value text-muted font-italic">Not Set</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1"
                                        v-if="school.registration_status_id === 2 && school.school_ownership_status_id === 2">
                                        <div class="data-col">
                                            <span class="data-label">License No. Expiry Date</span>
                                            <span class="data-value text-dark" v-if="school.license_number !== null">{{ formatDate(school.licence_certificate_expiry_date) }}</span>
                                            <span v-else class="data-value text-muted font-italic">Not Set</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->

                                    <div v-show="school.school_ownership_status_id === 1" class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Supply Number</span>
                                            <span v-if="school.degree_school.supply_number === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">{{ school.degree_school.supply_number }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Center Number</span>
                                            <span v-if="school.center_number === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark ucap">{{ school.center_number }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Highest Award</span>
                                            <span class="data-value text-dark ucap">DEGREE</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                    <div class="data-item py-1" v-show="school.school_ownership_status_id === 2">
                                        <div class="data-col">
                                            <span class="data-label">Capital For Establishment</span>
                                            <span v-if="school.capital_for_establishment === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark">UGX {{ formatMoney(school.capital_for_establishment) }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div>
                                    <!-- data-item -->
                                    <div class="data-item py-1">
                                        <div class="data-col">
                                            <span class="data-label">Distance to the nearest Health facility</span>
                                            <span v-if="school.health_facility_distance === null" class="data-value text-muted font-italic">Not Set</span>
                                            <span v-else class="data-value text-dark ucap">{{ school.health_facility_distance.name }}</span>
                                        </div>
                                        <div class="data-col w-15 data-col-end"></div>
                                    </div><!-- data-item -->
                                </div><!-- data-list -->
                            </div><!-- .nk-block -->
                        </div><!-- .tab-pane -->

                        <!-- campuses -->
                        <div class="tab-pane" id="campuses">
                            <div class="nk-block-head nk-block-head-lg">
                                <div class="nk-block-between">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Manage ACMIS Campuses</h4>
                                        <div class="nk-block-des">
                                            <p>Institution attached campuses information.</p>
                                        </div>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a href="#" class="toggle btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div><!-- .nk-block-head -->
                            <div class="nk-block">
                                <success-notifications ref="notifySuccessCampuses"></success-notifications>
                                <table class="table border border-dark">
                                    <thead class="bg-secondary">
                                        <tr>
                                            <th class="text-white align-middle text-uppercase">Name</th>
                                            <th class="text-white align-middle text-uppercase text-center border-left border-white">District</th>
                                            <th class="text-white align-middle text-uppercase text-center border-left border-white">County</th>
                                            <th class="text-white align-middle text-uppercase text-center border-left border-white">Sub County</th>
                                            <th class="text-white align-middle text-uppercase text-center border-left border-white">Parish</th>
                                        </tr>
                                    </thead>
                                    <tbody class="border-top-0 border-secondary">
                                        <tr v-for="campus in campuses">
                                            <td class="align-middle border-left border-secondary ucap text-dark">{{ campus.name }}</td>
                                            <td class="align-middle border-left border-secondary text-center">
                                                <span v-if="campus.parish !== null" class="text-dark text-uppercase">{{ campus.parish.subcounty.county.district.name }}</span>
                                                <span v-else class="text-muted font-italic text-uppercase">NOT SET</span>
                                            </td>
                                            <td class="align-middle border-left border-secondary text-center">
                                                <span v-if="campus.parish !== null" class="text-dark text-uppercase">{{ campus.parish.subcounty.county.name }}</span>
                                                <span v-else class="text-muted font-italic text-uppercase">NOT SET</span>
                                            </td>
                                            <td class="align-middle border-left border-secondary text-center">
                                                <span v-if="campus.parish !== null" class="text-dark text-uppercase">{{ campus.parish.subcounty.name }}</span>
                                                <span v-else class="text-muted font-italic text-uppercase">NOT SET</span>
                                            </td>
                                            <td class="align-middle border-left border-secondary text-center">
                                                <span v-if="campus.parish !== null" class="text-dark text-uppercase">{{ campus.parish.name }}</span>
                                                <span v-else class="text-muted font-italic text-uppercase">NOT SET</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div v-if="!campuses.length" class="card card-stretch" style="box-shadow: none;">
                                    <div class="card-inner-group">
                                        <div class="card-body">
                                            <div class="my-4 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                                <em class="icon ni ni-alert-circle"></em> There is no campus/branch
                                                information to display at the moment.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- .nk-block -->
                        </div><!-- .tab-pane -->

                        <div class="tab-pane" id="healthInfo">
                            <div class="nk-block-between">
                                <div class="nk-block-head-content d-flex justify-content-between w-100">
                                    <div class="nk-block-head-content">
                                        <h4 class="nk-block-title">Health Information</h4>
                                    </div>
                                    <div class="nk-block-head-content align-self-start d-lg-none">
                                        <a class="toggle cursor btn btn-icon btn-trigger mt-n1"
                                            data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a>
                                    </div>
                                </div>
                            </div>
                            <!-- sexuality education policy -->
                            <div class="nk-data data-list">
                                <div class="data-item py-1">
                                    <div class="data-col">
                                        <span class="data-label">Does this institution have/own a Health Facility within?</span>
                                        <span v-if="school.health_facility_distance !== null" class="pl-lg-5 ml-lg-5 data-value text-dark">Yes</span>
                                        <span v-else class="pl-lg-5 ml-lg-5 data-value text-dark">No</span>
                                    </div>
                                    <div class="data-col data-col-end">
                                    </div>
                                </div><!-- data-item -->
                                <div class="data-item py-1" v-show="school.health_facility_distance !== null">
                                    <div class="data-col">
                                        <span class="data-label">Distance to the nearest Health facility</span>
                                        <span v-if="school.health_facility_distance === null" class="data-value text-muted font-italic">Not Set</span>
                                        <span v-else class="data-value text-dark ucap">{{ school.health_facility_distance.name }}</span>
                                    </div>

                                </div><!-- data-item -->
                            </div>


                        </div>

                    </div><!-- .tab-content -->
                </div>
            </div><!-- .card-aside-wrap -->
        </div><!-- .card -->

        <!-- Logo Update Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateLogoModal">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <form @submit.prevent="updateLogo()">
                    <div class="modal-content">
                        <a @click="resetLogo()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Logo</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="align-self-center form-group">
                                        <input id="schoolLogo" 
                                            ref="logo" 
                                            @change="selectFile"
                                            accept="image/png,image/jpeg" 
                                            data-max-file-size="2M" 
                                            type="file"
                                            class="dropify" 
                                            data-height="180"
                                            data-allowed-file-extensions="jpeg jpg png"
                                            data-default-file="/images/school-logos/logo.png" />
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <span @click="school.logo === '' ? seletLogo() : resetLogo()"
                                        class="btn btn-block bg-dark-teal">
                                        <em class="icon ni ni-camera-fill mr-1"></em>
                                        <span v-if="school.logo === ''">Select Logo</span>
                                        <span v-else>Remove Logo</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetLogo()" :disabled="loading" type="button" data-dismiss="modal"
                                class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Updating...</span>
                                <span v-if="loading" class="sr-only">Updating...</span>
                                <span v-if="!loading" class="align-self-center">Update</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- Logo Update Modal -->

        <!-- Loading Modal -->
        <div style="display:none;" id="schoolLoadingMessage" class="card card-preview">
            <div class="card-inner">
                <div class="d-flex align-items-center">
                    <strong>Deleting...</strong>
                    <div class="spinner-border ml-auto" role="status" aria-hidden="true"></div>
                </div>
            </div>
        </div>
        <!-- /Loading Modal -->


        <!-- Update Campuses Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateCampusesModal">
            <form @submit.prevent="updateCampuses()">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a @click="resetCampuses()" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Update Branch/Campus</h5>
                        </div>
                        <div class="modal-body overflow-auto scrollbar-dark-teal" data-simplebar data-simplebar-auto-hide="false">
                            
                            <!-- Campus Name -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusName">Campus Name</label>
                                        <span class="form-note">Specify Branch Name</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="form_campus.name" placeholder="Enter Campus Name" id="campusName" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 align-center">
                                <div class="col-lg-6 mt-1">
                                    <div class="form-group">
                                        <label class="form-label" for="campusPhone">Campus Telephone Contact</label>
                                        <span class="form-note">Specify Branch Phone Number</span>
                                    </div>
                                </div>
                                <div class="col-lg-6 mt-1">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input v-model.trim="form_campus.phone" id="campusPhone" type="text" maxlength="10" class="form-control bg-primary-dim" autocomplete="off" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Campus Name -->

                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusDistrictId">District</label>
                                        <span class="form-note">Specify Institution's district.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusDistrictId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                            <option v-for="district in districts" :value="district.id">{{ district.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusCountyId">County/Municipality</label>
                                        <span class="form-note">Specify Institution's county/municipality.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusCountyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusSubCountyId">Sub County/Division</label>
                                        <span class="form-note">Specify Institution's sub county/division.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusSubCountyId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="campusParishId">Parish/Ward</label>
                                        <span class="form-note">Specify Institution's parish/town council.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <select required id="campusParishId" class="form-select-sm">
                                            <option value="">--Select--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button @click="resetCampuses()" :disabled="loading" type="button" data-dismiss="modal" class="btn btn-light ml-2"><em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button :disabled="loading" type="submit" class="btn btn-primary d-flex">
                                <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span v-if="loading" class="align-self-center">Saving...</span>
                                <span v-if="loading" class="sr-only">Saving...</span>
                                <span v-if="!loading" class="align-self-center">Save</span><em v-if="!loading" class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- /Update Campuses Modal -->
    </div><!-- .nk-block -->
</template>

<script>
import { Loader } from "@googlemaps/js-api-loader";
import ErrorNotifications from "../Notifications.vue";
import SuccessNotifications from "../Notifications.vue";
import { campusLoadLocationMixin } from "../../mixins/CampusLoadLocationMixin";
export default {
    name: "DegreeSchoolProfile",
    props: [
        'schoolObj',
        'googleMapsApi',
        'coursesObj',
        'campusesObj'
    ],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mixins: [campusLoadLocationMixin],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            apiUrl: '/institutions/degree-schools',
            loading: false,
            edit: false,
            api_url: '/institutions/campuses',
            valid_file: false,
            logoDropify: null,
            map: null,
            marker: null,
            courses: [],
            campuses: [],
            form_campus: {
                id: '',
                name: '',
                phone: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
            },
            school: {
                logo: '',
                district_id: '',
                county_id: '',
                sub_county_id: '',
                parish_id: '',
                examining_body_id: '',
                registration_status_id: '',
                registration_body_id: '',
                award_type_id: '',
                school_ownership_status_id: '',
                tertiary_institution_type_id: '',
                degree_school: {
                    authority: { name: '' },
                    school_type: {
                        name: '',
                        institution_category: {
                            name: '',
                            examining_body: { acronym: '' },
                            registering_body: {
                                name: ''
                            }
                        },
                    },
                    campuses: [],
                    registering_body_id: '',
                    supply_number: '',
                },
                district: {
                    name: '',
                },
                parish: {
                    name: '',
                },
                sub_county: {
                    name: '',
                },
                county: {
                    name: '',
                },
                founding_body: {
                    name: '',
                },
                funding_source: {
                    name: '',
                },
                examining_bodies: [],
                examining_body: {
                    name: '',
                },
                ownership_status: {
                    name: '',
                },
                legal_ownership_status: {
                    name: '',
                },
                operational_status: {
                    name: '',
                },
                registration_status: {
                    name: '',
                },
                school_gender_category: {
                    name: '',
                },
                health_facility_distance: {
                    name: '',
                },
                owners: [],
            }
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;

            $('#campusDistrictId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.district_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadCounties();
                    return data.text;
                },
            });

            $('#campusCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadSubCounties();
                    return data.text;
                },
            });

            $('#campusSubCountyId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.sub_county_id = data.id.length > 0 ? Number(data.id) : "";
                    self.loadParishes();
                    return data.text;
                },
            });

            $('#campusParishId').select2({
                minimumResultsForSearch: 0,
                dropdownParent: $('#updateCampusesModal'),
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form_campus.parish_id = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            let school_phone = document.querySelector('#campusPhone');
            let iti_school_phone = intlTelInput(school_phone, {
                initialCountry: 'ug',
                separateDialCode: true,
                preferredCountries: ['ug', 'ke', 'tz', 'rw', 'bi', 'ss'],
                customPlaceholder: function (selectedCountryPlaceholder, selectedCountryData) {
                    return 'eg. ' + selectedCountryPlaceholder;
                },
            });
            school_phone.addEventListener('blur', () => {
                self.form_campus.phone = iti_school_phone.getNumber().slice(-9);
                school_phone.value = iti_school_phone.getNumber().slice(-9);
            });
            school_phone.addEventListener('change', () => {
                self.form_campus.phone = iti_school_phone.getNumber().slice(-9);
                school_phone.value = iti_school_phone.getNumber().slice(-9);
            });

            this.logoDropify = $('#schoolLogo').dropify({
                messages: {
                    'default': '',
                    'replace': 'Click to replace',
                    'remove': 'Remove',
                    'error': 'Ooops, something wrong appended.'
                },
                error: {
                    'fileSize': 'The file size is too big (2MB max).'
                }
            });

            this.logoDropify.on('dropify.afterClear', function (event, element) {
                self.school.logo = '';
                self.$refs.logo.value = null;
            });

            $('.dropify-clear').click(() => {
                this.resetLogo();
            });

            this.school = this.schoolObj;
            this.courses = JSON.parse(this.coursesObj);
            this.campuses = JSON.parse(this.campusesObj);

            this.resetLogo();
            this.resetCampuses();
        },
        formatDate: function (raw_date) {
            return moment(raw_date).format("D MMMM, YYYY");
        },
        formatMoney: function (raw_money = '') {
            if (typeof raw_money === "string" && raw_money.length) {
                return moneyFormat.to(moneyFormat.from(raw_money.replace(/[^0-9]/g, "")));
            } else if (typeof raw_money === "number") {
                return moneyFormat.to(moneyFormat.from(raw_money.toString().replace(/[^0-9]/g, "")));
            }
            return null
        },
        selectFile() {
            this.school.logo = this.$refs.logo.files[0];
        },
        seletLogo() {
            $('.dropify').click();
        },
        updateLogo: function () {
            let formData = new FormData();
            this.loading = true;
            formData.append('logo', this.school.logo);
            axios.post(this.apiUrl + '/update-logo', formData, { headers: { 'Content-Type': 'multipart/form-data' } })
                .then(response => {
                    this.school.logo_url = response.data.logo_url;
                    this.school.logo = response.data.logo;
                    $('#updateLogoModal').modal('hide');
                    this.resetLogo();
                    this.$refs.notify.messages.push({status: 'success', title: 'Success', message:" Logo updated successfully"});
                })
                .catch(error => {
                    console.log(error)
                    this.loading = false;
                    if (error.response.data.errors !== undefined) {
                        for (let field in error.response.data.errors) {
                            this.showError(error.response.data.errors[field]);
                        }
                    } else {
                        this.renderError(error)
                    }
                });
        },
        resetLogo: function () {
            let filedropper = this.logoDropify.data('dropify');
            filedropper.resetPreview();
            filedropper.clearElement();
            filedropper.settings['defaultFile'] = this.school.logo_url;
            filedropper.destroy();
            filedropper.init();
            this.loading = false;
        },
        downloadLetter: function () {
            this.loading = true;
            try {
                axios({
                    method: 'get',
                    url: '/institution/emis-number-certificates/' + this.school.emis_number,
                    responseType: 'arraybuffer'
                })
                    .then(response => {
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        console.log(blob);
                        let a = window.document.createElement('a');
                        a.href = window.URL.createObjectURL(blob, {
                            type: 'data:application/pdf'
                        })
                        a.download = this.school.emis_number + '-emis-number-certificate.pdf';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(a.href);
                        document.body.removeChild(a);
                    })
                    .catch(error => {
                        // Handle error
                        this.renderError(error)
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            } catch (error) {
                // Handle error
                this.renderError(error)
            }
        },
        startLoading: function () {
            $.blockUI({
                message: $('#schoolLoadingMessage'),
                css: {
                    padding: 0,
                    margin: 0,
                    width: '30%',
                    top: '40%',
                    left: '35%',
                    textAlign: 'center',
                    color: '#364a63',
                    wordWrap: 'break-word',
                    backgroundColor: '#fff',
                    backgroundClip: 'border-box',
                    border: '0 solid rgba(0, 0, 0, 0.125)',
                    borderRadius: '4px',
                    cursor: 'wait'
                },
            });
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error: ', message:text});
        },
        renderError: function (error) {
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notifyError.messages.push({ status: 'error', title: 'System Error:', message: error.response.data.message });
            } else if (error.response && error.response.status === 401) {
                this.$refs.notifyError.messages.push({ status: 'error', title: 'Permission Error:', message: 'You are not authorised to perform this action' });
            } else if (error.response && error.response.status === 404) {
                this.$refs.notifyError.messages.push({ status: 'error', title: 'Resource Not Found:', message: 'You are trying to reach a url that does not exist' });
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notifyError.messages.push({ status: 'error', title: 'Other Error:', message: error.message });
            }
        },
    },
    computed: {}
}
</script>

<style scoped>
#map {
    height: 400px;
}
</style>
