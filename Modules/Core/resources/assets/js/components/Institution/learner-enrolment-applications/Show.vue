<template>
    <div class="w-100">
        <div class="nk-block-head-sub">
            <a class="back-to" :href="'/institution/learner-pre-registration-applications/'+applicationStatus.toLowerCase()"><em class="icon ni ni-arrow-left"></em><span>Back</span></a>
        </div>
        <nav class="nk-block-des mb-1">
            <ul class="breadcrumb breadcrumb-arrow">
                <li class="breadcrumb-item">
                    <a href="/institution/dashboard" class="text-primary">Dashboard</a>
                </li>
                <li class="breadcrumb-item">
                    <a :href="'/institution/learner-pre-registration-applications/'+applicationStatus.toLowerCase()" class="text-primary">
                        {{ applicationStatus }} Leaner Pre-Registration Applications
                    </a>
                </li>
                <li class="breadcrumb-item active text-soft">Application Details</li>
            </ul>
        </nav>
        <div class="nk-block">
            <div class="card card-stretch">
                <notifications ref="notify"></notifications>
                <div class="card-inner-group">
                    <div class="card-body card-bordered border-dark-teal">
                        <div class="card-inner card-inner-xl" style="padding: 3%">
                            <div class="entry">
                                <h4>Learner Pre-Registration Application Details</h4>
                                <button type="button" class="btn btn-sm btn-dark-teal float-right"><em class="icon ni ni-edit"></em><span class="mr-2">Edit</span></button>
                                <p>Below are the application details <span v-if="application.is_draft_yn" class="badge badge-gray">In Draft</span></p>
                                <h6 class="style-head"><span class="text-gray mr-2">Application ID:</span>#{{ application.application_number }}</h6>
                                <h6 class="style-head"><span class="text-gray mr-2">Date Submitted:</span>{{ formatApplicationDate(application) }}</h6>
                                <h6 class="style-head"><span class="text-gray mr-2">Approval Status:</span>
                                    <span v-if="application.approval_status === 'pending'" class="text-uppercase badge badge-primary">Pending</span>
                                    <span v-if="application.approval_status === 'approved'" class="text-uppercase badge badge-dark-teal">Approved</span>
                                    <span v-if="application.approval_status === 'rejected'" class="text-uppercase badge badge-danger">Rejected</span>
                                </h6>
                                <h6 v-if="application.date_approved !== null" class="style-head">
                                    <span class="text-gray mr-2">Approved By:</span>{{ application.moes_staff.full_name }} | MoES
                                </h6>
                                <h6 v-if="application.date_approved !== null" class="style-head"><span class="text-gray mr-2">Date Approved:</span>{{ formatApprovalDate(application.date_approved) }}</h6>
                                <div class="nk-divider divider md"></div>
                                <div class="preview-block">
                                    <div class="row gy-4">
                                        <div class="col-lg-5">
                                            <span class="preview-title-lg overline-title">Learner Details</span>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Name:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.full_name }}</h6>
                                                </div>
                                            </div>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.gender === 'M'">MALE</h6>
                                                    <h6 v-if="application.gender === 'F'">FEMALE</h6>
                                                </div>
                                            </div>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Date Of Birth:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head">{{ formatBirthDate(application.birth_date) }}</h6>
                                                </div>
                                            </div>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Country:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.country ? application.country.name : '' }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.nin !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">NIN:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.nin }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.student_pass !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Student Pass:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.student_pass }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.student_refugee_number !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Refugee Number:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.student_refugee_number }}</h6>
                                                </div>
                                            </div>
                                            <div class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Class:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.education_grade ? application.education_grade.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.district_of_birth_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">District Of Birth:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.district_of_birth ? application.district_of_birth.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.index_number !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Index Number:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.index_number }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.exam_year !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Exam Year:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.exam_year }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.equated_code !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Equated Code:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.equated_code }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.equated_year !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Equated Year:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.equated_year }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.exam_level !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Exam Level:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.exam_level }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.code_type !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Code Type:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.code_type }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="(application.post_primary_institution_course_id && application.post_primary_institution_course) || (application.institution_examined_course_id && application.institution_examined_course)" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Course:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">
                                                        {{ application.post_primary_institution_course ? application.post_primary_institution_course.name : (application.institution_examined_course ? application.institution_examined_course.name : '') }}
                                                    </h6>
                                                </div>
                                            </div>
                                            <div v-if="application.orphan_type !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Orphanage Status:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 v-if="application.orphan_type === 'father'" class="style-head text-uppercase">ONLY FATHER DEAD</h6>
                                                    <h6 v-if="application.orphan_type === 'mother'" class="style-head text-uppercase">ONLY MOTHER DEAD</h6>
                                                    <h6 v-if="application.orphan_type === 'both-dead'" class="style-head text-uppercase">BOTH PARENTS DEAD</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.familiar_language_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Familiar Language:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.familiar_language ? application.familiar_language.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.inter_sch_calendar_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Calendar:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.international_calendar ? application.international_calendar.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.inter_sch_curriculum_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Curriculum:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.international_curriculum ? application.international_curriculum.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.inter_sch_education_grade_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Grade:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.international_education_grade ? application.international_education_grade.name : null }}</h6>
                                                </div>
                                            </div>
                                            <div v-if="application.inter_sch_education_level_id !== null" class="row mt-1">
                                                <div class="col-lg-4">
                                                    <h6 class="style-head"><span class="text-gray">Level:</span></h6>
                                                </div>
                                                <div class="col-lg-8">
                                                    <h6 class="style-head text-uppercase">{{ application.district_of_birth ? application.district_of_birth.name : null }}</h6>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="col-lg-7">
                                            <span class="preview-title-lg overline-title">Parent Details</span>
                                            <div class="row mt-1">
                                                <div class="col-sm-8">
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Names:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6 class="style-head ucap">{{ application.parent_full_name }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 v-if="application.parent_nin !== null" class="style-head"><span class="text-gray">NIN:</span></h6>
                                                            <h6 v-if="application.parent_passport !== null" class="style-head"><span class="text-gray">Passport No:</span></h6>
                                                            <h6 v-if="application.parent_refugee_number !== null" class="style-head"><span class="text-gray">Refugee No:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6 v-if="application.parent_nin !== null" class="style-head ucap">{{ application.parent_nin }}</h6>
                                                            <h6 v-if="application.parent_passport !== null" class="style-head ucap">{{ application.parent_passport }}</h6>
                                                            <h6 v-if="application.parent_refugee_number !== null" class="style-head ucap">{{ application.parent_refugee_number }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Sex:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6 v-if="application.parent_gender === 'M'">MALE</h6>
                                                            <h6 v-if="application.parent_gender === 'F'">FEMALE</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Relationship:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6 class="text-uppercase">{{ application.parent_relationship }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Country:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6 v-if="application.parent_country !== undefined">{{ application.parent_country.name }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Email:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6>{{ application.parent_email }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Phone No.1:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6>{{ application.parent_phone_1 }}</h6>
                                                        </div>
                                                    </div>
                                                    <div class="row mt-1">
                                                        <div class="col-lg-4">
                                                            <h6 class="style-head"><span class="text-gray">Phone No.2:</span></h6>
                                                        </div>
                                                        <div class="col-lg-8">
                                                            <h6>{{ application.parent_phone_2 }}</h6>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-4">
                                                    <div class="row mt-1">
                                                        <div class="col-lg-8">
                                                            <div class="d-flex justify-content-start">
                                                                <div class="user-avatar lg sq bg-white justify-content-start">
                                                                    <img class="py-3 rounded-0" :src="application.parent_photo" :alt="application.parent_full_name">
                                                                    <img class="rounded-0" src="@images/default_male.jpg" alt="Parent photo">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
<!--                                To show duplicate records-->
                            </div>
                            <div v-if="application.date_approved !== null" class="nk-divider divider md"></div>
                            <div v-if="application.date_approved !== null && application.reject_reason !== null" class="nk-block">
                                <div class="nk-block-head nk-block-head-sm nk-block-between">
                                    <h5 class="title">Reject Reason</h5>
                                </div><!-- .nk-block-head -->
                                <div class="bq-note">
                                    <div class="bq-note-item">
                                        <div class="bq-note-text">
                                            <p>{{ application.reject_reason }}</p>
                                        </div>
                                    </div><!-- .bq-note-item -->
                                </div><!-- .bq-note -->
                            </div>
                        </div><!-- .card-inner -->
                    </div>
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
export default {
    name: "Show",
    props: ['applicationObj', 'schoolsObj'],
    components: {Notifications},
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            application: {
                deo: {
                    full_name: '',
                },
                moes_staff: {
                    full_name: '',
                    photo_url: '',
                },
            },
        }
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.application = this.applicationObj;
            this.schools = this.schoolsObj;
        },
        formatApplicationDate: function (application) {
            if (application.date_created === null) {
                return moment(application.date_updated).format("D MMMM, YYYY hh:mma");
            } else {
                return moment(application.date_created).format("D MMMM, YYYY hh:mma");
            }
        },
        formatApprovalDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        formatBirthDate: function (date) {
            if (date === null) {
                return null;
            }
            return moment(date).format("D MMMM, YYYY")
        },
        renderError: function (error) {
            const notifyError = (title, message) => {
                const target = this.reasoning ? this.$refs.modalNotify : this.$refs.notify;
                target.messages.push({ status: 'error', title, message });
                $("html, body").animate({ scrollTop: 0 }, "slow");
            };

            if (error.response) {
                const { status, data } = error.response;
                switch (status) {
                    case 500:
                    case 405:
                        notifyError('System Error:', data.message);
                        break;
                    case 401:
                        notifyError('Permission Error:', 'You are not authorised to perform this action');
                        break;
                    case 404:
                        notifyError('Resource Not Found:', 'You are trying to reach a URL that does not exist');
                        break;
                    case 422:
                        for (let field in data.errors) {
                            this.showError(data.errors[field]);
                        }
                        break;
                    default:
                        notifyError('Other Error:', error.message);
                }
            } else {
                notifyError('Other Error:', error.message);
            }
        },
        showError: function (message) {
            let text = message.join('<br>');
            const notifyDataError = (title, message) => {
                const target = this.reasoning ? this.$refs.modalNotify : this.$refs.notify;
                target.messages.push({ status: 'error', title, message });
                $("html, body").animate({ scrollTop: 0 }, "slow");
            };
            notifyDataError('Data Error!', text);
        },
    },
    computed: {
        deoTitle() {
            const lg = this.application.local_government;

            if (!lg) return null;

            const name = lg.name?.toUpperCase() || '';
            const type = lg.local_gov_type;

            if (name.endsWith(" CITY") || type === 2) {
                return "CEO";
            }

            if (name.endsWith(" MUNICIPALITY") || type === 3) {
                return "MEO";
            }

            return "DEO";
        },
        applicationStatus: function () {
            if (this.application.approval_status === 'rejected') {
                return 'Rejected';
            }

            if (this.application.approval_status === 'approved') {
                return 'Approved';
            }
            return 'Pending';
        }
    }
}
</script>
