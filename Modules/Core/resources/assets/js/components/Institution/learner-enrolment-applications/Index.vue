<template>
    <div class="w-100">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">{{ typeObj }} Learner Pre-Registration Applications</h3>
                    <div class="nk-block-des text-soft">
                        <p>You have a total of {{ applications.total+' applications' }}</p>
                    </div>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a href="#" class="btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch card-bordered border-dark-teal">
                <notifications ref="notify"></notifications>
                <div class="card-inner-group">
                    <div class="row card-inner">
                        <div class="col-lg-12">
                            <form @submit.prevent="loadApplications(1, true)">
                                <div class="row">
                                    <div class="col-lg-2">
                                        <div class="form-control-wrap">
                                            <select id="filterSex" class="form-select form-control form-control-xl">
                                                <option value="all" selected disabled>ALL</option>
                                                <option value="M">MALE</option>
                                                <option value="F">FEMALE</option>
                                            </select>
                                            <label class="form-label-outlined emis-label" for="filterSex">SELECT SEX</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="form-wrap">
                                            <div class="input-group">
                                                <input v-model.trim="filter.application_number" type="text" class="form-control text-uppercase border-dark-teal" :placeholder="`SEARCH APPLICATION NUMBER`">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="form-wrap">
                                            <div class="input-group">
                                                <input v-model.trim="filter.search_term" type="text" class="form-control text-uppercase border-dark-teal" :placeholder="`SEARCH LEARNER NAME`">
                                                <div class="input-group-append">
                                                    <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                        <em class="icon ni ni-cross"></em>
                                                    </button>
                                                    <button class="btn rounded-right bg-dark-teal" type="submit">
                                                        <em class="icon ni ni-filter mr-1"></em>Apply
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-2 offset-lg-2"></div>
                    </div>
                    <div class="card-inner p-0">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleAllApplications()" v-model="select_all_applications"  type="checkbox" class="custom-control-input" id="uid">
                                        <label class="custom-control-label" for="uid"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col"><span class="sub-text text-white">APPLICATION NUMBER</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">LEARNER</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">SEX</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">CLASS</span></div>
                                <div class="nk-tb-col"><span class="sub-text text-white">DATE SUBMITTED</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-white">APPROVAL STATUS</span></div>
                                <div class="nk-tb-col text-center"><span class="sub-text text-white">ACTIONS</span></div>
                            </div><!-- .nk-tb-item -->
                            <div v-for="application in applications.data" class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div v-if="application.active !== 0" class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleOneApplication()" v-model="selected_applications" :value="application.id" type="checkbox" class="custom-control-input" :id="'uid'+application.id">
                                        <label class="custom-control-label" :for="'uid'+application.id"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <a class="text-dark-teal" :href="'/institution/learner-pre-registration-applications/'+typeObj.toLowerCase()+'/'+application.application_number.toLowerCase()">
                                        <span class="d-block">{{ application.application_number }}</span>
                                    </a>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block text-uppercase">{{ application.full_name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block text-uppercase">{{ application.gender }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block text-uppercase">{{ application.education_grade.name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block">{{ formatDate(application) }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span v-if="application.approval_status === 'pending'" class="text-uppercase badge badge-primary">Pending</span>
                                    <span v-if="application.approval_status === 'approved'" class="text-uppercase badge badge-dark-teal">Approved</span>
                                    <span v-if="application.approval_status === 'rejected'" class="text-uppercase badge badge-danger">Rejected</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <a data-toggle="tooltip" data-placement="top" title="View Application" :href="'/institution/learner-pre-registration-applications/'+typeObj.toLowerCase()+'/'+application.application_number.toLowerCase()">
                                        <em class="icon ni ni-eye-fill"></em>
                                    </a>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                        <div v-if="!applications.data.length" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No applications to display at the moment...
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div class="card-inner d-flex flex-row justify-content-between">
                        <nav>
                            <ul class="pagination">
                                <li :class="[applications.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="applications.current_page > 1 ? loadApplications(1) : null" :class="[applications.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-first"></em>
                                    </a>
                                </li>
                                <li :class="[applications.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="applications.current_page > 1 ? loadApplications(applications.current_page-1) : null" :class="[applications.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadApplications(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[applications.current_page === applications.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="applications.current_page < applications.last_page ? loadApplications(applications.current_page+1) : null" :class="[applications.current_page === applications.last_page ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[applications.current_page === applications.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="applications.current_page < applications.last_page ? loadApplications(applications.last_page) : null" :class="[applications.current_page === applications.last_page ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-last"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex flex-row ml-4">
                            <span class="align-self-center mr-1">Show</span>
                            <div class="form-wrap align-self-center">
                                <select id="filterPerPage" class="form-select-sm">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex ml-4">
                            <span class="align-self-center">
                                Showing
                                <span class="text-primary">{{ applications.from }}</span> to
                                <span class="text-primary">{{ applications.to }}</span> of
                                <span class="text-primary">{{ applications.total }}</span> {{ typeObj }} Applications
                            </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
import Notifications from "../../Notifications.vue";
import FilterLocalGovernments from "../../Filters/FilterLocalGovernments.vue";
export default {
    name: "Index",
    props: ['typeObj', 'applicationsObj','schoolTypesObj'],
    components: {Notifications, FilterLocalGovernments},
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            api_url: '/institutions/learner-enrolment-applications',
            loading: false,
            filtering: false,
            select_all_applications: false,
            applications: {
                total: 0,
                data: [],
                links: [],
            },
            filter: {
                search_term: '',
                application_number: '',
                sex: '',
                per_page: '',
            },
            selected_applications: [],
            education_levels: [],
        };
    },
    methods: {
        initPlugins: function () {
            let self = this;
            this.api_url += '/'+this.typeObj.toLowerCase();
            this.education_levels = this.schoolTypesObj;
            this.applications = this.applicationsObj;

            $('#filterStatus').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
            }).on('change', function () {
                self.filter.status = $(this).val();
            });

            $('#filterSex').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'border-dark-teal',
            }).on('change', function () {
                self.filter.sex = $(this).val();
            });

            $('#filterPerPage').select2({
                minimumResultsForSearch: Infinity,
                containerCssClass: 'border-dark-teal',
                templateSelection: function (data) {
                    self.filter.per_page = data.id !== "" ? Number(data.id) : data.id;
                    self.loadApplications(1, self.filtering);
                    return data.text;
                },
            });
        },
        formatDate: function (application) {
            if (application.date_created === null) {
                return moment(application.date_updated).format("D MMMM, YYYY hh:mma");
            } else {
                return moment(application.date_created).format("D MMMM, YYYY hh:mma");
            }
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        toggleAllApplications: function () {
            this.selected_applications =[];

            if (this.select_all_applications) {
                this.applications.data.forEach(application=>{
                    this.selected_applications.push(application.id)
                });
            }
        },
        toggleOneApplication: function () {
            this.select_all_applications = this.selected_applications.length === this.applications.data.length
        },
        loadApplications: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.applications = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                search_term: '',
                status: '',
                local_government_id: '',
                school_type_id: '',
                per_page: '',
            };
            $('#filterLocalGovernment').val('all').change();
            $('#filterStatus').val('all').change();
            $('#filterEducationLevel').val('all').change();
            $('#filterPerPage').val(15).change();
        },

        renderError: function (error) {
            const notifyError = (title, message) => {
                this.$refs.notify.messages.push({ status: 'error', title, message });
                $("html, body").animate({ scrollTop: 0 }, "slow");
            };

            if (error.response) {
                const { status, data } = error.response;
                switch (status) {
                    case 500:
                    case 405:
                        notifyError('System Error: ', data.message);
                        break;
                    case 401:
                        notifyError('Permission Error: ', 'You are not authorised to perform this action');
                        break;
                    case 404:
                        notifyError('Resource Not Found: ', 'You are trying to reach a URL that does not exist');
                        break;
                    case 422:
                        for (let field in data.errors) {
                            this.showError(data.errors[field]);
                        }
                        break;
                    default:
                        notifyError('Other Error: ', error.message);
                }
            } else {
                notifyError('Other Error: ', error.message);
            }
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
            $("html, body").animate({ scrollTop: 0 }, "slow");
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.applications.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    }
}
</script>
