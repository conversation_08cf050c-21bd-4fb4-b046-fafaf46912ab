<template>
    <div class="">
        <!-- Update Form -->
        <form class="w-100" v-if="!showChangeRequests" @submit.prevent="submitOwnershipUpdate">
            <div class="form-group row">
                <label for="ownershipType" class="col-sm-4 col-form-label">New Ownership Status</label>
                <div class="col-sm-8">
                    <div class="form-control-wrap">
                        <select 
                            id="ownership_type_id"
                            class="form-control" 
                            
                        >
                        <option>--SELECT OWNERSHIP STATUS--</option>
                            <option 
                                v-for="type in ownershipTypes" 
                                :key="type.id" 
                                :value="type.id"
                            >
                                {{ type.name.toUpperCase() }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="reason" class="col-sm-4 col-form-label">Reason for amendment</label>
                <div class="col-sm-8">
                    <div class="form-control-wrap">
                        <select 
                            id="reason_id"
                            class="form-control" 
                            
                        >
                        <option >--SELECT REASON--</option>

                            <option 
                                v-for="reason in changeReasons" 
                                :key="reason.id" 
                                :value="reason.id"
                            >
                                {{ reason.name.toUpperCase() }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" class="col-sm-4 col-form-label">Description</label>
                <div class="col-sm-8">
                    <textarea 
                        v-model="form.description" 
                        class="form-control" 
                        placeholder="Enter detailed description"
                        rows="2"
                    ></textarea>
                </div>
            </div>

            <div class="mt-2 d-flex justify-content-between">
              
                <div>
                    <button 
                        type="submit" 
                        :disabled="loading" 
                        class="btn btn-primary btn-sm mr-2"
                    >
                        <em class="ni ni-save mr-1"></em>
                        <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <span v-if="loading">Submitting...</span>
                        <span v-if="!loading">Submit</span>
                    </button>
                    <button 
                        @click="$emit('cancel')" 
                        type="button" 
                        :disabled="loading" 
                        class="btn btn-light btn-sm"
                    >
                        <em class="ni ni-cross-circle mr-1"></em>
                        Cancel
                    </button>
                </div>
            </div>
        </form>

        <!-- Change Requests List -->
        <div v-else>
            <div class="nk-block">
                <!-- Back Button -->
                <div class="mb-3 d-flex justify-content-end">
                    <button type="button" 
                            class="btn btn-light btn-sm"
                            @click="showChangeRequests = false">
                        <em class="icon ni ni-arrow-left mr-1"></em>
                        Back to Form
                    </button>
                </div>

                <div class="card card-stretch">
                    <div class="card-inner-group">
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <!-- Table Headers -->
                                <div class="nk-tb-item nk-tb-head bg-secondary">
                                    <div class="nk-tb-col"><span class="sub-text text-white">OLD STATUS</span></div>
                                    <div class="nk-tb-col"><span class="sub-text text-white">NEW STATUS</span></div>
                                    <div class="nk-tb-col"><span class="sub-text text-white">REASON</span></div>
                                    <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span></div>
                                    <div class="nk-tb-col"><span class="sub-text text-white">DATE REQUESTED</span></div>
                                </div>

                                <!-- Table Rows -->
                                <div v-for="request in changeRequests" :key="request.id" class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span>{{ request.data_update?.old_value }}</span>
                                    </div>
                                    <div class="nk-tb-col">
                                        <span>{{ request.data_update?.new_value }}</span>
                                    </div>
                                    <div class="nk-tb-col">
                                        <span>{{ request.reason?.name }}</span>
                                    </div>
                                    <div class="nk-tb-col">
                                        <span :class="getStatusBadgeClass(request.approval_status)">
                                            {{ request.approval_status }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col">
                                        <span>{{ formatDate(request.date_created) }}</span>
                                    </div>
                                </div>

                                <!-- No Data Message -->
                                <div v-if="!changeRequests.length" class="nk-tb-item">
                                    <div class="nk-tb-col text-center" colspan="5">
                                        <em class="icon ni ni-alert-circle"></em> No amendment requests found
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'UpdateSchoolOwnership',
    props: {
        school: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            showChangeRequests: false,
            form: {
                ownershipType: '',
                reason: '',
                description: ''
            },
            ownershipTypes: [],
            changeReasons: [],
            changeRequests: []
        }
    },
    methods: {
        submitOwnershipUpdate() {
            if (!this.form.ownershipType) {
                Swal.fire({
                    title: 'Error',
                    text: 'Ownership status is required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }
      
            if (!this.form.reason) {
                Swal.fire({
                    title: 'Error',
                    text: 'Reason for amendment is required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }
            if (!this.form.description) {
                Swal.fire({
                    title: 'Error',
                    text: 'Description is required.',
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
                return;
            }

            const oldOwnership = this.ownershipTypes.find(t => t.id === this.school.school_ownership_status_id)?.name;
            const newOwnership = this.ownershipTypes.find(t => t.id === this.form.ownershipType)?.name;

            Swal.fire({
                title: 'Are you sure?',
                html: `
                    <div style="margin-bottom: 10px;">
                        You are about to amendment the ownership status from 
                        <strong>${oldOwnership}</strong> to <strong>${newOwnership}</strong>.
                    </div>
                    <div class="text-info">
                        NOTE: Once approved by admin, this ownership status amendment will be reflected in the system.
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Proceed',
                cancelButtonText: 'Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    this.loading = true;
                    axios.post('/institutions/update-ownership-request', {
                        school_id: this.school.id,
                        new_ownership_type_id: this.form.ownershipType,
                        reason_id: this.form.reason,
                        description: this.form.description || '',
                        old_ownership: {
                            id: this.school.school_ownership_status_id,
                            name: oldOwnership
                        },
                        new_ownership: {
                            id: this.form.ownershipType,
                            name: newOwnership
                        }
                    })
                    .then(response => {
                        this.loading = false;
                        
                        Swal.fire({
                            title: 'Success',
                            html: `
                                <div style="margin-bottom: 10px;">
                                    The ownership status amendment request has been submitted successfully.
                                </div>
                                <div class="text-info">
                                    NOTE: Once approved by admin, this ownership status amendment will be reflected in the system.
                                </div>
                            `,
                            icon: 'success',
                            confirmButtonText: 'Ok'
                        });

                        // Emit success event
                        this.$emit('success', response.data);
                        this.$emit('request-submitted');
                        this.$emit('request-submitted-refresh');

                    })
                    .catch(error => {
                        this.loading = false;
                        
                        Swal.fire({
                            title: 'Error',
                            text: error.response?.data?.message || 'An error occurred while submitting the request',
                            icon: 'error',
                            confirmButtonText: 'Ok'
                        });

                        // Emit error event
                        this.$emit('error', error);
                    });
                }
            });
        },
        fetchChangeRequests() {
            axios.get(`/institutions/${this.school.id}/change-requests-ownership`)
                .then(response => {
                    this.changeRequests = response.data;
                })
                .catch(error => {
                    console.error('Error fetching amendment requests:', error);
                });
        },
        getStatusBadgeClass(status) {
            const classes = {
                'PENDING': 'badge badge-warning',
                'APPROVED': 'badge badge-dark-teal',
                'REJECTED': 'badge badge-danger'
            };
            return classes[status] || 'badge badge-secondary';
        },
        formatDate(date) {
            return moment(date).format('D MMMM, YYYY');
        },
        initPlugins() {
            let self = this;
            
            $('#ownership_type_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form.ownershipType = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });

            $('#reason_id').select2({
                minimumResultsForSearch: 0,
                containerCssClass: 'bg-primary-dim',
                templateSelection: function (data, container) {
                    self.form.reason = data.id.length > 0 ? Number(data.id) : "";
                    return data.text;
                },
            });
        }
    },
    watch: {
        showChangeRequests(newVal) {
            if (newVal) {
                this.fetchChangeRequests();
            }
        }
    },
    created() {
        // Fetch ownership types
        axios.get('/institutions/ownership-types')
            .then(response => {
                this.ownershipTypes = response.data;
                this.form.ownershipType = this.school.school_ownership_status_id;
            })
            .catch(error => {
                console.error('Error fetching ownership types:', error);
            });

        // Fetch change reasons
        axios.get('/institutions/change-reasons')
            .then(response => {
                this.changeReasons = response.data;
            })
            .catch(error => {
                console.error('Error fetching amendment reasons:', error);
            });
    },
    mounted() {
        this.initPlugins();
    }
}
</script>

<style scoped>
.nk-tb-list {
    display: table;
    width: 100%;
}

.nk-tb-item {
    display: table-row;
    border-bottom: 1px solid #dbdfea;
}

.nk-tb-col {
    display: table-cell;
    padding: 1rem 0.25rem;
    vertical-align: middle;
}

.nk-tb-head {
    background: #364a63;
}

.card-inner {
    padding: 1.5rem;
}

.card-inner-group {
    border: 1px solid #dbdfea;
    border-radius: 4px;
}
</style>
