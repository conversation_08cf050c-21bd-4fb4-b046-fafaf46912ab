<template>
    <div class="nk-block">
        <div class="card card-stretch">
            <div class="card-inner-group">
                <div class="card-inner p-0">
                    <div class="nk-tb-list nk-tb-ulist">
                        <!-- Table Headers -->
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col"><span class="sub-text text-white">OLD NAME</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">NEW NAME</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">REASON</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">STATUS</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">DATE REQUESTED</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white">ACTIONS</span></div>
                        </div>

                        <!-- Loading State -->
                        <div v-if="loading" class="nk-tb-item">
                            <div class="nk-tb-col text-center" colspan="6">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                Loading requests...
                            </div>
                        </div>

                        <!-- Table Rows -->
                        <div v-else v-for="request in requests" :key="request.id" class="nk-tb-item">
                            <div class="nk-tb-col">
                                <span>{{ request.data_update?.old_value.toUpperCase() }}</span>
                            </div>
                            <div class="nk-tb-col">
                                <span>{{ request.data_update?.new_value.toUpperCase() }}</span>
                            </div>
                            <div class="nk-tb-col">
                                <span>{{ request.reason?.name }}</span>
                            </div>
                            <div class="nk-tb-col">
                                <span :class="getStatusBadgeClass(request.approval_status)">
                                    {{ request.approval_status }}
                                </span>
                            </div>
                            <div class="nk-tb-col">
                                <span>{{ formatDate(request.date_created) }}</span>
                            </div>
                            <div class="nk-tb-col">
                                <button v-if="request.reject_reason" @click="showRejectReason(request.reject_reason)" class="btn btn-sm btn-primary">
                                    <em class="icon ni ni-eye"></em> View Reason
                                </button>
                            </div>
                        </div>

                        <!-- No Data Message -->
                        <div v-if="!loading && !requests.length" class="nk-tb-item">
                            <div class="nk-tb-col text-center" colspan="6">
                                <em class="icon ni ni-alert-circle"></em> No amendment requests found
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'NameChangeRequestsTable',
    props: {
        schoolId: {
            type: [Number, String],
            required: true
        },
        refreshTrigger: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            loading: false,
            requests: [],
            error: null
        }
    },
    methods: {
        getStatusBadgeClass(status) {
            const classes = {
                'PENDING': 'badge badge-gray',
                'APPROVED': 'badge badge-dark-teal',
                'REJECTED': 'badge badge-danger'
            };
            return classes[status] || 'badge badge-secondary';
        },
        formatDate(date) {
            return moment(date).format('D MMMM, YYYY');
        },
        async fetchRequests() {
            if (!this.schoolId) {
                return;
            }
            this.loading = true;
            try {
                const response = await axios.get(`/institutions/${this.schoolId}/change-requests`);
                this.requests = response.data;
            } catch (error) {
                console.error('Error fetching name change requests:', error);
                this.error = error.response?.data?.message || 'Error fetching requests';
                this.$emit('error', error);
            } finally {
                this.loading = false;
            }
        },
        showRejectReason(reason) {
            Swal.fire({
                title: 'Rejection Reason',
                text: reason || 'No rejection reason available.',
                icon: 'info',
                confirmButtonText: 'Close'
            });
        }
    },
    created() {
        this.fetchRequests();
    },
    watch: {
        schoolId: {
            handler: 'fetchRequests',
            immediate: true
        },
        refreshTrigger: {
            handler() {
                this.fetchRequests();
            }
        }
    }
}
</script>

<style scoped>
.nk-tb-list {
    display: table;
    width: 100%;
}

.nk-tb-item {
    display: table-row;
    border-bottom: 1px solid #dbdfea;
}

.nk-tb-col {
    display: table-cell;
    padding: 1rem 0.25rem;
    vertical-align: middle;
    white-space: normal; /* Allow text to wrap */
    word-wrap: break-word; /* Break long words */
}

.nk-tb-head {
    background: #364a63;
}

.card-inner {
    padding: 1.5rem;
}

.card-inner-group {
    border: 1px solid #dbdfea;
    border-radius: 4px;
}
</style>
