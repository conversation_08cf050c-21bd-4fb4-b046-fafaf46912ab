<template>
    <div class="modal fade" tabindex="-1" id="schoolInitModal">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <a v-if="editMode"  class="cursor close" data-dismiss="modal" aria-label="Close"><em class="icon ni ni-cross"></em></a>
                <form ref="learnerSummaryForm" @submit.prevent="submitLearnerSummary()">
                    <div class="modal-header">
                        <h6 class="modal-title"> <em class="icon ni ni-users-fill"></em> Learner Summary Form</h6>
                    </div>
                    <div class="modal-body">
                        <error-notifications ref="notifyError"></error-notifications>
                        <div class="row mb-3">
                            <div class="col col-6">
                                <span>Please select the term and provide a summary of total learners by class and gender in your school</span>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <div class="form-control-wrap">
                                        <select id="term_select_id" class="form-control" v-model="select_term" required="" :disabled="loading">
                                            <option value="" selected>SELECT TERM</option>
                                            <option v-for="term in terms" :value="term.id" :key="term.id">{{ term.name.toUpperCase() }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table  class="table table-borderless table-hover ">
                            <thead class="thead-light">
                            <tr>
                                <th scope="col">Class</th>
                                <th scope="col">Male</th>
                                <th scope="col">Female</th>
                                <th scope="col">Total</th>
                            </tr>
                            </thead>
                            <tbody>
                            <!-- @foreach ($classes as $class)
                                <tr>
                                    <td>{{ $class->name }}</td>
                                    <td class="input"><input class="form-control" type="number" min="0" placeholder="0"/></td>
                                    <td class="input"><input class="form-control" type="number" min="0" placeholder="0"/></td>
                                    <td>0</td>
                                </tr>
                            @endforeach -->

                            <tr v-for="school_class in school_classes" :key="school_class.id" >
                                <td>{{ school_class.name }}</td>
                                <td class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control" v-model.number="learners_data[school_class.id]['m']" type="number" min="0" placeholder="0" :disabled="loading"/>
                                    </div>
                                </td>
                                <td class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control input-sm" v-model.number="learners_data[school_class.id]['f']" type="number" min="0" placeholder="0" :disabled="loading"/>
                                    </div>
                                </td>
                                <!-- <td class="subtotal">{{ totalClassLearners(school_class.id) }}</td> -->
                                <th class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control input-sm" :value="totalClassLearners(school_class.id)" type="text" min="0" placeholder="0" readonly aria-readonly="true" tabindex="-1"/>
                                    </div>
                                </th>
                            </tr>

                            </tbody>
                            <tfoot class="thead-light">
                            <tr>
                                <th>Total</th>
                                <!-- <th>{{ totalMaleLearners() }}</th> -->
                                <th  class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control input-sm" name="totalMaleLearners" :value="totalMaleLearners()" type="text" min="0" placeholder="0" readonly tabindex="-1"/>
                                    </div>
                                </th>
                                <!-- <th>{{ totalFemaleLearners() }}</th> -->
                                <th  class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control input-sm" name="totalFemaleLearners" :value="totalFemaleLearners()" type="text" min="0" placeholder="0" readonly tabindex="-1"/>
                                    </div>
                                </th>
                                <!-- <th class="total">{{ totalLearners() }}</th> -->
                                <th class="input">
                                    <div class="input-group input-group-md">
                                        <input class="form-control input-sm" name="totalLearners" :value="totalLearners()" type="text" min="0" placeholder="0" readonly tabindex="-1"/>
                                    </div>
                                </th>
                            </tr>
                            </tfoot>
                        </table>
                        <div class="row">
                            <div class="col offset-6">
                                <input @click="resetLearnerSummary" class="btn-block btn btn-warning" type="button" value="Reset" :disabled="loading"/>
                            </div>
                            <div class="col col-3">
                                <input class="btn-block btn btn-primary" type="submit" value="Submit" :disabled="loading" />
                            </div>
                        </div>

                        <div id="formintro" v-if="formintro" class="text-center">
                            <div class="card card-stretch h-100">
                                <div class="card-inner card-80">
                                    <div class="row card-70">
                                        <div class="col col-12 text-center mt-6" style="margin-top:48px">
                                            <h5>You have uploaded {{ learnersCountObj }} learners. </h5>
                                            <p>Please click proceed to submit a summary of your <b>Total learners by class and gender.</b></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col col-4 offset-4">
                                            <a class="btn btn-primary btn-block text-white" @click="toggleIntro"> <em class="icon ni ni-arrow-right"></em> Proceed</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>


            </div>
        </div>
    </div>
</template>
<script>
import ErrorNotifications from "../Notifications.vue";
import Notifications from "../Notifications.vue";


export default {
    name: "LearnerSummaryForm",
    props:['classesObj','termsObj','expectedEnrolmentObj','learnersCountObj'],
    components: {
        Notifications,
        ErrorNotifications
    },
    data:function(){
        return {
            school_classes:[],
            learners_data:{},
            select_term:null,
            terms:[],
            loading:false,
            editMode:false,
            formintro:true,
            learnerCount:0,
        }
    },
    methods:{
        toggleIntro:function(){
            this.formintro = false;
        },
        initPlugins:function(){
            // console.log(this.expectedEnrolmentObj);
            // console.log(this.classesObj);

            this.school_classes = JSON.parse(this.classesObj);
            this.terms = JSON.parse(this.termsObj);
            // console.log("termsObj", this.termsObj);
            // console.log("learnersCountObj", this.learnersCountObj);
            if(this.learnersCountObj == null){
                this.formintro = false;
            }
            // console.log("expectedEnrolmentObj", this.expectedEnrolmentObj);
            if(this.expectedEnrolmentObj){
                this.learners_data = JSON.parse(this.expectedEnrolmentObj.enrolment_data);
                this.select_term = this.expectedEnrolmentObj.term_id;
                this.editMode = true;
                // console.log(typeof this.expectedEnrolmentObj.enrolment_data);
            }else{
                // console.log(this.terms);
                // console.log(this.school_classes);
                // this.learners_data = this.school_classes.map(x => {x.id:{'m':0,'f':0}});
                // console.log(this.school_classes.map(x => { return {x:2} } ));
                this.school_classes.forEach(x => {
                    // console.log(x);
                    this.learners_data[x.id] = {'m':0,'f':0};
                });
            }
            // console.log(this.learners_data);
        },
        totalClassLearners:function(_class){
            // console.log(this.learners_data);
            return (this.learners_data[_class]['m'] + this.learners_data[_class]['f']).toLocaleString();
        },
        updateData:function(){
            // console.log("called");
            // console.log("called");
            // console.log(this.learners_data);
        },
        submitLearnerSummary:function(){
            // $("#schoolInitModal").modal("hide");
            // axios.post('/saveLearnerSummary', this.learners_data)

            if(this.totalLearners() == 0){
                //error.response.status
                this.renderError({"response":{"status":500,"data":{"message":"Learner enrollment total is required"}}});
                return;
            }
            this.loading = true;
            axios.post('/institutions/save-learner-summary', {"select_term":this.select_term, "learners_data":this.learners_data})
                .then(response=>{
                    //console.log(response);
                    $("#schoolInitModal").modal("hide");
                    if(this.editMode){
                        window.location.reload();
                    }
                })
                .catch(error=>{
                    // this.loading = false;
                    // console.log(error.response);
                    this.renderError(error);

                })
                .finally(()=>{
                    this.loading = false;
                })
        },
        resetLearnerSummary:function(){
            this.$refs.notifyError.messages = [];

            for(let learners_idx in this.learners_data){
                let learners = this.learners_data[learners_idx];
                learners['f'] = 0;
                learners['m'] = 0;
                this.learners_data[learners_idx] = learners;
                // totalAll += Number(learners['f']) + Number(learners['m']);
            }
        },
        // },
        // computed:{
        totalMaleLearners:function(){
            let totalMale = 0;
            for(let learners in this.learners_data){
                learners = this.learners_data[learners];
                // console.log(learners);
                totalMale += Number(learners['m']);
            }
            return totalMale.toLocaleString();
        },
        totalFemaleLearners:function(){
            let totalFemale = 0;
            for(let learners in this.learners_data){
                learners = this.learners_data[learners];
                totalFemale += Number(learners['f']);
            }
            return totalFemale.toLocaleString();
        },
        totalLearners:function(){
            let totalAll = 0;
            for(let learners in this.learners_data){
                learners = this.learners_data[learners];
                totalAll += Number(learners['f']) + Number(learners['m']);
            }
            return totalAll.toLocaleString();
        },
        renderError: function (error) {

            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                //this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                //this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                //this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                //this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
                this.$refs.notifyError.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notifyError.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
    },
    created(){
    },
    mounted(){
        this.initPlugins();
        // console.log(this.school_classes.length);
        if(this.school_classes.length==0){
            // alert(4444);
            return;
        }
        // return;
        setTimeout(()=>{
            // alert(4);
            $("#schoolInitModal").modal({
                backdrop: 'static',
                keyboard: false
            });
            $('#term_select_id2').select2({
                minimumResultsForSearch: Infinity,
                //placeholder: 'Select Term',
                //dropdownParent: $('#schoolInitModal'),
                templateSelection: function (data, container) {
                    return data.text;
                },
            });

        }, 100);

    }
}
</script>
<style scss scoped>

table{
    background-color: #eeeeee ;
}
.table .thead-light th {
    padding: 8px 4px!important;
}

td.input,  th.input{
    padding: 4px 4px;
}
th.input input {
    font-weight: bold;
}
td,th,.form-control{
    text-align: right;
}
td.subtotal, th.total{
    min-width: 100px;
    font-weight: bold;
    vertical-align: middle;
    padding-right: 16px;
}
select , option{
    text-align: left;
    padding: 8px;
}
#formintro{
    position: absolute;
    top:0px;
    left:0px;
    background: #fff;
    width:100%;
    height:100%;
}
.card-100{
    height:100%;
}
.card-70{
    height:70%;
}
.card-80{
    height:80%;
}
</style>
