<template>
    <div>
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h3 class="nk-block-title page-title">Manage Uploaded Excels</h3>
                    <div class="nk-block-des text-soft">
                        <p>You have a total of {{ excels.total+' excel uploads' }}</p>
                    </div>
                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <a class="cursor btn btn-icon btn-trigger toggle-expand mr-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                        <div class="toggle-expand-content" data-content="pageMenu">
                            <ul class="nk-block-tools g-3">
                                <li>
<!--                                    <a href="/excel/developer-mode/export-excel" class="cursor btn btn-secondary">-->
<!--                                        <em class="icon ni ni-download-cloud text-white"></em>-->
<!--                                        <span class="">Export</span>-->
<!--                                    </a>-->
                                </li>
                            </ul>
                        </div>
                    </div><!-- .toggle-wrap -->
                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <div class="card card-stretch">
                <div class="card-inner-group">
                    <div class="row card-inner">
                        <div class="col-lg-8">
                            <form @submit.prevent="loadExcels(1, true)">
                                <div class="row">
                                    <div class="form-wrap col-12">
                                        <div class="input-group">
                                            <input v-model.trim="filter.search_term" type="text" class="form-control bg-primary-dim" placeholder="Search by file type and NIN">
                                            <div class="input-group-append">
                                                <button @click.prevent="resetFilter()" v-if="filtering" class="btn rounded-0 bg-secondary px-2 text-white" type="button">
                                                    <em class="icon ni ni-cross"></em>
                                                </button>
                                                <button :disabled="loading" class="btn rounded-right bg-dark-teal" type="submit">
                                                    <span v-if="loading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span v-if="loading" class="align-self-center">Filtering...</span>
                                                    <span v-if="loading" class="sr-only">Filtering...</span>
                                                    <em v-if="!loading" class="icon ni ni-filter"></em>
                                                    <span v-if="!loading">Apply</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-inner p-0">
                        <div class="nk-tb-list nk-tb-ulist is-compact">
                            <div class="nk-tb-item nk-tb-head bg-secondary">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleAllExcels()" v-model="select_all_excels"  type="checkbox" class="custom-control-input" id="uid">
                                        <label class="custom-control-label" for="uid"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">Excel</span></div>
                                <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">File Type</span></div>
                                <div class="nk-tb-col text-uppercase"><span class="sub-text text-white">Date Uploaded</span></div>
                            </div><!-- .nk-tb-item -->
                            <div v-for="excel in excels.data" class="nk-tb-item">
                                <div class="nk-tb-col nk-tb-col-check">
                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                        <input @change="toggleOneExcel()" v-model="selected_excels" :value="excel.id" type="checkbox" class="custom-control-input" :id="'uid'+excel.id">
                                        <label class="custom-control-label" :for="'uid'+excel.id"></label>
                                    </div>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span @click="downloadExcel(excel)" class="cursor text-dark-teal">{{ excel.file_name }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block">{{ excel.file_type }}</span>
                                </div>
                                <div class="nk-tb-col text-dark">
                                    <span class="d-block">{{ formatDate(excel.date_created) }}</span>
                                </div>
                            </div><!-- .nk-tb-item -->
                        </div><!-- .nk-tb-list -->
                        <div v-if="!excels.data.length" class="p-5">
                            <div class="alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> No excel uploads to display at the moment...
                            </div>
                        </div>
                    </div><!-- .card-inner -->
                    <div v-if="excels.data.length && excels.total > excels.per_page" class="card-inner d-flex flex-row">
                        <nav>
                            <ul class="pagination">
                                <li :class="[excels.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="excels.current_page > 1 ? loadExcels(1) : null" :class="[excels.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevrons-left"></em><span class="ml-1">First</span>
                                    </a>
                                </li>
                                <li :class="[excels.current_page === 1 ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="excels.current_page > 1 ? loadExcels(excels.current_page-1) : null" :class="[excels.current_page === 1 ? '' : 'cursor', 'page-link']">
                                        <em class="icon ni ni-chevron-left"></em><span class="ml-1">Previous</span>
                                    </a>
                                </li>
                                <li :class="getLinkClasses(link)" v-for="link in getPaginationLinks">
                                    <a @click="loadExcels(link.label)" class="page-link cursor" v-html="link.label"></a>
                                </li>
                                <li :class="[excels.current_page === excels.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="excels.current_page < excels.last_page ? loadExcels(excels.current_page+1) : null" :class="[excels.current_page === excels.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Next</span><em class="icon ni ni-chevron-right"></em>
                                    </a>
                                </li>
                                <li :class="[excels.current_page === excels.last_page ? 'disabled' : '', getLinkClasses()]">
                                    <a @click="excels.current_page < excels.last_page ? loadExcels(excels.last_page) : null" :class="[excels.current_page === excels.last_page ? '' : 'cursor', 'page-link']">
                                        <span class="mr-1">Last</span><em class="icon ni ni-chevrons-right"></em>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <div class="d-flex ml-4">
                    <span class="align-self-center">
                        Showing <span class="text-primary">{{ excels.from }}</span> to <span class="text-primary">{{ excels.to }}</span> of <span class="text-primary">{{ excels.total }}</span>
                    </span>
                        </div>
                    </div><!-- .card-inner -->
                </div><!-- .card-inner-group -->
            </div><!-- .card -->
        </div><!-- .nk-block -->
    </div>
</template>

<script>
import ErrorNotifications from "../Notifications.vue";
import SuccessNotifications from "../Notifications.vue";
export default {
    name: "UploadedExcels",
    props:['excelObj'],
    components: {
        ErrorNotifications,
        SuccessNotifications,
    },
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            is_lg_excel: 0,
            select_all_excels: false,
            loading: false,
            filtering: false,
            edit: false,
            excels: {
                data: [],
                links: [],
                total: 0,
                per_page: 0,
                last_page: 1,
            },
            selected_excels: [],
            roles: [],
            permissions: [],
            filter: {
                search_term: '',
            },
            bulk_action: '',
            role: '',
            api_url: '/institutions/uploaded-excels',
            loading_message: '',
        }
    },
    methods: {
        showError: function (message) {
            let text = '';
            for (let i = 0; i < message.length; i++) {
                text += message[i]+'<br>'
            }
            this.$refs.notify.messages.push({status: 'error', title: 'Data Error!', message:text});
        },
        formatDate: function formatDate(raw_date) {
            return moment(raw_date).format("D MMMM, YYYY hh:mma");
        },
        downloadExcel: function (excel) {
            axios({
                method: 'get',
                url: excel.file_url+'?x='+moment().unix(),
                responseType: 'arraybuffer'
            })
                .then(response=>{
                    let blob = new Blob([response.data], { type: 'application/octet-stream' });
                    let a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(blob, {
                        type: 'data:attachment/xlsx'
                    })
                    a.download = excel.file_name;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(a.href);
                    document.body.removeChild(a);
                })
                .catch(error=>{
                    this.renderError(error)
                });
        },
        initPlugins: function () {
            this.excels = JSON.parse(this.excelObj);
        },
        getLinkClasses: function (link = null) {
            let css = "page-item";

            if (link !== null) {
                if (link.active) {
                    css += " active-dark-teal disabled";
                }

                if (link.url === null) {
                    css += " disabled";
                }
            }

            return css;
        },
        toggleAllExcels: function () {
            this.selected_excels =[];

            if (this.select_all_excels) {
                this.excels.data.forEach(excel=>{
                    this.selected_excels.push(excel.id)
                });
            }
        },
        toggleOneExcel: function (id) {
            this.select_all_excels = this.selected_excels.length === this.excels.data.length
        },
        loadExcels: function (page, filtering = false) {
            if (!isNaN(Number(page))) {
                this.loading = true;
                axios.post(this.api_url+'?page='+page, this.filter)
                    .then(response=>{
                        this.loading = false;
                        this.excels = response.data;
                        if (filtering) {
                            this.filtering = true;
                        }
                    })
                    .catch(error=>{
                        this.loading = false;
                        console.log(error);
                        this.renderError(error);
                    });
            }
        },
        resetFilter: function () {
            this.filtering = false;
            this.loading = false;
            this.filter = {
                search_term: '',
            };
            $('#filterRole').val('').change();
            this.loadExcels(1, false);
        },
        renderError: function (error) {
            console.log(error.response)
            if (error.response && (error.response.status === 500 || error.response.status === 405)) {
                this.$refs.notify.messages.push({status: 'error', title: 'System Error:', message:error.response.data.message});
            } else if (error.response && error.response.status === 401) {
                this.$refs.notify.messages.push({status: 'error', title: 'Permission Error:', message:'You are not authorised to perform this action'});
            } else if (error.response && error.response.status === 404) {
                this.$refs.notify.messages.push({status: 'error', title: 'Resource Not Found:', message:'You are trying to reach a url that does not exist'});
            } else if (error.response && error.response.status === 422) {
                for (let field in error.response.data.errors) {
                    this.showError(error.response.data.errors[field]);
                }
            } else {
                this.$refs.notify.messages.push({status: 'error', title: 'Other Error:', message:error.message});
            }
        },
    },
    computed: {
        getPaginationLinks: function () {
            let arr = this.excels.links;
            arr.pop();
            arr.shift();
            return arr;
        },
    }
}
</script>
<style scoped>

</style>
