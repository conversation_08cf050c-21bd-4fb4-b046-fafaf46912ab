<template>
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ category.name }}</h5>

                </div><!-- .nk-block-head-content -->
            </div><!-- .nk-block-between -->
        </div><!-- .nk-block-head -->
        <div class="table-responsive">
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text text-white">Infrastructure Type</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white">Completion Status</span></div>
                        <div v-if="category.gender_usage_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">Usage By Gender</span>
                        </div>
                        <div v-show="category.user_category_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">User Category</span>
                        </div>
                        <div v-show="category.is_area_captured_yn" class="nk-tb-col text-center">
                            <span class="sub-text text-white">Total Area Per Room</span>
                        </div>
                        <div class="nk-tb-col text-center">
                            <span class="sub-text text-white">Total Count</span>
                        </div>
                    </div><!-- .nk-tb-item -->
                    <div v-for="facility in schoolCategory" class="nk-tb-item">
                        <div v-if="categoryExists(category)" class="nk-tb-col">
                            <span class="text-secondary" v-if="facility.usage_mode === 1">Permanent</span>
                            <span class="text-secondary" v-if="facility.usage_mode === 2">Temporary</span>
                        </div>
                        <div v-if="categoryExists(category)" class="nk-tb-col">
                            <span class="text-secondary" v-if="facility.completion_status === 1">Complete</span>
                            <span class="text-secondary" v-if="facility.completion_status === 2">Incomplete</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.gender_usage !== '' && category.gender_usage_yn" class="nk-tb-col text-center">
                            <span v-if="facility.gender_usage === 1"  class="text-secondary">Male</span>
                            <span v-if="facility.gender_usage === 2" class="text-secondary">Female</span>
                            <span v-if="facility.gender_usage === 3" class="text-secondary">Both Male & Female</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.user_category !== '' && category.user_category_yn" class="nk-tb-col text-center">
                            <span v-if="facility.gender_usage === 1"  class="text-secondary">Learners</span>
                            <span v-if="facility.gender_usage === 2" class="text-secondary">{{ getLevelTitle }}</span>
                            <span v-if="facility.gender_usage === 3" class="text-secondary">Learners & {{ getLevelTitle }}</span>
                            <span v-if="facility.gender_usage === 4" class="text-secondary">Learners & {{ getLevelTitle }} With Disability(SNE)</span>
                        </div>
                        <div v-if="categoryExists(category) && facility.area !== '' && category.is_area_captured_yn" class="nk-tb-col text-center">
                            <span  class="text-secondary">
                                {{ facility.area }} SQ<sup>M</sup>
                            </span>
                        </div>
                        <div v-if="categoryExists(category) && facility.total_number !== ''" class="nk-tb-col text-center">
                            <span class="text-secondary" >
                                {{ facility.total_number }}
                            </span>
                        </div>
                    </div>
                </div>
                <div v-if="!categoryExists(category) && !loading" class="card card-stretch" style="box-shadow: none;">
                    <div class="card-inner-group">
                        <div class="card-body">
                            <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                <em class="icon ni ni-alert-circle"></em> There is no infrastructure information to display at the moment.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: "MainFacilitiesSection",
    props: [
        'schoolObj',
        'categoryObj',
        'preprimary',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            loading: false,
            api_url: '/institutions/surveys/infrastructure',
            school: {
                infrastructure: []
            },
            category: {
                id: '',
                name: '',
                is_area_captured_yn: false,
                gender_usage_yn: false,
                user_category_yn: false,
            },
            infrastructure: [],
            facility: {
                id: '',
                building_id: '',
                section_id: '',
                gender_usage: '',
                completion_status: '',
                user_category: '',
                usage_mode: '',
                area: 0,
                total_number: 0,
            },
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
            this.category = this.categoryObj;
        },
        categoryExists: function (category) {
            return this.school.infrastructure.find(cat=>{
                return cat.building_id === category.id;
            }) !== undefined;
        },
    },
    computed: {
        schoolCategory: function () {
            if (this.categoryExists(this.category)) {
                return this.school.infrastructure.filter(cat=>{
                    return cat.building_id === this.category.id
                });
            } else {
                return {
                    usage_mode: '',
                    completion_status: '',
                    gender_usage: '',
                    user_category: '',
                    area: 0,
                    total_number: 0,
                }
            }
        },
        getLevelTitle: function() {
            return this.preprimary === 'yes' ? 'Caregivers' : 'Teachers';
        }
    }
}
</script>

<style scoped>

</style>
