<template>
    <div class="w-100 vertical-scrollable">
        <infrastructure-main-facilities
            v-for="categoryObj in infrastructure_types"
            :school-obj="schoolObj"
            :category-obj="categoryObj"
            :key="categoryObj.id"
            :preprimary="preprimary"
        ></infrastructure-main-facilities>
    </div>
</template>

<script>

export default {
    name: "CategoriesSection",
    props: [
        'schoolObj',
        'infrastructureTypesObj',
        'preprimary',
    ],
    mounted() {
        this.initPlugins();
    },
    data: function () {
        return {
            infrastructure_types: [],
        }
    },
    methods: {
        initPlugins: function () {
            this.school = this.schoolObj;
            this.infrastructure_types = this.infrastructureTypesObj;
        },
    },
    computed: {}
}
</script>

<style scoped>
    .vertical-scrollable {
        height:800px;
        overflow-y: scroll;
    }
</style>
