// Schools
import Vue from "vue";

//Learner Summary Form
Vue.component("learner-summary-form", () =>
    import(
        /* webpackChunkName: "LearnerSummaryForm" */ "../js/components/Institution/LearnerSummaryForm.vue"
    )
);

//Emis Search
Vue.component("emis-search-form", () =>
    import(
        /* webpackChunkName: "InstitutionsSearchEmis" */ "../js/components/EmisSearchForm.vue"
    )
);
Vue.component("emis-number-status", () =>
    import(
        /* webpackChunkName: "InstitutionsEmisStatus" */ "../js/components/EmisNumberStatus.vue"
    )
);

Vue.component("school-emis-registration", () =>
    import(
        /* webpackChunkName: "InstitutionsEmisRegistration" */ "../js/components/Institution/registration/EmisRegistration.vue"
    )
);
Vue.component("school-form-registration", () =>
    import(
        /* webpackChunkName: "InstitutionsFormRegistration" */ "../js/components/Institution/registration/FormRegistration.vue"
    )
);
// Vue.component('school-academic-years', () => import(/* webpackChunkName: "SchoolsAcademicYears" */ '../js/components/Institution/AcademicYears.vue'));
Vue.component("school-manage-users", () =>
    import(
        /* webpackChunkName: "InstitutionsUserManager" */ "../js/components/Institution/users/Index.vue"
    )
);
Vue.component("change-temporary-password", () =>
    import(
        /* webpackChunkName: "ChangeTemporary" */ "../js/components/ChangePassword.vue"
    )
);

// Institutions Profile
Vue.component("pre-primary-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsPrePrimarySchoolProfile" */ "../js/components/Institution/PrePrimarySchoolProfile.vue"
    )
);
Vue.component("primary-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsPrimarySchoolProfile" */ "../js/components/Institution/PrimarySchoolProfile.vue"
    )
);
Vue.component("secondary-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsSecondarySchoolProfile" */ "../js/components/Institution/SecondarySchoolProfile.vue"
    )
);
Vue.component("certificate-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsCertificateSchoolProfile" */ "../js/components/Institution/CertificateSchoolProfile.vue"
    )
);
Vue.component("diploma-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsDiplomaSchoolProfile" */ "../js/components/Institution/DiplomaSchoolProfile.vue"
    )
);
Vue.component("degree-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsDegreeSchoolProfile" */ "../js/components/Institution/DegreeSchoolProfile.vue"
    )
);
Vue.component("international-school-profile", () =>
    import(
        /* webpackChunkName: "InstitutionsInternationalSchoolProfile" */ "../js/components/Institution/InternationalSchoolProfile.vue"
    )
);

// Survey
Vue.component("school-survey", () =>
    import(
        /* webpackChunkName: "SchoolSurvey" */ "../js/components/Institution/surveys/Survey.vue"
    )
);

//Facilities
Vue.component("survey-facilities-section-water", () =>
    import(
        /* webpackChunkName: "SurveySectionWater" */ "../js/components/Institution/surveys/facilities/WaterSection.vue"
    )
);
Vue.component("survey-facilities-section-energy", () =>
    import(
        /* webpackChunkName: "SurveySectionEnergy" */ "../js/components/Institution/surveys/facilities/EnergySection.vue"
    )
);
Vue.component("survey-library-facilities-section", () =>
    import(
        /* webpackChunkName: "SurveySectionLibraryFacilities" */ "../js/components/Institution/surveys/facilities/LibraryFacilitiesSection.vue"
    )
);
Vue.component("survey-facilities-section-other-facilities", () =>
    import(
        /* webpackChunkName: "SurveySectionOtherFacilities" */ "../js/components/Institution/surveys/facilities/OtherFacilitiesSection.vue"
    )
);
Vue.component("survey-facilities-section-garbage-disposal-methods", () =>
    import(
        /* webpackChunkName: "SurveySectionGarbageDisposalMethods" */ "../js/components/Institution/surveys/facilities/GarbageDisposalMethodsSection.vue"
    )
);
Vue.component("survey-facilities-section-sanitation", () =>
    import(
        /* webpackChunkName: "SurveySectionSanitation" */ "../js/components/Institution/surveys/facilities/HandWashingFacilitiesSection.vue"
    )
);
Vue.component("survey-section-practical-skills", () =>
    import(
        /* webpackChunkName: "SurveySectionPracticalSkills" */ "../js/components/Institution/surveys/facilities/PracticalSkillsSection.vue"
    )
);

//Infrastructure
Vue.component("survey-infrastructure-categories", () =>
    import(
        /* webpackChunkName: "SurveyInfrastructureTypes" */ "../js/components/Institution/surveys/infrastructure/CategoriesSection.vue"
    )
);
Vue.component("survey-infrastructure-main-facilities", () =>
    import(
        /* webpackChunkName: "SurveyInfrastructureMainFacilities" */ "../js/components/Institution/surveys/infrastructure/MainFacilitiesSection.vue"
    )
);

Vue.component("survey-file-uploads", () =>
    import(
        /* webpackChunkName: "SurveyFileUploads" */ "../js/components/Institution/surveys/FileUploads.vue"
    )
);
Vue.component("survey-file-upload-details", () =>
    import(
        /* webpackChunkName: "SurveyFileUploadDetails" */ "../js/components/Institution/surveys/FileUploadDetails.vue"
    )
);
Vue.component("survey-file-upload-ugandan-learners-details", () =>
    import(
        /* webpackChunkName: "SurveyFileUploadUgandanLearnersDetails" */ "../js/components/Institution/surveys/FileUploadUgandanLearnersDetails.vue"
    )
);

// PrePrimary Survey
Vue.component("survey-pre-primary-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryEmisReturns" */ "../js/components/Institution/surveys/pre-primary/EmisReturns.vue"
    )
);
Vue.component("survey-pre-primary-section-a", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySectionA" */ "../js/components/Institution/surveys/pre-primary/SectionA.vue"
    )
);
Vue.component("survey-pre-primary-section-b", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySectionB" */ "../js/components/Institution/surveys/pre-primary/SectionB.vue"
    )
);
Vue.component("survey-pre-primary-section-c", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySectionC" */ "../js/components/Institution/surveys/pre-primary/SectionC.vue"
    )
);
Vue.component("survey-pre-primary-caregiver-form-create", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryCaregiverFormCreate" */ "../js/components/Institution/surveys/pre-primary/CaregiverFormCreate.vue"
    )
);
Vue.component("survey-pre-primary-caregiver-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryCaregiverImportUgandans" */ "../js/components/Institution/surveys/pre-primary/CaregiverImportUgandans.vue"
    )
);
Vue.component("survey-pre-primary-caregiver-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryCaregiverImportForeigners" */ "../js/components/Institution/surveys/pre-primary/CaregiverImportForeigners.vue"
    )
);
Vue.component("survey-pre-primary-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryNonTeachingStaff" */ "../js/components/Institution/surveys/pre-primary/NonTeachingStaff.vue"
    )
);
Vue.component("survey-pre-primary-support-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySupportStaffImportUgandans" */ "../js/components/Institution/surveys/pre-primary/SupportStaffImportUgandans.vue"
    )
);
Vue.component("survey-pre-primary-support-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySupportStaffImportForeigners" */ "../js/components/Institution/surveys/pre-primary/SupportStaffImportForeigners.vue"
    )
);
Vue.component("survey-pre-primary-support-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySupportStaffFormCreate" */ "../js/components/Institution/surveys/pre-primary/SupportStaffFormCreate.vue"
    )
);
Vue.component("survey-pre-primary-section-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimarySectionSportsEquipment" */ "../js/components/Institution/surveys/pre-primary/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component("survey-pre-primary-learning-playing-materials", () =>
    import(
        /* webpackChunkName: "SurveyLearningPlayingMaterials" */ "../js/components/Institution/surveys/pre-primary/LearningAndPlayingMaterialsSection.vue"
    )
);
Vue.component("survey-pre-primary-health-meals-section", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryHealthMealsSection" */ "../js/components/Institution/surveys/pre-primary/health-meals/MealsSection.vue"
    )
);
Vue.component("survey-pre-primary-cmc-members", () =>
    import(
        /* webpackChunkName: "SurveyPrePrimaryCmcMembers" */ "../js/components/Institution/surveys/pre-primary/CmcMembers.vue"
    )
);
Vue.component("survey-pre-primary-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyGPSPrePrimaryLocation" */ "../js/components/Institution/surveys/pre-primary/location/LocationSection.vue"
    )
);
Vue.component("survey-finance-income-section", () =>
    import(
        /* webpackChunkName: "SurveyFinanceIncomeSection" */ "../js/components/Institution/surveys/finance/IncomeSection.vue"
    )
);
Vue.component("survey-finance-budget-section", () =>
    import(
        /* webpackChunkName: "SurveyFinanceBudgetSection" */ "../js/components/Institution/surveys/finance/BudgetSection.vue"
    )
);
Vue.component("survey-finance-expenses-section", () =>
    import(
        /* webpackChunkName: "SurveyFinanceExpensesSection" */ "../js/components/Institution/surveys/finance/ExpensesSection.vue"
    )
);

// PRIMARY SURVEY
Vue.component("survey-primary-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryEmisReturns" */ "../js/components/Institution/surveys/primary/EmisReturns.vue"
    )
);
Vue.component("survey-primary-section-a", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionA" */ "../js/components/Institution/surveys/primary/SectionA.vue"
    )
);
Vue.component("survey-primary-section-b", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionB" */ "../js/components/Institution/surveys/primary/SectionB.vue"
    )
);
Vue.component("survey-primary-section-c", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionC" */ "../js/components/Institution/surveys/primary/SectionC.vue"
    )
);
Vue.component("survey-primary-section-d", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionD" */ "../js/components/Institution/surveys/primary/SectionD.vue"
    )
);
Vue.component("survey-primary-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryTeacherFormCreate" */ "../js/components/Institution/surveys/primary/TeacherFormCreate.vue"
    )
);
Vue.component("survey-primary-teacher-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryTeacherImportUgandans" */ "../js/components/Institution/surveys/primary/TeacherImportUgandans.vue"
    )
);
Vue.component("survey-primary-teacher-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryTeacherImportForeigners" */ "../js/components/Institution/surveys/primary/TeacherImportForeigners.vue"
    )
);
Vue.component("survey-primary-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryNonTeachingStaff" */ "../js/components/Institution/surveys/primary/NonTeachingStaff.vue"
    )
);
Vue.component("survey-primary-non-teaching-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryNonTeachingStaffFormCreate" */ "../js/components/Institution/surveys/primary/NonTeachingStaffFormCreate.vue"
    )
);
Vue.component("survey-primary-non-teaching-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryNonTeachingStaffImportUgandans" */ "../js/components/Institution/surveys/primary/NonTeachingStaffImportUgandans.vue"
    )
);
Vue.component("survey-primary-non-teaching-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryNonTeachingStaffImportForeigners" */ "../js/components/Institution/surveys/primary/NonTeachingStaffImportForeigners.vue"
    )
);
Vue.component("survey-primary-section-ict", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryIct" */ "../js/components/Institution/surveys/primary/ict/IctFacilities.vue"
    )
);
Vue.component("survey-primary-extra-curricular-activity-participation", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryExtraCurricularActivityParticipation" */ "../js/components/Institution/surveys/primary/ExtraCurricularActivityParticipation.vue"
    )
);
Vue.component("survey-internet-sources", () =>
    import(
        /* webpackChunkName: "SurveyInternetSource" */ "../js/components/Institution/surveys/primary/ict/InternetSources.vue"
    )
);
Vue.component("survey-primary-health-meals-section", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryHealthMealsSection" */ "../js/components/Institution/surveys/primary/health-meals/MealsSection.vue"
    )
);
Vue.component("survey-primary-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryLearnerFormCreate" */ "../js/components/Institution/surveys/primary/LearnerFormCreate.vue"
    )
);
Vue.component("survey-primary-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryLearnerImportUgandans" */ "../js/components/Institution/surveys/primary/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-primary-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryLearnerImportForeigners" */ "../js/components/Institution/surveys/primary/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-primary-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryLearnerImportRefugees" */ "../js/components/Institution/surveys/primary/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-primary-section-sports", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionSports" */ "../js/components/Institution/surveys/primary/pe-sports/SportsSection.vue"
    )
);
Vue.component("survey-primary-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveySportsEquipment" */ "../js/components/Institution/surveys/primary/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component("survey-primary-sports-facilities", () =>
    import(
        /* webpackChunkName: "SurveySportsFacilities" */ "../js/components/Institution/surveys/primary/pe-sports/SportsFacilitiesSection.vue"
    )
);
Vue.component("survey-primary-smc-members", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySmcMembers" */ "../js/components/Institution/surveys/primary/SmcMembers.vue"
    )
);
Vue.component("survey-primary-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyGPSPrimaryLocation" */ "../js/components/Institution/surveys/primary/location/LocationSection.vue"
    )
);

//Instructional materials
Vue.component("survey-primary-section-instructional-materials", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySectionInstructionalMaterials" */ "../js/components/Institution/surveys/primary/instructional-materials/MaterialsSection.vue"
    )
);
Vue.component("survey-primary-textbooks", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryTextbooks" */ "../js/components/Institution/surveys/primary/instructional-materials/Textbooks.vue"
    )
);
Vue.component("survey-primary-reference-books", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryRerenceBooks" */ "../js/components/Institution/surveys/primary/instructional-materials/ReferenceBooks.vue"
    )
);
Vue.component("survey-primary-sne-kits", () =>
    import(
        /* webpackChunkName: "SurveyPrimarySneKits" */ "../js/components/Institution/surveys/primary/instructional-materials/SneKits.vue"
    )
);
Vue.component("survey-primary-wall-charts", () =>
    import(
        /* webpackChunkName: "SurveyPrimaryWallCharts" */ "../js/components/Institution/surveys/primary/instructional-materials/WallCharts.vue"
    )
);

// SECONDARY SURVEY
Vue.component("survey-secondary-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveySecondaryEmisReturns" */ "../js/components/Institution/surveys/secondary/EmisReturns.vue"
    )
);
Vue.component("survey-secondary-section-a", () =>
    import(
        /* webpackChunkName: "SurveySecondarySectionA" */ "../js/components/Institution/surveys/secondary/SectionA.vue"
    )
);
Vue.component("survey-secondary-section-b", () =>
    import(
        /* webpackChunkName: "SurveySecondarySectionB" */ "../js/components/Institution/surveys/secondary/SectionB.vue"
    )
);
Vue.component("survey-secondary-section-c", () =>
    import(
        /* webpackChunkName: "SurveySecondarySectionC" */ "../js/components/Institution/surveys/secondary/SectionC.vue"
    )
);
Vue.component("survey-secondary-section-d", () =>
    import(
        /* webpackChunkName: "SurveySecondarySectionD" */ "../js/components/Institution/surveys/secondary/SectionD.vue"
    )
);
Vue.component("survey-secondary-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveySecondaryTeacherFormCreate" */ "../js/components/Institution/surveys/secondary/TeacherFormCreate.vue"
    )
);
Vue.component("survey-secondary-teacher-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveySecondaryTeacherImportUgandans" */ "../js/components/Institution/surveys/secondary/TeacherImportUgandans.vue"
    )
);
Vue.component("survey-secondary-teacher-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveySecondaryTeacherImportForeigners" */ "../js/components/Institution/surveys/secondary/TeacherImportForeigners.vue"
    )
);
Vue.component("survey-secondary-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveySecondaryNonTeachingStaff" */ "../js/components/Institution/surveys/secondary/NonTeachingStaff.vue"
    )
);
Vue.component("survey-secondary-non-teaching-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveySecondaryNonTeachingStaffImportUgandans" */ "../js/components/Institution/surveys/secondary/NonTeachingStaffImportUgandans.vue"
    )
);
Vue.component("survey-secondary-non-teaching-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveySecondaryNonTeachingStaffImportForeigners" */ "../js/components/Institution/surveys/secondary/NonTeachingStaffImportForeigners.vue"
    )
);
Vue.component("survey-secondary-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLearnerFormCreate" */ "../js/components/Institution/surveys/secondary/LearnerFormCreate.vue"
    )
);
Vue.component("survey-secondary-learner-form-equated-code", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLearnerFormEquatedCode" */ "./components/Institution/surveys/secondary/LearnerFormEquatedCode.vue"
    )
);

Vue.component("survey-secondary-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLearnerImportUgandans" */ "../js/components/Institution/surveys/secondary/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-secondary-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLearnerImportForeigners" */ "../js/components/Institution/surveys/secondary/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-secondary-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLearnerImportRefugees" */ "../js/components/Institution/surveys/secondary/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-secondary-extra-curricular-activity-participation", () =>
    import(
        /* webpackChunkName: "SurveySecondaryExtraCurricularActivityParticipation" */ "../js/components/Institution/surveys/secondary/ExtraCurricularActivityParticipation.vue"
    )
);
Vue.component("survey-secondary-section-ict", () =>
    import(
        /* webpackChunkName: "SurveySecondaryIct" */ "../js/components/Institution/surveys/secondary/ict/IctFacilities.vue"
    )
);
Vue.component("survey-secondary-health-meals-section", () =>
    import(
        /* webpackChunkName: "SurveySecondaryHealthMealsSection" */ "../js/components/Institution/surveys/secondary/health-meals/MealsSection.vue"
    )
);
Vue.component("survey-secondary-bog-members", () =>
    import(
        /* webpackChunkName: "SurveySecondaryBogMembers" */ "../js/components/Institution/surveys/secondary/BogMembers.vue"
    )
);
Vue.component("survey-secondary-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyGPSSecondaryLocation" */ "../js/components/Institution/surveys/secondary/location/LocationSection.vue"
    )
);
//Infrastructure
Vue.component("survey-infrastructure-categories-secondary", () =>
    import(
        /* webpackChunkName: "SurveyInfrastructureSecondary" */ "../js/components/Institution/surveys/secondary/infrastructure/CategoriesSection.vue"
    )
);
Vue.component("survey-infrastructure-main-facilities-secondary", () =>
    import(
        /* webpackChunkName: "SurveyInfrastructureFacilitiesSecondary" */ "../js/components/Institution/surveys/secondary/infrastructure/MainFacilitiesSection.vue"
    )
);
//Instructional materials
Vue.component("survey-secondary-section-instructional-materials", () =>
    import(
        /* webpackChunkName: "SurveySecondarySectionInstructionalMaterials" */ "../js/components/Institution/surveys/secondary/instructional-materials/MaterialsSection.vue"
    )
);
Vue.component("survey-secondary-textbooks", () =>
    import(
        /* webpackChunkName: "SurveySecondaryTextbooks" */ "../js/components/Institution/surveys/secondary/instructional-materials/Textbooks.vue"
    )
);
Vue.component("survey-secondary-reference-books", () =>
    import(
        /* webpackChunkName: "SurveySecondaryReferenceBooks" */ "../js/components/Institution/surveys/secondary/instructional-materials/ReferenceBooks.vue"
    )
);
Vue.component("survey-secondary-sne-kits", () =>
    import(
        /* webpackChunkName: "SurveySecondarySneKits" */ "../js/components/Institution/surveys/secondary/instructional-materials/SneKits.vue"
    )
);
Vue.component("survey-secondary-lab-equipment", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLabEquipment" */ "../js/components/Institution/surveys/secondary/instructional-materials/LabEquipment.vue"
    )
);
Vue.component("survey-secondary-lab-reagents", () =>
    import(
        /* webpackChunkName: "SurveySecondaryLabReagents" */ "../js/components/Institution/surveys/secondary/instructional-materials/LabReagents.vue"
    )
);

// CERTIFICATE SURVEY
Vue.component("survey-certificate-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyCertificateEmisReturns" */ "../js/components/Institution/surveys/certificate/EmisReturns.vue"
    )
);
Vue.component("survey-certificate-section-a", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSectionA" */ "../js/components/Institution/surveys/certificate/SectionA.vue"
    )
);
Vue.component("survey-certificate-certificate-section-b", () =>
    import(
        /* webpackChunkName: "SurveyCertificateCertificateSectionB" */ "../js/components/Institution/surveys/certificate/SectionSchoolParticularsCertificate.vue"
    )
);
Vue.component("survey-certificate-institution-courses", () =>
    import(
        /* webpackChunkName: "SurveyCertificateInstitutionCourses" */ "../js/components/Institution/surveys/certificate/InstitutionCourses.vue"
    )
);
Vue.component("survey-certificate-section-d", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSectionD" */ "../js/components/Institution/surveys/certificate/SectionD.vue"
    )
);
Vue.component("survey-certificate-section-d", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSectionD" */ "../js/components/Institution/surveys/certificate/SectionD.vue"
    )
);
Vue.component("survey-certificate-section-c", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSectionC" */ "../js/components/Institution/surveys/certificate/SectionC.vue"
    )
);
Vue.component("survey-certificate-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveyCertificateLearnerFormCreate" */ "../js/components/Institution/surveys/certificate/LearnerFormCreate.vue"
    )
);
Vue.component("survey-certificate-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyCertificateLearnerImportUgandans" */ "../js/components/Institution/surveys/certificate/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-certificate-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyCertificateLearnerImportForeigners" */ "../js/components/Institution/surveys/certificate/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-certificate-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyCertificateLearnerImportRefugees" */ "../js/components/Institution/surveys/certificate/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-certificate-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveyCertificateTeacherFormCreate" */ "../js/components/Institution/surveys/certificate/TeacherFormCreate.vue"
    )
);
Vue.component("survey-certificate-tutors-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyCertificateTutorImportUgandans" */ "../js/components/Institution/surveys/certificate/TutorImportUgandans.vue"
    )
);
Vue.component("survey-certificate-tutors-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyCertificateTutorImportForeigners" */ "../js/components/Institution/surveys/certificate/TutorImportForeigners.vue"
    )
);
Vue.component("survey-certificate-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyCertificateNonAcademicStaff" */ "../js/components/Institution/surveys/certificate/NonAcademicStaff.vue"
    )
);
Vue.component("survey-certificate-non-teaching-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyCertificateNonTeachingStaffFormCreate" */ "../js/components/Institution/surveys/certificate/NonTeachingStaffFormCreate.vue"
    )
);
Vue.component("survey-non-academic-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyNonAcademicStaffImportUgandans" */ "../js/components/Institution/surveys/NonAcademicStaffImportUgandans.vue"
    )
);
Vue.component("survey-non-academic-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyNonAcademicStaffImportForeigners" */ "../js/components/Institution/surveys/NonAcademicStaffImportForeigners.vue"
    )
);
Vue.component("survey-certificate-section-ict", () =>
    import(
        /* webpackChunkName: "SurveyCertificateIct" */ "../js/components/Institution/surveys/certificate/ict/IctFacilities.vue"
    )
);
Vue.component("survey-certificate-internet-sources", () =>
    import(
        /* webpackChunkName: "SurveyCertificateInternetSource" */ "../js/components/Institution/surveys/certificate/ict/InternetSources.vue"
    )
);
Vue.component("survey-certificate-section-sports", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSectionSports" */ "../js/components/Institution/surveys/certificate/pe-sports/SportsSection.vue"
    )
);
Vue.component("survey-certificate-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSportsEquipment" */ "../js/components/Institution/surveys/certificate/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component(
    "survey-certificate-extra-curricular-activity-participation",
    () =>
        import(
            /* webpackChunkName: "SurveyCertificateExtraCurricularActivityParticipation" */ "../js/components/Institution/surveys/certificate/pe-sports/ExtraCurricularActivityParticipation.vue"
        )
);
Vue.component("survey-certificate-sports-facilities", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSportsFacilities" */ "../js/components/Institution/surveys/certificate/pe-sports/SportsFacilitiesSection.vue"
    )
);
Vue.component("survey-certificate-sports-activities", () =>
    import(
        /* webpackChunkName: "SurveyCertificateSportsActivities" */ "../js/components/Institution/surveys/certificate/pe-sports/SportsActivitiesSection.vue"
    )
);
Vue.component("survey-certificate-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyGPSCertificateLocation" */ "../js/components/Institution/surveys/certificate/location/LocationSection.vue"
    )
);

// DIPLOMA SURVEY
Vue.component("survey-diploma-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaEmisReturns" */ "../js/components/Institution/surveys/diploma/EmisReturns.vue"
    )
);
Vue.component("survey-diploma-section-a", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSectionA" */ "../js/components/Institution/surveys/diploma/SectionA.vue"
    )
);
Vue.component("survey-diploma-diploma-section-b", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaDiplomaSectionB" */ "../js/components/Institution/surveys/diploma/SectionSchoolParticularsDiploma.vue"
    )
);
Vue.component("survey-diploma-institution-courses", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaInstitutionCourses" */ "../js/components/Institution/surveys/diploma/InstitutionCourses.vue"
    )
);
Vue.component("survey-diploma-section-d", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSectionD" */ "../js/components/Institution/surveys/diploma/SectionD.vue"
    )
);
Vue.component("survey-diploma-section-d", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSectionD" */ "../js/components/Institution/surveys/diploma/SectionD.vue"
    )
);
Vue.component("survey-diploma-section-c", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSectionC" */ "../js/components/Institution/surveys/diploma/SectionC.vue"
    )
);
Vue.component("survey-diploma-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaLearnerFormCreate" */ "../js/components/Institution/surveys/diploma/LearnerFormCreate.vue"
    )
);
Vue.component("survey-diploma-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaLearnerImportUgandans" */ "../js/components/Institution/surveys/diploma/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-diploma-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaLearnerImportForeigners" */ "../js/components/Institution/surveys/diploma/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-diploma-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaLearnerImportRefugees" */ "../js/components/Institution/surveys/diploma/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-diploma-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaTeacherFormCreate" */ "../js/components/Institution/surveys/diploma/TeacherFormCreate.vue"
    )
);
Vue.component("survey-tutors-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyTutorImportUgandans" */ "../js/components/Institution/surveys/diploma/TutorImportUgandans.vue"
    )
);
Vue.component("survey-tutors-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyTutorImportForeigners" */ "../js/components/Institution/surveys/diploma/TutorImportForeigners.vue"
    )
);
Vue.component("survey-diploma-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaNonAcademicStaff" */ "../js/components/Institution/surveys/diploma/NonAcademicStaff.vue"
    )
);
Vue.component("survey-diploma-non-teaching-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaNonTeachingStaffFormCreate" */ "../js/components/Institution/surveys/diploma/NonTeachingStaffFormCreate.vue"
    )
);
Vue.component("survey-diploma-section-ict", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaIct" */ "../js/components/Institution/surveys/diploma/ict/IctFacilities.vue"
    )
);
Vue.component("survey-diploma-internet-sources", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaInternetSource" */ "../js/components/Institution/surveys/diploma/ict/InternetSources.vue"
    )
);
Vue.component("survey-diploma-section-sports", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSectionSports" */ "../js/components/Institution/surveys/diploma/pe-sports/SportsSection.vue"
    )
);
Vue.component("survey-diploma-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSportsEquipment" */ "../js/components/Institution/surveys/diploma/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component("survey-diploma-extra-curricular-activity-participation", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaExtraCurricularActivityParticipation" */ "../js/components/Institution/surveys/diploma/pe-sports/ExtraCurricularActivityParticipation.vue"
    )
);
Vue.component("survey-diploma-sports-facilities", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSportsFacilities" */ "../js/components/Institution/surveys/diploma/pe-sports/SportsFacilitiesSection.vue"
    )
);
Vue.component("survey-diploma-sports-activities", () =>
    import(
        /* webpackChunkName: "SurveyDiplomaSportsActivities" */ "../js/components/Institution/surveys/diploma/pe-sports/SportsActivitiesSection.vue"
    )
);
Vue.component("survey-diploma-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyGPSDiplomaLocation" */ "../js/components/Institution/surveys/diploma/location/LocationSection.vue"
    )
);

// DEGREE SURVEY
Vue.component("survey-degree-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyDegreeEmisReturns" */ "../js/components/Institution/surveys/degree/EmisReturns.vue"
    )
);
Vue.component("survey-degree-section-a", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSectionA" */ "../js/components/Institution/surveys/degree/SectionA.vue"
    )
);
Vue.component("survey-degree-section-b", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSectionB" */ "../js/components/Institution/surveys/degree/SectionB.vue"
    )
);
Vue.component("survey-degree-section-c", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSectionC" */ "../js/components/Institution/surveys/degree/SectionC.vue"
    )
);
Vue.component("survey-degree-section-d", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSectionD" */ "../js/components/Institution/surveys/degree/SectionD.vue"
    )
);
Vue.component("survey-degree-institution-courses", () =>
    import(
        /* webpackChunkName: "SurveyDegreeInstitutionCourses" */ "../js/components/Institution/surveys/degree/InstitutionCourses.vue"
    )
);
Vue.component("survey-degree-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLearnerFormCreate" */ "../js/components/Institution/surveys/degree/LearnerFormCreate.vue"
    )
);
Vue.component("survey-degree-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLearnerImportUgandans" */ "../js/components/Institution/surveys/degree/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-degree-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLearnerImportForeigners" */ "../js/components/Institution/surveys/degree/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-degree-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLearnerImportRefugees" */ "../js/components/Institution/surveys/degree/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-degree-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDegreeTeacherFormCreate" */ "../js/components/Institution/surveys/degree/TeacherFormCreate.vue"
    )
);
Vue.component("survey-degree-lecturer-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLecturerImportUgandans" */ "../js/components/Institution/surveys/degree/TeacherImportUgandans.vue"
    )
);
Vue.component("survey-degree-lecturer-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyDegreeLecturerImportForeigners" */ "../js/components/Institution/surveys/degree/TeacherImportForeigners.vue"
    )
);
Vue.component("survey-degree-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyDegreeNonAcademicStaff" */ "../js/components/Institution/surveys/degree/NonAcademicStaff.vue"
    )
);
Vue.component("survey-degree-non-teaching-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyDegreeNonTeachingStaffFormCreate" */ "../js/components/Institution/surveys/degree/NonTeachingStaffFormCreate.vue"
    )
);
Vue.component("survey-degree-non-academic-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyDegreeNonTeachingStaffImportUgandans" */ "../js/components/Institution/surveys/degree/NonTeachingStaffImportUgandans.vue"
    )
);
Vue.component("survey-degree-non-academic-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyDegreeNonTeachingStaffImportForeigners" */ "../js/components/Institution/surveys/degree/NonTeachingStaffImportForeigners.vue"
    )
);
Vue.component("survey-degree-section-ict", () =>
    import(
        /* webpackChunkName: "SurveyDegreeIct" */ "../js/components/Institution/surveys/degree/ict/IctFacilities.vue"
    )
);
Vue.component("survey-degree-internet-sources", () =>
    import(
        /* webpackChunkName: "SurveyDegreeInternetSource" */ "../js/components/Institution/surveys/degree/ict/InternetSources.vue"
    )
);
Vue.component("survey-degree-section-sports", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSectionSports" */ "../js/components/Institution/surveys/degree/pe-sports/SportsSection.vue"
    )
);
Vue.component("survey-degree-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSportsEquipment" */ "../js/components/Institution/surveys/degree/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component("survey-degree-extra-curricular-activity-participation", () =>
    import(
        /* webpackChunkName: "SurveyDegreeExtraCurricularActivityParticipation" */ "../js/components/Institution/surveys/degree/pe-sports/ExtraCurricularActivityParticipation.vue"
    )
);
Vue.component("survey-degree-sports-activities", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSportsActivities" */ "../js/components/Institution/surveys/degree/pe-sports/SportsActivitiesSection.vue"
    )
);
Vue.component("survey-degree-sports-facilities", () =>
    import(
        /* webpackChunkName: "SurveyDegreeSportsFacilities" */ "../js/components/Institution/surveys/degree/pe-sports/SportsFacilitiesSection.vue"
    )
);
Vue.component("survey-degree-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyDegreeGpsLocation" */ "../js/components/Institution/surveys/degree/location/LocationSection.vue"
    )
);

//INTERNATIONAL SURVEY
Vue.component("survey-international-emis-returns", () =>
    import(
        /* webpackChunkName: "SurveyInternationalEmisReturns" */ "../js/components/Institution/surveys/international/EmisReturns.vue"
    )
);
Vue.component("survey-international-section-a", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSectionA" */ "../js/components/Institution/surveys/international/SectionA.vue"
    )
);
Vue.component("survey-international-section-b", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSectionB" */ "../js/components/Institution/surveys/international/SectionB.vue"
    )
);
Vue.component("survey-international-curriculums", () =>
    import(
        /* webpackChunkName: "SurveyInternationalCurriculums" */ "../js/components/Institution/surveys/international/Curriculums.vue"
    )
);
Vue.component("survey-international-calendars", () =>
    import(
        /* webpackChunkName: "SurveyInternationalCalendars" */ "../js/components/Institution/surveys/international/Calendars.vue"
    )
);
Vue.component("survey-international-section-c", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSectionC" */ "../js/components/Institution/surveys/international/SectionC.vue"
    )
);
Vue.component("survey-international-section-d", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSectionD" */ "../js/components/Institution/surveys/international/SectionD.vue"
    )
);
Vue.component("survey-international-learner-form-create", () =>
    import(
        /* webpackChunkName: "SurveyInternationalLearnerFormCreate" */ "../js/components/Institution/surveys/international/LearnerFormCreate.vue"
    )
);
Vue.component("survey-international-learner-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyInternationalLearnerImportUgandans" */ "../js/components/Institution/surveys/international/LearnerImportUgandans.vue"
    )
);
Vue.component("survey-international-learner-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyInternationalLearnerImportForeigners" */ "../js/components/Institution/surveys/international/LearnerImportNonRefugees.vue"
    )
);
Vue.component("survey-international-learner-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyInternationalLearnerImportRefugees" */ "../js/components/Institution/surveys/international/LearnerImportRefugees.vue"
    )
);
Vue.component("survey-international-teacher-form-create", () =>
    import(
        /* webpackChunkName: "SurveyInternationalTeacherFormCreate" */ "../js/components/Institution/surveys/international/TeacherFormCreate.vue"
    )
);
Vue.component("survey-international-teacher-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyInternationalTeacherImportUgandans" */ "../js/components/Institution/surveys/international/TeacherImportUgandans.vue"
    )
);
Vue.component("survey-international-teacher-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyInternationalTeacherImportForeigners" */ "../js/components/Institution/surveys/international/TeacherImportForeigners.vue"
    )
);
Vue.component("survey-international-teacher-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyInternationalTeacherImportRefugees" */ "../js/components/Institution/surveys/international/TeacherImportRefugees.vue"
    )
);
Vue.component("survey-international-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "SurveyInternationalNonAcademicStaff" */ "../js/components/Institution/surveys/international/NonAcademicStaff.vue"
    )
);
Vue.component("survey-international-non-teaching-staff-form-create", () =>
    import(
        /* webpackChunkName: "SurveyInternationalNonTeachingStaffFormCreate" */ "../js/components/Institution/surveys/international/NonTeachingStaffFormCreate.vue"
    )
);
Vue.component("survey-international-non-academic-staff-import-ugandans", () =>
    import(
        /* webpackChunkName: "SurveyInternationalNonTeachingStaffImportUgandans" */ "../js/components/Institution/surveys/international/NonTeachingStaffImportUgandans.vue"
    )
);
Vue.component("survey-international-non-academic-staff-import-foreigners", () =>
    import(
        /* webpackChunkName: "SurveyInternationalNonTeachingStaffImportForeigners" */ "../js/components/Institution/surveys/international/NonTeachingStaffImportForeigners.vue"
    )
);
Vue.component("survey-international-non-academic-staff-import-refugees", () =>
    import(
        /* webpackChunkName: "SurveyInternationalNonTeachingStaffImportRefugees" */ "../js/components/Institution/surveys/international/NonTeachingStaffImportRefugees.vue"
    )
);
Vue.component("survey-international-section-ict", () =>
    import(
        /* webpackChunkName: "SurveyInternationalIct" */ "../js/components/Institution/surveys/international/ict/IctFacilities.vue"
    )
);
Vue.component("survey-international-internet-sources", () =>
    import(
        /* webpackChunkName: "SurveyInternationalInternetSource" */ "../js/components/Institution/surveys/international/ict/InternetSources.vue"
    )
);
Vue.component("survey-international-section-sports", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSectionSports" */ "../js/components/Institution/surveys/international/pe-sports/SportsSection.vue"
    )
);
Vue.component("survey-international-sports-equipment", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSportsEquipment" */ "../js/components/Institution/surveys/international/pe-sports/SportsEquipmentSection.vue"
    )
);
Vue.component("survey-international-sports-facilities", () =>
    import(
        /* webpackChunkName: "SurveyInternationalSportsFacilities" */ "../js/components/Institution/surveys/international/pe-sports/SportsFacilitiesSection.vue"
    )
);
Vue.component("survey-international-gps-location-section", () =>
    import(
        /* webpackChunkName: "SurveyInternationalGpsLocation" */ "../js/components/Institution/surveys/international/location/LocationSection.vue"
    )
);

//Smc members upload
Vue.component("pre-primary-smc-members-upload", () =>
    import(
        /* webpackChunkName: "InstitutionsPrePrimaryUploadSMCMembers" */ "../js/components/Institution/smc-members-upload/PrePrimaryUploadSMCMembers.vue"
    )
);
Vue.component("primary-smc-members-upload", () =>
    import(
        /* webpackChunkName: "InstitutionsPrimaryUploadSMCMembers" */ "../js/components/Institution/smc-members-upload/PrimaryUploadSMCMembers.vue"
    )
);
Vue.component("secondary-smc-members-upload", () =>
    import(
        /* webpackChunkName: "InstitutionsSecondaryUploadSMCMembers" */ "../js/components/Institution/smc-members-upload/SecondaryUploadSMCMembers.vue"
    )
);
Vue.component("post-primary-smc-members-upload", () =>
    import(
        /* webpackChunkName: "InstitutionsPostPrimaryUploadSMCMembers" */ "../js/components/Institution/smc-members-upload/PostPrimaryUploadSMCMembers.vue"
    )
);
Vue.component("tertiary-smc-members-upload", () =>
    import(
        /* webpackChunkName: "InstitutionsTertiaryUploadSMCMembers" */ "../js/components/Institution/smc-members-upload/TertiaryUploadSMCMembers.vue"
    )
);

// Learners
Vue.component("school-learners-upload-many", () =>
    import(
        /* webpackChunkName: "LearnersUploadMany" */ "../js/components/Institution/learners/UploadMany.vue"
    )
);
Vue.component("school-learners-degree", () =>
    import(
        /* webpackChunkName: "LearnersIndexDegree" */ "../js/components/Institution/learners/IndexDegree.vue"
    )
);
Vue.component("school-learners", () =>
    import(
        /* webpackChunkName: "LearnersIndex" */ "../js/components/Institution/learners/Index.vue"
    )
);
Vue.component("learners-overview", () =>
    import(
        /* webpackChunkName: "LearnersOverview" */ "../js/components/Institution/learners/Overview.vue"
    )
);
Vue.component("expected-enrolment", () =>
    import(
        /* webpackChunkName: "ExpectedEnrolment" */ "../js/components/Institution/learners/ExpectedEnrolment.vue"
    )
);
Vue.component("school-learners-transfers", () =>
    import(
        /* webpackChunkName: "LearnersTransfers" */ "../js/components/Institution/learners/Transfers.vue"
    )
);
Vue.component("incoming-learner-transfers", () =>
    import(
        /* webpackChunkName: "IncomingLearnerTransfers" */ "../js/components/Institution/learners/IncomingLearnerTransfers.vue"
    )
);
Vue.component("outgoing-learner-transfers", () =>
    import(
        /* webpackChunkName: "OutgoingLearnerTransfers" */ "../js/components/Institution/learners/OutgoingLearnerTransfers.vue"
    )
);
Vue.component("learner-photo-bulk-uploads", () =>
    import(
        /* webpackChunkName: "LearnersPhotoBulkUploads" */ "../js/components/Institution/learners/PhotoBulkUploads.vue"
    )
);
Vue.component("school-learners-promotions", () =>
    import(
        /* webpackChunkName: "LearnersPromotions" */ "../js/components/Institution/learners/Promotions.vue"
    )
);
Vue.component("school-learners-enrolments", () =>
    import(
        /* webpackChunkName: "LearnersEnrolments" */ "../js/components/Institution/learners/Enrolments.vue"
    )
);
Vue.component("school-learners-pending-promotions", () =>
    import(
        /* webpackChunkName: "LearnersPendingPromotions" */ "../js/components/Institution/learners/PromotionsPending.vue"
    )
);
Vue.component("school-learners-completed-promotions", () =>
    import(
        /* webpackChunkName: "LearnersCompletedPromotions" */ "../js/components/Institution/learners/PromotionsComplete.vue"
    )
);
Vue.component("school-learners-claims", () =>
    import(
        /* webpackChunkName: "LearnersClaims" */ "../js/components/Institution/learners/Claims.vue"
    )
);
Vue.component("school-learners-transitions", () =>
    import(
        /* webpackChunkName: "LearnersTransitions" */ "../js/components/Institution/learners/TransitionForm.vue"
    )
);
Vue.component("school-learners-o-level-index-numbers", () =>
    import(
        /* webpackChunkName: "LearnersIndexNumbersOLevel" */ "../js/components/Institution/learners/IndexNumbersOLevel.vue"
    )
);
Vue.component("school-learners-a-level-index-numbers", () =>
    import(
        /* webpackChunkName: "LearnersIndexNumbersALevel" */ "../js/components/Institution/learners/IndexNumbersALevel.vue"
    )
);
Vue.component("school-learners-index-numbers", () =>
    import(
        /* webpackChunkName: "LearnersIndexNumbers" */ "../js/components/Institution/learners/IndexNumbers.vue"
    )
);
Vue.component("school-candidates-index-numbers", () =>
    import(
        /* webpackChunkName: "CandidatesIndexNumbers" */ "../js/components/Institution/learners/CandidatesIndexNumbers.vue"
    )
);
Vue.component("incoming-learner-claims-form", () =>
    import(
        /* webpackChunkName: "IncomingLearnersClaimsForm" */ "../js/components/Institution/learners/IncomingLearnerClaimsForm.vue"
    )
);
Vue.component("incoming-learner-claims", () =>
    import(
        /* webpackChunkName: "IncomingLearnerClaims" */ "../js/components/Institution/learners/IncomingLearnerClaims.vue"
    )
);
Vue.component("outgoing-learner-claims", () =>
    import(
        /* webpackChunkName: "OutgoingLearnerClaims" */ "../js/components/Institution/learners/OutgoingLearnerClaims.vue"
    )
);
Vue.component("school-learners-profile", () =>
    import(
        /* webpackChunkName: "LearnersProfile" */ "../js/components/Institution/learners/Profile.vue"
    )
);
Vue.component("candidate-details-table", () =>
    import(
        /* webpackChunkName: "CandidateDetailsTable" */ "../js/components/Institution/learners/tables/CandidateDetailsTable.vue"
    )
);
Vue.component("uneb-details-table", () =>
    import(
        /* webpackChunkName: "UnebDetailsTable" */ "../js/components/Institution/learners/tables/UnebDetailsTable.vue"
    )
);
Vue.component("uneb-equated-code-details-table", () =>
    import(
        /* webpackChunkName: "UnebEquatedCodeDetailsTable" */ "../js/components/Institution/learners/tables/UnebEquatedCodeDetailsTable.vue"
    )
);
Vue.component("nira-details-table", () =>
    import(
        /* webpackChunkName: "NiraDetailsTable" */ "../js/components/Institution/learners/tables/NiraDetailsTable.vue"
    )
);
Vue.component("school-learners-profile-degree", () =>
    import(
        /* webpackChunkName: "LearnersProfileDegree" */ "../js/components/Institution/learners/ProfileDegree.vue"
    )
);
Vue.component("parents", () =>
    import(
        /* webpackChunkName: "LearnersParents" */ "../js/components/Institution/learners/Parents.vue"
    )
);
Vue.component("school-parents-report", () =>
    import(
        /* webpackChunkName: "SchoolParentsReport" */ "../js/components/Institution/parents/Index.vue"
    )
);

Vue.component("special-needs", () =>
    import(
        /* webpackChunkName: "LearnersSpecialNeeds" */ "../js/components/Institution/learners/SpecialNeeds.vue"
    )
);
Vue.component("health-issues", () =>
    import(
        /* webpackChunkName: "LearnersHealthIssues" */ "../js/components/Institution/learners/HealthIssues.vue"
    )
);
Vue.component("talents", () =>
    import(
        /* webpackChunkName: "LearnersTalents" */ "../js/components/Institution/learners/Talents.vue"
    )
);
Vue.component("learner-practical-skills", () =>
    import(
        /* webpackChunkName: "LearnersPracticalSkills" */ "../js/components/Institution/learners/PracticalSkills.vue"
    )
);
Vue.component("learner-subjects", () =>
    import(
        /* webpackChunkName: "LearnerSubjects" */ "../js/components/Institution/learners/Subjects.vue"
    )
);
Vue.component("familiar-languages", () =>
    import(
        /* webpackChunkName: "LearnersFamiliarLanguages" */ "../js/components/Institution/learners/FamiliarLanguages.vue"
    )
);
Vue.component("incoming-learner-search-lin-form-primary", () =>
    import(
        /* webpackChunkName: "SearchLinFormPrimary" */ "../js/components/Institution/learners/SearchLinPrimaryForm.vue"
    )
);
Vue.component("incoming-learner-search-lin-form-secondary", () =>
    import(
        /* webpackChunkName: "SearchLinFormSecondary" */ "../js/components/Institution/learners/SearchLinSecondaryForm.vue"
    )
);
Vue.component("incoming-learner-search-lin-form-international", () =>
    import(
        /* webpackChunkName: "SearchLinFormInternational" */ "../js/components/Institution/learners/SearchLinInternationalForm.vue"
    )
);
Vue.component("missing-class-learners", () =>
    import(
        /* webpackChunkName: "MissingClassLearners" */ "../js/components/Institution/learners/MissingClassLearnersForm.vue"
    )
);
Vue.component("learners-stuck-in-2022", () =>
    import(
        /* webpackChunkName: "LearnersStuckIn2022" */ "../js/components/Institution/learners/LearnersStuckIn2022Form.vue"
    )
);

//Data update
Vue.component("school-learners-for-deleting", () =>
    import(
        /* webpackChunkName: "LearnersDeleted" */ "../js/components/Institution/learners/LearnersDeleted.vue"
    )
);
Vue.component("school-learner-flagged-view-application", () =>
    import(
        /* webpackChunkName: "LearnerDeletedViewApplication" */ "../js/components/Institution/learners/LearnerDeletedViewApplication.vue"
    )
);

// Staff
Vue.component("hr-teachers", () =>
    import(
        /* webpackChunkName: "HrTeachers" */ "../js/components/Institution/staff/Teachers.vue"
    )
);
Vue.component("hr-teacher-profile", () =>
    import(
        /* webpackChunkName: "HrTeacherProfile" */ "../js/components/Institution/staff/TeacherProfile.vue"
    )
);
Vue.component("hr-teacher-contacts", () =>
    import(
        /* webpackChunkName: "HrTeacherContacts" */ "../js/components/Institution/staff/TeacherContacts.vue"
    )
);
Vue.component("hr-teacher-qualifications", () =>
    import(
        /* webpackChunkName: "HrTeacherQualifications" */ "../js/components/Institution/staff/TeacherQualifications.vue"
    )
);
Vue.component("hr-teacher-subjects", () =>
    import(
        /* webpackChunkName: "HrTeacherSubjects" */ "../js/components/Institution/staff/Subjects.vue"
    )
);
Vue.component("hr-teachers-upload-many", () =>
    import(
        /* webpackChunkName: "HrUploadManyTeachers" */ "../js/components/Institution/staff/UploadManyTeachers.vue"
    )
);
Vue.component("all-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "HrNonTeachingStaff" */ "../js/components/Institution/staff/NonTeachingStaff.vue"
    )
);
Vue.component("hr-non-teaching-staff-profile", () =>
    import(
        /* webpackChunkName: "HrNonTeachingStaffProfile" */ "../js/components/Institution/staff/NonTeachingStaffProfile.vue"
    )
);
Vue.component("non-teaching-staff-upload-many", () =>
    import(
        /* webpackChunkName: "NonTeachingStaffUploadMany" */ "../js/components/Institution/staff/NonTeachingStaffUploadMany.vue"
    )
);
Vue.component("hr-non-teaching-contacts", () =>
    import(
        /* webpackChunkName: "HrNonTeachingContacts" */ "../js/components/Institution/staff/NonTeachingContacts.vue"
    )
);
Vue.component("hr-postings-teachers", () =>
    import(
        /* webpackChunkName: "HrPostingsTeachers" */ "../js/components/Institution/staff/postings/Teachers/Index.vue"
    )
);
Vue.component("hr-transfers-teachers", () =>
    import(
        /* webpackChunkName: "HrTransfersTeachers" */ "../js/components/Institution/staff/transfers/Teachers/Index.vue"
    )
);
Vue.component("hr-transfers-teachers-incoming", () =>
    import(
        /* webpackChunkName: "HrTransfersTeachersIncoming" */ "../js/components/Institution/staff/transfers/Teachers/Incoming.vue"
    )
);
Vue.component("hr-transfers-teachers-outgoing", () =>
    import(
        /* webpackChunkName: "HrTransfersTeachersOutgoing" */ "../js/components/Institution/staff/transfers/Teachers/Outgoing.vue"
    )
);
Vue.component("hr-postings-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "HrPostingsNonTeachingStaff" */ "../js/components/Institution/staff/postings/NonTeachingStaff/Index.vue"
    )
);
Vue.component("hr-transfers-non-teaching-staff", () =>
    import(
        /* webpackChunkName: "HrTransfersNonTeachingStaff" */ "../js/components/Institution/staff/transfers/NonTeachingStaff/Index.vue"
    )
);
Vue.component("hr-transfers-non-teaching-staff-incoming", () =>
    import(
        /* webpackChunkName: "HrTransfersNonTeachingStaffIncoming" */ "../js/components/Institution/staff/transfers/NonTeachingStaff/Incoming.vue"
    )
);
Vue.component("hr-transfers-non-teaching-staff-outgoing", () =>
    import(
        /* webpackChunkName: "HrTransfersNonTeachingStaffOutgoing" */ "../js/components/Institution/staff/transfers/NonTeachingStaff/Outgoing.vue"
    )
);

// Infrastructure
Vue.component("infrastructure-categories", () =>
    import(
        /* webpackChunkName: "InfrastructureTypes" */ "../js/components/Institution/infrastructure/CategoriesSection.vue"
    )
);
Vue.component("infrastructure-main-facilities", () =>
    import(
        /* webpackChunkName: "InfrastructureMainFacilities" */ "../js/components/Institution/infrastructure/MainFacilitiesSection.vue"
    )
);

// Facilities
Vue.component("energy-sources", () =>
    import(
        /* webpackChunkName: "InstitutionsEnergySources" */ "../js/components/Institution/facilities/EnergySources.vue"
    )
);
Vue.component("water-sources", () =>
    import(
        /* webpackChunkName: "InstitutionsWaterSources" */ "../js/components/Institution/facilities/WaterSources.vue"
    )
);
Vue.component("ict-facilities", () =>
    import(
        /* webpackChunkName: "InstitutionsIctFacilities" */ "../js/components/Institution/facilities/IctFacilities.vue"
    )
);
Vue.component("post-primary-ict-facilities", () =>
    import(
        /* webpackChunkName: "InstitutionsPostPrimaryIctFacilities" */ "../js/components/Institution/facilities/post-primary-schools/IctFacilities.vue"
    )
);
Vue.component("garbage-disposal-methods", () =>
    import(
        /* webpackChunkName: "InstitutionsGarbageDisposalMethods" */ "../js/components/Institution/facilities/GarbageDisposalMethods.vue"
    )
);
Vue.component("hand-washing-facilities", () =>
    import(
        /* webpackChunkName: "InstitutionsHandWashingFacilities" */ "../js/components/Institution/facilities/HandWashingFacilities.vue"
    )
);
Vue.component("other-facilities", () =>
    import(
        /* webpackChunkName: "InstitutionsOtherFacilities" */ "../js/components/Institution/facilities/OtherFacilities.vue"
    )
);

// PE & Sports
Vue.component("extra-curricular-activities", () =>
    import(
        /* webpackChunkName: "InstitutionsExtraCurricularActivities" */ "../js/components/Institution/pe-sports/ExtraCurricularActivities.vue"
    )
);
Vue.component("extra-curricular-activity-participants", () =>
    import(
        /* webpackChunkName: "InstitutionsExtraCurricularActivityParticipants" */ "../js/components/Institution/pe-sports/ExtraCurricularActivityParticipants.vue"
    )
);
Vue.component("pre-primary-sports-equipment", () =>
    import(
        /* webpackChunkName: "InstitutionsPrePrimarySportsEquipment" */ "../js/components/Institution/pe-sports/PrePrimarySportsEquipment.vue"
    )
);
Vue.component("practical-skills", () =>
    import(
        /* webpackChunkName: "InstitutionsPracticalSkills" */ "../js/components/Institution/pe-sports/PracticalSkills.vue"
    )
);
Vue.component("sports-activities", () =>
    import(
        /* webpackChunkName: "InstitutionsSportsActivities" */ "../js/components/Institution/pe-sports/SportsActivities.vue"
    )
);

//Learning & Playing Materials
Vue.component("learning-and-playing-materials", () =>
    import(
        /* webpackChunkName: "InstitutionsLearningAndPlayingMaterials" */ "../js/components/Institution/instructional-materials/LearningAndPlayingMaterials.vue"
    )
);

// Finance
Vue.component("finance-school-expense", () =>
    import(
        /* webpackChunkName: "InstitutionsExpense" */ "../js/components/Institution/finance/Expense.vue"
    )
);
Vue.component("finance-school-budget", () =>
    import(
        /* webpackChunkName: "InstitutionsBudget" */ "../js/components/Institution/finance/Budget.vue"
    )
);
Vue.component("finance-school-income", () =>
    import(
        /* webpackChunkName: "InstitutionsIncome" */ "../js/components/Institution/finance/Income.vue"
    )
);

// Uploaded excels
Vue.component("institution-uploaded-excels", () =>
    import(
        /* webpackChunkName: "InstitutionUploadedExcels" */ "../js/components/Institution/UploadedExcels.vue"
    )
);

//Notifications
//Vue.component('learning-and-playing-materials', () => import(/* webpackChunkName: "InstitutionsLearningAndPlayingMaterials" */ '../js/components/Institution/instructional-materials/LearningAndPlayingMaterials.vue'));

//Download Centre
Vue.component("download-centre", () =>
    import(
        /* webpackChunkName: "DownloadCentre" */ "../js/components/Institution/download-centre/DownloadCentre.vue"
    )
);

//Learner Enrolment Applications
Vue.component("learner-enrolment-applications-list", () =>
    import(
        /* webpackChunkName: "LearnerEnrolmentApplicationsList" */ "../js/components/Institution/learner-enrolment-applications/Index.vue"
    )
);
Vue.component("learner-enrolment-applications-show", () =>
    import(
        /* webpackChunkName: "LearnerEnrolmentApplicationsShow" */ "../js/components/Institution/learner-enrolment-applications/Show.vue"
    )
);
