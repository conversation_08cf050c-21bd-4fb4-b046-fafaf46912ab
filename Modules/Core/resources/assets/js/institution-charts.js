import Chart from 'chart.js/auto';

// Function to initialize the enrolment chart
export function initializeEnrolmentChart() {
    // Check if enrolmentData is available
    // if (!window.enrolmentData) {
    //     console.error('Enrolment data is not available.');
    //     return;
    // }

    // console.log('Enrolment Data:', window.enrolmentData);

    // Learner Enrolment data for the chart
    const { maleEnrolments, femaleEnrolments, labels } = window.enrolmentData;

    // Ensure enrolment data is available
    // if (!maleEnrolments || !femaleEnrolments || !labels) {
    //     console.error('Enrolment data is incomplete.');
    //     return;
    // }

    // Fifth chart (Learner Enrolment Bar Chart)
    const ctxEnrolmentBar = document.getElementById('chartStacked1').getContext('2d');
    if (ctxEnrolmentBar) {
        new Chart(ctxEnrolmentBar, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Male',
                        data: maleEnrolments,
                        backgroundColor: '#6610f2',
                        barThickness: 12, // Thickness of individual bars
                         barPercentage: 0.3, // Control the width of the bars
                        categoryPercentage: 0.2, // Control the spacing between bars
                    },
                    {
                        label: 'Female',
                        data: femaleEnrolments,
                        backgroundColor: '#00879B',
                        barThickness: 12, // Thickness of individual bars
                         barPercentage: 0.3, // Control the width of the bars
                        categoryPercentage: 0.2, // Control the spacing between bars
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,

                plugins: {
                    legend: {
                        display: false // Hide the legend
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    datalabels: {
                        display: false // Hide labels inside the bars
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                animation: {
                    onComplete: function (animation) {
                        const firstSet = animation.chart.config.data.datasets[0].data;
                        const dataSum = firstSet.reduce((previousValue, currentValue) => previousValue + currentValue, 0);

                        if (dataSum === 0) {
                            document.getElementById('sch-bar-data-1').style.display = 'none';
                        }
                    }
                },
          
            }
        });
    } else {
        console.error('Chart canvas element for enrolment bar chart not found.');
    }
}
