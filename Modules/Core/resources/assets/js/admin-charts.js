import Chart from 'chart.js/auto';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Register the plugin with Chart.js
Chart.register(ChartDataLabels);

// Helper function to destroy existing charts
const destroyExistingChart = (canvasId) => {
    const canvas = document.getElementById(canvasId);
    if (canvas && canvas.chart) {
        canvas.chart.destroy();
    }
};

// Initialize charts
export function initializeCharts() {
    // Destroy existing charts
    destroyExistingChart('flotPieLearnersDashboard');
    destroyExistingChart('flotPieTeachers');
    destroyExistingChart('chartBar4');
    destroyExistingChart('chartBar5');

    // Learners chart data
    const {
        prePrimaryLearners,
        primaryLearners,
        secondaryLearners,
        certificateLearners,
        diplomaLearners,
        degreeLearners,
        internationalLearners
    } = window.chartData.learners;

    // Teaching staff chart data
    const {
        prePrimaryStaff,
        primaryStaff,
        secondaryStaff,
        certificateStaff,
        diplomaStaff,
        degreeStaff,
        internationalStaff
    } = window.chartData.staff;

    // Schools chart data
    const {
        prePrimarySchoolsEmis,
        primarySchoolsEmis,
        secondarySchoolsEmis,
        certificateSchoolsEmis,
        diplomaSchoolsEmis,
        degreeSchoolsEmis,
        internationalSchoolsEmis
    } = window.chartData.schools;

    // Termly enrollment data
    const {
        prePrimaryEnrolment,
        primaryEnrolment,
        secondaryEnrolment,
        certificateEnrolment,
        termLabels
    } = window.chartData.enrolment;

    // Helper function to get the percentage
    const getPercentage = (value, total) => ((value / total) * 100).toFixed(2);

    // First chart (Learners)
    const ctxLearners = document.getElementById('flotPieLearnersDashboard').getContext('2d');
    const chartLearners = new Chart(ctxLearners, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    prePrimaryLearners,
                    primaryLearners,
                    secondaryLearners,
                    certificateLearners,
                    diplomaLearners,
                    degreeLearners,
                    internationalLearners
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-1').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieLearnersDashboard').chart = chartLearners;

    // Second chart (Teaching Staff)
    const ctxStaff = document.getElementById('flotPieTeachers').getContext('2d');
    const chartStaff = new Chart(ctxStaff, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    prePrimaryStaff,
                    primaryStaff,
                    secondaryStaff,
                    certificateStaff,
                    diplomaStaff,
                    degreeStaff,
                    internationalStaff
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-2').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieTeachers').chart = chartStaff;

    // Third chart (Schools)
    const ctxSchools = document.getElementById('chartBar4').getContext('2d');
    const chartSchools = new Chart(ctxSchools, {
        type: 'bar',
        data: {
            labels: ['Pre-Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                label: 'Schools',
                data: [
                    prePrimarySchoolsEmis,
                    primarySchoolsEmis,
                    secondarySchoolsEmis,
                    certificateSchoolsEmis,
                    diplomaSchoolsEmis,
                    degreeSchoolsEmis,
                    internationalSchoolsEmis
                ],
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y', // Make the bars horizontal
            scales: {
                x: { // y axis now becomes x
                    beginAtZero: true,
                    ticks: {
                        fontSize: 10,
                        callback: value => value.toLocaleString(), // Humanize values
                    }
                },
                y: { // x axis now becomes y
                    ticks: {
                        fontSize: 11,
                    }
                }
            },
            plugins: {
                datalabels: {
                    display: false
                },
                legend: {
                    display: false
                }
            }
        }
    });
    document.getElementById('chartBar4').chart = chartSchools;

    // Fourth chart (Termly Enrollment)
    const ctxEnrolment = document.getElementById('chartBar5').getContext('2d');
    const chartEnrolment = new Chart(ctxEnrolment, {
        type: 'bar',
        data: {
            labels: termLabels,
            datasets: [{
                label: 'Pre-Primary',
                data: prePrimaryEnrolment,
                backgroundColor: '#6f42c1'
            }, {
                label: 'Primary',
                data: primaryEnrolment,
                backgroundColor: '#1458A1'
            }, {
                label: 'Secondary',
                data: secondaryEnrolment,
                backgroundColor: '#03879A'
            }, {
                label: 'Certificate',
                data: certificateEnrolment,
                backgroundColor: '#00cccc'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y', // Make the bars horizontal
            scales: {
                x: { // y axis now becomes x
                    beginAtZero: true,
                    ticks: {
                        fontSize: 11,
                        callback: value => value.toLocaleString(), // Humanize values
                    }
                },
                y: { // x axis now becomes y
                    ticks: {
                        fontSize: 11,
                    }
                }
            },
            plugins: {
                datalabels: {
                    display: false
                },
                legend: {
                    display: false
                }
            }
        }
    });
    document.getElementById('chartBar5').chart = chartEnrolment;
}

// Initialize Enrollment chart separately
export function initializeEnrolmentChart() {
    const enrolmentCanvas = document.getElementById('institutionEnrollmentChart');
    if (enrolmentCanvas) {
        const ctx = enrolmentCanvas.getContext('2d');
        // Destroy existing chart
        if (enrolmentCanvas.chart) {
            enrolmentCanvas.chart.destroy();
        }
        enrolmentCanvas.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: enrolment.labels,
                datasets: [{
                    label: 'Enrollment',
                    data: enrolment.values,
                    backgroundColor: '#007bff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

