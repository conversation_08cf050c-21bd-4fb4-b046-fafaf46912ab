// Import Chart.js and the DataLabels plugin
import Chart from 'chart.js/auto';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Register the DataLabels plugin with Chart.js
Chart.register(ChartDataLabels);

// Helper function to destroy existing charts
const destroyExistingChart = (canvasId) => {
    const canvas = document.getElementById(canvasId);
    if (canvas && canvas.chart) {
        canvas.chart.destroy();
    }
};

// Initialize charts
export function initializeClustersCharts() {
    // Destroy existing charts if they exist
    destroyExisting<PERSON>hart('flotPieLearnersDashboard');
    destroyExistingChart('flotPieTeachers');
    destroyExistingChart('chartBar4');
    destroyExistingChart('chartBar5');
    destroyExistingChart('flotPieStaff');
    destroyExistingChart('flotPieInfrastructure');
    destroyExistingChart('flotPieNonTeachingStaff');

    // Extract chart data from the window object
    const {
        prePrimaryLearners,
        primaryLearners,
        secondaryLearners,
        certificateLearners,
        diplomaLearners,
        degreeLearners,
        internationalLearners
    } = window.chartData.learners;

    const {
        prePrimaryStaff,
        primaryStaff,
        secondaryStaff,
        certificateStaff,
        diplomaStaff,
        degreeStaff,
        internationalStaff,
        pre_primary_teaching_staff,
        primary_teaching_staff,
        secondary_teaching_staff,
        certificate_teaching_staff,
        diploma_teaching_staff,
        degree_teaching_staff,
        international_teaching_staff,
        pre_primary_non_teaching_staff,
        primary_non_teaching_staff,
        secondary_non_teaching_staff,
        certificate_non_teaching_staff,
        diploma_non_teaching_staff,
        degree_non_teaching_staff,
        international_non_teaching_staff
    } = window.chartData.staff;

    const {
        prePrimarySchoolsEmis,
        primarySchoolsEmis,
        secondarySchoolsEmis,
        certificateSchoolsEmis,
        diplomaSchoolsEmis,
        degreeSchoolsEmis,
        internationalSchoolsEmis
    } = window.chartData.schools;

    const {
        prePrimaryEnrolment,
        primaryEnrolment,
        secondaryEnrolment,
        certificateEnrolment,
        termLabels
    } = window.chartData.enrolment;

    const {
        pre_primary_infrastructure,
        primary_infrastructure,
        secondary_infrastructure,
        certificate_infrastructure,
        diploma_infrastructure,
        degree_infrastructure,
        international_infrastructure
    } = window.chartData.infrastructure;

    // Helper function to calculate percentage
    const getPercentage = (value, total) => ((value / total) * 100).toFixed(2);

    // First Chart: Learners (Doughnut)
    const ctxLearners = document.getElementById('flotPieLearnersDashboard').getContext('2d');
    const chartLearners = new Chart(ctxLearners, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    prePrimaryLearners,
                    primaryLearners,
                    secondaryLearners,
                    certificateLearners,
                    diplomaLearners,
                    degreeLearners,
                    internationalLearners
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-1').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieLearnersDashboard').chart = chartLearners;

    // Second Chart: Teaching Staff (Doughnut)
    const ctxStaff = document.getElementById('flotPieTeachers').getContext('2d');
    const chartStaff = new Chart(ctxStaff, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    prePrimaryStaff,
                    primaryStaff,
                    secondaryStaff,
                    certificateStaff,
                    diplomaStaff,
                    degreeStaff,
                    internationalStaff
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-2').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieTeachers').chart = chartStaff;

    // New Chart: Non-Teaching Staff (Doughnut)
    const ctxNonTeachingStaff = document.getElementById('flotPieNonTeachingStaff').getContext('2d');
    const chartNonTeachingStaff = new Chart(ctxNonTeachingStaff, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    pre_primary_non_teaching_staff,
                    primary_non_teaching_staff,
                    secondary_non_teaching_staff,
                    certificate_non_teaching_staff,
                    diploma_non_teaching_staff,
                    degree_non_teaching_staff,
                    international_non_teaching_staff
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-3').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieNonTeachingStaff').chart = chartNonTeachingStaff;

    // Fourth Chart: Infrastructure (Doughnut)
    const ctxInfrastructure = document.getElementById('flotPieInfrastructure').getContext('2d');
    const chartInfrastructure = new Chart(ctxInfrastructure, {
        type: 'doughnut',
        data: {
            labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
                data: [
                    pre_primary_infrastructure,
                    primary_infrastructure,
                    secondary_infrastructure,
                    certificate_infrastructure,
                    diploma_infrastructure,
                    degree_infrastructure,
                    international_infrastructure
                ],
            }],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutoutPercentage: 60,
            plugins: {
                datalabels: {
                    formatter: (value, context) => {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = getPercentage(value, total);
                        return `${percentage}%`;
                    },
                    color: 'white',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    textAlign: 'center',
                    padding: 2
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function (context) {
                            const percentage = ((context.raw / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(2);
                            return `${percentage}% (${context.raw})`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            animation: {
                onComplete: function (animation) {
                    const chartData = animation.chart.config.data.datasets[0].data;
                    const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                    if (dataSum === 0) {
                        document.getElementById('pie-data-4').style.display = 'block';
                    }
                }
            }
        }
    });
    document.getElementById('flotPieInfrastructure').chart = chartInfrastructure;

   
    // Third chart (Schools)
    const ctxSchools = document.getElementById('chartBar4').getContext('2d');
    const chartSchools = new Chart(ctxSchools, {
        type: 'bar',
        data: {
            labels: ['Pre-Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
            datasets: [{
                label: 'Schools',
                data: [
                    prePrimarySchoolsEmis,
                    primarySchoolsEmis,
                    secondarySchoolsEmis,
                    certificateSchoolsEmis,
                    diplomaSchoolsEmis,
                    degreeSchoolsEmis,
                    internationalSchoolsEmis
                ],
                backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y', // Make the bars horizontal
            scales: {
                x: { // y axis now becomes x
                    beginAtZero: true,
                    ticks: {
                        fontSize: 10,
                        callback: value => value.toLocaleString(), // Humanize values
                    }
                },
                y: { // x axis now becomes y
                    ticks: {
                        fontSize: 11,
                    }
                }
            },
            plugins: {
                datalabels: {
                    display: false
                },
                legend: {
                    display: false
                }
            }
        }
    });
    document.getElementById('chartBar4').chart = chartSchools;

    // Fourth chart (Termly Enrollment)
    const ctxEnrolment = document.getElementById('chartBar5').getContext('2d');
    const chartEnrolment = new Chart(ctxEnrolment, {
        type: 'bar',
        data: {
            labels: termLabels,
            datasets: [{
                label: 'Pre-Primary',
                data: prePrimaryEnrolment,
                backgroundColor: '#6f42c1'
            }, {
                label: 'Primary',
                data: primaryEnrolment,
                backgroundColor: '#1458A1'
            }, {
                label: 'Secondary',
                data: secondaryEnrolment,
                backgroundColor: '#03879A'
            }, {
                label: 'Certificate',
                data: certificateEnrolment,
                backgroundColor: '#00cccc'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y', // Make the bars horizontal
            scales: {
                x: { // y axis now becomes x
                    beginAtZero: true,
                    ticks: {
                        fontSize: 11,
                        callback: value => value.toLocaleString(), // Humanize values
                    }
                },
                y: { // x axis now becomes y
                    ticks: {
                        fontSize: 11,
                    }
                }
            },
            plugins: {
                datalabels: {
                    display: false
                },
                legend: {
                    display: false
                }
            }
        }
    });
    document.getElementById('chartBar5').chart = chartEnrolment;
    // seventh chart:
    const ctxPieStaff = document.getElementById('flotPieStaff').getContext('2d');
const chartPieStaff = new Chart(ctxPieStaff, {
    type: 'doughnut',
    data: {
        labels: ['Pre Primary', 'Primary', 'Secondary', 'Certificate', 'Diploma', 'Degree', 'International'],
        datasets: [{
            backgroundColor: ['#6f42c1', '#1458A1', '#03879A', '#00cccc', '#007bff', '#969dab', '#A4036F'],
            data: [
                pre_primary_teaching_staff,
                primary_teaching_staff,
                secondary_teaching_staff,
                certificate_teaching_staff,
                diploma_teaching_staff,
                degree_teaching_staff,
                international_teaching_staff
            ],
        }],
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutoutPercentage: 60,
        plugins: {
            tooltip: {
                enabled: true, // Enable tooltips to show values on hover
            },
            labels: {
                render: false // Disable rendering of labels if you have any specific label plugin
            }
        },
        legend: {
            display: false // Ensure the legend is not displayed at all
        },
        animation: {
            onComplete: function (animation) {
                const chartData = animation.chart.config.data.datasets[0].data;
                const dataSum = chartData.reduce((acc, val) => acc + val, 0);

                if (dataSum === 0) {
                    document.getElementById('pie-data-4').style.display = 'block';
                }
            }
        }
    }
});

// Store the chart instance for future reference if needed
document.getElementById('flotPieStaff').chart = chartPieStaff;

}
