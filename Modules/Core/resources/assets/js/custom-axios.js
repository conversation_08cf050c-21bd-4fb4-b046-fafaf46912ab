import axios from "axios";

const instance = axios.create({
    baseURL: window.service_endpoint,
});

/**
 * Catch the AunAuthorized Request
 */
instance.interceptors.response.use(
    (response) => response,
    (error) => {
        // Check if error.response exists
        if (error.response) {
            // It's safe to access error.response.status here
            if (error.response.status === 401) {
                window.location = "/institution/login";
            }
            // Handle other specific status codes like 403
            else if (error.response.status === 403) {
                console.warn("Forbidden access: ", error.response); //TODO capture these error
                // Handle forbidden access if needed
            } else {
                console.error("Unhandled HTTP error:", error.response); //TODO capture these error
            }
        } else {
            // If error.response is undefined, handle the case for network errors or request timeouts
            if (error.code === "ECONNABORTED") {
                console.error("Request timeout:", error); //TODO capture these error
                // You could inform the user about the timeout and maybe prompt a retry
            } else {
                console.error("Network or request error: ", error); //TODO capture these error
                // You can also handle cases where no response is received
            }
        }
        // Return the error to propagate it to calling code
        return Promise.reject(error); // <-- This is important!
    }
);

export default instance;
