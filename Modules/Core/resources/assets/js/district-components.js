import Vue from 'vue';
// Upper Local Government
Vue.component('district-manage-all-schools', () => import(/* webpackChunkName: "DistrictManageAllSchools" */ './components/District/Schools/Index.vue'));
Vue.component('district-manage-all-schools-enrolments', () => import(/* webpackChunkName: "DistrictManageAllSchoolsEnrolments" */ './components/District/Schools/Enrolments/Index.vue'));

Vue.component('district-emis-number-applications-index', () => import(/* webpackChunkName: "DistrictEmisNumberApplications" */ './components/District/Emis Applications/Index.vue'));
Vue.component('district-emis-number-applications-show', () => import(/* webpackChunkName: "DistrictEmisNumberApplication" */ './components/District/Emis Applications/Show.vue'));
Vue.component('district-learner-enrolment-applications-index', () => import(/* webpackChunkName: "DistrictLearnerEnrolmentApplicationsIndex" */ './components/District/LearnerEnrolmentApplications/Index.vue'));
Vue.component('district-learner-enrolment-applications-show', () => import(/* webpackChunkName: "DistrictLearnerEnrolmentApplicationShow" */ './components/District/LearnerEnrolmentApplications/Show.vue'));
Vue.component('district-teacher-postings-index', () => import(/* webpackChunkName: "DistrictTeacherPostings" */ './components/District/Postings/Teachers/Index.vue'));
Vue.component('district-teacher-postings-create', () => import(/* webpackChunkName: "DistrictNewTeacherPosting" */ './components/District/Postings/Teachers/Create.vue'));
Vue.component('district-non-teaching-staff-postings-index', () => import(/* webpackChunkName: "DistrictNonTeachingStaffPostings" */ './components/District/Postings/Non Teaching Staff/Index.vue'));

Vue.component('district-teacher-transfers-index', () => import(/* webpackChunkName: "DistrictTeacherTransfers" */ './components/District/Transfers/Teachers/Index.vue'));
Vue.component('district-teacher-transfers-create', () => import(/* webpackChunkName: "DistrictNewTeacherTransfer" */ './components/District/Transfers/Teachers/Create.vue'));
Vue.component('district-non-teaching-staff-transfers-index', () => import(/* webpackChunkName: "DistrictNonTeachingStaffTransfers" */ './components/District/Transfers/Non Teaching Staff/Index.vue'));
Vue.component('district-non-teaching-staff-transfers-create', () => import(/* webpackChunkName: "DistrictNewNonTeachingStaffTransfer" */ './components/District/Transfers/Non Teaching Staff/Create.vue'));
//Parents
Vue.component('district-school-parents', () => import(/* webpackChunkName: "DistrictSchoolParents" */ './components/District/Schools/DistrictParents.vue'));

//Lower Local Government
Vue.component('sub-county-manage-all-schools', () => import(/* webpackChunkName: "SubCountyManageAllSchools" */ './components/SubCounty/Schools/Index.vue'));
Vue.component('sub-county-manage-all-schools-enrolments', () => import(/* webpackChunkName: "SubCountyManageAllSchoolsEnrolments" */ './components/SubCounty/Schools/Enrolments/Index.vue'));

Vue.component('sub-county-emis-number-applications-index', () => import(/* webpackChunkName: "SubCountyEmisNumberApplications" */ './components/SubCounty/EmisApplications/Index.vue'));
Vue.component('sub-county-emis-number-applications-show', () => import(/* webpackChunkName: "SubCountyEmisNumberApplication" */ './components/SubCounty/EmisApplications/Show.vue'));
//Parents
Vue.component('sub-county-school-parents', () => import(/* webpackChunkName: "SubCountySchoolParents" */ './components/SubCounty/Schools/SubCountyParents.vue'));


//Clusters
Vue.component('cluster-manage-all-schools', () => import(/* webpackChunkName: "ClusterManageAllSchools" */ './components/ClusterUser/Schools/Index.vue'));
Vue.component('cluster-manage-all-schools-enrolments', () => import(/* webpackChunkName: "ClusterManageAllSchoolsEnrolments" */ './components/ClusterUser/Schools/Enrolments/Index.vue'));

Vue.component('cluster-emis-number-applications-index', () => import(/* webpackChunkName: "ClusterEmisNumberApplications" */ './components/ClusterUser/EmisApplications/Index.vue'));
Vue.component('cluster-emis-number-applications-show', () => import(/* webpackChunkName: "ClusterEmisNumberApplication" */ './components/ClusterUser/EmisApplications/Show.vue'));

//Data Update
Vue.component('eso-school-learners-for-deleting', () => import(/* webpackChunkName: "EsoLearnersDeleted" */ './components/ClusterUser/DataUpdate/LearnersDeleted.vue'));
Vue.component('eso-school-learner-flagged-view-application', () => import(/* webpackChunkName: "EsoLearnerDeletedViewApplication" */ './components/ClusterUser/DataUpdate/LearnerDeletedViewApplication.vue'));
//Parents
Vue.component('eso-school-parents', () => import(/* webpackChunkName: "ESoSchoolParents" */ './components/ClusterUser/Schools/ESOParents.vue'));
