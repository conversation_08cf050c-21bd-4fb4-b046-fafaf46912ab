$mainColor: #00879B;
$baseColor: #FFFFFF;
$mutedColor: #7A7A7A;

.survey-bar {
    display: flex;
    justify-content: space-between;
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
}

.survey-bar li::before, .survey-bar li::after {
    box-sizing: initial !important;
}
.survey-bar li {
    flex: 2;
    position: relative;
    padding: 0 0 14px 0;
    font-size: .875rem;
    line-height: 1.5;
    color: $mainColor;
    font-weight: 600;
    white-space: nowrap;
    overflow: visible;
    min-width: 0;
    text-align: center;
    border-bottom: 2px solid $mutedColor;
}
.survey-bar li:first-child,
.survey-bar li:last-child {
    flex: 1;
}
.survey-bar li:last-child {
    text-align: right;
}
.survey-bar li:before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    background-color: $mutedColor;
    border-radius: 50%;
    border: 2px solid $baseColor;
    position: absolute;
    left: calc(50% - 6px);
    bottom: -7px;
    z-index: 3;
    transition: all .2s ease-in-out;
}
.survey-bar li:first-child:before {
    left: 0;
}
.survey-bar li:last-child:before {
    right: 0;
    left: auto;
}
.survey-bar span {
    transition: opacity .3s ease-in-out;
}
.survey-bar li:not(.is-active) span {
    opacity: 0;
}
.survey-bar .is-complete:not(:first-child):after,
.survey-bar .is-active:not(:first-child):after {
    content: "";
    display: block;
    width: 100%;
    position: absolute;
    bottom: -2px;
    left: -50%;
    z-index: 2;
    border-bottom: 2px solid $mainColor;
}
.survey-bar li:last-child span {
    width: 200%;
    display: inline-block;
    position: absolute;
    left: -100%;
}

.survey-bar .is-complete:last-child:after,
.survey-bar .is-active:last-child:after {
    width: 200%;
    left: -100%;
}

.survey-bar .is-complete:before {
    background-color: $mainColor;
}

.survey-bar .is-active:before,
.survey-bar li:hover:before,
.survey-bar .is-hovered:before {
    background-color: $baseColor;
    border-color: $mainColor;
}
.survey-bar li:hover:before,
.survey-bar .is-hovered:before {
    transform: scale(1.33);
}

.survey-bar li:hover span,
.survey-bar li.is-hovered span {
    opacity: 1;
}

.survey-bar:hover li:not(:hover) span {
    opacity: 0;
    color: $mutedColor;
}

.x-ray .survey-bar,
.x-ray .survey-bar li {
    border: 1px dashed red;
}

.survey-bar .has-changes {
    opacity: 1 !important;
}
.survey-bar .has-changes:before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    position: absolute;
    left: calc(50% - 4px);
    bottom: -20px;
    background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%208%208%22%3E%3Cpath%20fill%3D%22%23ed1c24%22%20d%3D%22M4%200l4%208H0z%22%2F%3E%3C%2Fsvg%3E');
}
