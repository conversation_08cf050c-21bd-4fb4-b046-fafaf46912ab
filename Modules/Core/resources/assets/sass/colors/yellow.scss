@use "dark-teal";

@use "../mixins.scss" as yellow;
$yellow: #FFAD05;
dark-teal.$white: #FFFFFF;

.scrollbar-yellow .simplebar-scrollbar::before {
    background-color: $yellow !important;
}

.alert-yellow { color: $yellow; background-color: #e9f9fc; border-color: $yellow; }

.alert-pro.alert-yellow { border-color: $yellow; }

.alert-pro.alert-yellow > .icon { color: $yellow; }

.bg-yellow {
    background-color: $yellow !important;
    color: dark-teal.$white !important;
}

.bg-yellow:after {
    color: dark-teal.$white !important;
}

.btn-yellow {
    @include yellow.button-variant(dark-teal.$white, $yellow, $yellow)
}

.text-yellow {
    color: $yellow !important;
}

.border-yellow {
    border-color: $yellow !important;
}

.badge-yellow {
    color: dark-teal.$white !important;
    border-color: $yellow !important;
    background-color: $yellow !important;
}

.badge-outline-yellow {
    color: $yellow !important;
    border-color: $yellow !important;
}

.badge-outline-yellow:hover {
    background-color: $yellow !important;
    border-color: $yellow !important;
    color: dark-teal.$white !important;
}

.btn-outline-yellow {
    border-color: $yellow !important;
    color: $yellow !important;
}

.btn-outline-yellow:hover {
    background-color: $yellow !important;
    border-color: $yellow !important;
    color: dark-teal.$white !important;
}

.page-item.active-yellow .page-link,
.custom-control-input-yellow:checked ~ .custom-control-label-yellow::before,
.custom-control-input-yellow:not(:disabled):active ~ .custom-control-label-yellow::before {
    background-color: $yellow !important;
    border-color: $yellow !important;
}
