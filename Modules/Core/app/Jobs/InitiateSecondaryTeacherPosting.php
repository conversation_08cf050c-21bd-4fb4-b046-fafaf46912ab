<?php

namespace Modules\Core\Jobs;

use AfromanSR\LaravelNiraApi\NiraClient;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Institutions\TeachersPostingListUpload;
use Modules\Core\Models\NIRA\Nira;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\Core\Models\Settings\IdentityDocumentType;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Modules\Core\Traits\InteractsWithPerson;

class InitiateSecondaryTeacherPosting implements ShouldQueue
{
    use Dispatchable, InteractsWithPerson, InteractsWithQueue, Queueable, SerializesModels;

    protected ?Country $country;

    protected ?IdentityDocumentType $identityDocumentType;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected TeachersPostingListUpload $uploadEntry,
    ) {
        $this->onQueue('emails');
        $this->country = Country::query()->where('name', 'UGANDA')->first();
        $this->identityDocumentType = IdentityDocumentType::query()->where('name', 'NATIONAL ID')->first();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Check for duplicate entries
        try {
            TeachersPostingListUpload::query()
                ->where('nin', $this->uploadEntry->nin)
                ->whereHas('teacher_posting', function (Builder $query) {
                    $query->where('status', 0)->orWhere('status', 1);
                })
                ->firstOrFail();

            $this->uploadEntry
                ->update([
                    'validation_errors->duplicate' => 'This entry is a duplicate. A teacher with this NIN has already been posted',
                    'passed_validation' => false,
                    'is_processed' => true,
                ]);
        } catch (ModelNotFoundException $exception) {
            // Check with NIRA
            try {
                /** @var Nira $niraPerson */
                $niraPerson = Nira::query()
                    ->where('national_id', Str::upper($this->uploadEntry->nin))
                    ->firstOrFail();
                $this->success($niraPerson);
            } catch (ModelNotFoundException $exception) {
                $connect = $this->getPerson(Str::upper($this->uploadEntry->nin));
                if ($connect instanceof Collection) {
                    if ($connect['status']) {
                        $this->success(Nira::query()
                            ->updateOrCreate([
                                'national_id' => $connect['responseObj']->nationalId,
                            ], [
                                'surname' => $connect['responseObj']->surname,
                                'given_names' => $connect['responseObj']->givenNames,
                                'maiden_names' => $connect['responseObj']->maidenNames,
                                'previous_surnames' => $connect['responseObj']->previousSurnames,
                                'date_of_birth' => $connect['responseObj']->dateOfBirth,
                                'gender' => $connect['responseObj']->gender,
                                'nationality' => $connect['responseObj']->nationality,
                                'living_status' => $connect['responseObj']->livingStatus,
                                'photo' => $this->downloadImage($connect['responseObj']->photo, $connect['responseObj']->nationalId),
                            ]));
                    } else {
                        $this->uploadEntry->update([
                            'nin_verification_status' => 2,
                            'nin_verification_error' => $connect['message'],
                            'validation_errors->nin' => $connect['message'],
                            'passed_validation' => false,
                            'is_processed' => true,
                        ]);
                    }
                } else {
                    if ($connect->status) {
                        $this->success(Nira::query()
                            ->updateOrCreate([
                                'national_id' => $connect->responseObj->nationalId,
                            ], [
                                'surname' => $connect->responseObj->surname,
                                'given_names' => $connect->responseObj->givenNames,
                                'maiden_names' => $connect->responseObj->maidenNames,
                                'previous_surnames' => $connect->responseObj->previousSurnames,
                                'date_of_birth' => $connect->responseObj->dateOfBirth,
                                'gender' => $connect->responseObj->gender,
                                'nationality' => $connect->responseObj->nationality,
                                'living_status' => $connect->responseObj->livingStatus,
                                'photo' => $this->downloadImage($connect->responseObj->photo, $connect->responseObj->nationalId),
                            ]));
                    } else {
                        $this->uploadEntry->update([
                            'nin_verification_status' => 2,
                            'nin_verification_error' => $connect->message,
                            'validation_errors->nin' => $connect->message,
                            'passed_validation' => false,
                            'is_processed' => true,
                        ]);
                    }
                }
            }
        }
    }

    private function success(Nira|Model $niraPerson)
    {
        $this->uploadEntry
            ->update([
                'first_name' => $niraPerson->given_names,
                'surname' => $niraPerson->surname,
                'other_names' => $niraPerson->other_names,
                'gender' => $niraPerson->gender === 'M' ? 'MALE' : 'FEMALE',
                'date' => $niraPerson->birth_date->format('j'),
                'month' => Str::upper($niraPerson->birth_date->format('F')),
                'year' => $niraPerson->birth_date->format('Y'),
                'nin_verification_status' => 1,
                'nin_verification_error' => null,
            ]);

        //Create Teacher Person
        /** @var Person $teacherPerson */
        $teacherPerson = $this->getPersonWithIdentity(Str::upper($this->uploadEntry->nin), intval($this->country->id), 'nin');

        if ($teacherPerson === null) {
            $teacherPerson = Person::query()
                ->create([
                    'first_name' => $this->uploadEntry->first_name,
                    'surname' => $this->uploadEntry->surname,
                    'other_names' => $this->uploadEntry->other_names,
                    'gender' => $this->uploadEntry->gender[0],
                    'birth_date' => intval($this->uploadEntry->date).' '.$this->uploadEntry->month.', '.$this->uploadEntry->year,
                    'identity_type_id' => $this->identityDocumentType->id,
                    'id_number' => Str::upper($this->uploadEntry->nin),
                    'country_id' => $this->country->id,
                ]);
        }

        $teacherPerson->update([
            'phone_1' => $this->uploadEntry->phone_1,
            'phone_2' => strlen($this->uploadEntry->phone_2) === 0 ? null : $this->uploadEntry->phone_2,
            'email' => $this->uploadEntry->email,
        ]);

        // create teacher person identities
        $teacherPerson
            ->identity_documents()
            ->updateOrCreate([
                'identity_type_id' => $this->identityDocumentType->id,
                'country_id' => $this->country->id,
            ], [
                'document_id' => Str::upper($this->uploadEntry->nin),
                'verification_status' => 1,
                'verification_error' => null,
            ]);

        // Check for valid school
        try {
            //Initiate valid posting
            /** @var School $school */
            $school = School::query()
                ->where('emis_number', $this->uploadEntry->school->emis_number)
                ->where('school_ownership_status_id', 1)
                ->firstOrFail();

            $this->uploadEntry
                ->teacher_posting()
                ->updateOrCreate([
                    'school_id' => $school->id,
                ], [
                    'person_id' => $teacherPerson->id,
                    'posted_by' => auth()->id(),
                    'posting_date' => $this->uploadEntry->posting_date.' '.$this->uploadEntry->posting_month.', '.$this->uploadEntry->posting_year,
                    'local_government_id' => $school->local_government_id,
                ]);

            $this->uploadEntry
                ->update([
                    'local_government_id' => $school->local_government_id,
                    'validation_errors' => null,
                    'passed_validation' => true,
                    'is_processed' => true,
                ]);
        } catch (ModelNotFoundException $exception) {
            $this->uploadEntry
                ->update([
                    'validation_errors->not_found' => 'The school where you are trying to post the teacher either does not exist or is not a government owned school',
                    'passed_validation' => false,
                    'is_processed' => true,
                ]);
        }
    }

    private function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])->get(env('NIRA_PROXY').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                logger()->channel('niraslack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
                abort(500, 'Failed to reach NIRA servers! Try again later...');
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }

    private function downloadImage($photo, $nin): ?string
    {
        if ($photo === null) {
            return null;
        }

        $path = 'images/nira-photos/';
        Storage::disk('local-public')->makeDirectory($path);

        $image = Image::make($photo);
        $image->resize(200, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $photoName = Str::lower($nin.'.png');
        $image->save(public_path($path.$photoName));

        $image->destroy();

        return $photoName;
    }
}
