<?php

namespace Modules\Core\Jobs;

use AfromanSR\LaravelNiraApi\NiraClient;
use Modules\Core\Models\IdentityDocument;
use Modules\Core\Models\Institutions\SchoolEmployee;
use Modules\Core\Models\Institutions\Teacher;
use Modules\Core\Models\Institutions\TeachersPostingListUpload;
use Modules\Core\Models\NIRA\Nira;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\Core\Models\Settings\EducationLevel;
use Modules\Core\Models\Settings\EmploymentStatus;
use Modules\Core\Models\Settings\IdentityDocumentType;
use Modules\Core\Models\Settings\MaritalStatus;
use Modules\Core\Models\Settings\TeacherProfessionalQualification;
use Modules\Core\Models\Settings\TeacherType;
use Modules\Core\Models\Settings\TeachingStaffDesignation;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Modules\Core\Traits\InteractsWithPerson;
use Throwable;

class InitiatePrimaryTeacherPostingBackup implements ShouldQueue
{
    use Dispatchable, InteractsWithPerson, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected TeachersPostingListUpload $uploadEntry,
        protected ?IdentityDocumentType $identityDocumentType,
        protected ?TeacherProfessionalQualification $teacherProfessionalQualification,
        protected ?TeacherType $teacherType,
        protected ?Country $country,
        protected ?TeachingStaffDesignation $teachingStaffDesignation,
        protected ?MaritalStatus $maritalStatus,
        protected ?EducationLevel $educationLevel,
        protected ?EmploymentStatus $employmentStatus,
    ) {
        $this->uploadEntry->load('school:id');
        $this->onQueue('emails');
        $this->identityDocumentType = IdentityDocumentType::query()->where('name', 'NATIONAL ID')->first();
        $this->country = Country::query()->where('name', 'UGANDA')->first();
        $this->teacherProfessionalQualification = TeacherProfessionalQualification::query()->where('name', $this->uploadEntry->highest_teaching_qualification)->first();
        $this->teacherType = TeacherType::query()->where('name', 'TRAINED')->first();
        $this->teachingStaffDesignation = TeachingStaffDesignation::query()->where('name', $this->uploadEntry->designation)->first();
        $this->maritalStatus = MaritalStatus::query()->where('name', $this->uploadEntry->marital_status)->first();
        $this->educationLevel = EducationLevel::query()->where('name', $this->uploadEntry->highest_education_level)->first();
        $this->employmentStatus = EmploymentStatus::query()->where('name', $this->uploadEntry->employment_status)->first();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            /** @var Nira $niraPerson */
            $niraPerson = Nira::query()->where('national_id', Str::upper($this->uploadEntry->nin))->firstOrFail();
            $this->success($niraPerson);
        } catch (ModelNotFoundException $exception) {
            $connect = $this->getPerson(Str::upper($this->uploadEntry->nin));
            if ($connect instanceof Collection) {
                if ($connect['status']) {
                    $this->success(Nira::updateOrCreate([
                        'national_id' => $connect['responseObj']->nationalId,
                    ], [
                        'surname' => $connect['responseObj']->surname,
                        'given_names' => $connect['responseObj']->givenNames,
                        'maiden_names' => $connect['responseObj']->maidenNames,
                        'previous_surnames' => $connect['responseObj']->previousSurnames,
                        'date_of_birth' => $connect['responseObj']->dateOfBirth,
                        'gender' => $connect['responseObj']->gender,
                        'nationality' => $connect['responseObj']->nationality,
                        'living_status' => $connect['responseObj']->livingStatus,
                        'photo' => $this->downloadImage($connect['responseObj']->photo, $connect['responseObj']->nationalId),
                    ]));
                } else {
                    $this->logNiraErrorMessage($connect['message']);
                }
            } else {
                if ($connect->status) {
                    $this->success(Nira::updateOrCreate([
                        'national_id' => $connect->responseObj->nationalId,
                    ], [
                        'surname' => $connect->responseObj->surname,
                        'given_names' => $connect->responseObj->givenNames,
                        'maiden_names' => $connect->responseObj->maidenNames,
                        'previous_surnames' => $connect->responseObj->previousSurnames,
                        'date_of_birth' => $connect->responseObj->dateOfBirth,
                        'gender' => $connect->responseObj->gender,
                        'nationality' => $connect->responseObj->nationality,
                        'living_status' => $connect->responseObj->livingStatus,
                        'photo' => $this->downloadImage($connect->responseObj->photo, $connect->responseObj->nationalId),
                    ]));
                } else {
                    $this->logNiraErrorMessage($connect->message);
                }
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        logger()->channel('queueslack')->error("QUEUE-ERROR MESSAGE: {$exception->getMessage()}");
    }

    private function logNiraErrorMessage(string $message)
    {
        $this->uploadEntry->update([
            'nin_verification_status' => 2,
            'nin_verification_error' => $message,
        ]);
    }

    private function success(Nira $niraPerson)
    {
        $this->uploadEntry
            ->update([
                'first_name' => $niraPerson->given_names,
                'surname' => $niraPerson->surname,
                'other_names' => $niraPerson->other_names,
                'gender' => $niraPerson->gender === 'M' ? 'MALE' : 'FEMALE',
                'date' => $niraPerson->birth_date->format('j'),
                'month' => Str::upper($niraPerson->birth_date->format('F')),
                'year' => $niraPerson->birth_date->format('Y'),
                'nin_verification_status' => 1,
                'nin_verification_error' => null,
            ]);

        IdentityDocument::query()
            ->where('document_id', Str::upper($this->uploadEntry->nin))
            ->update([
                'verification_status' => 1,
                'verification_error' => null,
            ]);

        $this->saveTeacher();
    }

    private function saveTeacher()
    {
        //Create Teacher Person
        /** @var Person $teacherPerson */
        $teacherPerson = $this->getPersonWithIdentity(Str::upper($this->uploadEntry->nin), intval($this->country->id), 'nin');

        if ($teacherPerson === null) {
            $teacherPerson = Person::query()
                ->create([
                    'first_name' => $this->uploadEntry->first_name,
                    'surname' => $this->uploadEntry->surname,
                    'other_names' => $this->uploadEntry->other_names,
                    'gender' => $this->uploadEntry->gender[0],
                    'birth_date' => intval($this->uploadEntry->date).' '.$this->uploadEntry->month.', '.$this->uploadEntry->year,
                    'identity_type_id' => $this->identityDocumentType->id,
                    'id_number' => Str::upper($this->uploadEntry->nin),
                    'country_id' => $this->country->id,
                    'phone_1' => $this->uploadEntry->phone_1,
                    'phone_2' => $this->uploadEntry->phone_2,
                    'email' => $this->uploadEntry->email,
                ]);
        }

        // create teacher person identities
        $teacherPerson->identity_documents()
            ->updateOrCreate([
                'identity_type_id' => $this->identityDocumentType->id,
                'country_id' => $this->country->id,
            ], [
                'document_id' => Str::upper($this->uploadEntry->nin),
                'verification_status' => 1,
            ]);

        // create teacher
        Teacher::query()
            ->updateOrCreate([
                'person_id' => $teacherPerson->id,
                'current_school_id' => $this->uploadEntry->school->id,
            ], [
                'qualification_id' => $this->teacherProfessionalQualification?->id,
                'teacher_type_id' => $this->teacherType->id,
                'designation_id' => $this->teachingStaffDesignation?->id,
                'is_on_government_payroll' => true,
                'appointment_minute_number' => $this->uploadEntry->first_appointment_number,
                'current_appointment_minute_number' => $this->uploadEntry->current_appointment_minute_number,
            ]);

        // create school employee
        /** @var SchoolEmployee $teacherEmployee */
        $teacherEmployee = $teacherPerson->school_employee()
            ->updateOrCreate([
                'school_id' => $this->uploadEntry->school->id,
                'is_teaching_staff' => true,
            ], [
                'marital_status_id' => $this->maritalStatus?->id,
                'highest_education_level_id' => $this->educationLevel?->id,
                'employment_status_id' => $this->employmentStatus?->id,
                'date_started' => now(),
            ]);

        // create teaching staff
        $teacherEmployee->teaching_staff()
            ->updateOrCreate([
                'person_id' => $teacherPerson->id,
                'school_id' => $this->uploadEntry->school->id,
                'is_transfer_appointment' => false,
            ]);
    }

    private function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])->get(env('NIRA_PROXY').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                logger()->channel('niraslack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
                abort(500, 'Failed to reach NIRA servers! Try again later...');
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }

    private function downloadImage($photo, $nin): ?string
    {
        if ($photo === null) {
            return null;
        }

        $path = 'images/nira-photos/';
        Storage::disk('local-public')->makeDirectory($path);

        $image = Image::make($photo);
        $image->resize(200, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $photoName = Str::lower($nin.'.png');
        $image->save(public_path($path.$photoName));

        $image->destroy();

        return $photoName;
    }
}
