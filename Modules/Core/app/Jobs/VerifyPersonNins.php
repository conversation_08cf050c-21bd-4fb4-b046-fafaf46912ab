<?php

namespace Modules\Core\Jobs;

use AfromanSR\LaravelNiraApi\NiraClient;
use Modules\Core\Models\IdentityDocument;
use Modules\Core\Models\NIRA\Nira;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class VerifyPersonNins implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $identityDocument;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(IdentityDocument $identityDocument)
    {
        //        $this->onQueue('nira');
        $this->identityDocument = $identityDocument;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::transaction(function () {
            try {
                Nira::where('national_id', Str::upper($this->identityDocument->document_id))->firstOrFail();
                $this->identityDocument->update(['verification_status' => 1]);
            } catch (ModelNotFoundException $exception) {
                $connect = $this->getPerson(Str::upper($this->identityDocument->document_id));

                if ($connect->status) {
                    Nira::create([
                        'national_id' => $connect->responseObj->nationalId,
                        'surname' => $connect->responseObj->surname,
                        'given_names' => $connect->responseObj->givenNames,
                        'maiden_names' => $connect->responseObj->maidenNames,
                        'previous_surnames' => $connect->responseObj->previousSurnames,
                        'date_of_birth' => $connect->responseObj->dateOfBirth,
                        'gender' => $connect->responseObj->gender,
                        'nationality' => $connect->responseObj->nationality,
                        'living_status' => $connect->responseObj->livingStatus,
                        'photo' => $this->downloadNiraPhoto($connect->responseObj->photo, $connect->responseObj->nationalId),
                    ]);
                    $this->identityDocument->update(['verification_status' => 1]);
                } else {
                    $this->identityDocument->update([
                        'verification_status' => 2,
                        'verification_error' => $connect->message,
                    ]);
                }
            }
        });
    }

    private function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])->get(env('NIRA_PROXY').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                logger()->channel('slack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
                abort(500, 'Failed to reach NIRA servers... Try registration without NIN or try again later.');
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }

    private function downloadNiraPhoto($photo, $nin): ?string
    {
        if ($photo === null) {
            return null;
        }

        $path = 'images/nira-photos/';
        Storage::disk('local-public')->makeDirectory($path);

        $image = Image::make($photo);
        $image->resize(200, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $photoName = Str::lower($nin.'.png');
        $image->save(public_path($path.$photoName));

        $image->destroy();

        return $photoName;
    }
}
