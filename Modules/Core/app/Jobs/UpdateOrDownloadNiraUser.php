<?php

namespace Modules\Core\Jobs;

use AfromanSR\LaravelNiraApi\NiraClient;
use Modules\Core\Models\IdentityDocument;
use Modules\Core\Models\NIRA\Nira;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class UpdateOrDownloadNiraUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $nin;

    protected $excelEntry;

    protected $type;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($excelEntry, $nin, $type = 'learner')
    {
        $this->excelEntry = $excelEntry;
        $this->nin = $nin;
        $this->type = $type;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->success(Nira::where('national_id', Str::upper($this->nin))->firstOrFail());
        } catch (ModelNotFoundException $exception) {
            $connect = $this->getPerson(Str::upper($this->nin));
            if ($connect instanceof Collection) {
                if ($connect['status']) {
                    $this->success(Nira::updateOrCreate([
                        'national_id' => $connect['responseObj']->nationalId,
                    ], [
                        'surname' => $connect['responseObj']->surname,
                        'given_names' => $connect['responseObj']->givenNames,
                        'maiden_names' => $connect['responseObj']->maidenNames,
                        'previous_surnames' => $connect['responseObj']->previousSurnames,
                        'date_of_birth' => $connect['responseObj']->dateOfBirth,
                        'gender' => $connect['responseObj']->gender,
                        'nationality' => $connect['responseObj']->nationality,
                        'living_status' => $connect['responseObj']->livingStatus,
                        'photo' => $this->downloadImage($connect['responseObj']->photo, $connect['responseObj']->nationalId),
                    ]));
                } else {
                    $this->failed($connect['message']);
                }
            } else {
                if ($connect->status) {
                    $this->success(Nira::updateOrCreate([
                        'national_id' => $connect->responseObj->nationalId,
                    ], [
                        'surname' => $connect->responseObj->surname,
                        'given_names' => $connect->responseObj->givenNames,
                        'maiden_names' => $connect->responseObj->maidenNames,
                        'previous_surnames' => $connect->responseObj->previousSurnames,
                        'date_of_birth' => $connect->responseObj->dateOfBirth,
                        'gender' => $connect->responseObj->gender,
                        'nationality' => $connect->responseObj->nationality,
                        'living_status' => $connect->responseObj->livingStatus,
                        'photo' => $this->downloadImage($connect->responseObj->photo, $connect->responseObj->nationalId),
                    ]));
                } else {
                    $this->failed($connect->message);
                }
            }
        }
    }

    private function success($niraPerson)
    {
        switch ($this->type) {
            case 'learner':
                $this->excelEntry->update([
                    'first_name' => $niraPerson->given_names,
                    'surname' => $niraPerson->surname,
                    'other_names' => $niraPerson->other_names,
                    'gender' => $niraPerson->gender,
                    'date' => $niraPerson->birth_date->format('j'),
                    'month' => Str::upper($niraPerson->birth_date->format('F')),
                    'year' => $niraPerson->birth_date->format('Y'),
                    'learner_nin_verification_status' => 1,
                    'learner_nin_verification_error' => null,
                ]);
                break;

            case 'parent':
                $this->excelEntry->update([
                    'parent_first_name' => $niraPerson->given_names,
                    'parent_surname' => $niraPerson->surname,
                    'parent_other_names' => $niraPerson->other_names,
                    'parent_gender' => $niraPerson->gender,
                    'parent_nin_verification_status' => 1,
                    'parent_nin_verification_error' => null,
                ]);
                break;

            default:
                $this->excelEntry->update([
                    'first_name' => $niraPerson->given_names,
                    'surname' => $niraPerson->surname,
                    'other_names' => $niraPerson->other_names,
                    'gender' => $niraPerson->gender,
                    'date' => $niraPerson->birth_date->format('j'),
                    'month' => Str::upper($niraPerson->birth_date->format('F')),
                    'year' => $niraPerson->birth_date->format('Y'),
                    'nin_verification_status' => 1,
                    'nin_verification_error' => null,
                ]);
                break;
        }

        IdentityDocument::where('document_id', Str::upper($this->nin))->update([
            'verification_status' => 1,
            'verification_error' => null,
        ]);

        if (($this->type === 'learner' and $this->excelEntry->parent_verification_status === 1) or ($this->type === 'parent' and $this->excelEntry->learner_verification_status !== 3)) {
            LoadLearner::dispatch($this->excelEntry);
        } elseif ($this->type === 'teacher') {
            LoadTeacher::dispatch($this->excelEntry);
        } elseif ($this->type === 'non-teaching-staff') {
            LoadNonTeachingStaff::dispatch($this->excelEntry);
        }
    }

    private function failed($message)
    {
        switch ($this->type) {
            case 'learner':
                $this->excelEntry->update([
                    'learner_nin_verification_status' => 2,
                    'learner_nin_verification_error' => $message,
                ]);
                break;

            case 'parent':
                $this->excelEntry->update([
                    'parent_nin_verification_status' => 2,
                    'parent_nin_verification_error' => $message,
                ]);
                break;

            default:
                $this->excelEntry->update([
                    'nin_verification_status' => 2,
                    'nin_verification_error' => $message,
                ]);
                break;
        }

        IdentityDocument::where('document_id', Str::upper($this->nin))->update([
            'verification_status' => 2,
            'verification_error' => $message,
        ]);
    }

    private function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])->get(env('NIRA_PROXY').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                dd($exception->getMessage());
                logger()->channel('niraslack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
                abort(500, 'Failed to reach NIRA servers! Try again later...');
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }

    private function downloadImage($photo, $nin): ?string
    {
        if ($photo === null) {
            return null;
        }

        $path = 'images/nira-photos/';
        Storage::disk('local-public')->makeDirectory($path);

        $image = Image::make($photo);
        $image->resize(200, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $photoName = Str::lower($nin.'.png');
        $image->save(public_path($path.$photoName));

        $image->destroy();

        return $photoName;
    }
}
