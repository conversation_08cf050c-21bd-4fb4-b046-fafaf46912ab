<?php

namespace Modules\Core\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Modules\Core\Models\Settings\CourseCategory;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use App\Http\Resources\CourseCategoryResource;




class CourseCategoryController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        return CourseCategoryResource::collection(CourseCategory::with(['course_type'])->get());
    }
}
