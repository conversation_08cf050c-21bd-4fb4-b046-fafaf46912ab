<?php

namespace Modules\Core\Http\Controllers\Api\V1\NIRA;

use AfromanSR\LaravelNiraApi\NiraClient;
use App\Http\Controllers\Controller;
use Modules\Core\Models\NIRA\Nira;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Modules\Core\Jobs\DownloadNiraUser;

class NiraApiController extends Controller
{
    public function userInfo(Request $request)
    {
        $request->validate([
            'id_number' => 'required|string|regex:/^[A-Z]{1}[FM][0-9A-Z]{12}$/',
        ], [
            'id_number.required' => 'Provide a NIN to proceed',
            'id_number.regex' => 'The NIN you provided is in the wrong format. Try again',
        ]);

        try {
            return Nira::where('national_id', Str::upper($request->id_number))->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            $connect = $this->getPerson(Str::upper($request->id_number));

            if ($connect instanceof Collection) {
                if ($connect['status']) {
                    DownloadNiraUser::dispatch($connect['responseObj']);

                    return response()->json([
                        'national_id' => $connect['responseObj']->nationalId,
                        'surname' => $connect['responseObj']->surname,
                        'given_names' => $connect['responseObj']->givenNames,
                        'maiden_names' => $connect['responseObj']->maidenNames,
                        'previous_surnames' => $connect['responseObj']->previousSurnames,
                        'date_of_birth' => $connect['responseObj']->dateOfBirth,
                        'birth_date' => Carbon::createFromFormat('d/m/Y', $connect['responseObj']->dateOfBirth),
                        'gender' => $connect['responseObj']->gender,
                        'nationality' => $connect['responseObj']->nationality,
                        'living_status' => $connect['responseObj']->livingStatus,
                        'photo' => $connect['responseObj']->photo,
                    ]);
                } else {
                    logger()->channel('niraslack')->error("NIRA-ERROR MESSAGE: {$connect['message']}");
                    if (Str::startsWith($connect['message'], 'Person not found')) {
                        abort(500, 'We cannot find a person with the NIN: '.Str::upper($request->id_number).'. Try again');
                    } else {
                        abort(500, 'Unable to process your validation due to bad network. Try again after 5 minutes.');
                    }
                }
            } else {
                if ($connect->status) {
                    DownloadNiraUser::dispatch($connect->responseObj);

                    return response()->json([
                        'national_id' => $connect->responseObj->nationalId,
                        'surname' => $connect->responseObj->surname,
                        'given_names' => $connect->responseObj->givenNames,
                        'maiden_names' => $connect->responseObj->maidenNames,
                        'previous_surnames' => $connect->responseObj->previousSurnames,
                        'date_of_birth' => $connect->responseObj->dateOfBirth,
                        'birth_date' => Carbon::createFromFormat('d/m/Y', $connect->responseObj->dateOfBirth),
                        'gender' => $connect->responseObj->gender,
                        'nationality' => $connect->responseObj->nationality,
                        'living_status' => $connect->responseObj->livingStatus,
                        'photo' => $connect->responseObj->photo,
                    ]);
                } else {
                    logger()->channel('niraslack')->error("NIRA-ERROR MESSAGE: {$connect->message}");
                    if (Str::startsWith($connect->message, 'Person not found')) {
                        abort(500, 'We cannot find a person with the NIN: '.Str::upper($request->id_number).'. Try again');
                    } else {
                        abort(500, 'Unable to reach NIRA to validate this NIN at the moment. Try again later...');
                    }
                }
            }
        }
    }

    public function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])
                ->withToken(config('nira.services.token'))
                ->get(config('nira.services.proxy').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                //                dd($exception->getMessage());
                logger()->channel('niraslack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
                abort(500, 'Failed to reach NIRA servers! Try again later...');
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }
}
