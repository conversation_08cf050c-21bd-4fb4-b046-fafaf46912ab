<?php

namespace Modules\Core\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class SystemSettingsController extends Controller
{
    public function showInactivityTimeout()
    {
        $envPath = base_path('.env');
        $defaultTimeout = 300; // 5 minutes in seconds
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            if (!preg_match('/^INACTIVITY_TIMEOUT=.*$/m', $envContent)) {
                $envContent .= "\nINACTIVITY_TIMEOUT={$defaultTimeout}\n";
                file_put_contents($envPath, $envContent);
            }
        }
        $timeout = env('INACTIVITY_TIMEOUT', $defaultTimeout); // 5 minutes default
        return view('core::admin.system-settings.inactivity-timeout', compact('timeout'));
    }

    public function updateInactivityTimeout(Request $request)
    {
        $request->validate([
            'timeout' => 'required|integer|min:1|max:1440', // 1 min to 24 hours
        ]);
        $timeoutMinutes = $request->input('timeout');
        $timeoutSeconds = $timeoutMinutes * 60;

        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            if (preg_match('/^INACTIVITY_TIMEOUT=.*$/m', $envContent)) {
                // Update existing variable
                $envContent = preg_replace(
                    '/^INACTIVITY_TIMEOUT=.*$/m',
                    'INACTIVITY_TIMEOUT=' . $timeoutSeconds,
                    $envContent
                );
            } else {
                // Append new variable
                $envContent .= "\nINACTIVITY_TIMEOUT=" . $timeoutSeconds . "\n";
            }
            file_put_contents($envPath, $envContent);
        }
        \Artisan::call('config:clear');
        return redirect()->back()->with('success', 'Inactivity timeout updated!');
    }
} 