<?php

namespace Modules\Core\Http\Controllers\Admin\Approvals;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\SchoolType;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;

class AdminLearnerEnrolmentApplicationsController extends Controller
{
    protected int $perPage = 15;

    public function __construct()
    {
        $this->middleware('admin');
    }

    public function pending(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Pending Learner Enrolment applications list', 'admin');
        return view('core::admin.learner-enrolment-applications.index')->with('type', 'Pending');
    }

    public function approved(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Approved Learner Enrolment applications list', 'admin');
        return view('core::admin.learner-enrolment-applications.index')->with('type', 'Approved');
    }

    public function rejected(): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Applications', '1', 'View', 'User checked Rejected Learner Enrolment applications list', 'admin');
        return view('core::admin.learner-enrolment-applications.index')->with('type', 'Rejected');
    }

    public function show($status, $applicationNumber): View
    {
        //Add to logs
        AdminLogActivity::addToLog('Learner Enrolment Application', '1', 'View', "User checked Learner Enrolment application details - Ref: #{$applicationNumber}", 'admin');
        return view('core::admin.learner-enrolment-applications.show')->with(['applicationNumber' => $applicationNumber, 'status' => $status]);
    }
}
