<?php

namespace Modules\Core\Traits;

use AfromanSR\LaravelNiraApi\NiraClient;
use Modules\Core\Models\NIRA\Nira;
use Modules\Core\Models\Settings\IdentityDocumentType;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

trait InteractsWithNira
{
    public function updateNiraDetails()
    {
        $nationalId = IdentityDocumentType::where('name', 'NATIONAL ID')->first();

        try {
            $nira_person = Nira::where('national_id', Str::upper($this->id_number))->firstOrFail();

            $this->update([
                'identity_type_id' => $nationalId->id,
                'birth_date' => $nira_person->birth_date->format('j F, Y'),
                'country_id' => $nira_person->country->id,
                'gender' => $nira_person->gender,
                'first_name' => $nira_person->given_names,
                'surname' => $nira_person->surname,
                'other_names' => $nira_person->other_names,
            ]);
        } catch (ModelNotFoundException $exception) {
            $connect = $this->getPerson(Str::upper($this->id_number));

            if ($connect instanceof Collection) {
                if ($connect['status']) {
                    $nira_person = Nira::updateOrCreate([
                        'national_id' => $connect['responseObj']->nationalId,
                    ], [
                        'surname' => $connect['responseObj']->surname,
                        'given_names' => $connect['responseObj']->givenNames,
                        'maiden_names' => $connect['responseObj']->maidenNames,
                        'previous_surnames' => $connect['responseObj']->previousSurnames,
                        'date_of_birth' => $connect['responseObj']->dateOfBirth,
                        'gender' => $connect['responseObj']->gender,
                        'nationality' => $connect['responseObj']->nationality,
                        'living_status' => $connect['responseObj']->livingStatus,
                        'photo' => $this->downloadImage($connect['responseObj']->photo, $connect['responseObj']->nationalId),
                    ]);

                    $this->update([
                        'identity_type_id' => $nationalId->id,
                        'birth_date' => $nira_person->birth_date->format('j F, Y'),
                        'country_id' => $nira_person->country->id,
                        'gender' => $nira_person->gender,
                        'first_name' => $nira_person->given_names,
                        'surname' => $nira_person->surname,
                        'other_names' => $nira_person->other_names,
                    ]);
                } else {
                    logger()->channel('niraslack')->error("NIRA-ERROR MESSAGE: {$connect['message']}");
                }
            } else {
                if ($connect !== null and $connect->status) {
                    $nira_person = Nira::updateOrCreate([
                        'national_id' => $connect->responseObj->nationalId,
                    ], [
                        'surname' => $connect->responseObj->surname,
                        'given_names' => $connect->responseObj->givenNames,
                        'maiden_names' => $connect->responseObj->maidenNames,
                        'previous_surnames' => $connect->responseObj->previousSurnames,
                        'date_of_birth' => $connect->responseObj->dateOfBirth,
                        'gender' => $connect->responseObj->gender,
                        'nationality' => $connect->responseObj->nationality,
                        'living_status' => $connect->responseObj->livingStatus,
                        'photo' => $this->downloadImage($connect->responseObj->photo, $connect->responseObj->nationalId),
                    ]);

                    $this->update([
                        'identity_type_id' => $nationalId->id,
                        'birth_date' => $nira_person->birth_date->format('j F, Y'),
                        'country_id' => $nira_person->country->id,
                        'gender' => $nira_person->gender,
                        'first_name' => $nira_person->given_names,
                        'surname' => $nira_person->surname,
                        'other_names' => $nira_person->other_names,
                    ]);
                } elseif ($connect === null) {
                    dump('Person ID: '.$this->id.' NIN: '.$this->id_number);
                } else {
                    logger()->channel('niraslack')->error("NIRA-ERROR MESSAGE: {$connect->message}");
                }
            }
        }
    }

    public function getPerson($nin = 'CF640761001DDD')
    {
        if (config('nira.services.use_proxy')) {
            try {
                return Http::withOptions([
                    'debug' => false,
                    'verify' => false,
                ])->get(env('NIRA_PROXY').'/'.$nin)->object();
            } catch (ConnectionException|Exception $exception) {
                logger()->channel('niraslack')->error("NIRA-ERROR CODE:{$exception->getCode()} MESSAGE: {$exception->getMessage()}");
            }
        } else {
            return (new NiraClient)->getPerson($nin);
        }
    }

    private function downloadImage($photo, $nin): ?string
    {
        if ($photo === null) {
            return null;
        }

        $path = 'images/nira-photos/';
        Storage::disk('local-public')->makeDirectory($path);

        $image = Image::make($photo);
        $image->resize(200, null, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $photoName = Str::lower($nin.'.png');
        $image->save(public_path($path.$photoName));

        $image->destroy();

        return $photoName;
    }
}
