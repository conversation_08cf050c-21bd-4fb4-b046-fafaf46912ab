<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\TeacherAccreditation\Livewire\Accreditation;
use Modules\TeacherAccreditation\Livewire\NewRequests;
use Modules\TeacherAccreditation\Livewire\Approved;
use Modules\TeacherAccreditation\Livewire\Rejected;

Route::get('teacheraccreditation', Accreditation::class)->name('teacheraccreditation.index');
Route::get('teacheraccreditation/new-requests', NewRequests::class)->name('admin.teacher-accreditation.new-requests');
Route::get('teacheraccreditation/approved', Approved::class)->name('admin.teacher-accreditation.approved');
Route::get('teacheraccreditation/rejected', Rejected::class)->name('admin.teacher-accreditation.rejected');
