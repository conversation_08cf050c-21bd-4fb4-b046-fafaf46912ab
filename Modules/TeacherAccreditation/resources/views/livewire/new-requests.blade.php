<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">

                <!-- Page Header -->
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between align-items-center">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Teacher Accreditation Requests</h3>
                            <nav class="nk-block-des">
                                <ul class="breadcrumb breadcrumb-arrow">
                                    <li class="breadcrumb-item">
                                        <a href="{{ url('/') }}" class="text-primary">Dashboard</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        New Accreditation Requests
                                    </li>
                                </ul>
                            </nav>
                            <div class="nk-block-des text-soft">
                                <p>You have a total of <strong>{{ $TotalRequests }}</strong> new teacher accreditation
                                    requests.</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if ($message)
                    <div class="alert alert-success">{{ $message }}</div>
                @endif


                <!-- Filter Form -->
                <div class="nk-block">
                    <div class="card card-stretch card-bordered border-dark-teal">
                        <div class="card-inner-group">
                            <div class="card-inner border-bottom">
                                <div class="row g-3 align-items-end">
                                    <div class="row g-3 align-items-end">
                                        <div class="col-md-3">
                                            <label class="form-label" for="filterName">Search Name</label>
                                            <input type="text" id="filterName" class="form-control border-dark-teal"
                                                wire:model.lazy="filter.name" placeholder="ENTER FULL NAME"
                                                style="font-size: 12px;">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label" for="filterQualification">Qualification</label>
                                            <input type="text" id="filterQualification"
                                                class="form-control border-dark-teal"
                                                wire:model.lazy="filter.qualification" placeholder="ENTER QUALIFICATION"
                                                style="font-size: 12px;">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label" for="filterNin">Search NIN</label>
                                            <input type="text" id="filterNin" class="form-control border-dark-teal"
                                                wire:model.lazy="filter.nin" placeholder="ENTER NIN"
                                                style="font-size: 12px;">
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-flex w-80 h-100 gap-2">
                                                <button type="button"
                                                    class="btn btn-outline-dark-teal btn-block d-flex align-items-center justify-content-center gap-1 me-2"
                                                    style="height: 37px;">
                                                    <em class="icon ni ni-filter"></em>
                                                    <span>Apply Filters</span>
                                                </button>
                                                <button type="button"
                                                    class="btn btn-outline-secondary flex-grow-1 d-flex align-items-center justify-content-center gap-1"
                                                    style="height: 37px;" wire:click="resetFilters">
                                                    <em class="icon ni ni-cross"></em>
                                                    <span>Reset</span>
                                                </button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Table -->
                            <div class="card-inner position-relative card-tools-toggle">
                                @if ($requests->count())
                                    <div class="nk-tb-list nk-tb-ulist is-compact">

                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col"><span class="sub-text text-white">Full Name</span>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Sex</span>
                                            </div>
                                            <div class="nk-tb-col"><span
                                                    class="sub-text text-white">Qualification</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Status</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Actions</span>
                                            </div>
                                        </div>


                                        @foreach ($requests as $request)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['surname'] }} {{ $request['other_names'] }}</span>
                                                </div>
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['gender'] }} </span>
                                                </div>
                                                <div class="nk-tb-col text-dark">
                                                    <span>{{ $request['qualification'] }}</span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span
                                                        class="text-uppercase badge
                            @if ($request['status'] === 'pending') badge-secondary
                            @elseif($request['status'] === 'recommended') badge-primary
                            @elseif($request['status'] === 'approved') badge-dark-teal
                            @elseif($request['status'] === 'rejected') badge-red
                            @elseif($request['status'] === 'endorsed') badge-amaranth
                            @else badge-secondary @endif">
                                                        {{ ucfirst($request['status']) }}
                                                    </span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <a href="#"
                                                        wire:click.prevent="showDetails({{ $request['id'] }})"
                                                        class="badge badge-dark-teal cursor-pointer">
                                                        <em class="icon ni ni-eye"></em> <span>View</span>
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col"><span class="sub-text text-white">NIN</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Full Name</span>
                                            </div>
                                            <div class="nk-tb-col"><span
                                                    class="sub-text text-white">Qualification</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Status</span></div>
                                            <div class="nk-tb-col"><span class="sub-text text-white">Actions</span>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="p-4 text-center">
                                        <div class="alert alert-secondary alert-icon d-inline-block m-0">
                                            <em class="icon ni ni-alert-circle"></em>
                                            No teacher accreditation requests to display...
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="mt-3 mb-3">
                                {{ $this->filteredRequests->links() }}
                            </div>


                        </div>
                    </div>
                </div>

                <!-- Modal -->
                @if ($selectedRequest)
                    <div class="modal fade show d-block" tabindex="-1" style="background:rgba(0,0,0,0.5);"
                        wire:key="modal-{{ $selectedRequest['id'] }}">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title"><em class="icon ni ni-user-check me-1"></em>Application
                                        Number:
                                        <span style="font-weight: 900">{{ $selectedRequest['application_number'] }}
                                        </span>
                                    </h5>
                                    <span
                                        style="font-weight: 900">{{ \Carbon\Carbon::parse($selectedRequest['created_at'])->diffForHumans() }}
                                    </span>
                                    <button type="button" class="btn-close"
                                        wire:click="$set('selectedRequest', null)" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <p class="mb-1"><em
                                                    class="icon ni ni-hash me-1 text-primary"></em><strong>NIN:</strong>
                                                <span class="text-dark">{{ $selectedRequest['nin'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-user me-1 text-primary"></em><strong>Full
                                                    Name:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['surname'] }}
                                                    {{ $selectedRequest['other_names'] }}</span></p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-mail me-1 text-primary"></em><strong>Email:</strong>
                                                <span class="text-dark">{{ $selectedRequest['email'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-call me-1 text-primary"></em><strong>Phone:</strong>
                                                <span class="text-dark">{{ $selectedRequest['phone'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-award me-1 text-primary"></em><strong>Qualification:</strong>
                                                <span class="text-dark">{{ $selectedRequest['qualification'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-user me-1 text-primary"></em><strong>Sex:</strong>
                                                <span class="text-dark">{{ $selectedRequest['gender'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="ni ni-unlink me-1 text-primary"></em><strong>Marital
                                                    Status:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['marital_status'] }}</span>
                                            </p>
                                            <p class="mb-1"><em
                                                    class="icon ni ni-calendar me-1 text-primary"></em><strong>Date of
                                                    Birth:</strong> <span
                                                    class="text-dark">{{ $selectedRequest['date_of_birth'] }}</span>
                                            </p>
                                        </div>
                                        <div class="col-md-6">

                                            <div class="mb-1">
                                                <em
                                                    class="icon ni ni-archive me-1 text-primary"></em><strong>Documents:</strong>
                                                <ul class="list-unstyled ms-3 mt-1">
                                                    @foreach ($selectedRequest['documents'] as $document)
                                                        <li class="mb-1 d-flex align-items-center">
                                                            <em class="icon ni ni-file-text me-1 text-info"></em>
                                                            <span class="me-2 text-truncate"
                                                                style="max-width: 180px; display: inline-block;"
                                                                title="{{ $document['name'] }}">{{ $document['name'] }}</span>
                                                            <a href="{{ $document['url'] }}" target="_blank"
                                                                class="btn btn-sm btn-light-primary px-2 py-0"
                                                                title="View Document">
                                                                <em class="icon ni ni-eye"></em>
                                                            </a>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <label for="comment" class="form-label"><em
                                                    class="icon ni ni-info me-1 text-primary"></em><strong>Comment/Justification
                                                    <span class="text-danger">*</span></strong></label>
                                            <textarea id="comment" class="form-control border-primary" rows="3" wire:model.defer="comment"
                                                placeholder="Enter comment or justification (required for rejection)"></textarea>
                                        </div>
                                    </div>
                                </div>

                                @if (session()->has('error'))
                                    <div class="alert alert-danger">{{ session('error') }}</div>
                                @endif

                                <div class="modal-footer bg-light">
                                    <button type="button" class="btn btn-secondary"
                                        wire:click="$set('selectedRequest', null)"><em
                                            class="icon ni ni-cross me-1"></em>Close</button>
                                    <button type="button" class="btn btn-primary"
                                        wire:click="approve({{ $selectedRequest['id'] }})">
                                        <em class="icon ni ni-check-circle me-1"></em> Approve
                                    </button>
                                    <button type="button" class="btn btn-danger"
                                        wire:click="reject({{ $selectedRequest['id'] }})">
                                        <em class="icon ni ni-cross-circle me-1"></em> Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

            </div>
        </div>
    </div>
</div>
