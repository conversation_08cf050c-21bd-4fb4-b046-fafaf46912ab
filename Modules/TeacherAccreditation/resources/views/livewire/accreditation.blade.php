<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">

            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Teacher Accreditation Application</h4>
                </div>
                <div class="card-body">

                    {{-- Step Navigation --}}
                    <div class="mb-4">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            <div class="text-center flex-fill">
                                <div class="rounded-circle bg-primary text-white fw-bold mx-auto mb-1" style="width: 2.2rem; height: 2.2rem; line-height:2.2rem;">
                                    1
                                </div>
                                <small class="fw-semibold @if($step === 1) text-primary @else text-muted @endif">Personal Info</small>
                            </div>
                            <div class="flex-fill border-top border-3 mx-1 @if($step > 1) border-primary @else border-secondary @endif"></div>
                            <div class="text-center flex-fill">
                                <div class="rounded-circle @if($step >= 2) bg-primary text-white @else bg-secondary text-white-50 @endif fw-bold mx-auto mb-1" style="width: 2.2rem; height: 2.2rem; line-height:2.2rem;">
                                    2
                                </div>
                                <small class="fw-semibold @if($step === 2) text-primary @else text-muted @endif">Contact Info</small>
                            </div>
                            <div class="flex-fill border-top border-3 mx-1 @if($step > 2) border-primary @else border-secondary @endif"></div>
                            <div class="text-center flex-fill">
                                <div class="rounded-circle @if($step >= 3) bg-primary text-white @else bg-secondary text-white-50 @endif fw-bold mx-auto mb-1" style="width: 2.2rem; height: 2.2rem; line-height:2.2rem;">
                                    3
                                </div>
                                <small class="fw-semibold @if($step === 3) text-primary @else text-muted @endif">Documents</small>
                            </div>
                            <div class="flex-fill border-top border-3 mx-1 @if($step > 3) border-primary @else border-secondary @endif"></div>
                            <div class="text-center flex-fill">
                                <div class="rounded-circle @if($step === 4) bg-primary text-white @else bg-secondary text-white-50 @endif fw-bold mx-auto mb-1" style="width: 2.2rem; height: 2.2rem; line-height:2.2rem;">
                                    4
                                </div>
                                <small class="fw-semibold @if($step === 4) text-primary @else text-muted @endif">Review</small>
                            </div>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ ($step-1)*33.33 }}%"></div>
                        </div>
                    </div>

                    {{-- Step 1: Personal Info --}}
                    @if($step === 1)
                        <form wire:submit.prevent="nextStep">
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="nin">NIN <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter your National Identification Number (NIN).</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="text" id="nin" class="form-control bg-primary-dim text-uppercase" wire:model.defer="nin" required>
                                        @error('nin') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="surname">Surname <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter your surname as it appears on your ID.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="text" id="surname" class="form-control bg-primary-dim text-uppercase" wire:model.defer="surname" required>
                                        @error('surname') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="other_names">Other Names <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter any other names you have.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="text" id="other_names" class="form-control bg-primary-dim text-uppercase" wire:model.defer="other_names" required>
                                        @error('other_names') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="gender">Sex <span class="text-danger">*</span></label>
                                        <span class="form-note">Select your sex.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select id="gender" class="form-control bg-primary-dim" wire:model.defer="gender" required>
                                            <option value="">Select Gender</option>
                                            @foreach($genders as $key => $label)
                                                <option value="{{ $key }}">{{ $label }}</option>
                                            @endforeach
                                        </select>
                                        @error('gender') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="marital_status_id">Marital Status <span class="text-danger">*</span></label>
                                        <span class="form-note">Select your marital status.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select id="marital_status_id" class="form-control bg-primary-dim" wire:model.defer="marital_status_id" required>
                                            <option value="">Select Marital Status</option>
                                            @foreach($maritalStatuses as $status)
                                                <option value="{{ $status['id'] }}">{{ $status['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('marital_status_id') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="date_of_birth">Date of Birth <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter your date of birth.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="date" id="date_of_birth" class="form-control bg-primary-dim" wire:model.defer="date_of_birth" required>
                                        @error('date_of_birth') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-end">
                                <button type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Next</span>
                                    <em class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    @endif

                    {{-- Step 2: Contact Info --}}
                    @if($step === 2)
                        <form wire:submit.prevent="nextStep">
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="email">Email <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter your email address.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="email" id="email" class="form-control bg-primary-dim" wire:model.defer="email" required>
                                        @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="phone">Phone <span class="text-danger">*</span></label>
                                        <span class="form-note">Enter your phone number.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input type="text" id="phone" class="form-control bg-primary-dim" wire:model.defer="phone" required>
                                        @error('phone') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-between">
                                <button type="button" class="btn btn-secondary d-flex" wire:click="prevStep">
                                    <em class="ni ni-arrow-left mr-2"></em>
                                    <span class="align-self-center">Back</span>
                                </button>
                                <button type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Next</span>
                                    <em class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    @endif

                    {{-- Step 3: Documents --}}
                    @if($step === 3)
                        <form wire:submit.prevent="nextStep">
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="qualification_id">Qualification Applying For <span class="text-danger">*</span></label>
                                        <span class="form-note">Select the qualification you are applying to be accredited for.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select id="qualification_id" class="form-control bg-primary-dim" wire:model.live="qualification_id" required>
                                            <option value="">-- Select Qualification --</option>
                                            @foreach($qualifications as $q)
                                                <option value="{{ $q['id'] }}">{{ $q['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('qualification_id') <span class="text-danger">{{ $message }}</span> @enderror
                                    </div>
                                </div>
                            </div>
                            @if($qualification_id)
                                @foreach($requiredDocuments as $index => $docType)
                                    <div class="row g-3 align-center">
                                        <div class="col-lg-5">
                                            <div class="form-group">
                                                <label class="form-label">{{ $docType }} <span class="text-danger">*</span></label>
                                            </div>
                                        </div>
                                        <div class="col-lg-7">
                                            <div class="form-group">
                                                <input type="file" class="form-control bg-primary-dim" wire:model.live="uploads.{{ $index }}" accept="application/pdf,image/*,.doc,.docx,.xls,.xlsx,.txt,.rtf,.odt,.ods">
                                                @error("uploads.$index") <span class="text-danger">{{ $message }}</span> @enderror
                                                @if(isset($uploads[$index]) && is_object($uploads[$index]))
                                                    <div class="mt-1 primary">{{ $uploads[$index]->getClientOriginalName() }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-info mt-3">Please select a qualification to see required documents.</div>
                            @endif
                            <div class="nk-kycfm-action pt-5 d-flex flex-row justify-content-between">
                                <button type="button" class="btn btn-secondary d-flex" wire:click="prevStep">
                                    <em class="ni ni-arrow-left mr-2"></em>
                                    <span class="align-self-center">Back</span>
                                </button>
                                <button type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Next</span>
                                    <em class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    @endif

                    {{-- Step 4: Review & Submit --}}
                    @if($step === 4)
                        @if($application_number)
                            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                                <div class="display-1 dark-teal mb-3" style="font-size: 5rem;">
                                    &#10003;
                                </div>
                                <h3 class="dark-teal mb-2">Application Submitted!</h3>
                                <div class="mb-2">Your application is <strong>pending review</strong>.</div>

                                <div class="text-muted">You’ll receive an email confirmation soon.</div>
                            </div>
                        @else
                            <div>
                                <h5>Review Your Application</h5>
                                <ul class="list-group mb-3">
                                    <li class="list-group-item"><strong>NIN:</strong> {{ $nin }}</li>
                                    <li class="list-group-item"><strong>Surname:</strong> {{ $surname }}</li>
                                    <li class="list-group-item"><strong>Other Names:</strong> {{ $other_names }}</li>
                                    <li class="list-group-item"><strong>Gender:</strong> {{ $genders[$gender] ?? $gender }}</li>
                                    <li class="list-group-item"><strong>Marital Status:</strong>
                                        @php
                                            $status = collect($maritalStatuses)->firstWhere('id', $marital_status_id);
                                        @endphp
                                        {{ $status['name'] ?? $marital_status_id }}
                                    </li>
                                    <li class="list-group-item"><strong>Date of Birth:</strong> {{ $date_of_birth }}</li>
                                    <li class="list-group-item"><strong>Email:</strong> {{ $email }}</li>
                                    <li class="list-group-item"><strong>Phone:</strong> {{ $phone }}</li>
                                    <li class="list-group-item"><strong>Qualification Applying For:</strong>
                                        @php
                                            $qualification = collect($qualifications)->firstWhere('id', $qualification_id);
                                        @endphp
                                        {{ $qualification['name'] ?? 'Not Selected' }}
                                    </li>
                                    <li class="list-group-item"><strong>Documents:</strong>
                                        <ul>
                                            @if($qualification_id)
                                                @foreach($requiredDocuments as $index => $docType)
                                                    <li>
                                                        {{ $docType }}:
                                                        @if(isset($uploads[$index]) && is_object($uploads[$index]))
                                                            {{ $uploads[$index]->getClientOriginalName() }}
                                                        @else
                                                            <span class="text-danger">Not uploaded</span>
                                                        @endif
                                                    </li>
                                                @endforeach
                                            @endif
                                        </ul>
                                    </li>
                                </ul>
                                @if(session()->has('success'))
                                    <div class="alert alert-success">
                                        {{ session('success') }}
                                    </div>
                                @endif
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-secondary" wire:click="prevStep">Back</button>
                                    <button type="button" class="btn badge-dark-teal" wire:click="submitApplication">Submit Application</button>
                                </div>
                                @if($application_number)
                                    <div class="alert alert-info mt-3">
                                        <strong>Your Application Number:</strong> {{ $application_number }}<br>

                                    </div>
                                @endif
                            </div>
                        @endif
                    @endif

                </div>
            </div>
        </div>
    </div>
</div>
