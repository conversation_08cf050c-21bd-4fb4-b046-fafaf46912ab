<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('setting_teacher_accreditation_qualifications', function (Blueprint $table) {
            $table->bigIncrements('id'); // PK
            $table->string('name');
            $table->timestamps(); // date_created, date_updated
        });
    }

    public function down()
    {
        Schema::dropIfExists('setting_teacher_accreditation_qualifications');
    }
}; 