<?php

namespace Modules\TeacherAccreditation\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SettingTeacherAccreditationQualificationsSeeder extends Seeder
{
    public function run()
    {
        $now = Carbon::now();
        $qualifications = [
            ['name' => 'Grade III Teacher'],
            ['name' => 'Grade V Teacher (Primary)'],
            ['name' => 'Graduate Teacher (Primary)'],
            ['name' => 'Grade V Teacher (Secondary)'],
            ['name' => 'Graduate Teacher (Secondary)'],
            ['name' => 'Instructor (Grade V)'],
            ['name' => 'Instructor (Graduate)'],
            ['name' => 'Health Tutor Diploma (Grade V)'],
            ['name' => 'Health Tutor Degree (Graduate)'],
        ];
        foreach ($qualifications as &$qualification) {
            $qualification['created_at'] = $now;
            $qualification['updated_at'] = $now;
        }
        DB::table('setting_teacher_accreditation_qualifications')->insert($qualifications);
    }
} 