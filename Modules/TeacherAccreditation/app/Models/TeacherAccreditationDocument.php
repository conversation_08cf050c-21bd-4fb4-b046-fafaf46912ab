<?php

namespace Modules\TeacherAccreditation\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherAccreditationDocument extends Model
{
    protected $table = 'teacher_accreditation_documents';
    protected $primaryKey = 'teacher_accreditation_application_id';
    public $incrementing = false;

    protected $fillable = [
        'teacher_accreditation_application_id',
        'document_type',
        'document_url',
    ];

    public function application()
    {
        return $this->belongsTo(TeacherAccreditationApplication::class, 'teacher_accreditation_application_id');
    }
}
