<?php

namespace Modules\TeacherAccreditation\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Modules\TeacherAccreditation\Models\TeacherAccreditationApplication;
use Mo<PERSON>les\TeacherAccreditation\Models\SettingTeacherAccreditationQualification;
use <PERSON><PERSON>les\TeacherAccreditation\Models\TeacherAccreditationDocument;
use Mo<PERSON>les\TeacherAccreditation\Notifications\ApplicationApproved;
use Illuminate\Support\Facades\Notification;
use Mo<PERSON>les\TeacherAccreditation\Notifications\ApplicationRejected;
use Illuminate\Support\Facades\Storage;


class NewRequests extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $selectedRequest;
    public $comment = '';
    public $message = '';

    public $filter = [
        'nin' => '',
        'name' => '',
        'qualification' => '',
    ];

    protected $queryString = ['filter'];

    // Reset pagination on filter update
    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function getFilteredRequestsProperty()
    {
        $query = TeacherAccreditationApplication::where('approval_status', 'PENDING');

        if (!empty($this->filter['nin'])) {
            $query->where('nin', 'like', '%' . $this->filter['nin'] . '%');
        }

        if (!empty($this->filter['name'])) {
            $name = $this->filter['name'];
            $query->where(function ($q) use ($name) {
                $q->where('surname', 'like', '%' . $name . '%')
                    ->orWhere('other_names', 'like', '%' . $name . '%')
                    ->orWhereRaw("CONCAT(other_names, ' ', surname) LIKE ?", ["%$name%"])
                    ->orWhereRaw("CONCAT(surname, ' ', other_names) LIKE ?", ["%$name%"]);
            });
        }

        if (!empty($this->filter['qualification'])) {
            $qualificationIds = SettingTeacherAccreditationQualification::where('name', 'like', '%' . $this->filter['qualification'] . '%')->pluck('id');
            $query->whereIn('qualification_id', $qualificationIds);
        }

        return $query->orderBy('created_at', 'desc')->paginate(15);
    }

    protected function maskNin(string $nin): string
    {
        if (strlen($nin) <= 6) {
            return $nin;
        }
        $firstThree = substr($nin, 0, 3);
        $lastThree = substr($nin, -3);
        $masked = str_repeat('*', strlen($nin) - 6);
        return $firstThree . $masked . $lastThree;
    }

    public function showDetails($id)
    {
        $qualificationMap = SettingTeacherAccreditationQualification::pluck('name', 'id');

        $app = TeacherAccreditationApplication::find($id);
        if (!$app) return;

        $documents = TeacherAccreditationDocument::where('teacher_accreditation_application_id', $app->id)->get();

        $this->selectedRequest = [
            'id' => $app->id,
            'application_number' => $app->application_number,
            'nin' => $this->maskNin($app->nin),
            'surname' => $app->surname,
            'other_names' => $app->other_names,
            'gender' => $app->gender,
            'marital_status' => $app->maritalStatus->name ?? '',
            'date_of_birth' => $app->date_of_birth,
            'email' => $app->email,
            'phone' => $app->phone,
            'created_at' => $app->created_at,
            'qualification' => isset($app->qualification_id) ? ($qualificationMap[$app->qualification_id] ?? '') : '',
            'documents' => $documents->map(function ($doc) {
                return [
                    'type' => $doc->document_type,
                    'url' => Storage::url($doc->document_url),
                    'name' => basename($doc->document_url),
                ];
            })->toArray(),
            'status' => strtolower($app->approval_status),

        ];

        $this->comment = '';
    }

    public function approve($id)
    {
        $app = TeacherAccreditationApplication::find($id);
        if ($app) {
            $app->approval_status = 'APPROVED';
            $app->reject_reason = $this->comment;
            $app->approved_by = auth()->user()->person_id;
            $app->date_approved = now();
            $app->save();

            Notification::route('mail', $app->email)
                ->notify(new ApplicationApproved($app->application_number));

            $this->message = 'Request approved successfully.';
            $this->reset(['selectedRequest', 'comment']);
        }
    }

    public function reject($id)
    {
        if (empty($this->comment)) {
            session()->flash('error', 'Please provide a justification for rejection.');
            return;
        }

        $app = TeacherAccreditationApplication::find($id);
        if ($app) {
            $app->approval_status = 'REJECTED';
            $app->approved_by = auth()->user()->person_id;
            $app->reject_reason = $this->comment;
            $app->date_approved = now();
            $app->save();

            Notification::route('mail', $app->email)
                ->notify(new ApplicationRejected($app->application_number, $this->comment));

            $this->message = 'Request rejected successfully.';
            $this->reset(['selectedRequest', 'comment']);
        }
    }



    public function resetFilters()
    {
        $this->filter = [
            'nin' => '',
            'name' => '',
            'qualification' => '',
        ];
        $this->resetPage();
    }

    public function render()
    {
        $qualificationMap = SettingTeacherAccreditationQualification::pluck('name', 'id');

        $requests = $this->filteredRequests->through(function ($app) use ($qualificationMap) {
            $documents = TeacherAccreditationDocument::where('teacher_accreditation_application_id', $app->id)->get();
            return [
                'id' => $app->id,
                'nin' => $this->maskNin($app->nin),
                'surname' => $app->surname,
                'other_names' => $app->other_names,
                'gender' => $app->gender,
                'marital_status' => $app->maritalStatus->name ?? '',
                'date_of_birth' => $app->date_of_birth,
                'email' => $app->email,
                'phone' => $app->phone,
                'qualification' => isset($app->qualification_id) ? ($qualificationMap[$app->qualification_id] ?? '') : '',
                'documents' => $documents->map(function ($doc) {
                    return [
                        'type' => $doc->document_type,
                        'url' => Storage::url($doc->document_url),
                        'name' => basename($doc->document_url),
                    ];
                })->toArray(),
                'status' => strtolower($app->approval_status),
            ];
        });

        return view('teacheraccreditation::livewire.new-requests', [
            'requests' => $requests,
            'TotalRequests' => $this->filteredRequests->total(),
        ]);
    }
}
