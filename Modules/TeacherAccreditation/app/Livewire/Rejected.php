<?php

namespace Modules\TeacherAccreditation\Livewire;

use Livewire\Component;
use Mo<PERSON>les\TeacherAccreditation\Models\TeacherAccreditationApplication;
use Modules\TeacherAccreditation\Models\SettingTeacherAccreditationQualification;
use Modules\TeacherAccreditation\Models\TeacherAccreditationDocument;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;


class Rejected extends Component
{
    use WithPagination;

    // public $requests = [];
    public $selectedRequest;
    public $comment = '';
    public $message = '';

    // Unified filter object
    public $filter = [
        'nin' => '',
        'name' => '',
        'qualification' => '',
    ];

    protected $paginationTheme = 'bootstrap';

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->filter = [
            'nin' => '',
            'name' => '',
            'qualification' => '',
        ];
        $this->resetPage();
    }

    protected function maskNin(string $nin): string
    {
        if (strlen($nin) <= 6) {
            return $nin;
        }
        $firstThree = substr($nin, 0, 3);
        $lastThree = substr($nin, -3);
        $masked = str_repeat('*', strlen($nin) - 6);
        return $firstThree . $masked . $lastThree;
    }

    public function showDetails($id)
    {
        $qualificationMap = SettingTeacherAccreditationQualification::pluck('name', 'id');
        $app = TeacherAccreditationApplication::with('approverPerson')->find($id);
        $documents = TeacherAccreditationDocument::where('teacher_accreditation_application_id', $id)->get();
        $approvedBy = null;
        if ($app->approverPerson) {
            $approvedBy = $app->approverPerson->first_name . ' ' . $app->approverPerson->surname;
        }
        $this->selectedRequest = [
            'id' => $app->id,
            'application_number' => $app->application_number,
            'nin' => $this->maskNin($app->nin),
            'surname' => $app->surname,
            'other_names' => $app->other_names,
            'gender' => $app->gender,
            'marital_status' => $app->maritalStatus->name ?? '',
            'date_of_birth' => $app->date_of_birth,
            'email' => $app->email,
            'created_at' => $app->created_at,
            'phone' => $app->phone,
            'reject_reason' => $app->reject_reason,
            'qualification' => $qualificationMap[$app->qualification_id] ?? '',
            'documents' => $documents->map(function ($doc) {
                return [
                    'type' => $doc->document_type,
                    'url' => Storage::url($doc->document_url),
                    'name' => basename($doc->document_url),
                ];
            })->toArray(),
            'status' => strtolower($app->approval_status),
            'approved_by' => $approvedBy,
            'approval_date' => $app->date_approved,
        ];

        $this->comment = '';
    }

    public function getFilteredRequestsProperty()
    {
        return TeacherAccreditationApplication::where('approval_status', 'REJECTED')
            ->when(
                $this->filter['nin'],
                fn($query) =>
                $query->where('nin', 'like', '%' . $this->filter['nin'] . '%')
            )
            ->when($this->filter['name'], function ($query) {
                $search = '%' . str_replace(' ', '', strtolower($this->filter['name'])) . '%';
                $query->whereRaw("LOWER(REPLACE(CONCAT(surname, other_names), ' ', '')) LIKE ?", [$search]);
            })
            ->when($this->filter['qualification'], function ($query) {
                $qualificationIds = SettingTeacherAccreditationQualification::where('name', 'like', '%' . $this->filter['qualification'] . '%')->pluck('id');
                $query->whereIn('qualification_id', $qualificationIds);
            })
            ->orderByDesc('created_at')
            ->paginate(15);
    }


    public function render()
    {
        $qualificationMap = SettingTeacherAccreditationQualification::pluck('name', 'id');
        $mappedRequests = $this->filteredRequests->map(function ($app) use ($qualificationMap) {
            return [
                'id' => $app->id,
                'gender' => $app->gender,
                'surname' => $app->surname,
                'other_names' => $app->other_names,
                'qualification' => $qualificationMap[$app->qualification_id] ?? '',
                'status' => strtolower($app->approval_status),
            ];
        });

        return view('teacheraccreditation::livewire.rejected', [
            'requests' => $mappedRequests,
            'totalCount' => $this->filteredRequests->total(),
        ]);
    }
}
