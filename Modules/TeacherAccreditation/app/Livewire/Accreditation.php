<?php

namespace Modules\TeacherAccreditation\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Mo<PERSON>les\TeacherAccreditation\Models\SettingTeacherAccreditationQualification;
use Modules\TeacherAccreditation\Models\TeacherAccreditationApplication;
use Modules\TeacherAccreditation\Models\TeacherAccreditationDocument;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Modules\TeacherAccreditation\Notifications\ApplicationSubmitted;

class Accreditation extends Component
{
    use WithFileUploads;

    // Step control
    public $step = 1;

    // Step 1: Personal Info
    public $nin;
    public $surname;
    public $other_names;
    public $gender;
    public $marital_status_id;
    public $date_of_birth;

    // Step 2: Contact Info
    public $email;
    public $phone;

    // Step 3: Documents
    public $qualification_id;
    public $qualifications = [];
    public $requiredDocuments = [];
    public $uploads = [];
    public $documentMap = [
        1 => ['O-Level Pass Slip', 'O-Level Certificate', 'Grade II Certificate', 'Grade III Pass Slip', 'Grade III Certificate', 'National ID'],
        2 => ['O-Level Certificate', 'Grade III Certificate', 'Grade III Registration Certificate', 'Grade V Transcript', 'Grade V Certificate', 'National ID'],
        3 => ['O-Level Pass Slip', 'O-Level Certificate', 'Grade III Registration Certificate', 'Grade V Registration Certificate', 'Grade III Pass Slip', 'Grade III Certificate', 'Grade V Transcript', 'Grade V Certificate', 'Degree Transcript', 'Degree Certificate', 'National ID'],
        4 => ['O-Level Pass Slip', 'O-Level Certificate', 'A-Level Pass Slip', 'A-Level Certificate', 'Grade V Transcript', 'Grade V Certificate', 'National ID'],
        5 => ['O-Level Pass Slip', 'O-Level Certificate', 'A-Level Pass Slip', 'A-Level Certificate', 'Grade V Transcript', 'Grade V Certificate', 'Grade V Registration Certificate', 'Degree Transcript', 'Degree Certificate', 'National ID'],
        6 => ['O-Level Pass Slip', 'O-Level Certificate', 'A-Level Pass Slip', 'A-Level Certificate', 'Diploma in Technical and Vocational Studies', 'Craft II Certificate', 'Craft III Certificate', 'National Certificate', 'Certificate in Technical Teacher Education', 'Diploma in Vocational Training Instruction', 'National ID'],
        7 => ['O-Level Pass Slip', 'O-Level Certificate', 'A-Level Pass Slip', 'A-Level Certificate', 'Diploma certificate', 'Diploma transcript', 'Craft II Certificate', 'Craft III Certificate', 'National Certificate', 'Diploma in Instructor and Technical Education (DITTE)', 'Degree Certificate', 'Degree Transcript', 'National ID'],
        8 => ['O-Level Certificate', 'Professional Certificate', 'Professional Diploma', 'Certificate of Registration for Professional Practice', 'Health Tutor Diploma', 'Health Tutor Certificate', 'Health Tutor Transcript', 'National ID'],
        9 => ['O-Level Certificate', 'A-Level Certificate', 'Professional Certificate', 'Professional Diploma', 'Certificate of Registration for Professional Practice', 'Degree Certificate', 'Degree Transcript', 'National ID'],
    ];

    public function updatedQualificationId()
    {
        $this->requiredDocuments = $this->documentMap[$this->qualification_id] ?? [];
        $this->uploads = [];
    }
    // Step 4: Review
    public $application_number;

    // For select options
    public $maritalStatuses = [
        ['id' => 1, 'name' => 'Single'],
        ['id' => 2, 'name' => 'Married'],
        ['id' => 3, 'name' => 'Divorced'],
        ['id' => 4, 'name' => 'Widowed'],
    ];

    public $genders = [
        'M' => 'Male',
        'F' => 'Female',
        'O' => 'Other',
    ];

    public function mount()
    {
        // Load qualifications from DB
        $this->qualifications = SettingTeacherAccreditationQualification::orderBy('name')->get(['id', 'name'])->toArray();
    }

    // Validation rules per step
    protected function rules()
    {
        // Step 1 rules
        $rulesStep1 = [
            'nin' => 'required|string|min:13|max:14',
            'surname' => 'required|string',
            'other_names' => 'required|string',
            'gender' => 'required|in:M,F,O',
            'marital_status_id' => 'required|integer',
            'date_of_birth' => 'required|date',
        ];

        // Step 2 rules
        $rulesStep2 = [
            'email' => 'required|email',
            'phone' => 'required|string|min:9|max:15',
        ];

        // Step 3 rules
        if ($this->step === 3) {
            $rules = [
                'qualification_id' => 'required|integer',
            ];
            foreach ($this->requiredDocuments as $index => $doc) {
                $rules["uploads.$index"] = 'required|file|mimes:pdf,jpg,jpeg,png,gif,doc,docx,xls,xlsx,txt,rtf,odt,ods|max:10240';
            }
            return $rules;
        }

        // Return rules based on current step
        if ($this->step === 1) return $rulesStep1;
        if ($this->step === 2) return $rulesStep2;

        // Final submission (step 4): validate everything
        $rulesStep3 = [
            'qualification_id' => 'required|integer',
        ];
        foreach ($this->requiredDocuments as $index => $doc) {
            $rulesStep3["uploads.$index"] = 'required|file|mimes:pdf,jpg,jpeg,png,gif,doc,docx,xls,xlsx,txt,rtf,odt,ods|max:10240';
        }
        return array_merge($rulesStep1, $rulesStep2, $rulesStep3);
    }

    // Step navigation
    public function nextStep()
    {
        $this->validate(); // Validate based on the current step
        $this->step++;
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function submitApplication()
    {
        $this->validate(); // Validate before submission

        // Simulate application number generation
        $this->application_number = 'TA-' . strtoupper(substr($this->nin, -6)) . '-' . now()->format('YmdHis');

        DB::beginTransaction();
        try {
            $genderMap = [
                'M' => 'Male',
                'F' => 'Female',
                'O' => 'Other',
            ];

            // Save the application
            $application = TeacherAccreditationApplication::create([
                'nin' => $this->nin,
                'email' => $this->email,
                'phone' => $this->phone,
                'surname' => strtoupper($this->surname),
                'other_names' => strtoupper($this->other_names),
                'qualification_id' => $this->qualification_id,
                'gender' => $genderMap[$this->gender] ?? $this->gender,
                'marital_status_id' => $this->marital_status_id,
                'date_of_birth' => $this->date_of_birth,
                'approval_status' => 'PENDING',
                'application_number' => $this->application_number,
            ]);

            // Save documents if any
            foreach ($this->requiredDocuments as $index => $docType) {
                $file = $this->uploads[$index] ?? null;
                if (is_object($file)) {
                    $path = $file->store('teacher_accreditation_documents', 'public');
                    TeacherAccreditationDocument::create([
                        'teacher_accreditation_application_id' => $application->id,
                        'document_type' => $docType,
                        'document_url' => $path,
                    ]);
                }
            }

            DB::commit();
            Notification::route('mail', $this->email)
                ->notify(new ApplicationSubmitted($this->application_number));
            session()->flash('success', 'Application submitted successfully! Check your email for confirmation.');
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'There was an error submitting your application. Please try again.');
            throw $e;
        }
    }

    public function render()
    {
        return view('teacheraccreditation::livewire.accreditation', [
            'requiredDocuments' => $this->requiredDocuments,
        ])
            ->layout('core::layouts.design');
    }
}
