<?php

namespace Modules\TeacherAccreditation\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationRejected extends Notification
{
    use Queueable;

    public $applicationNumber;
    public $reason;

    public function __construct($applicationNumber, $reason)
    {
        $this->applicationNumber = $applicationNumber;
        $this->reason = $reason;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Teacher Accreditation Application Rejected ❌')
            ->markdown('teacheraccreditation::emails.application-rejected', [
                'applicationNumber' => $this->applicationNumber,
                'reason' => $this->reason,
            ]);
    }

    public function toArray($notifiable)
    {
        return [];
    }
}
