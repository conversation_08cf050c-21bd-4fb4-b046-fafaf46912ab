<?php

namespace Modules\TeacherAccreditation\Notifications;

use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationSubmitted extends Notification
{
    use Queueable;

    public $applicationNumber;

    public function __construct($applicationNumber)
    {
        $this->applicationNumber = $applicationNumber;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Teacher Accreditation Application Received')
            ->markdown('teacheraccreditation::emails.application-submitted', [
                'applicationNumber' => $this->applicationNumber,
            ]);
    }

    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
