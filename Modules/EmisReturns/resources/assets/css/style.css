/* Custom styles for EmisReturns module */

/* Telephone input styling */
.iti {
    width: 100%;
}

.iti__flag-container {
    z-index: 999;
}

.progress-bar-container {
    width: 100%;
    margin: auto;
    position: absolute;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0);
    top: 0;
    z-index: 5000;
    cursor: progress;
}

.progress-bar {
    height: 4px;
    background-color: rgba(0, 135, 155, 0.2);
    width: 100%;
    overflow: hidden;
}

.progress-bar-value {
    width: 100%;
    height: 100%;
    background-color: rgb(0, 135, 155);
    animation: indeterminateAnimation 1s infinite linear;
    transform-origin: 0 50%;
}
@keyframes indeterminateAnimation {
    0% {
        transform: translateX(0) scaleX(0);
    }
    40% {
        transform: translateX(0) scaleX(0.4);
    }
    100% {
        transform: translateX(100%) scaleX(0.5);
    }
}
/* Mobile sidebar styling */
@media (max-width: 991.98px) {
    /* Hide sidebar initially */
    /* #sidebarMenu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
        z-index: 1050;
        background-color: white;
        overflow-y: auto;
        box-shadow: 3px 0 10px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease;
        transform: translateX(-100%);
    } */

    /* Show sidebar when active */
    /* #sidebarWrapper.content-active #sidebarMenu {
        display: block;
        transform: translateX(0);
    } */
    /*
    #sidebarWrapper.content-active::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    } */

    /* Improve menu items styling */
    /* #sidebarMenu .link-list-menu li a {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    } */

    /* #sidebarMenu .link-list-menu li a em {
        margin-right: 10px;
        font-size: 18px;
    } */

    /* Prevent body scrolling when sidebar is open */
    /* body.sidebar-open {
        overflow: hidden;
    } */

    /* Make sure the card wrapper positions correctly */
    /* .card-aside-wrap {
        position: relative;
    } */
}

/* Always show sidebar on large screens */
/* @media (min-width: 992px) {
    #sidebarMenu {
        display: block;
        width: 280px;
        min-width: 280px;
    }
} */

/* General styling improvements */
.cursor {
    cursor: pointer;
}

.link-list-menu li a {
    transition: background-color 0.2s ease;
}

.link-list-menu li a:hover:not(.bg-dark-teal) {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Fix icon display */
.icon.ni {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Gap utility for older browsers */
.gap-2 {
    gap: 0.5rem;
}

.custom-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(82, 100, 132, 0.5);
    z-index: 1040;
}

.custom-modal-show {
    z-index: 1050;
}

.custom-spinner-bg {
    background: rgba(255, 255, 255, 0.8); 
    top: 0; 
    left: 0; 
    z-index: 1051;
}
