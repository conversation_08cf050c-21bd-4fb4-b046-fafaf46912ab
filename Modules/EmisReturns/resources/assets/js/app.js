// Main JavaScript for EmisReturns module

window.addEventListener("popstate", function (event) {
    const matches = window.location.pathname.match(
        /survey\/(\d+)(\/(\w+)\/(\d+))?/
    );
    if (matches) {
        //const pathParts = window.location.pathname.split("/");
        //const sectionId = pathParts[pathParts.length - 1];
        const sectionId = matches[4] ?? 1; // Get the section ID from the URL
        Livewire.dispatch("navigate-to-section", { sectionId: sectionId });
    }
});

document.addEventListener("livewire:initialized", function () {
    /* School Particulars */
    // Only clean up after modal is fully hidden
    function cleanUpModals() {
        setTimeout(function () {
            $(".modal-backdrop").remove();
            $("body").removeClass("modal-open").css("padding-right", "");
        }, 100); // Delay to ensure <PERSON><PERSON><PERSON> has finished hiding
    }
    // Listen for the close-modal event from Livewire
    Livewire.on("close-modal", () => {

        // Hide modals using Bootstrap's API
        $("#updateSectionAModal").modal("hide");
        $("#updateSectionBModal").modal("hide");
        $("#updateCampusesModal").modal("hide");
        $("#updateEnergySourcesModal").modal("hide");
        $("#updateOtherFacilityModal").modal("hide");
        $("#updateEnergySourcesModal").modal("hide");
        $("#updateOtherFacilityModal").modal("hide");
        $("#handWashingFacilitiesModal").modal("hide");
        $("#schoolWallChartsModal").modal("hide");
        $("#schoolSneKitsModal").modal("hide");
        $("#schoolTextBooksModal").modal("hide");
        $("#schoolReferenceBooksModal").modal("hide");
        $("#schoolLabEquipmentModal").modal("hide");
        $("#schoolLabReagentsModal").modal("hide");
        $("[id^=schoolInfrastructureModal]").modal("hide");
        $("#SexualityEducationpolicyModal").modal("hide");
        $("#hotMealModal").modal("hide");
        $("#foodSourceModal").modal("hide");
        $("#updateSchoolSportsActivitiesModal").modal("hide");
        $("#incomeModal").modal("hide");
        $("#budgetModal").modal("hide");
        $("#expenseModal").modal("hide");
        $("#ictFacilitiesModal").modal("hide");
        $('#internetSourcesModal').modal('hide');
        cleanUpModals();
    });
    // Also clean up when modals are hidden through other means
    $(
        "#updateSectionAModal, #updateSectionBModal, #updateEnergySourcesModal, #updateOtherFacilityModal,  #schoolWallChartsModal, #schoolSneKitsModal, #schoolTextBooksModal,#schoolReferenceBooksModal, #schoolLabEquipmentModal, #schoolLabReagentsModal,#schoolInfrastructureModal, #SexualityEducationpolicyModal,#hotMealModal,#foodSourceModal,  #updateSchoolSportsActivitiesModal, #ictFacilitiesModal, #internetSourcesModal"
    ).on("hidden.bs.modal", function () {
        $(
            "#updateSectionAModal, #updateSectionBModal, #updateEnergySourcesModal, #updateOtherFacilityModal, #handWashingFacilitiesModal , #schoolWallChartsModal, #schoolSneKitsModal, #schoolTextBooksModal, #schoolReferenceBooksModal, #schoolLabEquipmentModal, #schoolLabReagentsModal,#schoolInfrastructureModal, #SexualityEducationpolicyModal, #hotMealModal,#foodSourceModal,  #updateSchoolSportsActivitiesModal, #ictFacilitiesModal, #internetSourcesModal"
        ).on("hidden.bs.modal", function () {
            cleanUpModals();
    });

        // Initialize phone input
        function initPhoneInput(inputId, livewireModel) {
            const input = document.querySelector(inputId);
            if (!input || !window.intlTelInput) return;

            // Destroy existing instance if any
            if (input.iti) {
                input.iti.destroy();
            }

            // Remove all non-digit characters except leading '+'
            let phoneNumber = input.value.replace(/[^+\d]/g, "");

            // Remove leading zeros and country codes
            phoneNumber = phoneNumber.replace(/^0+/, "");
            phoneNumber = phoneNumber.replace(/^(\+256|256|\+254|254)/, "");

            input.value = phoneNumber;

            // Create new instance
            input.iti = window.intlTelInput(input, {
                initialCountry: "ug",
                separateDialCode: true,
                preferredCountries: ["ug", "ke", "tz", "rw", "bi", "ss"],
                utilsScript:
                    "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
            });

            // Update Livewire model when phone number changes
            input.addEventListener("blur", function () {
                let number = input.iti.getNumber();

                // Remove all non-digit characters except leading '+'
                number = number.replace(/[^+\d]/g, "");

                // Remove country code if present
                const countryData = input.iti.getSelectedCountryData();
                const dialCode = countryData.dialCode;
                if (number.startsWith("+" + dialCode)) {
                    number = number.substring(("+" + dialCode).length);
                } else if (number.startsWith(dialCode)) {
                    number = number.substring(dialCode.length);
                }

                // Remove leading zeros
                number = number.replace(/^0+/, "");

                // Add leading zero if missing and number is not empty
                if (number && !number.startsWith("0")) {
                    number = "0" + number;
                }

                let wireId = document.querySelector("[wire\\:id]");
                if (wireId) {
                    Livewire.find(wireId.getAttribute("wire:id")).set(
                        "schoolData.phone",
                        number
                    );
                }
            });
        }

        // Initialize on page load
        initPhoneInput("#phone", "schoolData.phone");
        initPhoneInput("#campusphone", "campusForm.phone");

        Livewire.on("contentChanged", () => {
            initPhoneInput("#phone", "schoolData.phone");
            initPhoneInput("#campusphone", "campusForm.phone");

        });

        // Re-initialize when modal opens
        $("#updateSectionAModal").on("shown.bs.modal", function () {
            setTimeout(() => initPhoneInput("#phone", "schoolData.phone"), 100);
            setTimeout(() => initPhoneInput("#campusphone", "campusForm.phone"), 100);
        });

        // // Listen for Livewire updates
        Livewire.on("phone-input-updated", () => {
            setTimeout(() => initPhoneInput("#phone", "schoolData.phone"), 100);
            setTimeout(() => initPhoneInput("#campusphone", "campusForm.phone"), 100);

        });

        // Section B registration status handling
        $(document).on(
            "change",
            '[wire\\:model="schoolData.registration_status"]',
            function () {
                const value = $(this).val();
                if (value === "registered") {
                    $("#registrationNumberField").show();
                    $("#licenseNumberField, #licenseExpiryField").hide();
                } else if (value === "licensed") {
                    $("#registrationNumberField").hide();
                    $("#licenseNumberField, #licenseExpiryField").show();
                } else {
                    $(
                        "#registrationNumberField, #licenseNumberField, #licenseExpiryField"
                    ).hide();
                }
            }
        );

        // Initialize date picker for license expiry
        $("#updateSectionBModal").on("shown.bs.modal", function () {
            setTimeout(function () {
                if ($("#licenseNumberDateExpiry").length && $.fn.datepicker) {
                    $("#licenseNumberDateExpiry")
                        .datepicker({
                            format: "d MM, yyyy",
                            autoclose: true,
                        })
                        .on("changeDate", function (e) {
                            let wireId = document.querySelector("[wire\\:id]");
                            if (wireId) {
                                Livewire.find(
                                    wireId.getAttribute("wire:id")
                                ).set(
                                    "schoolData.licence_certificate_expiry_date",
                                    moment(e.date).format("D MMMM, YYYY")
                                );
                            }
                        });
                }

                //         // Format capital amount with commas
                $("#schoolCapitalEstablishment").on("blur", function () {
                    let value = $(this).val().replace(/[^\d]/g, "");
                    if (value) {
                        value = parseInt(value, 10).toLocaleString("en-US");
                        $(this).val(value);
                        let wireId = document.querySelector("[wire\\:id]");
                        if (wireId) {
                            Livewire.find(wireId.getAttribute("wire:id")).set(
                                "schoolData.capital_for_establishment",
                                value
                            );
                        }
                    }
                });
            }, 100);
        });
    });
});
