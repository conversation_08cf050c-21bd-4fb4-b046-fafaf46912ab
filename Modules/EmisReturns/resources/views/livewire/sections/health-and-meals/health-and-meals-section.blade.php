<div>
    <div class="w-100">
        <div class="">
            <div>
                {{-- HIV/AIDS Policy --}}
                <livewire:emis-returns.notification-banner />
                @if (($schoolType && $schoolType->id == 2) || ($schoolType && $schoolType->id == 3))
                    <div class="data-item py-1">
                        <div class="data-col">
                            <span class="data-label">Has your school adopted the HIV/AIDS and Sexuality Education
                                Policy?
                            </span>
                            @if ($schoolType && $schoolType->id == 2)
                                <span class="ml-lg-5 data-value text-dark">
                                    {{ $school->primary_school?->has_adopted_sex_hiv_policy ? 'YES' : 'NO' }}
                                </span>
                            @elseif($schoolType && $schoolType->id == 3)
                                <span class="ml-lg-5 data-value text-dark">
                                    {{ $school->secondary_school?->has_adopted_sex_hiv_policy ? 'YES' : 'NO' }}
                                </span>
                            @endif

                        </div>
                        <div class="data-col data-col-end">
                            @if ($schoolType && $schoolType->id == 2)
                                <button type="button" data-toggle="modal" data-target="#SexualityEducationpolicyModal"
                                    class="btn btn-sm bg-dark-teal" wire:click="loadPolicyValuePrimary">
                                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                                </button>
                            @elseif($schoolType && $schoolType->id == 3)
                                <button type="button" data-toggle="modal" data-target="#SexualityEducationpolicyModal"
                                    class="btn btn-sm bg-dark-teal" wire:click="loadPolicyValueSecondary">
                                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                                </button>
                            @endif

                        </div>
                    </div>
                @endif

                {{-- Midday Meal --}}
                @if ($schoolType && in_array($schoolType->id, [1, 2, 3]))
                    <div class="data-item py-1">
                        <div class="data-col">
                            <span class="data-label">Does your school provide a hot midday meal to learners?</span>
                            <span class="ml-lg-5 data-value text-dark">
                                @if ($schoolType->id == 2)
                                    {{ $school->primary_school?->offer_hot_midday_meal_to_learners ? 'YES' : 'NO' }}
                                @elseif($schoolType->id == 3)
                                    {{ $school->secondary_school?->offer_hot_midday_meal_to_learners ? 'YES' : 'NO' }}
                                @elseif($schoolType->id == 1)
                                    {{ $school->pre_primary_school?->offer_hot_midday_meal_to_learners ? 'YES' : 'NO' }}
                                @else
                                    {{ $school->offer_hot_midday_meal_to_learners ? 'YES' : 'NO' }}
                                @endif
                            </span>
                        </div>
                        <div class="data-col data-col-end">
                            <button type="button" data-toggle="modal" data-target="#hotMealModal"
                                class="btn btn-sm bg-dark-teal" wire:click="loadHotMealValues">
                                <em class="ni ni-edit-fill text-white mr-1"></em>Update
                            </button>
                        </div>
                    </div>
                @endif
                @if ($school->meal_types->count())
                    <div class="data-item py-1">
                        <div class="data-col">
                            <span class="data-label">Meal Types</span>
                            <span class="ml-lg-5 text-dark">
                                @foreach ($school->meal_types as $meal_type)
                                    <span class="d-block">- {{ strtoupper($meal_type->name) }}</span>
                                @endforeach
                            </span>
                        </div>
                        <div class="data-col data-col-end"></div>
                    </div>
                @endif

                {{-- Food Sources --}}
                <div class="data-item py-1">
                    <div class="data-col">
                        <span class="data-label">Source of Food</span>
                        @if ($school->food_sources->count())
                            <span class="ml-lg-5 text-dark">
                                @foreach ($school->food_sources as $source)
                                    <span class="d-block">- {{ strtoupper($source->name) }}</span>
                                @endforeach
                            </span>
                        @else
                            <span class="ml-lg-5 data-value text-dark">NONE</span>
                        @endif
                    </div>
                    <div class="data-col data-col-end">
                        <button data-toggle="modal" data-target="#foodSourceModal" type="button"
                            class="btn btn-sm bg-dark-teal" wire:click="loadFoodSourceValues">
                            <em class="ni ni-edit-fill text-white mr-1"></em>Update
                        </button>
                    </div>
                </div>
            </div>

            <!-- HIV/AIDS CASES -->
            @if (($schoolType && $schoolType->id == 2) || ($schoolType && $schoolType->id == 3))
                <div class="card mt-5">
                    <div class="table-responsive">
                        <h5 class="mb-2">Integrated Health Services</h5>
                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                                <tr>
                                    <th class="text-white align-middle text-uppercase" rowspan="3">Category</th>
                                </tr>
                                <tr>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Learners</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Teachers</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Support Staff</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Total</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Action</th>
                                </tr>
                                <tr>
                                    <th class="bg-secondary-dim border-secondary text-center">M</th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        F
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        M
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        F
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        M
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        F
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        M
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                        F
                                    </th>
                                    <th
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="border-top-0 border-secondary">
                                <tr>
                                    <td class="align-middle">Registered and Supported HIV/AIDS Cases</td>

                                    {{-- Learners M --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_learners"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_male_learners'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Learners F --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_learners"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_female_learners'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Teachers M --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_teachers"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_male_teachers'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Teachers F --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_teachers"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_female_teachers'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Support Staff M --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_support_staff"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_male_support_staff'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Support Staff F --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_support_staff"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[1]['total_female_support_staff'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Totals M --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ ($integratedServices[1]['total_male_learners'] ?? 0) +
                                            ($integratedServices[1]['total_male_teachers'] ?? 0) +
                                            ($integratedServices[1]['total_male_support_staff'] ?? 0) }}
                                    </td>

                                    {{-- Totals F --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ ($integratedServices[1]['total_female_learners'] ?? 0) +
                                            ($integratedServices[1]['total_female_teachers'] ?? 0) +
                                            ($integratedServices[1]['total_female_support_staff'] ?? 0) }}
                                    </td>

                                    {{-- Action --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 1)
                                            <button wire:click="saveIntegratedService(1)"
                                                class="btn btn-sm bg-dark-teal">
                                                <em class="icon ni ni-check"></em><span>Save</span>
                                            </button>
                                            <button wire:click="toggleEdit(null)"
                                                class="btn btn-sm btn-secondary mt-1">
                                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                                            </button>
                                        @else
                                            <button wire:click="toggleEdit(1)" class="btn btn-sm btn-primary">
                                                <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Sexuality -->
                <div class="card mt-4">
                    <div class="table-responsive">
                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                                <tr>
                                    <th class="text-white align-middle text-uppercase" rowspan="3">Category</th>
                                </tr>
                                <tr>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Learners</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Teachers</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Support Staff</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Total</th>
                                    <th class="py-2 text-center text-white text-uppercase border-left border-white"
                                        colspan="2">Action</th>
                                </tr>
                                <tr>
                                    @for ($i = 0; $i < 4; $i++)
                                        <th style="height: 40px; vertical-align: middle;"
                                            class="bg-secondary-dim border-secondary text-center">M</th>
                                        <th style="height: 40px; vertical-align: middle;"
                                            class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                            F</th>
                                    @endfor
                                    <th style="height: 40px; vertical-align: middle;"
                                        class="bg-secondary-dim border-secondary text-center border-left border-secondary">
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="border-top-0 border-secondary">
                                <tr>
                                    <td class="align-middle">Number of persons that received HIV/AIDS and Sexuality
                                        Education based life skills</td>

                                    {{-- Learners Male --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_learners"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_male_learners'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Learners Female --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_learners"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_female_learners'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Teachers Female --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_teachers"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_female_teachers'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Teachers Male --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_teachers"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_male_teachers'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Support Staff Female --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_female_support_staff"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_female_support_staff'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Support Staff Male --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <input type="number" min="0"
                                                wire:model.defer="formValues.total_male_support_staff"
                                                class="form-control text-center border-dark-teal" />
                                        @else
                                            {{ $integratedServices[2]['total_male_support_staff'] ?? 0 }}
                                        @endif
                                    </td>

                                    {{-- Total Female --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ ($integratedServices[2]['total_female_learners'] ?? 0) +
                                            ($integratedServices[2]['total_female_teachers'] ?? 0) +
                                            ($integratedServices[2]['total_female_support_staff'] ?? 0) }}
                                    </td>

                                    {{-- Total Male --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ ($integratedServices[2]['total_male_learners'] ?? 0) +
                                            ($integratedServices[2]['total_male_teachers'] ?? 0) +
                                            ($integratedServices[2]['total_male_support_staff'] ?? 0) }}
                                    </td>

                                    {{-- Actions --}}
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if ($editingServiceId === 2)
                                            <div wire:click="saveIntegratedService(2)"
                                                class="cursor btn-sm bg-dark-teal mb-1">
                                                <em class="icon ni ni-check"></em><span>Save</span>
                                            </div>
                                            <div wire:click="toggleEdit(null)"
                                                class="cursor btn-sm text-white bg-secondary">
                                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                                            </div>
                                        @else
                                            <div wire:click="toggleEdit(2)" class="cursor btn-sm btn-primary">
                                                <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            {{-- Integrated health services for pre primary --}}
            @if ($schoolType && $schoolType->id == 1)
                <div class="card card-inner mt-3">

                    {{-- Learners Table --}}
                    <div class="table-responsive">
                        <h5>Integrated Health Services</h5>
                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                                <tr>
                                    <th class="text-white align-middle text-uppercase" rowspan="3">Service Types
                                    </th>
                                    <th class="text-white align-middle text-center border-left border-white py-2"
                                        colspan="4">
                                        How many learners received this service?
                                    </th>
                                </tr>
                                <tr>
                                    <th class="bg-secondary-dim border-secondary text-center">F</th>
                                    <th class="bg-secondary-dim border-secondary text-center">M</th>
                                    <th class="bg-secondary-dim border-secondary text-center">Total</th>
                                    <th class="bg-secondary-dim border-secondary text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody class="border-top-0 border-secondary">
                                @foreach ($healthServicesLearners as $item)
                                    <tr>
                                        <td class="align-middle">{{ $item->name }}</td>

                                        {{-- Female --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <input type="number" min="0"
                                                    wire:model.defer="formValues.total_female_learners"
                                                    class="form-control text-center border-dark-teal" />
                                            @else
                                                {{ $integratedServices[$item->id]['total_female_learners'] ?? 0 }}
                                            @endif
                                        </td>

                                        {{-- Male --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <input type="number" min="0"
                                                    wire:model.defer="formValues.total_male_learners"
                                                    class="form-control text-center border-dark-teal" />
                                            @else
                                                {{ $integratedServices[$item->id]['total_male_learners'] ?? 0 }}
                                            @endif
                                        </td>

                                        {{-- Total --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            {{ ($integratedServices[$item->id]['total_female_learners'] ?? 0) +
                                                ($integratedServices[$item->id]['total_male_learners'] ?? 0) }}
                                        </td>
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <div wire:click="saveIntegratedService({{ $item->id }})"
                                                    class="cursor btn-sm bg-dark-teal mb-1">
                                                    <em class="icon ni ni-check"></em> <span>Save</span>
                                                </div>
                                                <div wire:click="toggleEdit(null)"
                                                    class="cursor btn-sm text-white bg-secondary">
                                                    <em class="icon ni ni-cross"></em> <span>Cancel</span>
                                                </div>
                                            @else
                                                <span wire:click="toggleEdit({{ $item->id }})"
                                                    class="cursor btn-sm btn-primary">
                                                    <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    {{-- Parents Table --}}
                    <div class="table-responsive mt-2">
                        <table class="table border border-dark-teal">
                            <thead class="bg-secondary">
                                <tr>
                                    <th class="text-white align-middle text-uppercase w-45" rowspan="3">Service
                                        Types</th>
                                    <th class="text-white align-middle text-center border-left border-white py-2"
                                        colspan="4">
                                        How many parents / guardians received this service?
                                    </th>
                                </tr>
                                <tr>
                                    <th class="bg-secondary-dim border-secondary text-center">M</th>
                                    <th class="bg-secondary-dim border-secondary text-center">F</th>
                                    <th class="bg-secondary-dim border-secondary text-center">Total</th>
                                    <th class="bg-secondary-dim border-secondary text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody class="border-top-0 border-secondary">
                                @foreach ($healthServicesParents as $item)
                                    <tr>
                                        <td class="align-middle">{{ $item->name }}</td>

                                        {{-- Male --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <input type="number" min="0"
                                                    wire:model.defer="formValues.total_male_parents"
                                                    class="form-control text-center border-dark-teal" />
                                            @else
                                                {{ $integratedServices[$item->id]['total_male_parents'] ?? 0 }}
                                            @endif
                                        </td>

                                        {{-- Female --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <input type="number" min="0"
                                                    wire:model.defer="formValues.total_female_parents"
                                                    class="form-control text-center border-dark-teal" />
                                            @else
                                                {{ $integratedServices[$item->id]['total_female_parents'] ?? 0 }}
                                            @endif
                                        </td>

                                        {{-- Total --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            {{ ($integratedServices[$item->id]['total_male_parents'] ?? 0) +
                                                ($integratedServices[$item->id]['total_female_parents'] ?? 0) }}
                                        </td>

                                        {{-- Actions --}}
                                        <td class="align-middle border-left border-secondary text-center">
                                            @if ($editingServiceId === $item->id)
                                                <div wire:click="saveIntegratedService({{ $item->id }})"
                                                    class="cursor btn-sm bg-dark-teal mb-1">
                                                    <em class="icon ni ni-check"></em><span>Save</span>
                                                </div>
                                                <div wire:click="toggleEdit(null)"
                                                    class="cursor btn-sm text-white bg-secondary">
                                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                                </div>
                                            @else
                                                <span wire:click="toggleEdit({{ $item->id }})"
                                                    class="cursor btn-sm btn-primary">
                                                    <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- HIV and sexuality modal --}}
    <div>
        <div wire:ignore.self class="modal fade zoom" tabindex="-1" id="SexualityEducationpolicyModal">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form wire:submit.prevent="updatePolicy('{{ $schoolType->id == 2 ? 'primary' : 'secondary' }}')">
                        <div class="modal-header">
                            <h5 class="modal-title">Update HIV/AIDS & Sexuality Education</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row px-4">
                                <div class="col-12">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">Has your school adopted the HIV/AIDS and Sexuality
                                            Education Policy?</label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input wire:model="has_adopted_sex_hiv_policy" type="radio"
                                                        value="1" class="custom-control-input" id="policy_yes">
                                                    <label class="custom-control-label" for="policy_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input wire:model="has_adopted_sex_hiv_policy" type="radio"
                                                        value="0" class="custom-control-input" id="policy_no">
                                                    <label class="custom-control-label" for="policy_no">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- Hot meal modal --}}
    <div class="modal fade zoom" tabindex="-1" id="hotMealModal" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit.prevent="updateMiddayMeal">
                    <div class="modal-header">
                        <h5 class="modal-title">Update Hot Midday Meals</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row px-4">
                            {{-- Question --}}
                            <div class="col-12">
                                <div class="form-group d-flex flex-column justify-content-center">
                                    <label class="form-label font-weight-bold">
                                        Does your school provide a hot midday meal to learners?
                                    </label>
                                    <ul class="custom-control-group g-3 align-center flex-wrap">
                                        <li>
                                            <div class="custom-control custom-radio">
                                                <input wire:model.live="offer_hot_midday_meal_to_learners"
                                                    type="radio" value="1" class="custom-control-input"
                                                    id="hot_day_meal_yes">
                                                <label class="custom-control-label" for="hot_day_meal_yes">Yes</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="custom-control custom-radio">
                                                <input wire:model.live="offer_hot_midday_meal_to_learners"
                                                    type="radio" value="0" class="custom-control-input"
                                                    id="hot_day_meal_no">
                                                <label class="custom-control-label" for="hot_day_meal_no">No</label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            {{-- Meal Types (visible only if Yes) --}}
                            @if ($offer_hot_midday_meal_to_learners == '1')
                                <div class="col-12 mt-4" wire:transition>
                                    <label class="form-label font-weight-bold mb-2">Select Meal Types</label>
                                    @foreach ($mealTypes as $meal)
                                        <div class="custom-control custom-checkbox d-block mb-2">
                                            <input type="checkbox" wire:model="selected_meal_types"
                                                value="{{ $meal->id }}" class="custom-control-input"
                                                id="mealType{{ $meal->id }}">
                                            <label class="custom-control-label" for="mealType{{ $meal->id }}">
                                                {{ $meal->name }}
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                        </div>
                    </div>

                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary d-flex">
                            <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    {{-- Food source modal --}}
    <div class="modal fade zoom" tabindex="-1" id="foodSourceModal" wire:ignore>
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit.prevent="updateFoodSources">
                    <div class="modal-header">
                        <h5 class="modal-title">Update school source of food</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row px-4">
                            <div class="col-12">
                                <div class="form-group d-flex flex-column justify-content-center">
                                    <label class="form-label font-weight-bold">What is the source of food for learners
                                        in your school?</label>
                                    @foreach ($foodSources as $source)
                                        <div class="custom-control custom-checkbox d-block mb-2">
                                            <input type="checkbox" wire:model="selected_food_sources"
                                                value="{{ $source->id }}" class="custom-control-input"
                                                id="foodSource{{ $source->id }}">
                                            <label class="custom-control-label"
                                                for="foodSource{{ $source->id }}">{{ $source->name }}</label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary d-flex">
                            <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>
