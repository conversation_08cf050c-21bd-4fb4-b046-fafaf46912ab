<div>
    <div class="mt-5">
        <div class="w-100 mb-5">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h5 class="nk-block-title">LAB EQUIPMENT</h5>
                    </div>
                    <div class="nk-block-head-content">
                        <div class="toggle-wrap nk-block-tools-toggle">
                            <button data-toggle="modal" data-target="#schoolLabEquipmentModal"
                                class="cursor btn bg-dark-teal btn-md d-sm-none">
                                <em class="icon ni ni-plus-circle-fill text-white"></em><span>Update Lab
                                    Equipment</span>
                            </button>
                            <div class="toggle-expand-content" data-content="pageMenu">
                                <div>
                                    <button data-toggle="modal" data-target="#schoolLabEquipmentModal"
                                        class="cursor btn bg-dark-teal btn-md">
                                        <em class="icon ni ni-plus-circle-fill text-white"></em><span>Update Lab
                                            Equipment</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Replace Vue v-for with Blade foreach --}}
            <div class="table-responsive">
                <livewire:emis-returns.notification-banner />
                <div class="table table-bordered table-hover table-sm">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">Item</span></div>
                            <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span>
                            </div>
                        </div>

                        @forelse ($labEquipmentChemicals['equipments'] as $equipment)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span
                                        class="text-secondary text-center ucap">{{ strtoupper(optional($equipment->equipment)->name ?? 'N/A') }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span class="text-secondary">{{ $equipment->quantity }}</span>
                                </div>
                            </div>
                        @empty
                            <div class="card card-stretch" style="box-shadow: none;">
                                <div class="card-inner-group">
                                    <div class="card-body">
                                        <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                            <em class="icon ni ni-alert-circle"></em> There is no Lab Equipment
                                            information to display at the moment.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            {{-- Modal --}}
            <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolLabEquipmentModal">
                <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <form wire:submit.prevent="saveLabEquipment">
                            <div class="modal-header">
                                {{-- <h5 class="modal-title ucap">{{ edit ? 'EDIT' : 'UPDATE' }} Lab Equipment</h5> --}}
                            </div>
                            <div class="modal-body">
                                <error-notifications ref="notifyError"></error-notifications>
                                <div class="row g-4">

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="labEquipmentId">Select Lab Equipment<span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select wire:model="lab_equipment_id" id="labEquipmentId"
                                                    data-placeholder="Select Lab Equipment" class="form-select-sm">
                                                    <option value="">--Select Lab Equipment--</option>
                                                    @foreach ($labEquipmentChemicals['items'] as $item)
                                                        <option value="{{ $item->id }}">
                                                            {{ strtoupper($item->name) }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="quantity" class="form-label">Number <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <input wire:model.lazy="quantity" type="number" id="quantity"
                                                    class="form-control" required autocomplete="off"
                                                    placeholder="Enter Number">
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="modal-footer d-flex justify-content-center">
                                <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>

    {{-- LAB REAGENTS --}}
    <div class="w-100 mb-5">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">LAB REAGENTS</h5>

                </div>
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolLabReagentsModal"
                            class="cursor btn bg-dark-teal btn-md d-sm-none"><em
                                class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update Lab
                                Reagents</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolLabReagentsModal"
                                    class="cursor btn bg-dark-teal btn-md"><em
                                        class="icon ni ni-plus-circle-fill text-white"></em><span
                                        class="">Update Lab Reagents</span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <livewire:emis-returns.notification-banner />
            <div class="table table-bordered table-hover table-sm">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    {{-- Table Header --}}
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col">
                            <span class="sub-text text-white text-uppercase">Item</span>
                        </div>
                        <div class="nk-tb-col text-center">
                            <span class="sub-text text-white text-uppercase">Number</span>
                        </div>
                    </div>

                    {{-- Table Body --}}
                    @forelse ($labReagents as $item)
                        <div class="nk-tb-item">
                            <div class="nk-tb-col">
                                <span class="text-secondary text-capitalize">{{ $item->reagent->name }}</span>
                            </div>
                            <div class="nk-tb-col text-center">
                                <span class="text-secondary">{{ $item->quantity }}</span>
                            </div>
                        </div>
                    @empty
                        <div class="nk-tb-item">
                            <div class="nk-tb-col" colspan="2">
                                <div class="alert alert-secondary alert-icon text-center w-100 my-4">
                                    <em class="icon ni ni-alert-circle"></em>
                                    There is no Lab Reagents information to display at the moment.
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>


        <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolLabReagentsModal">
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>

                    <form wire:submit.prevent="saveReagent">
                        <div class="modal-header">
                            <h5 class="modal-title">LAB REAGENTS</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">

                                <div class="col-12">
                                    <div class="form-group text-black">
                                        <label for="labReagentId">Select Lab Reagent <span
                                                class="text-danger">*</span></label>
                                        <div class="col-12">
                                            <div class="form-group">
                                                <div class="form-control-wrap">
                                                    <select wire:model="lab_reagent_id" id="labReagentId"
                                                        data-placeholder="Select Lab Equipment" class="form-control">
                                                        <option value="">--Select Lab Equipment--</option>
                                                        @foreach ($setLabReagents as $item)
                                                            <option value="{{ $item->id }}">
                                                                {{ strtoupper($item->name) }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="quantity" class="form-label">Number <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input wire:model="reagent_quantity" type="number" id="quantity"
                                                class="form-control" placeholder="Enter Number" required>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span><em class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>


    </div>
</div>
