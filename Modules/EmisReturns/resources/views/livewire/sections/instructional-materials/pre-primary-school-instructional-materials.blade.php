<div>
    <div class="table-responsive">
        <livewire:emis-returns.notification-banner />
        <table class="table border border-dark-teal">
            <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase" rowspan="2">Learning Materials</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2"
                        colspan="{{ $grades->count() + 1 }}">
                        Class
                    </th>
                </tr>
                <tr>
                    @foreach ($grades as $grade)
                        <th class="py-2 text-center text-white text-uppercase border-left border-white">
                            {{ $grade->name }}
                        </th>
                    @endforeach
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
            </thead>

            <tbody>
                @foreach ($categories as $category)
                    <tr class="bg-light">
                        <td class="align-middle text-uppercase font-weight-bold text-center text-dark-teal border-top border-bottom border-dark-teal"
                            colspan="{{ $grades->count() + 2 }}">
                            {{ $category->name }}
                        </td>
                    </tr>

                    @foreach ($category->materials as $material)
                        <tr>
                            <td class="align-middle">{{ $material->name }}</td>

                            @foreach ($grades as $grade)
                                <td class="text-center align-middle border-left border-gray">
                                    @php
                                        $entries = $materialGradeMap->get($material->id, collect());
                                        $match = $entries->firstWhere('education_grade_id', $grade->id);
                                        $quantity = $match ? $match->quantity : 0;
                                    @endphp

                                    @if ($editMaterial === $material->id)
                                        <input type="number"
                                            wire:model.lazy="materialGradeStates.{{ $material->id }}.{{ $grade->id }}"
                                            class="form-control text-center border-dark-teal" min="0" />
                                    @else
                                        <span>{{ $quantity }}</span>
                                    @endif

                                </td>
                            @endforeach

                            <td class="align-middle border-left text-center">
                                @if ($editMaterial !== $material->id)
                                    <span wire:click="toggleUpdate({{ $material->id }})"
                                        class="cursor-pointer btn btn-primary">
                                        <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                    </span>
                                @else
                                    <div wire:click="saveUpdates({{ $material->id }})"
                                        class="cursor-pointer btn-sm bg-dark-teal text-white">
                                        <em class="icon ni ni-check"></em><span>Save</span>
                                    </div>
                                    <div wire:click="toggleUpdate(null)"
                                        class="cursor-pointer btn-sm bg-secondary text-white mt-2">
                                        <em class="icon ni ni-cross cursor"></em><span>Cancel</span>
                                    </div>
                                @endif
                            </td>

                        </tr>
                    @endforeach
                @endforeach
            </tbody>
        </table>
    </div>
</div>
