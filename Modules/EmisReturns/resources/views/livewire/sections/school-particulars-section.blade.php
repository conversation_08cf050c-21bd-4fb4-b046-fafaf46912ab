<div>
        <!-- Section A -->
        <div class="card-inner card-inner-lg">
        <livewire:emis-returns.notification-banner key="sectionA" :eventName="'notifySectionA'"/>

            <table class="table table-sm table-hover">
                <thead>
                    <tr class="bg-secondary">
                        <th colspan="2" class="align-middle text-uppercase text-white border-secondary border">Section A: Institution Identification</th>
                    </tr>
                    <tr class="bg-secondary-dim">
                        <th class="align-middle text-uppercase text-dark border-secondary border w-35">Identifier</th>
                        <th class="align-middle text-uppercase text-dark border-secondary border">Name/Detail</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Name Of The School</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['name'])
                                {{ $schoolData['name'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">EMIS Number</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['emis_number'])
                                {{ $schoolData['emis_number'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Region</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['region'])
                                {{ $schoolData['region'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">District</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['district'])
                                {{ $schoolData['district'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">County/Municipality</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['county'])
                                {{ $schoolData['county'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Sub-County/Division/Town Council</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['sub_county'])
                                {{ $schoolData['sub_county'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Parish</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['parish'])
                                {{ $schoolData['parish'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Physical Address</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['physical_address'])
                                {{ $schoolData['physical_address'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Postal Address</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['postal_address'])
                                {{ $schoolData['postal_address'] }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Email Address</th>
                        <td class="align-middle text-dark border border-secondary">
                            @if($schoolData['email'])
                                {{ strtolower($schoolData['email']) }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="align-middle text-uppercase text-dark border border-secondary">Telephone Contact</th>
                        <td class="align-middle text-uppercase text-dark border border-secondary">
                            @if($schoolData['phone'])
                                {{ '+' . ltrim($schoolData['phone'], '+') }}
                            @else
                                <span class="text-muted font-italic">NOT SET</span>
                            @endif
                        </td>

                    </tr>

                    <!-- Only show for certificate, diploma, international and degree institutions -->
                    @if(in_array($schoolType, ['certificate', 'diploma', 'international', 'degree']))
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Website</th>
                            <td class="align-middle text-dark border border-secondary">
                                @if($schoolData['website'])
                                    {{ $schoolData['website'] }}
                                @else
                                    <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Area (Acres)</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @if($schoolData['school_land_area'])
                                    {{ $schoolData['school_land_area'] }} Acres
                                @else
                                    <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>
                    @endif
                </tbody>
            </table>

            <div class="text-center mt-3">
                <button data-toggle="modal" data-target="#updateSectionAModal" class="btn bg-dark-teal btn-sm">
                    <span class="text-uppercase">UPDATE INSTITUTION IDENTIFICATION</span>
                </button>
            </div>
        </div>
        <!-- Section B -->
        <div class="card-inner card-inner-lg mt-4">
        <livewire:emis-returns.notification-banner key="sectionB" :eventName="'notifySectionB'"/>

            <table class="table table-sm table-hover">
                <thead>
                    <tr class="bg-secondary">
                        <th colspan="2" class="align-middle text-uppercase text-white border-secondary border">
                            @if(in_array($schoolType, ['certificate', 'diploma', 'degree', 'international']))
                                Section B: Institution PARTICULARS & PROGRAMS
                            @else
                                Section B: School Particulars
                            @endif
                        </th>
                    </tr>
                    <tr class="bg-secondary-dim">
                        <th class="align-middle text-uppercase text-dark border-secondary border w-35">Identifier</th>
                        <th class="align-middle text-uppercase text-dark border-secondary border">Name/Detail</th>
                    </tr>
                </thead>
                <tbody>
                    {{-- Pre-Primary --}}
                    @if($schoolType === 'pre-primary')
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">ECCE CENTER TYPE</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @php
                                    $ecceTypeMap = [
                                        'both' => 'Nursery and Day Care Centre',
                                        'nursery' => 'Nursery Centre',
                                        'daycare' => 'Day Care Centre',
                                    ];
                                @endphp
                                {{ $ecceTypeMap[$schoolData['ecce_center_type'] ?? ''] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">ATTACHED TO PRIMARY SCHOOL</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @if($schoolData['attached_to_primary_school'] === 1) YES
                                @elseif($schoolData['attached_to_primary_school'] === 0) NO
                                @else <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>
                        @if($schoolData['attached_to_primary_school'] == 1)
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">PRIMARY SCHOOL</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['primary_school_name'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">PRIMARY SCHOOL PHONE</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['primary_school_phone'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['legal_ownership_status'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ strtoupper($schoolData['registration_status'] ?? 'NOT SET') }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['registration_number'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">sex composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @php
                                    $sexMap = [
                                        'mixed' => 'Mixed',
                                        'male' => 'Male Only',
                                        'female' => 'Female Only',
                                    ];
                                @endphp
                                {{ $sexMap[$schoolData['sex_composition'] ?? ''] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">DAY OR BOARDING</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @php
                                    $boardingMap = [
                                        'both' => 'Day and Boarding',
                                        'day' => 'Day Only',
                                        'boarding' => 'Boarding Only',
                                    ];
                                @endphp
                                {{ $boardingMap[$schoolData['day_or_boarding'] ?? ''] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['capital_for_establishment'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to DEO/MEO/CEO Main office</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_deo'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Pre-Primary School</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_nearest_preprimary'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                     @elseif($schoolType === 'primary')
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Ownership Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['ownership_status'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @if($schoolData['school_ownership_status_id'] == 1)
                                    GOVERNMENT
                                @elseif(!empty($schoolData['legal_ownership_status']) && $schoolData['school_ownership_status_id'] == 2)
                                    {{ strtoupper($schoolData['legal_ownership_status']) }}
                                @else
                                    <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>
                        
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">School Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Supply Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['supply_number'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">UNEB Center Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['uneb_center_number'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['sex_composition'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Day Or Boarding</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['day_or_boarding'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to DEO/MEO/CEO Main Office</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_deo'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Primary School</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_nearest_primary'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health Facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] ?: 'NOT SET' }}
                            </td>
                        </tr>
                    @elseif($schoolType === 'secondary')
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['ownership_status'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @if($schoolData['school_ownership_status_id'] === 1)
                                    <span>GOVERNMENT</span>
                                @elseif($schoolData['legal_ownership_status'] !== null)
                                    <span>{{ $schoolData['legal_ownership_status'] }}</span>
                                @else
                                    <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">School Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['founder'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['year_founded'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        @if($schoolData['ownership_status'] === 'GOVT AIDED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">Supply Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['supply_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">UNEB Center Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['uneb_center_number'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">SEX COMPOSITION</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['sex_composition'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>

                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">DAY OR BOARDING</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                @if($schoolData['day_or_boarding'] === 'both')
                                    <span>Day and Boarding</span>
                                @elseif($schoolData['day_or_boarding'] === 'day')
                                    <span>Day Only</span>
                                @elseif($schoolData['day_or_boarding'] === 'boarding')
                                    <span>Boarding Only</span>
                                @else
                                    <span class="text-muted font-italic">NOT SET</span>
                                @endif
                            </td>
                        </tr>


                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Is this a USE/UPOLET school?</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['use_upolet'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">School Level</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                            {{ $schoolData['school_level'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to DEO/MEO/CEO Main office</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['distance_to_deo'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Primary school</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['distance_to_nearest_secondary'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['distance_to_health_facility'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                    @elseif($schoolType === 'certificate')
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">INSTITUTION TYPE</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['institution_type'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['legal_ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Main Funding Source</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['funding_source'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registering Authority</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['registering_authority'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['registration_status'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        @if($schoolData['registration_status'] === 'REGISTERED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['registration_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @elseif($schoolData['registration_status'] === 'LICENSED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['license_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['licence_certificate_expiry_date'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @endif
                        @if(!empty($schoolData['examining_body_acronym']))
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">{{ $schoolData['examining_body_acronym'] }} Center Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['center_number'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['sex_composition'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Day Or Boarding</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['day_or_boarding'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Highest Award</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                CERTIFICATES
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['capital_for_establishment'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health Facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] }}
                            </td>
                        </tr>
                    @elseif($schoolType === 'diploma')
                        {{-- ...repeat as above, but with DIPLOMAS as Highest Award... --}}
                        {{-- ...same structure as certificate, but Highest Award is DIPLOMAS... --}}
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">INSTITUTION TYPE</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['institution_type'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['legal_ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Main Funding Source</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['funding_source'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registering Authority</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['registering_authority'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['registration_status'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        @if($schoolData['registration_status'] ==='REGISTERED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['registration_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @elseif($schoolData['registration_status'] === 'LICENSED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['license_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['licence_certificate_expiry_date'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @endif
                        @if(!empty($schoolData['examining_body_acronym']))
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">{{ $schoolData['examining_body_acronym'] }} Center Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['center_number'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['sex_composition'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Day Or Boarding</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['day_or_boarding'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Highest Award</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                DIPLOMAS
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['capital_for_establishment'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health Facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] }}
                            </td>
                        </tr>
                        @elseif($schoolType === 'degree')
                        {{-- ...repeat as above, but with DEGREES as Highest Award... --}}
                        {{-- ...same structure as certificate, but Highest Award is DEGREES... --}}
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">INSTITUTION TYPE</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['institution_type'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['legal_ownership_status'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Main Funding Source</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['funding_source'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registering Authority</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['registering_authority'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['registration_status'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        @if($schoolData['registration_status'] === 'REGISTERED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['registration_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @elseif($schoolData['registration_status'] === 'LICENSED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['license_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['licence_certificate_expiry_date'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @endif
                        @if(!empty($schoolData['examining_body_acronym']))
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">{{ $schoolData['examining_body_acronym'] }} Center Number</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['center_number'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['sex_composition'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Day Or Boarding</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['day_or_boarding'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Highest Award</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                DEGREES
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['capital_for_establishment'] }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health Facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] }}
                            </td>
                        </tr>
                    @elseif($schoolType === 'international')
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">LEGAL OWNERSHIP STATUS</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['legal_ownership_status'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Founder</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['founder'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Funding Source</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['funding_source'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Year Founded</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['year_founded'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Registration Status</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                <span>{{ $schoolData['registration_status'] ?? 'NOT SET' }}</span>
                            </td>
                        </tr>
                        @if($schoolData['registration_status'] === 'REGISTERED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">Registration Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['registration_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @elseif($schoolData['registration_status'] === 'LICENSED')
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License Number</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['license_number'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th class="align-middle text-uppercase text-dark border border-secondary">License No. Expiry Date</th>
                                <td class="align-middle text-uppercase text-dark border border-secondary">
                                    <span>{{ $schoolData['licence_certificate_expiry_date'] ?? 'NOT SET' }}</span>
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Sex Composition</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['sex_composition'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">DAY OR BOARDING</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['day_or_boarding'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Capital For Establishment</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['capital_for_establishment'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to DEO/MEO/CEO Main office</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_deo'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest International School</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_nearest_preprimary'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border border-secondary">Distance to the nearest Health facility</th>
                            <td class="align-middle text-uppercase text-dark border border-secondary">
                                {{ $schoolData['distance_to_health_facility'] ?? 'NOT SET' }}
                            </td>
                        </tr>
                    @else
                        <tr>
                            <td colspan="2" class="text-center text-danger">Section B data not available for this school type ({{ $schoolType }})</td>
                        </tr>
                    @endif
                </tbody>
            </table>
            <div class="text-center mt-3">
                <button data-toggle="modal" data-target="#updateSectionBModal" class="btn bg-dark-teal btn-sm">
                    <span class="text-uppercase">UPDATE SCHOOL PARTICULARS</span>
                </button>
            </div>
                
        <!-- Campuses/Branches Section -->
        @if(in_array($schoolType, ['certificate', 'diploma', 'degree', 'secondary', 'international']))
            <div class="mt-5">
                <!-- Has Campuses -->
                <div class="row w-100">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-label">Does this institution have branches/campuses?</label>
                            <span class="form-note">Specify whether your institution has branches/campuses.</span>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" wire:model.live="schoolData.has_campuses" value="1" id="institutionHasCampuses">
                                <label class="custom-control-label text-uppercase" for="institutionHasCampuses">YES</label>
                            </div>
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" wire:model.live="schoolData.has_campuses" value="0" id="institutionWithoutCampuses">
                                <label class="custom-control-label text-uppercase" for="institutionWithoutCampuses">NO</label>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <!-- /Has Campuses -->
                <livewire:emis-returns.notification-banner key="campus" :eventName="'notifyCampus'" />
                @if($schoolData['has_campuses'] == '1')
                    <div class="mt-3">
                        <button type="button" class="btn bg-dark-teal btn-sm mt-1" data-toggle="modal" data-target="#updateCampusesModal">
                            <span class="text-uppercase">ADD BRANCHES / CAMPUSES</span>
                        </button>
                        <table class="table border border-dark mt-2">
                            <thead class="bg-secondary">
                            <tr>
                                <th class="text-white align-middle text-uppercase">Name</th>
                                <th class="py-2 text-center text-white text-uppercase border-left border-white">Phone No.</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white">District</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white">County</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white">Sub County</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white">Parish</th>
                            </tr>
                            </thead>
                            <tbody class="border-top-0 border-secondary">
                            @forelse($schoolData['campuses'] as $campus)
                                <tr>
                                    <td class="align-middle border-left border-secondary ucap text-dark">{{ $campus['name'] }}</td>
                                    <td class="align-middle border-left border-secondary text-center">
                                        @if(!empty($campus['phone']))
                                            <span class="text-dark text-uppercase">+256{{ $campus['phone'] }}</span>
                                        @else
                                            <span class="text-muted font-italic text-uppercase">NOT SET</span>
                                        @endif
                                    </td>
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ $campus['district']['name'] ?? 'NOT SET' }}
                                    </td>
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ $campus['county']['name'] ?? 'NOT SET' }}
                                    </td>
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ $campus['sub_county']['name'] ?? 'NOT SET' }}
                                    </td>
                                    <td class="align-middle border-left border-secondary text-center">
                                        {{ $campus['parish']['name'] ?? 'NOT SET' }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">There is no campus/branch information to display at the moment.</td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
             
            <!-- Update Campuses Modal -->
            <div class="modal fade zoom" tabindex="-1" id="updateCampusesModal" wire:ignore.self>
                
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a href="#" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <div class="modal-header">
                            <h5 class="modal-title">Add Branches/Campuses</h5>
                        </div>
                        <form wire:submit.prevent="saveCampus">
                            <div class="modal-body overflow-auto scrollbar-dark-teal" style="max-height: 500px;">
                            
                                <!-- Campus Name -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="campusName">Campus Name</label>
                                            <span class="form-note">Specify Branch Name</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <input wire:model.defer="campusForm.name" placeholder="Enter Campus Name" id="campusName" type="text" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                            @error('campusForm.name') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6 mt-1">
                                        <div class="form-group">
                                            <label class="form-label" for="campusPhone">Campus Telephone Contact</label>
                                            <span class="form-note">Specify Branch Phone Number</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 mt-1">
                                       
                                        <div class="form-group">
                                        <div class="form-control-wrap" wire:ignore>
                                            <input wire:model.defer="campusForm.phone"
                                                id="campusphone"
                                                type="tel"
                                                class="form-control bg-primary-dim"
                                                placeholder="712345678">
                                        </div>
                                        @error('campusForm.phone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    </div>
                                </div>
                                
                                <!-- District -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="campusDistrictId">District</label>
                                            <span class="form-note">Specify Institution's district.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <select wire:model.change="campusForm.district_id" id="campusDistrictId" class="form-control" >
                                                <option value="">--Select--</option>
                                                @foreach($districts as $district)
                                                    <option value="{{ $district->id }}">{{ strtoupper($district->name) }}</option>
                                                @endforeach
                                            </select>
                                            @error('campusForm.district_id') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                </div>
                                <!-- County -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="campusCountyId">County/Municipality</label>
                                            <span class="form-note">Specify Institution's county/municipality.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <select wire:model.change="campusForm.county_id" id="campusCountyId" class="form-control">
                                                <option value="">--Select--</option>
                                                @foreach($counties as $county)
                                                    @if($county->district_id == $campusForm['district_id'])
                                                        <option value="{{ $county->id }}">{{ strtoupper($county->name) }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            @error('campusForm.county_id') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                </div>
                                <!-- Sub County -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="campusSubCountyId">Sub County/Division</label>
                                            <span class="form-note">Specify Institution's sub county/division.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <select wire:model.change="campusForm.sub_county_id" id="campusSubCountyId" class="form-control" >
                                                <option value="">--Select--</option>
                                                @foreach($subcounties as $subcounty)
                                                    @if($subcounty->county_id == $campusForm['county_id'])
                                                        <option value="{{ $subcounty->id }}">{{ $subcounty->name }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            @error('campusForm.sub_county_id') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                </div>
                                <!-- Parish -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="campusParishId">Parish/Ward</label>
                                            <span class="form-note">Specify Institution's parish/town council.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <select wire:model.defer="campusForm.parish_id" id="campusParishId" class="form-control" >
                                                <option value="">--Select--</option>
                                                @foreach($parishes as $parish)
                                                    @if($parish->sub_county_id == $campusForm['sub_county_id'])
                                                        <option value="{{ $parish->id }}">{{ $parish->name }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            @error('campusForm.parish_id') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="modal-footer d-flex justify-content-center">
                                <button type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Save</span>
                                    <em class="ni ni-arrow-right ml-2"></em>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- /Update Campuses Modal -->
        @endif
        </div>
 
        <!-- Section A Modal -->
        <div class="modal fade zoom" tabindex="-1" id="updateSectionAModal" wire:ignore.self>
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <form wire:submit="saveSectionA">
                        
                        <div class="modal-header">
                            <h5 class="modal-title text-uppercase">Update Pre Identification</h5>
                        </div>
                        <div class="modal-body">
                            <!-- Physical Address -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="physicalAddress">Physical Address</label>
                                        <span class="form-note">Specify the physical address of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input wire:model="schoolData.physical_address"
                               placeholder="PLOT 234, KAMPALA"
                               id="physicalAddress"
                               type="text"
                               class="form-control bg-primary-dim text-uppercase">
                                            @error('schoolData.physical_address')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Postal Address -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="postalAddress">Postal Address</label>
                                        <span class="form-note">Specify the postal address of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <input wire:model="schoolData.postal_address"
                               placeholder="P.O BOX 32234"
                               id="postalAddress"
                               type="text"
                               class="form-control bg-primary-dim text-uppercase">
                                            @error('schoolData.postal_address')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Phone Contact -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label" for="phone">Phone Contact</label>
                                        <span class="form-note">Specify the phone number of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="form-control-wrap" wire:ignore>
                                            <input wire:model.defer="schoolData.phone"
                                                id="phone"
                                                type="tel"
                                                class="form-control bg-primary-dim"
                                                placeholder="712345678">
                                        </div>
                                        @error('schoolData.phone')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Only show for certificate, diploma, international and degree institutions -->
                            @if(in_array($schoolType, ['certificate', 'diploma', 'international', 'degree']))
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="website">Website</label>
                                            <span class="form-note">Specify the website of the school</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <div class="form-control-wrap">
                                                <input wire:model="schoolData.website"
                                                    placeholder="https://schoolwebsite.com"
                                                    id="website"
                                                    type="text"
                                                    class="form-control bg-primary-dim">
                                                @error('schoolData.website')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3 align-center">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-label" for="school_land_area">Area (Acres)</label>
                                            <span class="form-note">Specify the land area of the school in acres</span>
                                        </div>
                                    </div>
                                   <div class="col-lg-6">
                                        <div class="form-group">
                                            <div class="form-control-wrap">
                                                <input wire:model="schoolData.school_land_area"
                                                    placeholder="Enter land area in acres"
                                                    id="school_land_area"
                                                    type="number"
                                                    step="0.01"
                                                    class="form-control bg-primary-dim">
                                                @error('schoolData.school_land_area')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="modal-footer d-flex justify-content-center mt-4">
                            <button wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                                <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span wire:loading class="align-self-center">Updating...</span>
                                <span wire:loading.remove class="align-self-center">Update</span>
                                <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

            <!-- Section B Modal start -->
        <div class="modal fade zoom" tabindex="-1" id="updateSectionBModal" wire:ignore.self>
            <!-- Pre-Primary Section B Modal -->
            <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                <div class="modal-content">
                    <form wire:submit="saveSectionB">
                        <div class="modal-header">
                            @php
                               
                                $schoolTitles = [
                                    'primary' => 'Update Primary School Particulars',
                                    'pre-primary' => 'Update Pre Primary School Particulars',
                                    'secondary' => 'Update Secondary School Particulars',
                                    'tertiary' => 'Update Tertiary Identification',
                                    'certificate' => 'Update Certificate Institution Particulars',
                                    'diploma' => 'Update Diploma Institution Particulars',
                                    'degree' => 'Update Degree Institution Particulars',
                                    'international' => 'Update International School Particulars',
                                    
                                ];

                            
                                $selectedSchoolType = $schoolType; 
                                $modalTitle = $schoolTitles[$selectedSchoolType] ?? 'Update School Particulars';
                            @endphp
                            <h5 class="modal-title text-uppercase">{{ $modalTitle }}</h5>
                            <a href="#" class="close" data-dismiss="modal" aria-label="Close">
                                <em class="icon ni ni-cross"></em>
                            </a>
                        </div>
                        <div class="modal-body" style="max-height: 500px; overflow-y: auto;">
                        @if($schoolType === 'pre-primary')
                            <!-- ECCE Center Type -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">ECCE Center Type</label>
                                        <span class="form-note">Specify this ECCE Center's type</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <select wire:model="schoolData.ecce_center_type" class="form-control" id="schoolEcceCenterType">
                                                <option value="">Select Type</option>
                                                <option value="daycare">Daycare Only</option>
                                                <option value="nursery">Nursery Only</option>
                                                <option value="both">Nursery and Daycare</option>
                                            </select>
                                            @error('schoolData.ecce_center_type')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Attached To Primary School -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Attached To Primary School</label>
                                        <span class="form-note">Is this school attached to a primary school?</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input wire:model.live="schoolData.attached_to_primary_school" type="radio" value="1" class="custom-control-input" id="attachedToPrimaryYes">
                                                    <label class="custom-control-label" for="attachedToPrimaryYes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input wire:model.live="schoolData.attached_to_primary_school" type="radio" value="0" class="custom-control-input" id="attachedToPrimaryNo">
                                                    <label class="custom-control-label" for="attachedToPrimaryNo">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                        @error('schoolData.attached_to_primary_school')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Conditionally Rendered: Primary School EMIS Number -->
                            @if ($schoolData['attached_to_primary_school'] == '1')
                            <div class="row g-3 align-center" id="primarySchoolEmisContainer">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Primary School EMIS Number</label>
                                        <span class="form-note">Specify the EMIS number this school is attached to</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.primary_school_emis" type="text" class="form-control form-control-sm" placeholder="Enter Primary School EMIS Number">
                                        @error('schoolData.primary_school_emis')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif


                                                <!-- Legal Ownership Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Legal Ownership Status</label>
                                        <span class="form-note">Specify the legal ownership status</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.legal_ownership_status_id" class="form-control" id="schoolLegalOwnershipStatusId">
                                            <option value="">--Select--</option>
                                            @foreach($legalOwnershipStatuses as $status)
                                                <option value="{{ $status->id }}">{{ strtoupper($status->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.legal_ownership_status_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            
                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Founding Body</label>
                                        <span class="form-note">Specify the founder of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model.live="schoolData.founding_body_id" class="form-control form-select form-select-sm" id="schoolFoundingBodyId">
                                            <option value="">--Select--</option>
                                            @foreach($foundingBodies as $body)
                                                <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.founding_body_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Year Founded</label>
                                        <span class="form-note">Specify the year the school was founded</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.year_founded" type="text" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                        @error('schoolData.year_founded')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Registration Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Registration Status {{$schoolData['registration_status']}}</label>
                                        <span class="form-note">Specify the registration status</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model.change="schoolData.registration_status" class="form-control">
                                        
                                            <option value="REGISTERED">Registered</option>
                                            <option value="LICENSED">Licensed</option>
                                            <option value="NOT LICENSED">Not Licensed</option>
                                        </select>
                                        @error('schoolData.registration_status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @if($schoolData['registration_status'] == 'REGISTERED')
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Registration Number</label>
                                        <span class="form-note">Specify this school's registration number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.registration_number" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                        @error('schoolData.registration_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                                @elseif($schoolData['registration_status'] == 'LICENSED')
                                    <!-- Registration Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">License Number</label>
                                        <span class="form-note">Specify this school's license number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.license_number" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                        @error('schoolData.license_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        
                            <!-- Registration Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">License Number Expiry Date</label>
                                        <span class="form-note">Specify this School's license expiry date</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.licence_certificate_expiry_date" type="text" class="form-control form-control-sm"  placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}"">
                                        @error('schoolData.licence_certificate_expiry_date')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                                @endif
                        

                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Sex Composition</label>
                                        <span class="form-note">Specify the sex composition of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.sex_composition" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="mixed">Mixed</option>
                                            <option value="male">Male Only</option>
                                            <option value="female">Female Only</option>
                                        </select>
                                        @error('schoolData.sex_composition')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Day or Boarding -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Day or Boarding</label>
                                        <span class="form-note">Specify whether the school is day, boarding, or both</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.day_or_boarding" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="day">Day Only</option>
                                            <option value="boarding">Boarding Only</option>
                                            <option value="both">Both Day and Boarding</option>
                                        </select>
                                        @error('schoolData.day_or_boarding')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Capital For Establishment -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Capital For Establishment</label>
                                        <span class="form-note">Specify the capital for establishment</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.capital_for_establishment" type="text" class="form-control form-control-sm" placeholder="Enter Capital Amount">
                                        @error('schoolData.capital_for_establishment')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Distance to DEO/MEO/CEO Main Office -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to DEO/MEO/CEO Main Office</label>
                                        <span class="form-note">Distance from this school to the DEO/MEO/CEO's office.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                    <select wire:model="schoolData.estimated_distance_to_deo_office_id" id="distanceToDEO" class="form-control form-select form-select-sm">
                                    <option value="">Select Distance</option>
                                    @foreach($deoDistances as $distance)
                                        <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                    @endforeach
                                </select>
                                        @error('schoolData.estimated_distance_to_deo_office_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Distance to Nearest Pre-Primary School -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to Nearest Pre-Primary School</label>
                                        <span class="form-note">Distance to the nearest pre-primary school</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                <select wire:model="schoolData.estimated_distance_to_ecce_school_id" id="distanceToPrePrimary" class="form-control">
                                    <option value="">Select Distance</option>
                                    @foreach($schoolDistances as $distance)
                                        <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                    @endforeach
                                </select>
                                        @error('schoolData.estimated_distance_to_ecce_school_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Distance to Nearest Health Facility -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to Nearest Health Facility</label>
                                        <span class="form-note">Distance from this school to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                    <select wire:model="schoolData.health_facility_distance_range_id" id="distanceToHealth" class="form-control">
                                    <option value="">Select Distance</option>
                                    @foreach($healthDistances as $distance)
                                        <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                    @endforeach
                                </select>
                                        @error('schoolData.health_facility_distance_range_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif
                                        
                        @if($schoolType === 'primary')
                            <!-- School Founder -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Founding Body</label>
                                        <span class="form-note">Specify the school's founding body</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.founding_body_id" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($foundingBodies as $body)
                                                <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.founding_body_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Year Founded</label>
                                        <span class="form-note">Specify the year the school was founded</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.year_founded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                        @error('schoolData.year_founded')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Supply Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Supply Number</label>
                                        <span class="form-note">Specify the supply number (if applicable)</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.supply_number" type="text" class="form-control form-control-sm" placeholder="Enter Supply Number">
                                        @error('schoolData.supply_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- UNEB Center Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">UNEB Center Number</label>
                                        <span class="form-note">Specify the UNEB center number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.uneb_center_number" type="text" class="form-control form-control-sm" placeholder="Enter UNEB Center Number">
                                        @error('schoolData.uneb_center_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Sex Composition</label>
                                        <span class="form-note">Specify the sex composition of the school</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.sex_composition" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="mixed">Mixed</option>
                                            <option value="male">Male Only</option>
                                            <option value="female">Female Only</option>
                                        </select>
                                        @error('schoolData.sex_composition')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Day or Boarding -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Day or Boarding</label>
                                        <span class="form-note">Specify whether the school is day, boarding, or both</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.day_or_boarding" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="day">Day Only</option>
                                            <option value="boarding">Boarding Only</option>
                                            <option value="both">Day and Boarding</option>
                                        </select>
                                        @error('schoolData.day_or_boarding')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Distance to DEO/MEO/CEO Main Office -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to DEO/MEO/CEO Main Office</label>
                                        <span class="form-note">Distance from this school to the DEO/MEO/CEO's office.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.distance_to_deo_id" class="form-control form-select form-select-sm">
                                            <option value="">Select Distance</option>
                                            @foreach($deoDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.distance_to_deo_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Distance to Nearest Primary School -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to Nearest Primary School</label>
                                        <span class="form-note">Distance to the nearest primary school</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.distance_to_nearest_primary_id" class="form-control form-select form-select-sm">
                                            <option value="">Select Distance</option>
                                            @foreach($schoolDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.distance_to_nearest_primary_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Distance to Nearest Health Facility -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Distance to Nearest Health Facility</label>
                                        <span class="form-note">Distance from this school to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.health_facility_distance_range_id" class="form-control form-select form-select-sm">
                                            <option value="">Select Distance</option>
                                            @foreach($healthDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.health_facility_distance_range_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif
                                              
                        @if($schoolType === 'secondary')
                       
                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                        <span class="form-note">Specify the School's founding body.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.founding_body_id" id="schoolFoundingBodyId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($foundingBodies as $body)
                                                <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.founding_body_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                        <span class="form-note">Specify the year this School was founded.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.year_founded" id="schoolYearFounded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                        @error('schoolData.year_founded')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Registration Status (PRIVATE only) -->
                            @if($schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationStatus">Registration Status</label>
                                        <span class="form-note">Specify if this School is registered or licensed.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.registration_status" id="schoolRegistrationStatus" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="REGISTERED">REGISTERED</option>
                                            <option value="LICENSED">LICENSED</option>
                                            <option value="NOT LICENSED">NOT LICENSED</option>
                                        </select>
                                        @error('schoolData.registration_status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- Registration Number -->
                            @if($schoolData['registration_status'] == 'REGISTERED' && $schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolRegistrationNumber">Registration Number</label>
                                        <span class="form-note">Specify this School's registration number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.registration_number" id="schoolRegistrationNumber" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                        @error('schoolData.registration_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- License Number -->
                            @if($schoolData['registration_status'] == 'LICENSED' && $schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLicenseNumber">License Number</label>
                                        <span class="form-note">Specify this School's license number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.license_number" id="schoolLicenseNumber" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                        @error('schoolData.license_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="licenseNumberDateExpiry">License Number Expiry Date</label>
                                        <span class="form-note">Specify this School's license expiry date</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.licence_certificate_expiry_date" id="licenseNumberDateExpiry" type="text" class="form-control form-control-sm"  placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}">
                                        @error('schoolData.licence_certificate_expiry_date')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- Supply Number (GOVT only) -->
                            @if($schoolData['school_ownership_status_id'] == 1)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5 mt-2">
                                    <div class="form-group">
                                        <label class="form-label" for="supplyNumber">Supply Number</label>
                                        <span class="form-note">Specify School's Supply Number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7 mt-2">
                                    <div class="form-group">
                                        <input wire:model="schoolData.supply_number" id="supplyNumber" type="text" class="form-control form-control-sm" placeholder="Enter Supply Number">
                                        @error('schoolData.supply_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- UNEB Center Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolUnebCenterNumber">UNEB Center Number</label>
                                        <span class="form-note">Specify this School's UNEB Center number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.uneb_center_number" id="schoolUnebCenterNumber" type="text" class="form-control form-control-sm" placeholder="Enter UNEB Center Number">
                                        @error('schoolData.uneb_center_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSexComposition">Sex Composition</label>
                                        <span class="form-note">Specify this School's sex composition.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.sex_composition" id="schoolSexComposition" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="male">MALES ONLY</option>
                                            <option value="female">FEMALES ONLY</option>
                                            <option value="mixed">MIXED</option>
                                        </select>
                                        @error('schoolData.sex_composition')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Day or Boarding -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDayOrBoarding">Day Or Boarding</label>
                                        <span class="form-note">Specify whether this school is day, boarding or both.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.day_or_boarding" id="schoolDayOrBoarding" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="day">DAY ONLY</option>
                                            <option value="boarding">BOARDING ONLY</option>
                                            <option value="both">DAY & BOARDING</option>
                                        </select>
                                        @error('schoolData.day_or_boarding')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- USE/UPOLET (GOVT only) -->
                            @if($schoolData['school_ownership_status_id'] == 1)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolUSEStatus">Is this a USE/UPOLET school?</label>
                                        <span class="form-note">Specify if this school is USE/UPOLET or Non-USE.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.use_upolet" id="schoolUSEStatus" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="USE">USE</option>
                                            <option value="USE/UPOLET">USE/UPOLET</option>
                                            <option value="NON_USE">NON-USE</option>
                                        </select>
                                        @error('schoolData.use_upolet')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- School Level -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLevelStatus">School Level Status: {{$schoolData['school_level']}}</label>
                                        <span class="form-note">Specify if this school has O' level, A' level or both O and A-level.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.school_level" id="schoolLevelStatus" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="O' Level">O' Level</option>
                                            <option value="A' Level">A' Level</option>
                                            <option value="Both O & A' Levels">Both O & A-Level</option>
                                        </select>
                                        @error('schoolData.school_level')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Capital for establishment (PRIVATE only) -->
                            @if($schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                        <span class="form-note">Specify this School's Capital For Establishment.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control form-control-sm" placeholder="E.g 5,000,000">
                                        @error('schoolData.capital_for_establishment')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- Distance to DEO/MEO/CEO Main Office -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDeoDistanceId">Distance to DEO/MEO/CEO Main Office</label>
                                        <span class="form-note">Distance from this school to the DEO/MEO/CEO's office.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.distance_to_deo_id" id="schoolDeoDistanceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($deoDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.distance_to_deo_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Distance to nearest Secondary School -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDistanceId">Distance to nearest Secondary School</label>
                                        <span class="form-note">Distance from this school to the nearest Secondary School.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.distance_to_nearest_secondary_id" id="schoolDistanceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($schoolDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.distance_to_nearest_secondary_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Distance to nearest Health Facility -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                        <span class="form-note">Distance from this school to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.health_facility_distance_range_id" id="schoolHealthDistanceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($healthDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.health_facility_distance_range_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($schoolType === 'international')
                                                
                        <!-- Legal Ownership Status -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                    <span class="form-note">Specify the school's legal ownership status.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.legal_ownership_status_id" id="schoolLegalOwnershipStatusId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($legalOwnershipStatuses as $status)
                                            <option value="{{ $status->id }}">{{ strtoupper($status->name) }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.legal_ownership_status_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Founding Body -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                    <span class="form-note">Specify the school's founding body.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.founding_body_id" id="schoolFoundingBodyId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($foundingBodies as $body)
                                            <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.founding_body_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Funding Source -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolFundingSourceId">Funding Source</label>
                                    <span class="form-note">Specify the school's funding source.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.funding_source_id" id="schoolFundingSourceId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($fundingSources as $source)
                                            <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.funding_source_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Year Founded -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                    <span class="form-note">Specify the year this School was founded.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <input wire:model="schoolData.year_founded" id="schoolYearFounded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                    @error('schoolData.year_founded')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                            <!-- Registration Status -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label">Registration Status </label>
                                    <span class="form-note">Specify the registration status</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model.change="schoolData.registration_status" class="form-control">
                                        <option value="REGISTERED">Registered</option>
                                        <option value="LICENSED">Licensed</option>
                                        <option value="NOT LICENSED">Not Licensed</option>
                                    </select>
                                    @error('schoolData.registration_status')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        @if($schoolData['registration_status'] == 'REGISTERED')
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Registration Number</label>
                                        <span class="form-note">Specify this school's registration number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.registration_number" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                        @error('schoolData.registration_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @elseif($schoolData['registration_status'] == 'LICENSED')
                            <!-- License Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">License Number</label>
                                        <span class="form-note">Specify this school's license number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.license_number" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                        @error('schoolData.license_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- License Number Expiry Date -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">License Number Expiry Date</label>
                                        <span class="form-note">Specify this School's license expiry date</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.licence_certificate_expiry_date" type="text" class="form-control form-control-sm" placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}">
                                        @error('schoolData.licence_certificate_expiry_date')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif                                                           
                                                

                        <!-- Sex Composition -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolSexComposition">Sex Composition</label>
                                    <span class="form-note">Specify this school's sex composition.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.sex_composition" id="schoolSexComposition" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        <option value="male">MALES ONLY</option>
                                        <option value="female">FEMALES ONLY</option>
                                        <option value="mixed">MIXED</option>
                                    </select>
                                    @error('schoolData.sex_composition')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Day or Boarding -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolDayOrBoarding">Day Or Boarding</label>
                                    <span class="form-note">Specify whether this school is day, boarding or both.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.day_or_boarding" id="schoolDayOrBoarding" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        <option value="Day Only">DAY ONLY</option>
                                        <option value="Boarding Only">BOARDING ONLY</option>
                                        <option value="Day and Boarding">DAY & BOARDING</option>
                                    </select>
                                    @error('schoolData.day_or_boarding')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Capital For Establishment (PRIVATE only) -->
                        @if($schoolData['school_ownership_status_id'] == 2)
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                    <span class="form-note">Specify this School's Capital For Establishment.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <input wire:model="schoolData.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control form-control-sm" placeholder="E.g 5,000,000">
                                    @error('schoolData.capital_for_establishment')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        @endif
                        <!-- Distance to DEO/MEO/CEO Main Office -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolDeoDistanceId">Distance to DEO/MEO/CEO Main Office</label>
                                    <span class="form-note">Distance from this school to the DEO/MEO/CEO's office.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.estimated_distance_to_deo_office_id" id="schoolDeoDistanceId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($deoDistances as $distance)
                                            <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.estimated_distance_to_deo_office_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Distance to nearest International School -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolInternationalDistanceId">Distance to nearest International School</label>
                                    <span class="form-note">Distance from this school to the nearest International School.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.estimated_distance_to_international_school_id" id="schoolInternationalDistanceId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($schoolDistances as $distance)
                                            <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.estimated_distance_to_international_school_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <!-- Distance to nearest Health Facility -->
                        <div class="row g-3 align-center">
                            <div class="col-lg-5">
                                <div class="form-group">
                                    <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                    <span class="form-note">Distance from this school to the nearest Health Facility.</span>
                                </div>
                            </div>
                            <div class="col-lg-7">
                                <div class="form-group">
                                    <select wire:model="schoolData.health_facility_distance_range_id" id="schoolHealthDistanceId" class="form-control form-select form-select-sm">
                                        <option value="">--Select--</option>
                                        @foreach($healthDistances as $distance)
                                            <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('schoolData.health_facility_distance_range_id')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        @endif
                           <!-- Section B Modal for Certificate Institutions -->
                           @if($schoolType === 'certificate')
                         
                         <!-- Legal Ownership Status -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                     <span class="form-note">Specify Institution's legal ownership status.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.legal_ownership_status_id" id="schoolLegalOwnershipStatusId" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         @foreach($legalOwnershipStatuses as $status)
                                             <option value="{{ $status->id }}">{{ strtoupper($status->name) }}</option>
                                         @endforeach
                                     </select>
                                     @error('schoolData.legal_ownership_status_id')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Founding Body -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                     <span class="form-note">Specify Institution's founding body.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.founding_body_id" id="schoolFoundingBodyId" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         @foreach($foundingBodies as $body)
                                             <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                         @endforeach
                                     </select>
                                     @error('schoolData.founding_body_id')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Funding Source -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolFundingSourceId">Main Funding Source</label>
                                     <span class="form-note">Specify Institution's main funding source.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.funding_source_id" id="schoolFundingSourceId" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         @foreach($fundingSources as $source)
                                             <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                         @endforeach
                                     </select>
                                     @error('schoolData.funding_source_id')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         
                         <!-- Year Founded -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                     <span class="form-note">Specify the year this Institution was founded.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <input wire:model="schoolData.year_founded" id="schoolYearFounded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                     @error('schoolData.year_founded')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Registration Status -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label">Registration Status  </label>
                                     <span class="form-note">Specify the registration status</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model.change="schoolData.registration_status" class="form-control">
                                         <option value="REGISTERED">Registered</option>
                                         <option value="LICENSED">Licensed</option>
                                         <option value="NOT LICENSED">Not Licensed</option>
                                     </select>
                                     @error('schoolData.registration_status')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>

                         @if($schoolData['registration_status'] == 'REGISTERED')
                             <div class="row g-3 align-center">
                                 <div class="col-lg-5">
                                     <div class="form-group">
                                         <label class="form-label">Registration Number</label>
                                         <span class="form-note">Specify this school's registration number</span>
                                     </div>
                                 </div>
                                 <div class="col-lg-7">
                                     <div class="form-group">
                                         <input wire:model="schoolData.registration_number" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                         @error('schoolData.registration_number')
                                             <span class="text-danger">{{ $message }}</span>
                                         @enderror
                                     </div>
                                 </div>
                             </div>
                         @elseif($schoolData['registration_status'] == 'LICENSED')
                             <!-- License Number -->
                             <div class="row g-3 align-center">
                                 <div class="col-lg-5">
                                     <div class="form-group">
                                         <label class="form-label">License Number</label>
                                         <span class="form-note">Specify this school's license number</span>
                                     </div>
                                 </div>
                                 <div class="col-lg-7">
                                     <div class="form-group">
                                         <input wire:model="schoolData.license_number" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                         @error('schoolData.license_number')
                                             <span class="text-danger">{{ $message }}</span>
                                         @enderror
                                     </div>
                                 </div>
                             </div>

                             <!-- License Number Expiry Date -->
                             <div class="row g-3 align-center">
                                 <div class="col-lg-5">
                                     <div class="form-group">
                                         <label class="form-label">License Number Expiry Date</label>
                                         <span class="form-note">Specify this School's license expiry date</span>
                                     </div>
                                 </div>
                                 <div class="col-lg-7">
                                     <div class="form-group">
                                         <input wire:model="schoolData.licence_certificate_expiry_date" type="text" class="form-control form-control-sm"  placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}">
                                         @error('schoolData.licence_certificate_expiry_date')
                                             <span class="text-danger">{{ $message }}</span>
                                         @enderror
                                     </div>
                                 </div>
                             </div>
                         @endif
                         <!-- Supply Number (Public only) -->
                         @if($schoolData['school_ownership_status_id'] == 1)
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="supplyNumber">Supply Number</label>
                                     <span class="form-note">Specify Institution's Supply Number</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <input wire:model="schoolData.supply_number" id="supplyNumber" type="text" class="form-control form-control-sm" placeholder="Enter Supply Number">
                                     @error('schoolData.supply_number')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         @endif
                         <!-- Center Number -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolCenterNumber">UBTEB Center Number</label>
                                     <span class="form-note">Specify this Institution's UBTEB Center number</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <input wire:model="schoolData.center_number" id="schoolCenterNumber" type="text" class="form-control form-control-sm" placeholder="Enter Center Number">
                                     @error('schoolData.center_number')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Sex Composition -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolSexComposition">Sex Composition {{$schoolData['sex_composition']}}</label>
                                     <span class="form-note">Specify this Institution's sex composition.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.sex_composition" id="schoolSexComposition" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         <option value="Males Only">MALES ONLY</option>
                                         <option value="Females Only">FEMALES ONLY</option>
                                         <option value="Mixed">MIXED</option>
                                     </select>
                                     @error('schoolData.sex_composition')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Day or Boarding -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolDayOrBoarding">Residential Or Non-Residential</label>
                                     <span class="form-note">Specify whether this Institution is Residential, Non-Residential or both.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.day_or_boarding" id="schoolDayOrBoarding" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         <option value="RESIDENTIAL ONLY">RESIDENTIAL ONLY</option>
                                         <option value="NON-RESIDENTIAL ONLY">NON-RESIDENTIAL ONLY</option>
                                         <option value="RESIDENTIAL & NON-RESIDENTIAL">RESIDENTIAL & NON-RESIDENTIAL</option>
                                     </select>
                                     @error('schoolData.day_or_boarding')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         <!-- Capital For Establishment (Private only) -->
                         @if($schoolData['school_ownership_status_id'] == 2)
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                     <span class="form-note">Specify this Institution's Capital For Establishment.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <input wire:model="schoolData.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control form-control-sm" placeholder="E.g 5,000,000">
                                     @error('schoolData.capital_for_establishment')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                         @endif
                         <!-- Health Distance -->
                         <div class="row g-3 align-center">
                             <div class="col-lg-5">
                                 <div class="form-group">
                                     <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                     <span class="form-note">Distance from this Institution to the nearest Health Facility.</span>
                                 </div>
                             </div>
                             <div class="col-lg-7">
                                 <div class="form-group">
                                     <select wire:model="schoolData.health_facility_distance_range_id" id="schoolHealthDistanceId" class="form-control form-select form-select-sm">
                                         <option value="">--Select--</option>
                                         @foreach($healthDistances as $distance)
                                             <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                         @endforeach
                                     </select>
                                     @error('schoolData.health_facility_distance_range_id')
                                         <span class="text-danger">{{ $message }}</span>
                                     @enderror
                                 </div>
                             </div>
                         </div>
                     @endif
                        
                        <!-- Section B Modal for Diploma Institutions -->
                        @if($schoolType === 'diploma')
                         
                            <!-- Legal Ownership Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                        <span class="form-note">Specify Institution's legal ownership status.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.legal_ownership_status_id" id="schoolLegalOwnershipStatusId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($legalOwnershipStatuses as $status)
                                                <option value="{{ $status->id }}">{{ strtoupper($status->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.legal_ownership_status_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                        <span class="form-note">Specify Institution's founding body.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.founding_body_id" id="schoolFoundingBodyId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($foundingBodies as $body)
                                                <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.founding_body_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Funding Source -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFundingSourceId">Main Funding Source</label>
                                        <span class="form-note">Specify Institution's main funding source.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.funding_source_id" id="schoolFundingSourceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($fundingSources as $source)
                                                <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.funding_source_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                        <span class="form-note">Specify the year this Institution was founded.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.year_founded" id="schoolYearFounded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                        @error('schoolData.year_founded')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Registration Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Registration Status {{$schoolData['registration_status']}} </label>
                                        <span class="form-note">Specify the registration status</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model.change="schoolData.registration_status" class="form-control">
                                            <option value="REGISTERED">Registered</option>
                                            <option value="LICENSED">Licensed</option>
                                            <option value="NOT LICENSED">Not Licensed</option>
                                        </select>
                                        @error('schoolData.registration_status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            @if($schoolData['registration_status'] == 'REGISTERED')
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">Registration Number</label>
                                            <span class="form-note">Specify this school's registration number</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.registration_number" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                            @error('schoolData.registration_number')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            @elseif($schoolData['registration_status'] == 'LICENSED')
                                <!-- License Number -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">License Number</label>
                                            <span class="form-note">Specify this school's license number</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.license_number" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                            @error('schoolData.license_number')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- License Number Expiry Date -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">License Number Expiry Date</label>
                                            <span class="form-note">Specify this School's license expiry date</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.licence_certificate_expiry_date" type="text" class="form-control form-control-sm"  placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}">
                                            @error('schoolData.licence_certificate_expiry_date')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <!-- Supply Number (Public only) -->
                            @if($schoolData['school_ownership_status_id'] == 1)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="supplyNumber">Supply Number</label>
                                        <span class="form-note">Specify Institution's Supply Number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.supply_number" id="supplyNumber" type="text" class="form-control form-control-sm" placeholder="Enter Supply Number">
                                        @error('schoolData.supply_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- Center Number -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCenterNumber">UBTEB Center Number</label>
                                        <span class="form-note">Specify this Institution's UBTEB Center number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.center_number" id="schoolCenterNumber" type="text" class="form-control form-control-sm" placeholder="Enter Center Number">
                                        @error('schoolData.center_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Sex Composition -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolSexComposition">Sex Composition {{$schoolData['sex_composition']}}</label>
                                        <span class="form-note">Specify this Institution's sex composition.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.sex_composition" id="schoolSexComposition" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="Males Only">MALES ONLY</option>
                                            <option value="Females Only">FEMALES ONLY</option>
                                            <option value="Mixed">MIXED</option>
                                        </select>
                                        @error('schoolData.sex_composition')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Day or Boarding -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolDayOrBoarding">Residential Or Non-Residential</label>
                                        <span class="form-note">Specify whether this Institution is Residential, Non-Residential or both.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.day_or_boarding" id="schoolDayOrBoarding" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            <option value="RESIDENTIAL ONLY">RESIDENTIAL ONLY</option>
                                            <option value="NON-RESIDENTIAL ONLY">NON-RESIDENTIAL ONLY</option>
                                            <option value="RESIDENTIAL & NON-RESIDENTIAL">RESIDENTIAL & NON-RESIDENTIAL</option>
                                        </select>
                                        @error('schoolData.day_or_boarding')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Capital For Establishment (Private only) -->
                            @if($schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                        <span class="form-note">Specify this Institution's Capital For Establishment.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control form-control-sm" placeholder="E.g 5,000,000">
                                        @error('schoolData.capital_for_establishment')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                            <!-- Health Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                        <span class="form-note">Distance from this Institution to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.health_facility_distance_range_id" id="schoolHealthDistanceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($healthDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.health_facility_distance_range_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- Section B Modal for Degree Institutions -->
                        @if($schoolType === 'degree')
                         
                            <!-- Legal Ownership Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolLegalOwnershipStatusId">Legal Ownership Status</label>
                                        <span class="form-note">Specify Institution's legal ownership status.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.legal_ownership_status_id" id="schoolLegalOwnershipStatusId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($legalOwnershipStatuses as $status)
                                                <option value="{{ $status->id }}">{{ strtoupper($status->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.legal_ownership_status_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Founding Body -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFoundingBodyId">Founding Body</label>
                                        <span class="form-note">Specify Institution's founding body.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.founding_body_id" id="schoolFoundingBodyId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($foundingBodies as $body)
                                                <option value="{{ $body->id }}">{{ strtoupper($body->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.founding_body_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Funding Source -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolFundingSourceId">Main Funding Source</label>
                                        <span class="form-note">Specify Institution's main funding source.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.funding_source_id" id="schoolFundingSourceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($fundingSources as $source)
                                                <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.funding_source_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Year Founded -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolYearFounded">Year Founded</label>
                                        <span class="form-note">Specify the year this Institution was founded.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.year_founded" id="schoolYearFounded" type="number" class="form-control form-control-sm" placeholder="Enter Year Founded">
                                        @error('schoolData.year_founded')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <!-- Registration Status -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label">Registration Status {{$schoolData['registration_status']}} </label>
                                        <span class="form-note">Specify the registration status</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model.change="schoolData.registration_status" class="form-control">
                                            <option value="REGISTERED">Registered</option>
                                            <option value="LICENSED">Licensed</option>
                                            <option value="NOT LICENSED">Not Licensed</option>
                                        </select>
                                        @error('schoolData.registration_status')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            @if($schoolData['registration_status'] == 'REGISTERED')
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">Registration Number</label>
                                            <span class="form-note">Specify this school's registration number</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.registration_number" type="text" class="form-control form-control-sm" placeholder="Enter Registration Number">
                                            @error('schoolData.registration_number')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            @elseif($schoolData['registration_status'] == 'LICENSED')
                                <!-- License Number -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">License Number</label>
                                            <span class="form-note">Specify this school's license number</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.license_number" type="text" class="form-control form-control-sm" placeholder="Enter License Number">
                                            @error('schoolData.license_number')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- License Number Expiry Date -->
                                <div class="row g-3 align-center">
                                    <div class="col-lg-5">
                                        <div class="form-group">
                                            <label class="form-label">License Number Expiry Date</label>
                                            <span class="form-note">Specify this School's license expiry date</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="form-group">
                                            <input wire:model="schoolData.licence_certificate_expiry_date" type="text" class="form-control form-control-sm"  placeholder="Enter expiry date, e.g. {{ now()->format('j F, Y') }}">
                                            @error('schoolData.licence_certificate_expiry_date')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <!-- Supply Number (Public only) -->
                            @if($schoolData['school_ownership_status_id'] == 1)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="supplyNumber">Supply Number</label>
                                        <span class="form-note">Specify Institution's Supply Number</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.supply_number" id="supplyNumber" type="text" class="form-control form-control-sm" placeholder="Enter Supply Number">
                                        @error('schoolData.supply_number')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                           
                          
                            <!-- Capital For Establishment (Private only) -->
                            @if($schoolData['school_ownership_status_id'] == 2)
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolCapitalEstablishment">Capital For Establishment</label>
                                        <span class="form-note">Specify this Institution's Capital For Establishment.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <input wire:model="schoolData.capital_for_establishment" id="schoolCapitalEstablishment" type="text" class="form-control form-control-sm" placeholder="E.g 5,000,000">
                                        @error('schoolData.capital_for_establishment')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            @endif
                                                        <!-- Has Health Facility -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="form-label">Do you have/own a Health Facility within?</label>
                                        <span class="form-note">Specify whether your institution has a Health Facility within.</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="school_form.has_health_facility_yn" value="yes" id="institutionHasHealthFacility">
                                            <label class="custom-control-label text-uppercase" for="institutionHasHealthFacility">YES</label>
                                        </div>
                                        <div class="custom-control custom-control-inline custom-radio">
                                            <input type="radio" class="custom-control-input" v-model="school_form.has_health_facility_yn" value="no" id="institutionWithoutHealthFacility">
                                            <label class="custom-control-label text-uppercase" for="institutionWithoutHealthFacility">NO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /Has Health Facility -->
                            <!-- Health Distance -->
                            <div class="row g-3 align-center">
                                <div class="col-lg-5">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolHealthDistanceId">Distance to nearest Health Facility</label>
                                        <span class="form-note">Distance from this Institution to the nearest Health Facility.</span>
                                    </div>
                                </div>
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <select wire:model="schoolData.health_facility_distance_range_id" id="schoolHealthDistanceId" class="form-control form-select form-select-sm">
                                            <option value="">--Select--</option>
                                            @foreach($healthDistances as $distance)
                                                <option value="{{ $distance['id'] }}">{{ $distance['name'] }}</option>
                                            @endforeach
                                        </select>
                                        @error('schoolData.health_facility_distance_range_id')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                        <div class="modal-footer d-flex justify-content-center mt-4">
                                    <button wire:loading.attr="disabled" type="submit" data-dismiss="modal" class="btn btn-light ml-2">
                                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                                    </button>
                                    <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                                        <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span wire:loading class="align-self-center">Updating...</span>
                                        <span wire:loading.remove class="align-self-center">Update</span>
                                        <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                    </button>
                        </div>
                    </form>
                </div>

        </div>
  <!-- Section B Modal end -->


</div>
