<div>
    <!-- .card-preview -->
    <div class="card card-preview">
        <ul class="nav nav-tabs">
            <li class="nav-item">
                <a class="nav-link {{ $active_tab === 'income' ? 'active' : '' }}" href="#income" wire:click.prevent="setActiveTab('income')">Income</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ $active_tab === 'budgets' ? 'active' : '' }}" href="#budgets" wire:click.prevent="setActiveTab('budgets')">Budgets</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ $active_tab === 'expenses' ? 'active' : '' }}" href="#expenses" wire:click.prevent="setActiveTab('expenses')">Expenses</a>
            </li>
        </ul>
        <div class="tab-content">
            <div class="tab-pane {{ $active_tab === 'income' ? 'active' : '' }}" id="income">
                {{-- income section --}}
                <div>
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h5 class="nk-block-title page-title">Income</h5>
                                <div class="nk-block-des text-soft">
                                    <p>You have raised a total of UGX {{ $this->formatMoney($total_incomes) }}.</p>
                                </div>
                            </div><!-- .nk-block-head-content -->
                            <div class="nk-block-head-content">
                                <button data-toggle="modal" data-target="#incomeModal" data-backdrop="static" type="button" class="btn bg-dark-teal">
                                    <em class="icon ni ni-plus-circle-fill text-white mr-1"></em>Add To Income
                                </button>
                            </div><!-- .nk-block-head-content -->
                        </div><!-- .nk-block-between -->
                    </div><!-- .nk-block-head -->
                    <livewire:emis-returns.notification-banner />
                    <div class="nk-block">
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-inner position-relative card-tools-toggle">
                                    <div class="card-title-group">
                                        <div class="card-tools">
                                            <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                                <div style="width: 350px !important" class="form-wrap">
                                                    <select wire:model.live="income_filter.income_source_id" class="form-select-sm w-100">
                                                        <option value="">All Income Sources</option>
                                                        @foreach($income_sources as $source)
                                                            <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="form-wrap">
                                                    <div class="btn-group" aria-label="Filter Actions">
                                                        @if($filtering_income)
                                                            <button wire:click="resetIncomeFilter" type="button" class="btn bg-secondary text-white">
                                                                <em class="icon ni ni-cross"></em>
                                                            </button>
                                                        @endif
                                                        <button wire:click="filterIncomes" wire:loading.attr="disabled" type="button" class="btn bg-dark-teal">
                                                            <em class="icon ni ni-filter"></em> Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!-- .card-tools -->
                                    </div><!-- .card-inner -->
                                </div>
                                <div class="card-inner p-0">
                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col nk-tb-col-check">
                                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                                    <input wire:model.live="select_all_incomes" wire:click="toggleAllIncomes" type="checkbox" class="custom-control-input" id="select_all_incomes">
                                                    <label class="custom-control-label" for="select_all_incomes"></label>
                                                </div>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text ucap text-white">INCOME SOURCE</span></div>
                                            <div class="nk-tb-col"><span class="sub-text ucap text-white">PURPOSE</span></div>
                                            <div class="nk-tb-col text-right"><span class="sub-text ucap text-white">AMOUNT</span></div>
                                            <div class="nk-tb-col text-center"><span class="sub-text ucap text-white">ACTIONS</span></div>
                                        </div><!-- .nk-tb-item -->

                                        @forelse($incomes as $income)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check">
                                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                                        <input wire:model.live="selected_incomes" wire:click="toggleOneIncome" value="{{ $income->id }}" type="checkbox" class="custom-control-input" id="select_income_{{ $income->id }}">
                                                        <label class="custom-control-label" for="select_income_{{ $income->id }}"></label>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="text-dark">{{ strtoupper($income->income_source->name) }}</span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="text-dark">{{ $income->income_purpose ? strtoupper($income->income_purpose->name) : 'Not set' }}</span>
                                                </div>
                                                <div class="nk-tb-col text-right">
                                                    <span class="text-dark">UGX {{ $this->formatMoney($income->amount) }}</span>
                                                </div>
                                                <div class="nk-tb-col text-center">
                                                    <a wire:click="editIncome({{ $income->id }})" data-toggle="modal" data-toggle="tooltip" data-placement="top" title="Edit Income" data-target="#incomeModal" class="cursor lead mr-1 text-dark-teal">
                                                        <em class="icon ni ni-edit-fill"></em>
                                                    </a>
                                                </div>
                                            </div><!-- .nk-tb-item -->
                                            @empty
                                        @endforelse

                                        @if($incomes && $incomes->count() > 0)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check border-top border-bottom border-dark"></div>
                                                <div class="nk-tb-col border-top border-bottom border-dark">
                                                    <span class="text-dark font-weight-bold ucap">Total</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark"></div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-right">
                                                    <span class="text-dark font-weight-bold">UGX {{ $this->formatMoney($total_incomes) }}</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-center"></div>
                                            </div><!-- .nk-tb-item -->
                                        @endif
                                    </div><!-- .nk-tb-list -->
                                </div><!-- .card-inner -->

                                @if($incomes && $incomes->count() > 0)
                                    <div class="card-inner d-flex flex-row">
                                        {{ $incomes->links() }}
                                        <div class="d-flex ml-4">
                                            <span class="align-self-center">
                                                Showing <span class="text-primary">{{ $incomes->firstItem() }}</span> to <span class="text-primary">{{ $incomes->lastItem() }}</span> of <span class="text-primary">{{ $incomes->total() }}</span>
                                            </span>
                                        </div>
                                    </div><!-- .card-inner -->
                                @endif
                            </div><!-- .card-inner-group -->
                        </div><!-- .card -->
                    </div><!-- .nk-block -->

                    @if(!$incomes || $incomes->count() === 0)
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There are no income sources to display at the moment.
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Income Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="incomeModal" wire:ignore.self>
                        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a wire:click="resetIncome" class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <form wire:submit="{{ $edit_income ? 'updateIncome' : 'createIncome' }}">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ $edit_income ? 'Edit' : 'Add' }} Income Entry</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="incomeSourceId">Income Source</label>
                                                    <div class="form-control-wrap">
                                                        <select @if($edit_income) disabled @endif id="incomeSourceId" class="form-control" wire:model.live="income_source_id" required>
                                                            <option value="">--Select--</option>
                                                            @foreach($income_sources as $source)
                                                                <option value="{{ $source->id }}">{{ strtoupper($source->name) }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="incomePurposeId">Purpose</label>
                                                    <select wire:model="income.income_purpose_id" class="form-select form-control" id="income_purpose">
                                                        <option value="">--Select--</option>
                                                        @if($income_source_id)
                                                            @foreach($purposes as $purpose)
                                                                <option value="{{ $purpose->id }}">{{ strtoupper($purpose->name) }}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                    @error('income.income_purpose_id') <span class="text-danger">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="income_amount">Amount</label>
                                                    <div class="form-control-wrap">
                                                        <div class="form-text-hint bg-primary-dim">
                                                            <span class="overline-title">UGX</span>
                                                        </div>
                                                        <input wire:model="income.amount" type="text" class="form-control bg-primary-dim" id="income_amount" placeholder="e.g 200000" autocomplete="off" required />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer d-flex justify-content-center">
                                        <button wire:click="resetIncome" wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                                        </button>
                                        <button wire:loading.attr="disabled" type="submit" class="btn bg-dark-teal d-flex">
                                            <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span wire:loading class="align-self-center">Saving...</span>
                                            <span wire:loading class="sr-only">Saving...</span>
                                            <span wire:loading.remove class="align-self-center">Save</span>
                                            <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- income section --}}
            </div>

            <div class="tab-pane{{ $active_tab === 'budgets' ? 'active' : '' }}" id="budgets">
                {{-- budget section --}}
                <div>
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h3 class="nk-block-title page-title">Budget</h3>
                                <div class="nk-block-des text-soft">
                                    <p>You have budgeted for a total of UGX {{ $this->formatMoney($total_budgets) }}.</p>
                                </div>
                            </div><!-- .nk-block-head-content -->
                            <div class="nk-block-head-content">
                                <button data-toggle="modal" data-target="#budgetModal" data-backdrop="static" type="button" class="btn bg-dark-teal">
                                    <em class="icon ni ni-plus-circle-fill text-white mr-1"></em>Add To Budget
                                </button>
                            </div><!-- .nk-block-head-content -->
                        </div><!-- .nk-block-between -->
                    </div><!-- .nk-block-head -->

                    <livewire:emis-returns.notification-banner />

                    <div class="nk-block">
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-inner position-relative card-tools-toggle">
                                    <div class="card-title-group">
                                        <div class="card-tools">
                                            <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                                <div style="width: 350px !important" class="form-wrap">
                                                    <select wire:model.live="budget_filter.budget_item_id" class="form-select-sm w-100">
                                                        <option value="">All Budget Items</option>
                                                        @foreach($budget_items as $item)
                                                            <option value="{{ $item->id }}">{{ strtoupper($item->name) }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="form-wrap">
                                                    <div class="btn-group" aria-label="Filter Actions">
                                                        @if($filtering_budget)
                                                            <button wire:click="resetBudgetFilter" type="button" class="btn bg-secondary text-white">
                                                                <em class="icon ni ni-cross"></em>
                                                            </button>
                                                        @endif
                                                        <button wire:click="filterBudgets" wire:loading.attr="disabled" type="button" class="btn bg-dark-teal">
                                                            <em class="icon ni ni-filter"></em> Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!-- .card-tools -->
                                    </div><!-- .card-inner -->
                                </div>
                                <div class="card-inner p-0">
                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col nk-tb-col-check">
                                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                                    <input wire:model.live="select_all_budgets" wire:click="toggleAllBudgets" type="checkbox" class="custom-control-input" id="select_all_budgets">
                                                    <label class="custom-control-label" for="select_all_budgets"></label>
                                                </div>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text ucap text-white">ITEM</span></div>
                                            <div class="nk-tb-col text-right"><span class="sub-text ucap text-white">AMOUNT</span></div>
                                            <div class="nk-tb-col text-center"><span class="sub-text ucap text-white">ACTIONS</span></div>
                                        </div><!-- .nk-tb-item -->

                                        @forelse($budgets as $budget)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check">
                                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                                        <input wire:model.live="selected_budgets" wire:click="toggleOneBudget" value="{{ $budget->id }}" type="checkbox" class="custom-control-input" id="select_budget_{{ $budget->id }}">
                                                        <label class="custom-control-label" for="select_budget_{{ $budget->id }}"></label>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="text-dark">{{ strtoupper($budget->budget_item->name) }}</span>
                                                </div>
                                                <div class="nk-tb-col text-right">
                                                    <span class="text-dark">UGX {{ $this->formatMoney($budget->amount) }}</span>
                                                </div>
                                                <div class="nk-tb-col text-center">
                                                    <span @click="$dispatch('edit-budget', {id: {{ $budget->id }}})" data-toggle="modal" data-target="#budgetModal" data-backdrop="static" class="cursor lead mr-1 text-dark-teal">
                                                        <em class="icon ni ni-edit-fill"></em>
                                                    </span>
                                                </div>
                                            </div><!-- .nk-tb-item -->
                                            @empty
                                        @endforelse

                                        @if($budgets && $budgets->count() > 0)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check border-top border-bottom border-dark"></div>
                                                <div class="nk-tb-col border-top border-bottom border-dark">
                                                    <span class="text-dark font-weight-bold ucap">Total</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-right">
                                                    <span class="text-dark font-weight-bold">UGX {{ $this->formatMoney($total_budgets) }}</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-center"></div>
                                            </div><!-- .nk-tb-item -->
                                        @endif
                                    </div><!-- .nk-tb-list -->
                                </div><!-- .card-inner -->

                                @if($budgets && $budgets->count() > 0)
                                    <div class="card-inner d-flex flex-row">
                                        {{ $budgets->links() }}
                                        <div class="d-flex ml-4">
                                            <span class="align-self-center">
                                                Showing <span class="text-primary">{{ $budgets->firstItem() }}</span> to <span class="text-primary">{{ $budgets->lastItem() }}</span> of <span class="text-primary">{{ $budgets->total() }}</span>
                                            </span>
                                        </div>
                                    </div><!-- .card-inner -->
                                @endif
                            </div><!-- .card-inner-group -->
                        </div><!-- .card -->
                    </div><!-- .nk-block -->

                    @if(!$budgets || $budgets->count() === 0)
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There are no budgets to display at the moment.
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Budget Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="budgetModal" wire:ignore.self>
                        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a wire:click="resetBudget" class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <form wire:submit="{{ $edit_budget ? 'updateBudget' : 'createBudget' }}">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ $edit_budget ? 'Edit' : 'Add'}} Budget Entry</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="budgetItemId">Budget Item</label>
                                                    <div class="form-control-wrap">
                                                        <select @if($edit_budget) disabled @endif id="budgetItemId" class="form-control" wire:model="budget.budget_item_id" required>
                                                            <option value="">--Select--</option>
                                                            @foreach($budget_items as $item)
                                                                <option value="{{ $item->id }}">{{ strtoupper($item->name) }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="budget_amount">Amount</label>
                                                    <div class="form-control-wrap">
                                                        <div class="form-text-hint bg-primary-dim">
                                                            <span class="overline-title">UGX</span>
                                                        </div>
                                                        <input wire:model="budget.amount" type="text" class="form-control bg-primary-dim" id="budget_amount" placeholder="eg. 200000" autocomplete="off" required>
                                                    </div>
                                                    @error('budget.amount') <span class="text-danger">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer d-flex justify-content-center">
                                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                                        </button>
                                        <button wire:loading.attr="disabled" type="submit" class="btn bg-dark-teal d-flex">
                                            <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span wire:loading class="align-self-center">Saving...</span>
                                            <span wire:loading class="sr-only">Saving...</span>
                                            <span wire:loading.remove class="align-self-center">Save</span>
                                            <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane{{ $active_tab === 'expenses' ? 'active' : '' }}" id="expenses">
                {{-- expenses section --}}
                <div>
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h3 class="nk-block-title page-title">Expenses</h3>
                                <div class="nk-block-des text-soft">
                                    <p>You have spent a total of UGX {{ $this->formatMoney($total_expenses) }}.</p>
                                </div>
                            </div><!-- .nk-block-head-content -->
                            <div class="nk-block-head-content">
                                <button data-toggle="modal" data-target="#expenseModal" data-backdrop="static" type="button" class="btn bg-dark-teal">
                                    <em class="icon ni ni-plus-circle-fill text-white mr-1"></em>Add Expense
                                </button>
                            </div><!-- .nk-block-head-content -->
                        </div><!-- .nk-block-between -->
                    </div><!-- .nk-block-head -->

                    <livewire:emis-returns.notification-banner />

                    <div class="nk-block">
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-inner position-relative card-tools-toggle">
                                    <div class="card-title-group">
                                        <div class="card-tools">
                                            <div class="form-inline d-flex flex-lg-row justify-content-between gx-3">
                                                <div style="width: 350px !important" class="form-wrap">
                                                    <select wire:model.live="expense_filter.expense_item_id" class="form-select-sm w-100">
                                                        <option value="">All Expense Items</option>
                                                        @foreach($generic_expense_items as $item)
                                                            <option value="{{ $item->id }}">{{ strtoupper($item->name) }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="form-wrap">
                                                    <div class="btn-group" aria-label="Filter Actions">
                                                        @if($filtering_expense)
                                                            <button wire:click="resetExpenseFilter" type="button" class="btn bg-secondary text-white">
                                                                <em class="icon ni ni-cross"></em>
                                                            </button>
                                                        @endif
                                                        <button wire:click="filterExpenses" wire:loading.attr="disabled" type="button" class="btn bg-dark-teal">
                                                            <em class="icon ni ni-filter"></em> Apply
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!-- .card-tools -->
                                    </div><!-- .card-inner -->
                                </div>
                                <div class="card-inner p-0">
                                    <div class="nk-tb-list nk-tb-ulist is-compact">
                                        <div class="nk-tb-item nk-tb-head bg-secondary">
                                            <div class="nk-tb-col nk-tb-col-check">
                                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                                    <input wire:model.live="select_all_expenses" wire:click="toggleAllExpenses" type="checkbox" class="custom-control-input" id="select_all_expenses">
                                                    <label class="custom-control-label" for="select_all_expenses"></label>
                                                </div>
                                            </div>
                                            <div class="nk-tb-col"><span class="sub-text ucap text-white">EXPENSE ITEM</span></div>
                                            <div class="nk-tb-col text-right"><span class="sub-text ucap text-white">AMOUNT</span></div>
                                            <div class="nk-tb-col text-center"><span class="sub-text ucap text-white">ACTIONS</span></div>
                                        </div><!-- .nk-tb-item -->

                                        @forelse($expenses as $expense)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check">
                                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                                        <input wire:model.live="selected_expenses" wire:click="toggleOneExpense" value="{{ $expense->id }}" type="checkbox" class="custom-control-input" id="select_expense_{{ $expense->id }}">
                                                        <label class="custom-control-label" for="select_expense_{{ $expense->id }}"></label>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="text-dark">{{ strtoupper($expense->expenseable->name) }}</span>
                                                </div>
                                                <div class="nk-tb-col text-right">
                                                    <span class="text-dark">UGX {{ $this->formatMoney($expense->amount) }}</span>
                                                </div>
                                                <div class="nk-tb-col text-center">
                                                    <span @click="$dispatch('edit-expense', {id: {{ $expense->id }}})" data-toggle="modal" data-target="#expenseModal" data-backdrop="static" class="cursor lead mr-1 text-dark-teal">
                                                        <em class="icon ni ni-edit-fill"></em>
                                                    </span>
                                                </div>
                                            </div><!-- .nk-tb-item -->
                                            @empty
                                        @endforelse

                                        @if($expenses && $expenses->count() > 0)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col nk-tb-col-check border-top border-bottom border-dark"></div>
                                                <div class="nk-tb-col border-top border-bottom border-dark">
                                                    <span class="text-dark font-weight-bold ucap">Total</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-right">
                                                    <span class="text-dark font-weight-bold">UGX {{ $this->formatMoney($total_expenses) }}</span>
                                                </div>
                                                <div class="nk-tb-col border-top border-bottom border-dark text-center"></div>
                                            </div><!-- .nk-tb-item -->
                                        @endif
                                    </div><!-- .nk-tb-list -->
                                </div><!-- .card-inner -->

                                @if($expenses && $expenses->count() > 0)
                                    <div class="card-inner d-flex flex-row">
                                        {{ $expenses->links() }}
                                        <div class="d-flex ml-4">
                                            <span class="align-self-center">
                                                Showing <span class="text-primary">{{ $expenses->firstItem() }}</span> to <span class="text-primary">{{ $expenses->lastItem() }}</span> of <span class="text-primary">{{ $expenses->total() }}</span>
                                            </span>
                                        </div>
                                    </div><!-- .card-inner -->
                                @endif
                            </div><!-- .card-inner-group -->
                        </div><!-- .card -->
                    </div><!-- .nk-block -->

                    @if(!$expenses || $expenses->count() === 0)
                        <div class="card card-stretch">
                            <div class="card-inner-group">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There are no expenses to display at the moment.
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Expense Modal -->
                    <div class="modal fade zoom" tabindex="-1" id="expenseModal" wire:ignore.self>
                        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <a wire:click.prevent="resetExpense" class="cursor close" data-dismiss="modal" aria-label="Close">
                                    <em class="icon ni ni-cross"></em>
                                </a>
                                <form wire:submit.prevent="{{ $edit_expense ? 'updateExpense' : 'createExpense' }}">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ $edit_expense ? 'Edit' : 'Add' }} Expense</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="expense_item">Expense Item</label>
                                                    <select wire:model="expense.expense_item_id" class="form-control form-select" id="expense_item" {{ $edit_expense ? 'disabled' : '' }}>
                                                        <option value="">--Select--</option>
                                                        @foreach($generic_expense_items as $item)
                                                            <option value="{{ $item->id }}">{{ strtoupper($item->name) }}</option>
                                                        @endforeach
                                                    </select>
                                                    @error('expense.expense_item_id') <span class="text-danger">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label" for="expense_amount">Amount</label>
                                                    <div class="form-control-wrap">
                                                        <div class="form-text-hint bg-primary-dim">
                                                            <span class="overline-title">UGX</span>
                                                        </div>
                                                        <input wire:model="expense.amount" type="text" class="form-control bg-primary-dim" id="expense_amount" placeholder="eg. 200000" autocomplete="off" required>
                                                    </div>
                                                    @error('expense.amount') <span class="text-danger">{{ $message }}</span> @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer d-flex justify-content-center">
                                        <button wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                                        </button>
                                        <button wire:loading.attr="disabled" type="submit" class="btn bg-dark-teal d-flex">
                                            <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span wire:loading class="align-self-center">Saving...</span>
                                            <span wire:loading class="sr-only">Saving...</span>
                                            <span wire:loading.remove class="align-self-center">Save</span>
                                            <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- .card-preview -->
</div>
