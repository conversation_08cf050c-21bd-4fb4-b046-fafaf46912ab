<div>
    <livewire:emis-returns.notification-banner key="curriculum" :eventName="'notifyCirriculum'"/>


    <button class="btn bg-dark-teal btn-sm mt-1" data-toggle="modal" wire:click="openCurriculumModal">
        <span class="text-uppercase">UPDATE CURRICULUM</span>
    </button>

    <div class="table-responsive mt-2 mb-4">
        <table class="table table-bordered table-sm">
            <tr class="bg-secondary">
                <td class="text-uppercase align-middle text-white border-1 border-white">curriculums</td>
                <td class="text-uppercase align-middle text-white border-1 border-white">Sections</td>
                <td class="text-uppercase align-middle text-white border-1 border-white">Action</td>
            </tr>
            @forelse($assignedCurriculums as $curriculum)
                <tr>
                    <td class="text-uppercase align-middle text-dark border-secondary border-1">
                        {{ $curriculum->name }}
                    </td>
                    <td class="text-uppercase align-middle text-dark border-secondary border-1">
                        <ul class="list list-sm">
                            @foreach($curriculum->sections as $section)
                                <li>{{ $section->name }}</li>
                            @endforeach
                        </ul>
                    </td>
                    <td class="text-uppercase align-middle text-dark border-secondary border-1">
                        <button class="btn btn-sm bg-dark-teal"
                                data-toggle="modal"
                                wire:click="openSectionModal({{ $curriculum->id }}, '{{ $curriculum->name }}')">
                            <span class="text-uppercase text-white">UPDATE SECTIONS</span>
                        </button>
                    </td>
                </tr>
            @empty
                <tr> 
                    <td colspan="3" class="text-center text-muted">
                        <div class="alert alert-secondary alert-icon">
                            <em class="icon ni ni-alert-circle"></em> There are no curriculums to display at the moment.
                        </div>
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    {{-- Update Curriculum Modal --}}
    @if($showCurriculumModal)
        <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1040;"></div>
        <div class="modal d-block" tabindex="-1" style="z-index:1050;">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <a class="cursor close" wire:click="closeCurriculumModal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Update Curriculum</h5>
                    </div>
                    <div class="modal-body">
                        <form wire:submit.prevent="saveCurriculums">
                            <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Curriculum for your school</span>
                            @if(count($selectedCurriculums))
                                <div @if(count($selectedCurriculums) > 5) class="h-150px overflow-auto" @endif>
                                    <ul class="list list-sm">
                                        @foreach($allCurriculums->whereIn('id', $selectedCurriculums) as $curriculum)
                                            <li class="py-0 fs-13px">{{ $curriculum->name }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                                <hr class="border-dark-teal border-1 my-4">
                            @endif
                            <div>
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available curriculums</span>
                                <div @if(count($allCurriculums) > 9) class="h-200px overflow-auto my-1" @else class="my-1" @endif>
                                    @foreach($allCurriculums as $curriculum)
                                        <div class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input wire:model.live="selectedCurriculums"
                                                   value="{{ $curriculum->id }}"
                                                   id="curriculum-{{ $curriculum->id }}"
                                                   type="checkbox"
                                                   class="custom-control-input">
                                            <label class="custom-control-label" for="curriculum-{{ $curriculum->id }}">
                                                <span class="text-dark fs-13px">{{ $curriculum->name }}</span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-light" wire:click="closeCurriculumModal">Close</button>
                                <button type="submit" class="btn bg-dark-teal text-white">
                                    <span wire:loading.remove wire:target="saveCurriculums">
                                        <em class="icon ni ni-save"></em> Save Changes
                                    </span>
                                    <span wire:loading wire:target="saveCurriculums">
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Update Section Modal --}}
    @if($showSectionModal)
        <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1040;"></div>
        <div class="modal d-block" tabindex="-1" style="z-index:1050;">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <a class="cursor close" wire:click="closeSectionModal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Update Sections for <span class="text-dark-teal">{{ $currentCurriculumName }}</span></h5>
                    </div>
                    <div class="modal-body">
                        <form wire:submit.prevent="saveSections">
                            <div>
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Sections under this curriculum</span>
                                @if(count($selectedSections))
                                    <div @if(count($selectedSections) > 5) class="h-150px overflow-auto" @endif>
                                        <ul class="list list-sm">
                                            @foreach($allSections->whereIn('id', $selectedSections) as $section)
                                                <li class="py-0 fs-13px">{{ $section->name }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    <hr class="border-dark-teal border-1 my-4">
                                @endif
                            </div>
                            <div>
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available sections</span>
                                <div @if(count($allSections) > 9) class="h-200px overflow-auto my-1" @else class="my-1" @endif>
                                    @foreach($allSections as $section)
                                        <div class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input wire:model.live="selectedSections"
                                                   value="{{ $section->id }}"
                                                   id="section-{{ $section->id }}"
                                                   type="checkbox"
                                                   class="custom-control-input">
                                            <label class="custom-control-label" for="section-{{ $section->id }}">
                                                <span class="text-dark fs-13px">{{ $section->name }}</span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-light" wire:click="closeSectionModal">Close</button>
                                <button type="submit" class="btn bg-dark-teal text-white">
                                    <span wire:loading.remove wire:target="saveSections">
                                        <em class="icon ni ni-save"></em> Save Changes
                                    </span>
                                    <span wire:loading wire:target="saveSections">
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
    
    <div class="my-4"></div>
    <livewire:emis-returns.notification-banner key="calendar" :eventName="'notifyCalendar'" />


    <button class="btn bg-dark-teal btn-sm mt-1" data-toggle="modal" wire:click="openCalendarModal">
        <span class="text-uppercase">UPDATE CALENDARS</span>
    </button>

    <div class="table-responsive mt-2">
        <table class="table table-bordered table-sm">
            <tr class="bg-secondary">
                <td class="text-uppercase align-middle text-white border-1 border-white">Calendars</td>
            </tr>
            @forelse($assignedCalendars as $calendar)
                <tr>
                    <td class="text-uppercase align-middle text-dark border-secondary border-1">
                        {{ $calendar->name }}
                    </td>
                </tr>
            @empty
                <tr>
                    <td class="text-center text-muted">
                        <div class="alert alert-secondary alert-icon">
                            <em class="icon ni ni-alert-circle"></em> There are no calendars to display at the moment.
                        </div>
                    </td>
                </tr>
            @endforelse
        </table>
    </div>

    @if($showCalendarModal)
        <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);z-index:1040;"></div>
        <div class="modal d-block" tabindex="-1" style="z-index:1050;">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <a class="cursor close" wire:click="closeCalendarModal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h5 class="modal-title">Update Calendar</h5>
                    </div>
                    <div class="modal-body">
                        <form wire:submit.prevent="saveCalendars">
                            <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Calendars for your school</span>
                            @if(count($selectedCalendars))
                                <div @if(count($selectedCalendars) > 5) class="h-150px overflow-auto" @endif>
                                    <ul class="list list-sm">
                                        @foreach($allCalendars->whereIn('id', $selectedCalendars) as $calendar)
                                            <li class="py-0 fs-13px">{{ $calendar->name }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                                <hr class="border-dark-teal border-1 my-4">
                            @endif
                            <div>
                                <span class="text-dark-teal text-uppercase fs-14px d-block mb-1">Available calendars</span>
                                <div @if(count($allCalendars) > 9) class="h-200px overflow-auto my-1" @else class="my-1" @endif>
                                    @foreach($allCalendars as $calendar)
                                        <div class="d-block custom-control custom-control-sm custom-checkbox">
                                            <input wire:model.live="selectedCalendars"
                                                   value="{{ $calendar->id }}"
                                                   id="calendar-{{ $calendar->id }}"
                                                   type="checkbox"
                                                   class="custom-control-input">
                                            <label class="custom-control-label" for="calendar-{{ $calendar->id }}">
                                                <span class="text-dark fs-13px">{{ $calendar->name }}</span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-light" wire:click="closeCalendarModal">Close</button>
                                <button type="submit" class="btn bg-dark-teal text-white">
                                    <span wire:loading.remove wire:target="saveCalendars">
                                        <em class="icon ni ni-save"></em> Save Changes
                                    </span>
                                    <span wire:loading wire:target="saveCalendars">
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>