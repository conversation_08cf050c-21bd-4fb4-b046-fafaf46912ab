<div style="max-height: 600px; overflow-y: auto;">
    @if (($schoolType && $schoolType->id == 2) || ($schoolType && $schoolType->id == 3))
        <div class="w-100 mb-5">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="mt-5 nk-block-head-content">
                        <h5 class="nk-block-title">Textbooks</h5>
                    </div>
                    <div class="nk-block-head-content">
                        <div class="mt-5 toggle-wrap nk-block-tools-toggle">
                            <button data-toggle="modal" data-target="#schoolTextBooksModal"
                                class="cursor btn bg-dark-teal btn-md d-sm-none">
                                <em class="icon ni ni-plus-circle-fill text-white"></em><span>Update Textbooks</span>
                            </button>
                            <div class="toggle-expand-content" data-content="pageMenu">
                                <div>
                                    <button data-toggle="modal" data-target="#schoolTextBooksModal"
                                        class="cursor btn bg-dark-teal btn-md">
                                        <em class="icon ni ni-plus-circle-fill text-white"></em><span>Update
                                            Textbooks</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <livewire:emis-returns.notification-banner />
                <div class="table table-bordered table-hover table-sm">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col">
                                <span class="sub-text text-white ucap">Class</span>
                            </div>
                            <div class="nk-tb-col">
                                <span class="sub-text text-white ucap">Subject</span>
                            </div>
                            @if ($schoolType && $schoolType->id == 2)
                                <div class="nk-tb-col">
                                    <span class="sub-text text-white ucap">Textbook</span>
                                </div>
                            @endif
                            <div class="nk-tb-col text-center">
                                <span class="sub-text text-white ucap">Number</span>
                            </div>
                        </div>

                        @forelse ($instructional_materials as $index => $material)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-center ucap">
                                        {{ $material->education_grade->name ?? 'N/A' }}
                                    </span>
                                </div>
                                @if ($schoolType && $schoolType->id == 2)
                                    <div class="nk-tb-col">
                                        <span class="text-secondary text-center ucap">
                                            {{ $material->subject->name ?? 'N/A' }}
                                        </span>
                                    </div>
                                @elseif ($schoolType && $schoolType->id == 3)
                                    <div class="nk-tb-col">
                                        <span class="text-secondary text-center ucap">
                                            {{ $material->secondary_subject->name ?? 'N/A' }}
                                        </span>
                                    </div>
                                @endif
                                <div class="nk-tb-col text-center">
                                    <span class="text-secondary">
                                        {{ $material->quantity ?? 'Not Set' }}
                                    </span>
                                </div>
                            </div>
                        @empty
                            <div class="card card-stretch" style="box-shadow: none;">
                                <div class="card-inner-group">
                                    <div class="card-body">
                                        <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                            <em class="icon ni ni-alert-circle"></em>
                                            There are no Textbooks to display at the moment.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforelse

                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade zoom" id="schoolTextBooksModal" tabindex="-1" data-backdrop="static" wire:ignore.self>
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a wire:click="resetTextbookForm()" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form wire:submit.prevent="{{ $formMode === 'create' ? 'storeTextbooks' : 'updateTextbook' }}">
                        <div class="modal-header">
                            <h5 class="modal-title ucap">Update Textbooks</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="textBookGradeId">Select Class <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select wire:model.live="form_education_grade_id" id="textBookGradeId"
                                                class="form-control bg-primary-dim custom-select">
                                                <option value="">--Select Class--</option>
                                                @foreach ($grades as $grade)
                                                    <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                                                @endforeach
                                            </select>
                                            @error('form_education_grade_id')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="primaryTextbookSubjectId">Select Subject<span
                                                class="text-danger">*</span></label>
                                        @if ($schoolType && $schoolType->id == 2)
                                            <div class="form-control-wrap">
                                                <select wire:model.live="form_subject_id" id="primaryTextbookSubjectId"
                                                    class="form-control bg-primary-dim custom-select"
                                                    {{ count($primarySubjects) == 0 ? 'disabled' : '' }}>
                                                    <option value="">--Select Subject--</option>
                                                    @foreach ($primarySubjects as $subject)
                                                        <option value="{{ $subject->id }}">{{ $subject->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('form_subject_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        @endif
                                        @if ($schoolType && $schoolType->id == 3)
                                            <div class="form-control-wrap">
                                                <select wire:model.live="form_subject_id" id="primaryTextbookSubjectId"
                                                    class="form-control bg-primary-dim custom-select"
                                                    {{ count($secondarySubjects) == 0 ? 'disabled' : '' }}>
                                                    <option value="">--Select Subject--</option>
                                                    @foreach ($secondarySubjects as $subject)
                                                        <option value="{{ $subject->id }}">{{ $subject->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('form_subject_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                @if ($schoolType && $schoolType->id == 2)
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="textBookId">Select Textbook<span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select wire:model="form_book_id" id="textBookId"
                                                    class="form-control bg-primary-dim custom-select">
                                                    <option value="">--Select Textbook--</option>

                                                    @forelse ($availableTextbooks as $textbook)
                                                        <option value="{{ $textbook->id }}">
                                                            {{ $textbook->name }}
                                                        </option>
                                                    @empty
                                                        <option disabled>-- No results found --</option>
                                                    @endforelse
                                                </select>

                                                @error('form_book_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="quantity" class="form-label">Number <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input wire:model="form_quantity" type="number" id="quantity"
                                                class="form-control" placeholder="Enter Number" autocomplete="off">
                                            @error('form_quantity')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="modal-footer d-flex justify-content-center mt-4">
                            <button type="button" data-dismiss="modal" class="btn btn-light ml-2"
                                wire:click="resetTextbookForm">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary d-flex">
                                <span class="align-self-center">Save</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    @endif

    {{-- REFERENCE BOOKS --}}
    @if (($schoolType && $schoolType->id == 2) || ($schoolType && $schoolType->id == 3))
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">Reference Books</h5>

                </div><!-- .nk-block-head-content -->
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolReferenceBooksModal"
                            class="cursor btn bg-dark-teal btn-md d-sm-none"><em
                                class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update
                                Reference
                                Books</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolReferenceBooksModal"
                                    class="cursor btn bg-dark-teal btn-md"><em
                                        class="icon ni ni-plus-circle-fill text-white"></em><span
                                        class="">Update
                                        Reference Books</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <livewire:emis-returns.notification-banner />
            <div class="table table-bordered table-hover table-sm mb-5">
                <div class="nk-tb-list nk-tb-ulist is-compact">
                    <div class="nk-tb-item nk-tb-head bg-secondary">
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Class</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white ucap">Book Type</span></div>
                        @if ($schoolType && $schoolType->id == 2)
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">Reference Book</span></div>
                        @endif
                        <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span></div>

                    </div>

                    @forelse($primary_school_reference_books as $item)
                        <div class="nk-tb-item">
                            <div class="nk-tb-col">
                                <span
                                    class="text-secondary text-center ucap">{{ $item->education_grade->name ?? 'N/A' }}</span>
                            </div>
                            <div class="nk-tb-col">
                                <span class="text-secondary text-center ucap">{{ $item->book->name ?? 'N/A' }}</span>
                            </div>
                            @if ($schoolType && $schoolType->id == 2)
                                <div class="nk-tb-col">
                                    <span
                                        class="text-secondary text-center ucap">{{ $item->subject->name ?? 'N/A' }}</span>
                                </div>
                            @endif
                            <div class="nk-tb-col text-center">
                                <span class="text-secondary">{{ $item->quantity ?? 'N/A' }}</span>
                            </div>

                        </div>
                    @empty
                        <div class="card card-stretch" style="box-shadow: none;">
                            <div class="card-inner-group">
                                <div class="card-body">
                                    <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                        <em class="icon ni ni-alert-circle"></em> There are no reference books to
                                        display at the moment.
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolReferenceBooksModal"
            wire:ignore.self>
            <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a wire:click="resetReferenceBookForm()" class="cursor close" data-dismiss="modal"
                        aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form
                        wire:submit.prevent="{{ $reference_formMode === 'create' ? 'storeReferenceBook' : 'updateReferenceBook' }}">
                        <div class="modal-header">
                            <h5 class="modal-title ucap">{{ $reference_formMode === 'edit' ? 'EDIT' : 'ADD' }}
                                Reference
                                Books</h5>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="referenceBookGradeId">Select Class <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select wire:model="reference_education_grade_id"
                                                id="referenceBookGradeId"
                                                class="form-control bg-primary-dim custom-select">
                                                <option value="">--Select Class--</option>
                                                @foreach ($reference_grades as $grade)
                                                    <option value="{{ $grade->id }}">{{ $grade->name }}</option>
                                                @endforeach
                                            </select>
                                            @error('reference_education_grade_id')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="subjectId">Select Subject<span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select wire:model.live="subjectId" id="subjectId"
                                                class="form-control bg-primary-dim custom-select">
                                                <option value="">--Select Subject--</option>
                                                @foreach ($referenceBookTypes as $subjects)
                                                    <option value="{{ $subjects->id }}">{{ $subjects->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('subjectId')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="referenceBookId">Select Reference Book <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <select wire:model.live="reference_book_id" id="referenceBookId"
                                                class="form-control bg-primary-dim custom-select"
                                                {{ empty($subjectId) ? 'disabled' : '' }}>

                                                <option value="">
                                                    {{ empty($subjectId) ? '--Select Subject First--' : '--Select Reference Book--' }}
                                                </option>

                                                @forelse ($availableReferenceBooks as $referenceBook)
                                                    <option value="{{ $referenceBook->id }}">
                                                        {{ $referenceBook->name }}
                                                    </option>
                                                @empty
                                                    <option disabled>-- No results found --</option>
                                                @endforelse
                                            </select>

                                            @error('reference_book_id')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror

                                            @error('reference_book_id')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror

                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="referenceQuantity" class="form-label">Number <span
                                                class="text-danger">*</span></label>
                                        <div class="form-control-wrap">
                                            <input wire:model="reference_quantity" type="number"
                                                id="referenceQuantity" class="form-control"
                                                placeholder="Enter Number" autocomplete="off">
                                            @error('reference_quantity')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="modal-footer d-flex justify-content-center mt-4">
                            <button wire:click="resetReferenceBookForm()" type="button" data-dismiss="modal"
                                class="btn btn-light ml-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                                <span
                                    class="align-self-center">{{ $reference_formMode === 'edit' ? 'Update' : 'Save' }}</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    @endif

    {{-- SNE KITS --}}
    @if (($schoolType && $schoolType->id == 2) || ($schoolType && $schoolType->id == 3))
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">SNE KITS</h5>
                </div>
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button data-toggle="modal" data-target="#schoolSneKitsModal"
                            class="cursor btn bg-dark-teal btn-md d-sm-none"><em
                                class="icon ni ni-plus-circle-fill text-white"></em><span class="">Update SNE
                                Kits</span></button>
                        <div class="toggle-expand-content" data-content="pageMenu">

                            <div class="">
                                <button data-toggle="modal" data-target="#schoolSneKitsModal"
                                    class="cursor btn bg-dark-teal btn-md"><em
                                        class="icon ni ni-plus-circle-fill text-white"></em><span
                                        class="">Update
                                        SNE Kits</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <livewire:emis-returns.notification-banner />
                <div class="table table-bordered table-hover table-sm">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">Class</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">SNE Kit</span></div>
                            <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span>
                            </div>
                        </div>
                        @forelse ($sne_kits as $sne_kit)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span
                                        class="text-secondary text-center">{{ $sne_kit->education_grade->name ?? 'N/A' }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-center">{{ $sne_kit->kit->name ?? 'N/A' }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span class="text-secondary">{{ $sne_kit->quantity }}</span>
                                </div>
                            </div>
                        @empty
                            <div class="card card-stretch" style="box-shadow: none;">
                                <div class="card-inner-group">
                                    <div class="card-body">
                                        <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                            <em class="icon ni ni-alert-circle"></em> There are no sne kits to display
                                            at
                                            the
                                            moment.
                                        </div>
                                    </div>
                                </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <div class="modal fade zoom" data-backdrop="static" tabindex="-1" id="schoolSneKitsModal"
                aria-hidden="true" wire:ignore.self>
                <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <a wire:click="resetForm" class="cursor close" data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </a>
                        <form wire:submit.prevent="createSneKit">
                            <div class="modal-header">
                                <h5 class="modal-title">ADD NEW SNE KITS</h5>
                            </div>
                            <div class="modal-body">
                                @if (session()->has('message'))
                                    <div class="alert alert-success">
                                        {{ session('message') }}
                                    </div>
                                @endif
                                @if (session()->has('error'))
                                    <div class="alert alert-danger">
                                        {{ session('error') }}
                                    </div>
                                @endif

                                <div class="row g-4">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="sneEducationGradeId">Select Class <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select wire:model.live="education_grade_id" id="sneEducationGradeId"
                                                    data-placeholder="Select Class"
                                                    class="form-select-sm form-control">
                                                    <option value="">--Select Class--</option>
                                                    @foreach ($grades as $grade)
                                                        {{-- Using $educationGrades from component --}}
                                                        <option value="{{ $grade->id }}">{{ $grade->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('education_grade_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="sneKitId">Select SNE Kit<span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select wire:model.live="sne_kit_id" id="sneKitId"
                                                    data-placeholder="Select SNE Kit"
                                                    class="form-select-sm form-control">
                                                    <option value="">--Select SNE Kit--</option>
                                                    @foreach ($allKits as $kit)
                                                        {{-- Using $allKits from component --}}
                                                        <option value="{{ $kit->id }}">{{ $kit->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('sne_kit_id')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="quantity" class="form-label">Number <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <input wire:model.live="quantity" type="number" id="quantity"
                                                    class="form-control" autocomplete="off" required
                                                    placeholder="Enter Number">
                                                @error('quantity')
                                                    <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                </div><!-- .form-inline -->
                            </div>
                            <div class="modal-footer d-flex justify-content-center mt-4">
                                <button type="button" data-dismiss="modal" class="btn btn-light ml-2"
                                    wire:click="resetForm">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                                    <span class="align-self-center">Save</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
    @endif

    {{-- Wall Charts and Work Cards --}}
    @if ($schoolType && $schoolType->id == 2)
        <div class="w-100 mt-5 mb-5">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h5 class="nk-block-title">Wall Charts & Work Cards</h5>
                    </div>
                    <div class="nk-block-head-content">
                        <div class="toggle-wrap nk-block-tools-toggle">
                            <button data-toggle="modal" data-target="#schoolWallChartsModal"
                                class="cursor btn bg-dark-teal btn-md d-sm-none">
                                <em class="icon ni ni-plus-circle-fill text-white"></em>
                                <span>Update Wall Charts & Work Cards</span>
                            </button>
                            <div class="toggle-expand-content" data-content="pageMenu">
                                <div>
                                    <button data-toggle="modal" data-target="#schoolWallChartsModal"
                                        class="cursor btn bg-dark-teal btn-md">
                                        <em class="icon ni ni-plus-circle-fill text-white"></em>
                                        <span>Update Wall Charts & Work Cards</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <livewire:emis-returns.notification-banner />
                <div class="table table-bordered table-hover table-sm">
                    <div class="nk-tb-list nk-tb-ulist is-compact">
                        <div class="nk-tb-item nk-tb-head bg-secondary">
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">Class</span></div>
                            <div class="nk-tb-col"><span class="sub-text text-white ucap">Chart Name</span></div>
                            <div class="nk-tb-col text-center"><span class="sub-text text-white ucap">Number</span>
                            </div>
                        </div>

                        @forelse ($wall_charts as $item)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span
                                        class="text-secondary text-center">{{ $item->education_grade->name ?? 'N/A' }}</span>
                                </div>
                                <div class="nk-tb-col">
                                    <span class="text-secondary text-center">{{ $item->chart->name ?? 'N/A' }}</span>
                                </div>
                                <div class="nk-tb-col text-center">
                                    <span class="text-secondary">{{ $item->quantity }}</span>
                                </div>
                            </div>
                        @empty
                            <div class="card card-stretch" style="box-shadow: none;">
                                <div class="card-inner-group">
                                    <div class="card-body">
                                        <div class="my-5 mx-1 mx-lg-4 alert alert-secondary alert-icon">
                                            <em class="icon ni ni-alert-circle"></em> There are no Wall Charts & Work
                                            Cards
                                            to
                                            display at the moment.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <div class="modal fade zoom" tabindex="-1" id="schoolWallChartsModal" wire:ignore.self>
                <div class="modal-dialog modal-md modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <button wire:click="resetWallChartForm()" type="button" class="close cursor"
                            data-dismiss="modal" aria-label="Close">
                            <em class="icon ni ni-cross"></em>
                        </button>

                        <form wire:submit.prevent="createWallChart">
                            <div class="modal-header">
                                <h5 class="modal-title ucap">Add Wall Charts & Work Cards</h5>
                            </div>
                            <div class="modal-body">
                                <div class="row g-4">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="wallChartGradeId">Select Class <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select name="education_grade_id" wire:model="education_grade_id"
                                                    id="wallChartGradeId" class="form-select-sm form-control"
                                                    required>
                                                    <option value="">--Select Class--</option>
                                                    @foreach ($grades as $grade)
                                                        <option value="{{ $grade->id }}">{{ $grade->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="wallChartId">Select Wall Chart <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <select name="chart_id" wire:model="chart_id" id="wallChartId"
                                                    class="form-select-sm form-control" required>
                                                    <option value="">--Select--</option>
                                                    @foreach ($allWallCharts as $chart)
                                                        <option value="{{ $chart->id }}">{{ $chart->name }}
                                                        </option>
                                                    @endforeach
                                                </select>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="quantity" class="form-label">Number <span
                                                    class="text-danger">*</span></label>
                                            <div class="form-control-wrap">
                                                <input type="number" wire:model="quantity" name="quantity"
                                                    id="quantity" class="form-control" required
                                                    placeholder="Enter Number">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modal-footer d-flex justify-content-center mt-4">
                                <button wire:click="resetWallChartForm()" type="button" data-dismiss="modal"
                                    class="btn btn-light ml-2">
                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                </button>
                                <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                                    <span wire:loading.remove class="align-self-center">Save</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Secondary School Instructional Materials --}}
    @if ($schoolType && $schoolType->id == 3)
        <livewire:emis-returns.sections.instructional-materials.secondary-school-instructional-materials
            :survey="$survey" />
    @endif

    {{-- Pre Primary School Instructional Materials --}}
    @if ($schoolType && $schoolType->id == 1)
        <livewire:emis-returns.sections.instructional-materials.pre-primary-school-instructional-materials
            :survey="$survey" />
    @endif

</div>
