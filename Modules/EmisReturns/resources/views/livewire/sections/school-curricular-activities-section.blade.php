<div class="w-100">
        <div class="table-responsive">
            <livewire:emis-returns.notification-banner />
            <table class="table border border-dark-teal">
                <thead class="bg-secondary">
                    <tr>
                        <th class="text-white align-middle text-uppercase w-45">Extra-curricular Activity</th>
                        <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                        <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                    </tr>
                </thead>
                <tbody class="border-top-0 border-secondary">

                @foreach($curricularActivitiesNames as $curricularActivity)
                    <tr>
                        <td class="align-middle border-left border-secondary">{{$curricularActivity['name']}}</td>
                        <td class="align-middle border-left border-secondary text-center" >
                            <div class="preview-icon-wrap">
                                    @php
                                        $activity = collect($curricularActivities)->firstWhere('practical_skill_id', $curricularActivity['id']);
                                    @endphp
                                    @if($activity && $activity['present_in_school'])
                                        <span class="text-dark-teal text-uppercase">Yes</span>
                                    @elseif($activity && !$activity['present_in_school'])
                                        <span class="text-danger text-uppercase">No</span>
                                    @else
                                        <span class="text-muted text-uppercase">Not Set</span>
                                    @endif
                            </div>
                        </td>
                        <td class="align-middle border-left border-secondary text-center">
                            <button 
                                x-data
                                x-on:click="$dispatch('open-curricular-modal', {
                                    id: {{ $curricularActivity['id'] }}, 
                                    name: '{{ $curricularActivity['name'] }}'
                                })" 
                                class="btn bg-dark-teal btn-xs align-self-center">
                                <em class="icon ni ni-edit-fill"></em>
                                <span class="">Update</span>
                            </button>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>

        {{-- Update Modal --}}
        <div 
            x-data="{ open: false }"
            x-on:open-curricular-modal.window="open = true"
            x-on:close-curricular-modal.window="open = false"
            x-show="open"
            x-cloak >
        <div class="custom-modal-backdrop"></div>
		<div
            class="modal d-block custom-modal-show" 
            tabindex="-1" 
            id="practicalSkillModal">
			<div class="modal-dialog modal-dialog-centered ">
				<div class="modal-content">
					<a @click="open = false" class="close absolute top-2 right-2 text-gray-700">
						<em class="icon ni ni-cross"></em>
					</a>
					<form wire:submit="updateActivity">
						<div class="modal-header">
							<h6 class="modal-title">Update Extra-curricular Activity</h6>
						</div>
						<div class="modal-body">
							<div class="row py-5"
                                x-data="activityModal()" 
                                x-on:open-curricular-modal.window="handleModalOpen($event.detail)">
								<div class="col-12 d-flex flex-column">
									<span 
                                        x-text="'Does this school offer ' + activityName + '?'"
                                        class="font-weight-bold lead align-self-center">
                                    </span>
                                    <div class="align-self-center mt-2">
										<div class="custom-control custom-radio custom-control-inline">
											<input 
                                                type="radio" 
                                                id="facility_yes" 
                                                value="true" 
                                                x-bind:checked="activityPresent === true"
                                                x-on:change="updateActivityStatus(true)"
                                                class="custom-control-input">
											<label class="custom-control-label" for="facility_yes">Yes</label>
										</div>
										<div class="custom-control custom-radio custom-control-inline">
											<input 
                                                type="radio" 
                                                id="facility_no" 
                                                value="false" 
                                                x-bind:checked="activityPresent === false"
                                                x-on:change="updateActivityStatus(false)"
                                                class="custom-control-input">
											<label class="custom-control-label" for="facility_no">No</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer d-flex justify-content-center">
							<button  type="button" @click="open = false" class="btn btn-light mr-2">
								<em class="icon ni ni-cross"></em><span>Cancel</span>
							</button>
							<button type="submit" class="btn btn-primary  d-flex" wire:target="updateActivity" wire:loading.attr="disabled">
                                <span wire:loading.remove wire:target="updateActivity" class="align-self-center">Save</span>
                                <span wire:loading wire:target="updateActivity" class="align-self-center">
                                    <span wire:loading wire:target="updateActivity" class="align-self-center d-flex align-items-center">
                                        <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                        Saving...
                                    </span>
                                    <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateActivity"></em>
                                </span>
                            </button>
						</div>
					</form>
				</div>
			</div>
		</div>
        </div>
	</div>

<script>
function activityModal() {
    return {
        activityName: '{{ $selectedActivityName }}',
        activityPresent: @json($selectedActivityPresent),
        
        handleModalOpen(detail) {
            this.activityName = detail.name;
            // Set the current state from data
            const currentActivity = @json($curricularActivities).find(a => a.practical_skill_id == detail.id);
            this.activityPresent = currentActivity ? currentActivity.present_in_school : null;
        },
        
        updateActivityStatus(value) {
            this.activityPresent = value;
            this.$wire.set('selectedActivityPresent', value);
        }
    }
}
</script>