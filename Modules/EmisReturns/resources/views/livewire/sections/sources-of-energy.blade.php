<div class="w-100">
    <div class="table-responsive">
        <livewire:emis-returns.notification-banner />
        <table class="table table-bordered table-hover table-sm">
            <thead>
            <tr class="bg-secondary">
                <td colspan="2" class="text-uppercase text-white">SECTION F: SOURCE OF ENERGY</td>
            </tr>
            <tr class="bg-secondary-dim">
                <th class="text-uppercase border-secondary text-dark">USAGE</th>
                <th class="text-uppercase border-secondary text-dark">ENERGY SOURCE TYPE</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td class="text-uppercase border-secondary text-dark">MAIN SOURCE OF ENERGY FOR COOKING</td>
                <td class="text-uppercase border-secondary text-dark">
                    @if ($this->energySources?->cooking)
                        {{ strtoupper($this->energySources->cooking->name) }}
                    @else
                        <span class="font-italic text-muted">Not set</span>
                    @endif
                </td>
            </tr>
            <tr>
                <td class="text-uppercase border-secondary text-dark">MAIN SOURCE OF ENERGY FOR LIGHTING</td>
                <td class="text-uppercase border-secondary text-dark">
                    @if ($this->energySources?->lighting)
                        {{ strtoupper($this->energySources->lighting->name) }}
                    @else
                        <span class="font-italic text-muted">Not set</span>
                    @endif
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="d-flex justify-content-center">
        <button class="btn bg-dark-teal btn-sm mt-3" data-toggle="modal" data-target="#updateEnergySourcesModal">
            <span class="text-uppercase">UPDATE ENERGY SOURCES INFORMATION</span>
        </button>
    </div>

    <div class="modal fade zoom" id="updateEnergySourcesModal" tabindex="-1" wire:ignore.self>
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <form wire:submit="updateEnergySources">
                    <div class="modal-header">
                        <h6 class="modal-title text-uppercase">UPDATE ENERGY SOURCES</h6>
                    </div>
                    <div class="modal-body">
                        <div class="row g-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="cookingEnergySourceId" class="form-label">
                                        Cooking Energy Source<span class="text-danger">*</span>
                                    </label>
                                    <div class="form-control-wrap">
                                        <select wire:model="cooking_source_id"
                                                required
                                                id="cookingEnergySourceId"
                                                class="form-control bg-primary-dim custom-select">
                                            <option value="">--Select--</option>
                                            @foreach ($this->cookingSources as $source)
                                                <option value="{{ $source->id }}">
                                                    {{ strtoupper($source->name) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('cooking_source_id')
                                    <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-group">
                                    <label for="lightingEnergySourceId" class="form-label">
                                        Lighting Energy Source<span class="text-danger">*</span>
                                    </label>
                                    <div class="form-control-wrap">
                                        <select wire:model="lighting_source_id"
                                                required
                                                id="lightingEnergySourceId"
                                                class="form-control bg-primary-dim custom-select">
                                            <option value="">--Select--</option>
                                            @foreach ($this->lightingSources as $source)
                                                <option value="{{ $source->id }}">
                                                    {{ strtoupper($source->name) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('lighting_source_id')
                                    <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center mt-4">
                        <button wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light ml-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button wire:loading.attr="disabled" type="submit" class="btn btn-primary d-flex">
                            <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span wire:loading class="align-self-center">Updating...</span>
                            <span wire:loading.remove class="align-self-center">Save</span>
                            <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


