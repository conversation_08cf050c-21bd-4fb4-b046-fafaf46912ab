<div x-data="{ open: false }"
     x-on:open-governance-modal.window="open = true"
     x-on:close-governance-modal.window="open = false"
     x-show="open"
     x-cloak>
    <div class="custom-modal-backdrop"></div>
    <div 
        class="modal d-block custom-modal-show" 
        tabindex="-1"
        id="governanceModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a @click="open = false" class="close absolute top-2 right-2 text-gray-700">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="updateGovernance">
                    <div class="modal-header">
                        <h5 class="modal-title">UPDATE {{ $schoolType === 'preprimary' ? 'ECCE CENTRE' : 'SCHOOL'}} GOVERNANCE</h5>
                    </div>
                    {{-- Loading spinner --}}
                    @if($loading)
                    <div x-show="$wire.loading"
                         class="custom-spinner-bg position-absolute w-100 h-100 d-flex align-items-center justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                    @endif
                    @if(!$loading)
                    <div class="modal-body">
                        <div x-data="{ hasCmcSmcOrBog: @entangle('has_cmc_smc_or_bog_yn') }"
                            class="row px-4">
                            <div class="col-12">
                                <div class="form-group d-flex flex-column justify-content-center">
                                    @if($schoolType === 'preprimary')
                                        <span class="form-label">Does the ECCE centre have a Centre Management Committee (CMC)?</span>
                                    @elseif($schoolType === 'primary')
                                        <span class="form-label">Does this school have a School Management Committee (SMC)?</span>
                                    @elseif($schoolType === 'secondary')
                                        <span class="form-label">Does this school have Board Of Governors (BOG)?</span>
                                    @endif
                                    <ul class="custom-control-group g-3 align-center flex-wrap">
                                        <li>
                                            <div class="custom-control custom-radio">
                                                <input 
                                                type="radio" 
                                                value="true" 
                                                x-model="hasCmcSmcOrBog"
                                                wire:model="has_cmc_smc_or_bog_yn" 
                                                class="custom-control-input" 
                                                id="committee_yes">
                                                <label class="custom-control-label" for="committee_yes">Yes</label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="custom-control custom-radio">
                                                <input 
                                                type="radio" 
                                                value="false" 
                                                x-model="hasCmcSmcOrBog"
                                                wire:model="has_cmc_smc_or_bog_yn" 
                                                class="custom-control-input" 
                                                id="committee_no">
                                                <label class="custom-control-label" for="committee_no">No</label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div x-data="{ holdsAssemblies: @entangle('holds_assemblies_yn') }"
                                x-show="$wire.has_cmc_smc_or_bog_yn === 'true'">
                                <div class="col-12 mt-5">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label">
                                            How many 
                                            {{$schoolType === 'preprimary' ? 'CMC' : ($schoolType === 'primary' ? 'SMC' : 'BOG')}}  
                                            meetings were held in the last 12 months?
                                        </label>
                                        <input 
                                            type="number" 
                                            placeholder="Enter Number" 
                                            class="form-control" 
                                            wire:model="no_of_meetings"
                                            :required="hasCmcSmcOrBog === 'true'">
                                    </div>
                                </div>
                                <div class="col-12 mt-5">
                                    <div class="form-group d-flex flex-column justify-content-center">
                                        <label class="form-label font-weight-bold">Does this School hold assemblies?</label>
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input 
                                                    type="radio" 
                                                    value="true" 
                                                    x-model="holdsAssemblies"
                                                    wire:model="holds_assemblies_yn" 
                                                    class="custom-control-input" 
                                                    id="holds_assemblies_yes">
                                                    <label class="custom-control-label" for="holds_assemblies_yes">Yes</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input 
                                                    type="radio" 
                                                    value="false" 
                                                    x-model="holdsAssemblies"
                                                    wire:model="holds_assemblies_yn" 
                                                    class="custom-control-input" 
                                                    id="holds_assemblies_no">
                                                    <label class="custom-control-label" for="holds_assemblies_no">No</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div x-show="$wire.holds_assemblies_yn === 'true'" class="col-12 mt-4">
                                    <div class="form-group">
                                        <label class="form-label" for="schoolParadeId">Parade</label>
                                        <span class="form-note">Specify the school's parade.</span>
                                    </div>
                                    <div class="form-group">
                                        <select 
                                            :required="holdsAssemblies === 'true' && hasCmcSmcOrBog === 'true'"
                                            wire:model="assembly_frequency_id" 
                                            id="schoolParadeId" 
                                            class="form-select form-control bg-primary-dim">
                                            <option value="">--Select--</option>
                                            @foreach($assemblyFrequency as $id => $name)
                                                <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" @click="open = false"  class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex" wire:target="updateGovernance" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateGovernance" class="align-self-center">Save</span>
                            <span wire:loading wire:target="updateGovernance" class="align-self-center">
                                <span wire:loading wire:target="updateGovernance" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateGovernance"></em>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
