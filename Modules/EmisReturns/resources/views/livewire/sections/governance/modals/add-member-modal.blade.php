<div x-data="{ open: false }"
     x-on:open-add-member-modal.window="open = true"
     x-on:close-add-member-modal.window="open = false"
     x-show="open"
     x-cloak >
    <div class="custom-modal-backdrop"></div>
    <div class="modal d-block custom-modal-show" tabindex="-1" id="addMemberModal">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <a x-on:click="open = false; $wire.resetInputFields()" class="cursor close"  aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <div class="modal-header">
                    <h6 class="modal-title">
                        ADD 
                        {{ $schoolType === 'preprimary' ? 'CMC' : ($schoolType === 'primary' ? 'SMC' : 'BOG') }} 
                        MEMBERS' INFORMATION
                    </h6>
                </div>
                <div class="modal-body">
                    <form wire:submit="addMember">
                        <livewire:emis-returns.notification-banner key="addMemberModal" :eventName="'notifyAddMemberModal'" />
                        @if(!$verificationStatus)
                        <div  class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-control-wrap">
                                        <div class="form-text-hint bg-primary-dim">
                                            <span class="overline-title">NIN</span>
                                        </div>
                                        <input 
                                            required  
                                            wire:model="id_number"
                                            type="text" 
                                            class="form-control bg-primary-dim text-uppercase" 
                                            minlength="14" 
                                            maxlength="14" 
                                            placeholder="Enter Committee Member NIN">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <button 
                                        wire:click="verifyMemberNIN" 
                                        wire:loading.attr="disabled" 
                                        wire:target="verifyMemberNIN" 
                                        class="btn bg-dark-teal btn-block text-center d-flex" 
                                        type="button">
                                        <span wire:loading wire:target="verifyMemberNIN" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span wire:loading wire:target="verifyMemberNIN" class="align-self-center">Verifying...</span>
                                        <span wire:loading wire:target="verifyMemberNIN" class="sr-only">Verifying...</span>
                                        <span wire:loading.remove wire:target="verifyMemberNIN">Verify NIN</span>
                                        <em wire:loading.remove wire:target="verifyMemberNIN" class="icon ni ni-user-fill-c"></em>
                                    </button>
                                </div>
                            </div>
                        </div>
                        @endif
                        @if($verificationStatus)
                        <div  class="row g-4">
                            <div class="col-12 col-lg-6">
                                <div class="table-responsive py-3">
                                    <table class="table table-sm table-hover">
                                        <tr>
                                            <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                                <div class="user-card">
                                                    <div class="w-150px">
                                                        <img id="committeeMemberAvatar" src="{{ $memberData->photo_url ?? '' }}" class="rounded-0" alt="{{ $memberData->full_name ?? '' }}">
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                                <span class="">{{ $memberData->national_id ?? '' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                                <span class="">{{ $memberData->surname ?? '' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                                <span class="">{{ $memberData->given_names ?? '' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                                <span class="">{{ $memberData->gender ?? '' }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-2 align-middle text-uppercase text-dark">
                                                <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                                <span class="">{{ $memberData->date_of_birth ?? '' }}</span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 mt-1">
                                <div class="row g-4 align-self-center">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <div class="form-label-group">
                                                <label for="form_member_email" class="form-label">Email</label>
                                            </div>
                                            <div class="form-control-group">
                                                <input  
                                                    id="form_member_email" 
                                                    type="email"
                                                    wire:model="email"
                                                    placeholder="Enter Email Address" 
                                                    class="form-control bg-primary-dim">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <div class="form-label-group">
                                                <label for="member_phone_1" class="form-label">Phone <span class="text-danger">*</span></label>
                                            </div>
                                            <div class="form-control-group">
                                                <input  
                                                    id="member_phone_1" 
                                                    wire:model="phone_1"
                                                    maxlength="10" 
                                                    type="text" 
                                                    placeholder="Enter Phone Number" 
                                                    class="form-control bg-primary-dim">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <div class="form-label-group">
                                                <label for="member_appointment_date" class="form-label">Appointment Date <span class="text-danger">*</span></label>
                                            </div>
                                            <div class="form-control-group">
                                                <input  
                                                    id="member_appointment_date" 
                                                    wire:model="appointment_date"
                                                    type="date" 
                                                    data-date-format="dd-mm-yyyy"
                                                    placeholder="Enter Appointment Date" 
                                                    autocomplete="off" 
                                                    class="form-control bg-primary-dim">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span class="ml-4">
                               Is this member trained on their respective role and responsibilities by MoES in the last 12months?
                            </span>
                            <div class="col-lg-12 ml-2">
                                <div class="form-group">
                                    <div class="form-control-group">
                                        <ul class="custom-control-group g-3 align-center flex-wrap">
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input  
                                                        type="radio" 
                                                        class="custom-control-input" 
                                                        value="1" 
                                                        id="member_trained"
                                                        wire:model="is_trained_yn">
                                                    <label class="custom-control-label text-uppercase" for="member_trained">Trained</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="custom-control custom-radio">
                                                    <input  
                                                        type="radio" 
                                                        class="custom-control-input" 
                                                        value="0" 
                                                        id="member_not_trained"
                                                        wire:model="is_trained_yn">
                                                    <label class="custom-control-label text-uppercase" for="member_not_trained">Not Trained</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div  class="nk-kycfm-action pt-5 row">
                            <div class="col-lg-6">
                                <button x-on:click="open = false; $wire.resetInputFields()" type="button" class="btn btn-secondary btn-block text-center d-flex">
                                    <span class="align-self-center text-uppercase">CANCEL</span>
                                </button>
                            </div>
                            <div class="col-lg-6">
                                <button type="submit" class="btn bg-dark-teal text-center btn-block d-flex" wire:target="addMember" wire:loading.attr="disabled">
                                    <span wire:loading.remove wire:target="addMember" class="align-self-center">Save</span>
                                    <span wire:loading wire:target="addMember" class="align-self-center">
                                        <span wire:loading wire:target="addMember" class="align-self-center d-flex align-items-center">
                                            <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                                Saving...
                                        </span>
                                    </span>
                                    <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="addMember"></em>
                                </button>
                            </div>
                        </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
