<div>
    <div class="table-responsive mt-5">
        <table class="table table-sm table-hover table-bordered">
            <thead>
                <tr class="bg-secondary">
                    <td colspan="4" 
                        class="text-uppercase border-secondary text-white">
                        RECENTLY ADDED 
                        {{ $schoolType === 'preprimary' ? 'CMC' : ($schoolType === 'primary' ? 'SMC' : 'BOG') }} 
                        MEMBERS
                    </td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">NAMES</th>
                    <th class="text-uppercase border-secondary text-dark">GENDER</th>
                    <th class="text-uppercase border-secondary text-dark">NATIONALITY</th>
                </tr>
            </thead>
            <tbody>
                @foreach($recentlyAddedMembers as $member)
                <tr >
                    <td class="text-uppercase text-dark border-secondary">
                        <div class="user-card">
                            <div class="user-avatar">
                                <img src="{{ $member->person->photo_url}}" class="rounded-0" alt="{{ $member->person->full_name}}">
                            </div>
                            <div class="user-name text-uppercase">
                                <span class="tb-lead cursor text-dark-teal">{{ $member->person->full_name}}</span>
                            </div>
                        </div>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span class="">{{ $member->person->gender === 'M' ? "MALE" : "FEMALE"}}</span>
                    </td>
                    <td class="align-middle text-uppercase text-dark border-secondary">
                        <span>{{ $member->person->country->name}}</span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
