<div class="w-100">
    <livewire:emis-returns.notification-banner />
    @if($schoolType === 'secondary')
        <div class="row mb-3">
            <div class="col-lg-6">
                <div class="form-group">
                    <div class="custom-control custom-control-inline custom-radio">
                        <input type="radio" class="custom-control-input" id="withIndexNumber" value="yes" wire:model="has_index_number" :disabled="$wire.uneb_verify">
                        <label class="custom-control-label text-uppercase" for="withIndexNumber">With Index Number</label>
                    </div>
                    <div class="custom-control custom-control-inline custom-radio">
                        <input type="radio" class="custom-control-input" id="withEquatedCode" value="no" wire:model="has_index_number" :disabled="$wire.uneb_verify">
                        <label class="custom-control-label text-uppercase" for="withEquatedCode">With Equated Code</label>
                    </div>
                </div>
            </div>
        </div>
        <div x-show="!$wire.uneb_verify" class="alert alert-info alert-icon">
            <em class="icon ni ni-alert-circle"></em>
            <h5 class="mb-2"><span class="text-dark-teal">Follow Instructions Below:</span></h5>
            <h6 class="small mb-2"><span class="text-danger">Step 1:</span> Select Class</h6>
            <h6 x-show="$wire.has_index_number === 'yes'" class="small mb-2"><span class="text-danger">Step 2:</span> Select Exam Year</h6>
            <h6 x-show="$wire.has_index_number === 'no'"  class="small mb-2"><span class="text-danger">Step 2:</span> Select Exam Level</h6>
            <h6 x-show="$wire.has_index_number === 'no'"  class="small mb-2"><span class="text-danger">Step 3:</span> Select Equated Year</h6>
            <h6 x-show="$wire.has_index_number === 'yes'" class="small mb-2"><span class="text-danger">Step 3:</span> Enter the Index Number: use the PLE index number for S1 to S4 and the UCE index number for S5 to S6.</h6>
            <h6 x-show="$wire.has_index_number === 'no'"  class="small mb-2"><span class="text-danger">Step 4:</span> Enter Equated Code</h6>
            <h6 class="small mb-2">
                <span x-show="$wire.has_index_number === 'yes'" class="text-danger">Step 4:</span>
                <span x-show="$wire.has_index_number === 'no'" class="text-danger">Step 5:</span>
                Click Verify Button
            </h6>
        </div>
    @endif
    <form wire:submit.prevent="saveLearner">
        @if($schoolType === 'secondary')
            <div x-data="{schoolType: @entangle('schoolType')}" class="row">
                <div class="col-lg-4 mt-lg-0 mt-2">
                    <div class="form-group">
                        <label class="form-label" for="learnerEducationGradeId">Class <span class="text-danger">*</span></label>
                        <select x-data="educationGradeWatcher(@entangle('form_learner.education_grade_id'))" wire:model="form_learner.education_grade_id" :required="schoolType === 'secondary'" :disabled="$wire.uneb_verify" class="form-select form-control bg-primary-dim" id="learnerEducationGradeId">
                            <option value="">--SELECT--</option>
                            @foreach($education_grades as $grade)
                                <option value="{{ $grade->id }}">{{ strtoupper($grade->name) }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div x-show="$wire.has_index_number === 'no'" class="col-lg-2 mt-lg-0 mt-2">
                    <div class="form-group">
                        <label class="form-label" for="learnerExamLevel">Exam Level <span class="text-danger">*</span></label>
                        <select wire:model="form_learner.exam_level" :required="schoolType === 'secondary' && $wire.has_index_number === 'no'" :disabled="$wire.uneb_verify" class="form-select form-control bg-primary-dim" id="learnerExamLevel">
                            <option value="">--SELECT--</option>
                            <option value="PLE">PLE</option>
                            <option value="UCE">UCE</option>
                            <option value="UACE">UACE</option>
                        </select>
                    </div>
                </div>

                <div x-show="$wire.has_index_number === 'yes'" class="col-lg-4 mt-lg-0 mt-2">
                    <div class="form-group">
                        <label class="form-label" for="learnerExamYearId">Select Year When Learner Sat {{ $aLevel ? 'UCE' : 'PLE' }} Exams <span class="text-danger">*</span></label>
                        <select wire:model="form_learner.exam_year" :required="schoolType === 'secondary' && $wire.has_index_number === 'yes'" :disabled="$wire.uneb_verify" class="form-select form-control bg-primary-dim" id="learnerExamYearId">
                            <option value="">--SELECT--</option>
                                @foreach($academicYears as $year)
                                    <option value="{{ $year }}">{{ $year }}</option>
                                @endforeach
                        </select>
                    </div>
                </div>
                <div x-show="$wire.has_index_number === 'no'" class="col-lg-2 mt-lg-0 mt-2">
                    <div class="form-group">
                        <label class="form-label" for="learnerEquatedYearId">Equated Year <span class="text-danger">*</span></label>
                        <select wire:model="form_learner.equated_year" :required="schoolType === 'secondary' && $wire.has_index_number === 'no'" :disabled="$wire.uneb_verify" class="form-select form-control bg-primary-dim" id="learnerEquatedYearId">
                            <option value="">--SELECT--</option>
                            @foreach($academicYears as $year)
                                <option value="{{ $year }}">{{ $year }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div x-show="!$wire.aLevel && $wire.has_index_number === 'yes'" class="col-lg-4 mt-lg-0 mt-3">
                    <div class="form-group">
                        <div class="form-label-group">
                            <label for="learnerPleIndexNumber" class="form-label">PLE Index Number <span class="text-danger">*</span></label>
                        </div>
                        <div class="form-control-group">
                            <div class="input-group">
                                <input type="text" id="learnerPleIndexNumber" class="form-control bg-primary-dim text-uppercase" placeholder="eg. 012345/012" minlength="10" maxlength="10" autocomplete="off" :required="schoolType === 'secondary' && $wire.has_index_number === 'yes'" wire:model.defer="form_learner.index_number" :disabled="$wire.uneb_verify">
                                <div class="input-group-append">
                                    <button type="button" class="btn rounded-right text-white bg-dark-teal" wire:click.prevent="verifyIndexNumber" wire:loading.attr="disabled" wire:target="verifyIndexNumber" :disabled="$wire.form_learner.exam_year === '' || $wire.form_learner.index_number === '' || $wire.uneb_verify">
                                        <span wire:loading wire:target="verifyIndexNumber" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="align-self-center">Verifying...</span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="sr-only">Verifying...</span>
                                        <span wire:loading.remove wire:target="verifyIndexNumber">Verify</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.aLevel && $wire.has_index_number === 'yes'" class="col-lg-4 mt-lg-0 mt-3">
                    <div class="form-group">
                        <div class="form-label-group">
                            <label for="learnerUceIndexNumber" class="form-label">UCE Index Number <span class="text-danger">*</span></label>
                        </div>
                        <div class="form-control-group">
                            <div class="input-group">
                                <input type="text" id="learnerUceIndexNumber" class="form-control bg-primary-dim text-uppercase" placeholder="eg. U0123/012 or U0001/001T" minlength="9" maxlength="10" autocomplete="off" :required="schoolType === 'secondary' && $wire.has_index_number === 'yes'" wire:model.defer="form_learner.index_number" :disabled="$wire.uneb_verify">
                                <div class="input-group-append">
                                    <button type="button" class="btn rounded-right text-white bg-dark-teal" wire:click.prevent="verifyIndexNumber" wire:loading.attr="disabled" wire:target="verifyIndexNumber" :disabled="$wire.form_learner.exam_year === '' || $wire.form_learner.index_number === '' || $wire.uneb_verify">
                                        <span wire:loading wire:target="verifyIndexNumber" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="align-self-center">Verifying...</span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="sr-only">Verifying...</span>
                                        <span wire:loading.remove wire:target="verifyIndexNumber">Verify</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.has_index_number === 'no'" class="col-lg-4 mt-lg-0 mt-3">
                    <div class="form-group">
                        <div class="form-label-group">
                            <label for="learnerEquatedCode" class="form-label">Enter Equated Code <span class="text-danger">*</span></label>
                        </div>
                        <div class="form-control-group">
                            <div class="input-group">
                                <input type="text" id="learnerEquatedCode" class="form-control bg-primary-dim text-uppercase" placeholder="eg. ART/O/0302 or 2020/PLE/PA/0341" minlength="10" autocomplete="off" :required="schoolType === 'secondary' && $wire.has_index_number === 'no'" wire:model.defer="form_learner.equated_code" :disabled="$wire.uneb_verify">
                                <div class="input-group-append">
                                    <button type="button" class="btn rounded-right text-white bg-dark-teal" wire:click.prevent="verifyIndexNumber" wire:loading.attr="disabled" wire:target="verifyIndexNumber" :disabled="$wire.form_learner.exam_level === '' || $wire.form_learner.equated_year === '' || $wire.form_learner.equated_code === '' || $wire.uneb_verify">
                                        <span wire:loading wire:target="verifyIndexNumber" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="align-self-center">Verifying...</span>
                                        <span wire:loading wire:target="verifyIndexNumber" class="sr-only">Verifying...</span>
                                        <span wire:loading.remove wire:target="verifyIndexNumber">Verify</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        <div x-show="$wire.schoolType !== 'secondary' || ($wire.schoolType === 'secondary' && $wire.uneb_verify)" class="row mt-4">
            <div class="col-lg-4 col-md-12">
                <div class="form-group text-center">
                    <div class="text-center mb-4">
                        <img id="photoPreview"
                         src="@if($photo){{ $photo->temporaryUrl() }}@else{{ (isset($form_learner['gender']) && $form_learner['gender'] === 'F') ? asset('/images/default_female.jpg') : asset('/images/default_male.jpg') }}@endif"
                         alt="Photo preview"
                         style="width: 200px; height: 200px; object-fit: cover; border-radius: 10px; margin-bottom: 15px;"
                        />
                        <input wire:model="photo" accept="image/x-png,image/jpeg" id="photoInput" type="file" class="d-none" onchange="handlePhotoChange(this)" />
                        <div class="d-flex flex-column justify-content-center">
                            @if($photo)
                                <button type="button" id="photoButton" class="align-self-center btn btn-primary" wire:click="removePhoto">
                                    <em id="photoIcon" class="icon ni ni-trash"></em>
                                    <span id="photoButtonText">Remove Photo</span>
                                </button>
                            @else
                                <button type="button" id="photoButton" class="align-self-center btn btn-primary" :disabled="$wire.learner_nin === 'yes'" onclick="document.getElementById('photoInput').click();">
                                    <em id="photoIcon" class="icon ni ni-camera-fill"></em>
                                    <span id="photoButtonText">Choose Photo</span>
                                </button>
                            @endif
                        </div>
                        @error('photo')
                            <span class="text-danger d-block mt-2">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>
            <div class="col-lg-8 col-md-12 pb-4 overflow-auto scrollbar-dark-teal h-425px">
                <h6 x-show="$wire.schoolType !== 'secondary' || ($wire.schoolType === 'secondary' && $wire.uneb_verify)" class="overline-title title text-dark-teal">LEARNER DETAILS</h6>
                <div x-show="$wire.schoolType !== 'secondary' || ($wire.schoolType === 'secondary' && $wire.uneb_verify)" class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label class="form-label">Does this learner have a NIN?</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" id="learnerWithNIN" value="yes" wire:model="learner_nin" :disabled="$wire.learner_verify || $wire.parent_verify || !$wire.uganda">
                                <label class="custom-control-label text-uppercase" for="learnerWithNIN">YES</label>
                            </div>
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" id="learnerWithoutNIN" value="no" wire:model="learner_nin" :disabled="$wire.learner_verify || $wire.parent_verify || !$wire.uganda">
                                <label class="custom-control-label text-uppercase" for="learnerWithoutNIN">NO</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'yes'" class="row">
                    <div class="col-12">
                        <div x-data="{ learnerNin: @entangle('form_learner.nin') }" class="form-group">
                            <div class="form-label-group">
                                <label for="learnerNIN" class="form-label">Learner NIN <span class="text-danger">*</span></label>
                            </div>
                            <div class="form-control-group">
                                <div class="input-group">
                                    <input x-model="learnerNin" id="learnerNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off" :disabled="$wire.learner_verify || $wire.loading" :required="$wire.learner_nin === 'yes'">
                                    <div class="input-group-append">
                                        <button wire:click.prevent="verifyLearnerNIN" wire:loading.attr="disabled" wire:target="verifyLearnerNIN" class="btn rounded-right text-white bg-dark-teal" type="button" :disabled="learnerNin.length !== 14 || $wire.learner_verify">
                                            <span wire:loading wire:target="verifyLearnerNIN" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span wire:loading wire:target="verifyLearnerNIN" class="align-self-center">Verifying...</span>
                                            <span wire:loading wire:target="verifyLearnerNIN" class="sr-only">Verifying...</span>
                                            <span wire:loading.remove wire:target="verifyLearnerNIN">Verify Learner NIN</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{--                @endif--}}
                <hr x-show="$wire.learner_nin === 'no' && $wire.learner_verify" class="border-dark-teal my-2">
                <div x-show="$wire.learner_verify" class="row">
                    <div class="col-12 mt-lg-0 mt-3">
                        <div class="table-responsive py-3">
                            <table class="table table-sm">
                                <tr>
                                    <td class="px-2 align-middle border-dark-teal border-1 text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">NAME</h6>
                                        <span>{{ $nira_learner['surname'] ?? '' }} {{ $nira_learner['given_names'] ?? '' }}</span>
                                    </td>
                                    <td class="px-2 align-middle border-dark-teal border-1 text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                        <span>{{ $nira_learner['national_id'] ?? '' }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle border-bottom border-dark-teal border-1 text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                        @if(($nira_learner['gender'] ?? '') === 'M')
                                            <span>MALE</span>
                                        @elseif(($nira_learner['gender'] ?? '') === 'F')
                                            <span>FEMALE</span>
                                        @endif
                                    </td>
                                    <td class="px-2 align-middle border-bottom border-dark-teal border-1 text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">DATE OF BIRTH</h6>
                                        <span>{{ $nira_learner['date_of_birth'] ?? '' }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'no'" class="row mt-3">
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label" for="learnerCountryId">Learner Nationality <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <select x-data="countryWatcher(@entangle('form_learner.country_id'))" wire:model="form_learner.country_id" :required="$wire.learner_nin === 'no'" :disabled="$wire.uneb_verify" id="learnerCountryId" data-search="on" class="form-select form-control bg-primary-dim">
                                    <option value="">--SELECT--</option>
                                    @foreach($countries as $country)
                                        <option value="{{ $country->id }}" @if($learner_refugee_no === 'yes' && $form_learner['country_id'] == $country->id && strtoupper($country->name) === 'UGANDA') disabled @endif>
                                            {{ strtoupper($country->name) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div x-show="!$wire.uganda && $wire.learner_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                        <div class="form-group">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerStudentNumber" class="form-label">Student Pass <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input
                                        wire:model.defer="form_learner.student_pass"
                                        pattern="^(S|s)(T|t)[0-9]{7}$"
                                        :required="!$wire.uganda && $wire.learner_nin === 'no' && $wire.learner_refugee_no === 'no'"
                                        id="learnerStudentNumber"
                                        minlength="9"
                                        maxlength="9"
                                        type="text"
                                        title="Student Pass Format ST0011223"
                                        placeholder="eg. ST0011223"
                                        class="form-control bg-primary-dim text-uppercase"
                                        autocomplete="off"
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                    <div x-show="!$wire.uganda && $wire.learner_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                        <div class="form-group">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerRefugeeNumber" class="form-label">Learner Refugee Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input
                                        wire:model.defer="form_learner.student_refugee_number"
                                        :required="!$wire.uganda && $wire.learner_nin === 'no' && $wire.learner_refugee_no === 'yes'"
                                        id="learnerRefugeeNumber"
                                        minlength="12"
                                        type="text"
                                        title="Learner Refugee Number"
                                        placeholder="eg. RN1-10011223"
                                        class="form-control bg-primary-dim text-uppercase"
                                        autocomplete="off"
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'no'" class="row mt-3">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label class="form-label">Is this learner a refugee?</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" id="learnerIsRefuge" value="yes" wire:model.defer="learner_refugee_no" :disabled="$wire.learner_verify || $wire.parent_verify || $wire.uganda">
                                <label class="custom-control-label text-uppercase" for="learnerIsRefuge">YES</label>
                            </div>
                            <div class="custom-control custom-control-inline custom-radio">
                                <input type="radio" class="custom-control-input" id="learnerIsNotRefugee" value="no" wire:model.defer="learner_refugee_no" :disabled="$wire.learner_verify || $wire.parent_verify || $wire.uganda">
                                <label class="custom-control-label text-uppercase" for="learnerIsNotRefugee">NO</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'no'" class="row mt-3">
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.first_name" :required="$wire.learner_nin === 'no'" :disabled="$wire.uneb_verify" id="learnerFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerSurname" class="form-label">Surname <span class="text-danger">*</span></label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.surname" :required="$wire.learner_nin === 'no'" :disabled="$wire.uneb_verify" id="learnerSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'no'" class="row mt-3">
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerOtherNames" class="form-label">Other Names</label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.other_names" :disabled="$wire.uneb_verify" id="learnerOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label" for="learnerBirthDate">Date Of Birth <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <input
                                    wire:model="form_learner.birth_date"
                                    id="learnerBirthDate"
                                    type="date"
                                    class="form-control bg-primary-dim"
                                    autocomplete="off"
                                    :disabled="($wire.schoolType === 'secondary' && $wire.has_index_number === 'yes') || $wire.learner_verify"
                                    :required="$wire.learner_nin === 'no'"
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div x-show="$wire.learner_nin === 'no'"  class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label class="form-label">Sex <span class="text-danger">*</span></label>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" class="custom-control-input" id="learnerMale" value="M" wire:model.defer="form_learner.gender" :disabled="($wire.schoolType === 'secondary' && $wire.has_index_number === 'yes') || $wire.learner_verify" wire:change="updateDefaultPhoto">
                                    <label class="custom-control-label text-uppercase" for="learnerMale">Male</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" class="custom-control-input" id="learnerFemale" value="F" wire:model.defer="form_learner.gender" :disabled="($wire.schoolType === 'secondary' && $wire.has_index_number === 'yes') || $wire.learner_verify" wire:change="updateDefaultPhoto">
                                    <label class="custom-control-label text-uppercase" for="learnerFemale">Female</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div x-show="$wire.learner_nin === 'no' || ($wire.uneb_verify && $wire.learner_verify)"  class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label class="form-label">Is learner an orphan?</label>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" class="custom-control-input" id="learnerIsOrphanYes" value="yes" wire:model.defer="form_learner.is_orphan">
                                    <label class="custom-control-label text-uppercase" for="learnerIsOrphanYes">YES</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" class="custom-control-input" id="learnerIsOrphanNo" value="no" wire:model.defer="form_learner.is_orphan">
                                    <label class="custom-control-label text-uppercase" for="learnerIsOrphanNo">NO</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row my-3">
                    <div x-show="$wire.form_learner['is_orphan'] === 'yes'" class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label" for="learnerOrphanType">Type Of Orphan <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <select wire:model.defer="form_learner.orphan_type" :required="$wire.form_learner['is_orphan'] === 'yes'" id="learnerOrphanType" data-placeholder="--SELECT--" class="form-select form-control bg-primary-dim">
                                    <option value="">--SELECT--</option>
                                    <option value="father">ONLY FATHER DEAD</option>
                                    <option value="mother">ONLY MOTHER DEAD</option>
                                    <option value="both-dead">BOTH PARENTS DEAD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    @if($schoolType !== 'secondary' && $schoolType !== 'international')
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="learnerEducationGradeId">
                                    {{ $this->gradeLabel }} <span class="text-danger">*</span>
                                </label>
                                <div class="form-control-wrap">
                                    <select wire:model.defer="form_learner.education_grade_id" :required="$wire.schoolType !== 'secondary' && $wire.schoolType !== 'international'" id="learnerEducationGradeId" data-search="on" data-placeholder="--SELECT--" class="form-select form-control bg-primary-dim">
                                        <option value="">--SELECT--</option>
                                        @foreach($education_grades as $grade)
                                            <option value="{{ $grade->id }}">{{ strtoupper($grade->name) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                @if($schoolType === 'international')
                    <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row my-3">
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label">Calendar <span class="text-danger">*</span></label>
                                <select wire:model.defer="form_learner.inter_sch_calendar_id" :required="$wire.learner_verify && $wire.schoolType === 'international'" id="learnerCalendarId" data-placeholder="--SELECT--" class="form-select form-control bg-primary-dim">
                                    <option value="">--SELECT--</option>
                                    @foreach($international_calendars as $calendar)
                                        <option value="{{ $calendar->id }}">{{ strtoupper($calendar->name) }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="col-lg-6 mt-lg-0 mt-3" x-data="curriculumSelector()">
                            <div class="form-group">
                                <label class="form-label">Curriculum <span class="text-danger">*</span></label>
                                <select x-model="selectedCurriculum" @change="handleCurriculumChange()" :required="$wire.learner_verify && $wire.schoolType === 'international'" id="learnerCurriculumId" class="form-select form-control bg-primary-dim">
                                    <option value="">--SELECT--</option>
                                    @foreach($international_curriculums as $curriculum)
                                        <option value="{{ $curriculum->id }}">{{ strtoupper($curriculum->name) }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row my-3">
                        <div class="col-lg-6 mt-lg-0 mt-3" x-data="gradeSelector()">
                            <div class="form-group">
                                <label class="form-label" for="learnerEducationGradeId">
                                    Grade <span class="text-danger">*</span>
                                </label>
                                <div class="form-control-wrap">
                                    <select x-model="selectedGrade" @change="handleGradeChange()" :required="$wire.schoolType === 'international'" id="learnerEducationGradeIdInternational" class="form-select form-control bg-primary-dim">
                                        <option value="">--SELECT--</option>
                                        @foreach($international_grades as $grade)
                                            <option value="{{ $grade['id'] }}" data-education-level="{{ $grade['inter_sch_education_level_id'] ?? '' }}">{{ strtoupper($grade['name']) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row my-3">
                    @if(in_array($schoolType, ['certificate', 'diploma', 'degree']))
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label class="form-label">Is student offering an examinable course?</label>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" wire:model.defer="form_learner.is_offering_examinable_course" value="yes" id="learnerExaminableYes">
                                        <label class="custom-control-label text-uppercase" for="learnerExaminableYes">YES</label>
                                    </div>
                                    <div class="custom-control custom-control-inline custom-radio">
                                        <input type="radio" class="custom-control-input" wire:model.defer="form_learner.is_offering_examinable_course" value="no" id="learnerExaminableNo">
                                        <label class="custom-control-label text-uppercase" for="learnerExaminableNo">NO</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Show if 'yes' -->
                        <div x-show="$wire.form_learner.is_offering_examinable_course === 'yes'" x-transition class="col-lg-6 mt-lg-0 mt-3">
                            <div x-data="{courses: @js($school['certificate_institution_courses']), selectedCourse: @entangle('form_learner.post_primary_institution_course_id')}" class="form-group">
                                <label class="form-label" for="examCourseId">Examinable Course <span class="text-danger">*</span></label>
                                <select x-model="selectedCourse" :required="$wire.form_learner.is_offering_examinable_course === 'yes'" id="examCourseId" class="form-select form-control bg-primary-dim" data-search="on" data-placeholder="--SELECT--">
                                    <option value="">--SELECT--</option>
                                    <template x-for="course in courses" :key="course.post_primary_institution_course_id">
                                        <option
                                            :value="course.post_primary_institution_course_id"
                                            x-text="course.post_primary_institution_course ? course.post_primary_institution_course.name : 'Unknown Course'">
                                        </option>
                                    </template>
                                </select>
                            </div>
                        </div>
                        <!-- Show if 'no' -->
                        <div x-show="$wire.form_learner.is_offering_examinable_course === 'no'" x-transition class="col-lg-6 mt-lg-0 mt-3">
                            <div x-data="{courses: @js($school['institution_examined_courses']), selectedCourse: @entangle('form_learner.institution_examined_course_id')}" class="form-group">
                                <label class="form-label" for="nonExamCourseId">Non Examinable Course <span class="text-danger">*</span></label>
                                <select x-model="selectedCourse" :required="$wire.form_learner.is_offering_examinable_course === 'no'" id="nonExamCourseId" class="form-select form-control bg-primary-dim" data-search="on" data-placeholder="--SELECT--">
                                    <option value="">--SELECT--</option>
                                    <template x-for="course in courses" :key="course.id">
                                        <option :value="course.id" x-text="course ? course.name : 'Unknown Course'"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                    @endif
                </div>

                <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row mt-3">
                    <div x-show="['preprimary', 'primary', 'secondary'].includes($wire.schoolType)" class="col-lg-6 mt-lg-0 mt-3">
                        <div x-data="{languages: @js($familiar_languages),selectedLanguage: @entangle('form_learner.familiar_language_id')}" class="form-group">
                            <label class="form-label" for="learnerFamiliarLanguageId">Familiar Language <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <select x-model="selectedLanguage" :required="$wire.learner_verify && ['preprimary', 'primary', 'secondary'].includes($wire.schoolType)" id="learnerFamiliarLanguageId" class="form-select form-control bg-primary-dim" data-search="on" data-placeholder="--SELECT--">
                                    <option value="">--SELECT--</option>
                                    <template x-for="language in languages" :key="language.id">
                                        <option :value="language.id" x-text="language.name"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div x-show="$wire.uganda && ($wire.schoolType === 'secondary' || $wire.schoolType === 'primary' || $wire.schoolType === 'preprimary') || $wire.schoolType === 'international'" class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <label class="form-label" for="learnerDistrictOfBirthId">District Of Birth <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <select
                                    wire:model.defer="form_learner.district_of_birth_id"
                                    :required="$wire.uganda && $wire.learner_verify && ($wire.schoolType === 'secondary' || $wire.schoolType === 'primary' || $wire.schoolType === 'preprimary')"
                                    id="learnerDistrictOfBirthId"
                                    data-search="on"
                                    data-placeholder="--SELECT--"
                                    class="form-select form-control bg-primary-dim"
                                >
                                    <option value="">--SELECT--</option>
                                    @foreach($districts as $district)
                                        <option value="{{ $district->id }}">{{ strtoupper($district->name) }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                @if($schoolType === 'secondary')
                    <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" x-data="subjectFunctions()" x-init="principalSubjects = @js($principalSubjects); subsidiarySubjects = @js($subsidiarySubjects)" class="row mt-3">
                        @if($aLevel)
                            <div class="col-12">
                                <h6 class="overline-title title text-dark-teal">Subject Combination</h6>
                                <div class="form-group">
                                    {{-- Principal Subjects --}}
                                    <template x-for="subject in principalSubjects" :key="subject.id">
                                        <div class="custom-control custom-control-sm custom-checkbox d-block">
                                            <input wire:model.defer="form_learner.principal_subjects" :value="subject.id" type="checkbox" :checked="isChecked('principal', subject.id)" @change="toggle('principal', subject.id)" :disabled="isDisabled('principal', subject.id, 3)" :id="`learnerSubject${subject.id}`" class="custom-control-input"/>
                                            <label class="custom-control-label" :for="`learnerSubject${subject.id}`" x-text="subject.name.toUpperCase()"></label>
                                        </div>
                                    </template>

                                    <hr class="border-dark-teal my-1">
                                    {{-- Subsidiary Subjects --}}
                                    <template x-for="subject in subsidiarySubjects" :key="subject.id">
                                        <div class="custom-control custom-control-sm custom-checkbox d-block">
                                            <input wire:model.defer="form_learner.subsidiary_subject" :value="subject.id" type="checkbox" :checked="isChecked('subsidiary', subject.id)" @change="toggle('subsidiary', subject.id)" :disabled="isDisabled('subsidiary', subject.id, 1)" :id="`learnerSubjectSub${subject.id}`" class="custom-control-input"/>
                                            <label class="custom-control-label" :for="`learnerSubjectSub${subject.id}`" x-text="subject.name.toUpperCase()"></label>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        @endif
                    </div>
                @endif
                <hr x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="border-dark-teal my-4 mt-3">
                <h6 x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="overline-title title text-dark-teal">SPECIAL NEEDS</h6>
                <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row">
                    <div class="col-12">
                        <div x-data="{special_needs: @js($special_needs),selectedSpecialNeeds: @entangle('form_learner.learner_special_needs')}" class="form-group">
                            <template x-for="special_need in special_needs" :key="special_need.id">
                                <div class="custom-control custom-control-sm custom-checkbox d-block">
                                    <input type="checkbox" class="custom-control-input" :id="`learnerSpecialNeed${special_need.id}`" :value="special_need.id" x-model="selectedSpecialNeeds">
                                    <label class="custom-control-label" :for="`learnerSpecialNeed${special_need.id}`" x-text="special_need.name.toUpperCase()"></label>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                <hr x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="border-dark-teal my-4">
                <h6 x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="overline-title title text-dark-teal">HEALTH ISSUES</h6>
                <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row">
                    <div class="col-12">
                        <div x-data="{health_issues: @js($health_issues),selectedHealthIssues: @entangle('form_learner.learner_health_issues')}"  class="form-group">
                            <template x-for="issue in health_issues" :key="issue.id">
                                <div class="custom-control custom-control-sm custom-checkbox d-block">
                                    <input type="checkbox" class="custom-control-input" :id="`learnerHealthIssue${issue.id}`" :value="issue.id" x-model="selectedHealthIssues">
                                    <label class="custom-control-label" :for="`learnerHealthIssue${issue.id}`" x-text="issue.name.toUpperCase()"></label>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                @if($schoolType !== 'preprimary')
                    <hr x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="border-dark-teal my-4">
                    <h6 x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="overline-title title text-dark-teal">TALENTS</h6>
                    <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row">
                        <div class="col-12">
                            <div x-data="{talents: @js($talents),selectedTalents: @entangle('form_learner.learner_talents')}" class="form-group">
                                <template x-for="talent in talents" :key="talent.id">
                                    <div class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input type="checkbox" class="custom-control-input" :id="`learnerTalent${talent.id}`" :value="talent.id" x-model="selectedTalents">
                                        <label class="custom-control-label" :for="`learnerTalent${talent.id}`" x-text="talent.name.toUpperCase()"></label>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    @if(in_array($schoolType, ['primary', 'secondary']))
                    <hr x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="border-dark-teal my-4">
                    <h6 x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="overline-title title text-dark-teal">PRACTICAL SKILLS</h6>
                    <div x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="row">
                        <div class="col-12">
                            <div x-data="{skills: @js($practical_skills),selectedSkills: @entangle('form_learner.learner_practical_skills')}" class="form-group">
                                <template x-for="skill in skills" :key="skill.id">
                                    <div class="custom-control custom-control-sm custom-checkbox d-block">
                                        <input type="checkbox" class="custom-control-input" :id="`learnerPracticalSkill${skill.id}`" :value="skill.id" x-model="selectedSkills">
                                        <label class="custom-control-label" :for="`learnerPracticalSkill${skill.id}`" x-text="skill.name.toUpperCase()"></label>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                    @endif
                @endif
                <hr x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="border-dark-teal my-4">
                <h6 x-show="$wire.learner_nin === 'no' || $wire.learner_verify" class="overline-title title text-dark-teal">{{ $this->parentLabel }} DETAILS</h6>
                <div x-show="($wire.learner_nin === 'yes' && $wire.learner_verify) || ($wire.learner_nin === 'no' && $wire.uganda)" class="row">
                    <div class="col-12">
                        <div x-data="{ parentNin: @entangle('form_learner.parent_nin') }" class="form-group">
                            <div class="form-label-group">
                                <label for="learnerParentNIN" class="form-label">
                                    {{ $this->parentLabel }} NIN <span class="text-danger">*</span>
                                </label>
                            </div>
                            <div class="form-control-group">
                                <div class="input-group">
                                    <input x-model="parentNin" id="learnerParentNIN" minlength="14" maxlength="14" type="text" placeholder="eg. CM001122334455" class="form-control bg-primary-dim text-center text-uppercase" autocomplete="off" :disabled="$wire.parent_verify || $wire.loading" :required="$wire.learner_nin === 'yes' && $wire.learner_verify">
                                    <div class="input-group-append">
                                        <button wire:click.prevent="verifyParentNIN" wire:loading.attr="disabled" wire:target="verifyParentNIN" class="btn rounded-right text-white bg-dark-teal" type="button" :disabled="parentNin.length !== 14 || $wire.parent_verify">
                                            <span wire:loading wire:target="verifyParentNIN" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                            <span wire:loading wire:target="verifyParentNIN" class="align-self-center">Verifying...</span>
                                            <span wire:loading wire:target="verifyParentNIN" class="sr-only">Verifying...</span>
                                            <span wire:loading.remove wire:target="verifyParentNIN">Verify {{ $this->parentLabel }} NIN</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="($wire.learner_nin === 'yes' && $wire.learner_verify && $wire.parent_verify) || ($wire.learner_nin === 'no' && $wire.uganda && $wire.parent_verify)" class="row">
                    <div class="col-12">
                        <div class="table-responsive py-3">
                            <table class="table table-sm table-hover">
                                <tr>
                                    <td rowspan="5" class="align-middle text-uppercase text-dark text-center w-175px">
                                        <div class="user-card">
                                            <div class="w-150px">
                                                <img src="{{ $form_learner['parent_photo'] ?? asset('images/default_male.jpg') }}" class="rounded-0" alt="contact person photo">
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-2 align-middle text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">NIN</h6>
                                        <span>{{ $nira_parent['national_id'] ?? '' }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">SURNAME</h6>
                                        <span>{{ $nira_parent['surname'] ?? '' }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">GIVEN NAME</h6>
                                        <span>{{ $nira_parent['given_names'] ?? '' }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-2 align-middle text-uppercase text-dark">
                                        <h6 class="overline-title mb-0 text-dark-teal">SEX</h6>
                                        <span>{{ $nira_parent['gender'] ?? '' }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div x-show="!$wire.uganda && ($wire.learner_nin === 'no' || $wire.learner_verify)" class="row">
                    <div x-show="!$wire.uganda && $wire.learner_refugee_no === 'no'" class="col-lg-6 mt-lg-0">
                        <div class="form-group">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentPassport" class="form-label">Parent Passport <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <div class="input-group">
                                        <input wire:model.defer="form_learner.parent_passport" :required="$wire.learner_nin === 'no' && $wire.learner_refugee_no === 'no' && !$wire.parent_verify" :disabled="$wire.parent_verify" id="learnerParentPassport" maxlength="9" type="text" placeholder="eg. B03373323" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn rounded-right text-white bg-dark-teal" wire:click.prevent="verifyPassport" wire:loading.attr="disabled" wire:target="verifyPassport" :disabled="$wire.form_learner.parent_passport === '' || $wire.parent_verify">
                                                <span wire:loading wire:target="verifyPassport" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span wire:loading wire:target="verifyPassport" class="align-self-center">Verifying...</span>
                                                <span wire:loading wire:target="verifyPassport" class="sr-only">Verifying...</span>
                                                <span wire:loading.remove wire:target="verifyPassport">Verify</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div x-show="!$wire.uganda && $wire.learner_nin === 'no' && $wire.learner_refugee_no === 'yes'" class="col-lg-6 mt-lg-0">
                        <div class="form-group">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="learnerParentRefugeeNumber" class="form-label">Parent Refugee Number <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <div class="input-group">
                                        <input wire:model.defer="form_learner.parent_refugee_number" :required="$wire.learner_nin === 'no' && $wire.learner_refugee_no === 'yes' && !$wire.parent_verify" :disabled="$wire.parent_verify" id="learnerParentRefugeeNumber" minlength="12" type="text" title="Learner Refugee Number" placeholder="eg. RN1-10011223" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn rounded-right text-white bg-dark-teal" wire:click.prevent="verifyRefugeeNumber" wire:loading.attr="disabled" wire:target="verifyRefugeeNumber" :disabled="$wire.form_learner.parent_refugee_number === '' || $wire.parent_verify">
                                                <span wire:loading wire:target="verifyRefugeeNumber" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                <span wire:loading wire:target="verifyRefugeeNumber" class="align-self-center">Verifying...</span>
                                                <span wire:loading wire:target="verifyRefugeeNumber" class="sr-only">Verifying...</span>
                                                <span wire:loading.remove wire:target="verifyRefugeeNumber">Verify</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-lg-0">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerParentFirstName" class="form-label">First Name <span @if(!$uganda) class="text-danger" @endif>*</span></label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.parent_first_name" :disabled="$wire.parent_verify" :required="!$wire.uganda && $wire.learner_nin === 'no' && $wire.learner_verify" id="learnerParentFirstName" type="text" placeholder="eg. Bill" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div x-show="!$wire.uganda && ($wire.learner_nin === 'no' || $wire.learner_verify)" class="row mt-3">
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerParentSurname" class="form-label">Surname <span x-show="!$wire.uganda" class="text-danger">*</span></label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.parent_surname" :disabled="$wire.parent_verify" :required="!$wire.uganda && $wire.learner_nin === 'no' && $wire.learner_verify" id="learnerParentSurname" type="text" placeholder="eg. Clinton" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mt-lg-0 mt-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label for="learnerParentOtherNames" class="form-label">Other Names</label>
                            </div>
                            <div class="form-control-group">
                                <input wire:model.defer="form_learner.parent_other_names" :disabled="$wire.parent_verify" id="learnerParentOtherNames" type="text" placeholder="eg. Other" class="form-control bg-primary-dim text-uppercase" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div x-show="!$wire.uganda && ($wire.learner_nin === 'no' || $wire.learner_verify)" class="col-lg-6 mt-lg-0 mt-3 mb-3">
                        <div class="form-group">
                            <label class="form-label" for="parentBirthDate">Date Of Birth <span class="text-danger">*</span></label>
                            <div class="form-control-wrap">
                                <input wire:model="form_learner.parent_birth_date" id="parentBirthDate" type="date" class="form-control bg-primary-dim" data-date-format="dd-mm-yyyy" placeholder="eg. 14 APRIL, 2022" autocomplete="off" :readonly="$wire.parent_verify" :required="$wire.learner_nin === 'no'">
                            </div>
                        </div>
                    </div>
                    <div x-show="!$wire.uganda && ($wire.learner_nin === 'no' || $wire.learner_verify)"  class="col-lg-6 mt-lg-0 mt-3 mb-3">
                        <div class="form-group">
                            <div class="form-label-group">
                                <label class="form-label">Sex <span class="text-danger">*</span></label>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input wire:model.defer="form_learner.parent_gender" :disabled="$wire.parent_verify" type="radio" class="custom-control-input" value="M" id="learnerParentMale">
                                    <label class="custom-control-label text-uppercase" for="learnerParentMale">Male</label>
                                </div>
                                <div class="custom-control custom-control-inline custom-radio">
                                    <input wire:model="form_learner.parent_gender" :disabled="$wire.parent_verify" type="radio" class="custom-control-input" value="F" id="learnerParentFemale">
                                    <label class="custom-control-label text-uppercase" for="learnerParentFemale">Female</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if ($parent_verify)
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <label class="form-label" for="learnerParentRelationship">Relationship <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <select wire:model.defer="form_learner.parent_relationship" :required="$wire.learner_verify || $wire.parent_verify" id="learnerParentRelationship" class="form-select form-control bg-primary-dim" data-placeholder="--SELECT--">
                                        <option value="">--SELECT--</option>
                                        <option value="parent">PARENT</option>
                                        <option value="guardian">GUARDIAN</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-0 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentPhoneOne" class="form-label">Phone Number 1 <span class="text-danger">*</span></label>
                                </div>
                                <div class="form-control-group">
                                    <input wire:model.defer="form_learner.parent_phone_1" :required="$wire.parent_verify" id="parentPhoneOne" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-2 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentPhoneTwo" class="form-label">Phone Number 2</label>
                                </div>
                                <div class="form-control-group">
                                    <input wire:model.defer="form_learner.parent_phone_2" id="parentPhoneTwo" maxlength="10" type="text" placeholder="Enter Phone Number" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mt-lg-2 mt-3">
                            <div class="form-group">
                                <div class="form-label-group">
                                    <label for="parentEmail" class="form-label">Email Address</label>
                                </div>
                                <div class="form-control-group">
                                    <input wire:model.defer="form_learner.parent_email" id="parentEmail" type="email" placeholder="Enter Email Address" class="form-control bg-primary-dim" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="d-flex pt-3 mt-2 border-top border-dark-teal justify-content-center">
            <button wire:click="resetForm" :disabled="$wire.schoolType === 'secondary' && !$wire.uneb_verify || $wire.loading" wire:loading.attr="disabled" wire:target="saveLearner,verifyLearnerNIN,verifyParentNIN,verifyIndexNumber,verifyPassportRefugeeId" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                <em class="icon ni ni-cross"></em>
                <span>Cancel</span>
            </button>
            <button wire:click="saveLearnerDraft" type="button" :disabled="($wire.schoolType === 'secondary' && !$wire.uneb_verify) || !$wire.parent_verify" wire:loading.attr="disabled" wire:target="saveLearnerDraft" class="btn btn-primary d-flex mr-2">
                <span wire:loading wire:target="saveLearnerDraft" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span wire:loading wire:target="saveLearnerDraft" class="align-self-center">Saving...</span>
                <span wire:loading wire:target="saveLearnerDraft" class="sr-only">Saving...</span>
                <span wire:loading.remove wire:target="saveLearnerDraft" class="align-self-center">Save As Draft</span>
                <em wire:loading.remove wire:target="saveLearnerDraft" class="icon ni ni-edit ml-1"></em>
            </button>
            <button type="submit" :disabled="($wire.schoolType === 'secondary' && !$wire.uneb_verify) || !$wire.parent_verify" class="btn bg-dark-teal d-flex" wire:loading.attr="disabled" wire:target="saveLearner">
                <span wire:loading wire:target="saveLearner" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span wire:loading wire:target="saveLearner" class="align-self-center">Submitting...</span>
                <span wire:loading wire:target="saveLearner" class="sr-only">Submitting...</span>
                <span wire:loading.remove wire:target="saveLearner" class="align-self-center">Submit</span>
                <em wire:loading.remove wire:target="saveLearner" class="ni ni-arrow-right ml-1"></em>
            </button>
        </div>
    </form>
</div>
