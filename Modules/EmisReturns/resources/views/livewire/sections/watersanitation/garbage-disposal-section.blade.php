<div>
    <div class="mt-4">
        
        <livewire:emis-returns.notification-banner :eventName="'notifyGarbage'" />
    </div>
    <div class="nk-block-head nk-block-head-lg mt-1">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h4 class="nk-block-title ">Garbage Disposal Method</h4>
            </div>
        </div>

    </div>
    <div style="margin-top:-30px;" class="data-item py-1 mb-4 ">
        <div class="data-col">
            <span style="font-size: initial ">Main Garbage Disposal Method</span>
            <span class="ml-lg-5 data-value {{ $this->LatestMethodName === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                {{ $this->LatestMethodName }}
            </span>


        </div>
        <div class="data-col data-col-end">
            <button type="submit" data-target="#garbageDisposalMethodModal" data-toggle="modal" class="btn btn-sm bg-dark-teal">
                <em class="ni ni-edit-fill text-white mr-1"></em>Update
            </button>
        </div>
    </div><!-- data-item -->


    <!-- garbage disposal modal -->
    <div class="modal fade zoom" tabindex="-1" id="garbageDisposalMethodModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a href="#" class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="updateGarbageDisposalMethod">
                    <div class="modal-header">
                        <h6 class="modal-title">Update Garbage Disposal Method</h6>
                    </div>
                    <div class="modal-body">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label" for="garbageDisposalMethodId">
                                    How is garbage finally disposed off ?
                                </label>
                                <span class="form-note">Specify the method used.</span>
                            </div>

                            <div class="form-group position-relative">
                                <div class="form-control-wrap">
                                    <select required wire:model="garbage_disposal_method_id" class=" text-uppercase max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                        <option value="">-- Select --</option>
                                        @foreach($garbage_disposal_method_names as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                        @endforeach
                                    </select>

                                    <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                        <em class="icon ni ni-chevron-down text-muted"></em>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex" wire:target="updateGarbageDisposalMethod" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateGarbageDisposalMethod" class="align-self-center">Save</span>
                            <span wire:loading wire:target="updateGarbageDisposalMethod" class="align-self-center">
                                <span wire:loading wire:target="updateGarbageDisposalMethod" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateGarbageDisposalMethod"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- garbage disposal  modal -->