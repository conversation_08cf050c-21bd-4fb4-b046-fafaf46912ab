<div>

    <livewire:emis-returns.notification-banner :eventName="'notifyHandWashing'" />

    <div class="nk-block-head nk-block-head-lg">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h4 class="nk-block-title ">Hand Washing Facilities</h4>
            </div>
        </div>

    </div>
    <div class="data-item py-1 mb-4">
        <div class="data-col">
            <span style="font-size: initial">Functional hand washing facility near or in the toilet/latrine</span>
            <span class="ml-lg-5 data-value {{ $this->LatestMethodName === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                {{ $this->LatestMethodName }}
            </span>


        </div>
        <div class="data-col data-col-end">
            <button type="submit" data-target="#handWashingMethodModal" data-toggle="modal" class="btn btn-sm bg-dark-teal">
                <em class="ni ni-edit-fill text-white mr-1"></em>Update
            </button>
        </div>
    </div><!-- data-item -->


    <!-- hand wash modal -->
    <div class="modal fade zoom" tabindex="-1" id="handWashingMethodModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a href="#" class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="updateHandWashingMethod">
                    <div class="modal-header">
                        <h6 class="modal-title">Update Hand Washing Method</h6>
                    </div>
                    <div class="modal-body">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label" for="handWashMethodId">
                                    Does this School have a functional hand washing facility near or in the toilet/latrine?
                                </label>
                                <span class="form-note">Specify the method used.</span>
                            </div>

                            <div class="form-group position-relative">
                                <div class="form-control-wrap">
                                    <select required wire:model="hand_washing_method_id" class=" text-uppercase form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                        <option value="">-- Select --</option>
                                        @foreach($hand_washing_method_names as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                        @endforeach
                                    </select>

                                    <span class="position-absolute" style="top: 50%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                        <em class="icon ni ni-chevron-down text-muted"></em>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex" wire:target="updateHandWashingMethod" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateHandWashingMethod" class="align-self-center">Save</span>
                            <span wire:loading wire:target="updateHandWashingMethod" class="align-self-center">
                                <span wire:loading wire:target="updateHandWashingMethod" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateHandWashingMethod"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- hand wash modal -->

    <div class="table-responsive">
        <table class="table border border-dark-teal">
            <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Facility</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
            </thead>
            <tbody class="border-top-0 border-secondary">
                @foreach ($facilityNames as $id => $name)
                <tr>
                    <td class="align-middle border-left border-secondary">{{ strtoupper($name) }}</td>
                    <td class="align-middle border-left border-secondary text-center">
                        <div class="preview-icon-wrap">
                            <span class="text-uppercase {{ isset($facilityStates[$id]) ? ($facilityStates[$id] ? 'text-dark-teal' : 'text-danger') : 'text-muted fst-italic' }}">
                                {{ isset($facilityStates[$id]) 
        ? ($facilityStates[$id] ? 'YES' : 'NO') 
        : 'NOT SET' }}
                            </span>

                        </div>
                    </td>
                    <td class="align-middle border-left border-secondary text-center">
                        <button wire:click="selectFacility('{{ $name }}')" data-toggle="modal" data-target="#handWashingFacilitiesModal" class="btn bg-dark-teal btn-xs align-self-center">
                            <em class="icon ni ni-edit-fill"></em>
                            <span>Update</span>
                        </button>
                    </td>
                </tr>

                @endforeach
            </tbody>

        </table>
    </div>


    <div wire:ignore.self class="modal fade zoom" tabindex="-1" id="handWashingFacilitiesModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a href="#" class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit.prevent="updateFacility">
                    <div class="modal-header">
                        <h6 class="modal-title">Edit Facility</h6>
                    </div>
                    <div class="modal-body">
                        <div id="notifyError"></div>
                        <div class="row py-5">
                            <div class="col-12 d-flex flex-column">

                                <span class="font-weight-bold lead align-self-center">Does this School have {{ $selectedFacilityName }} ?</span>
                                <div class="align-self-center mt-2">
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input wire:model.defer="presentInSchool" type="radio" id="facility_yes" value="1" class="custom-control-input" name="facility_present">
                                        <label class="custom-control-label" for="facility_yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input wire:model.defer="presentInSchool" type="radio" id="facility_no" value="0" class="custom-control-input" name="facility_present">
                                        <label class="custom-control-label" for="facility_no">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>

                        <button type="submit" class="btn btn-primary  d-flex" wire:target="updateFacility" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateFacility" class="align-self-center">Save</span>
                            <span wire:loading wire:target="updateFacility" class="align-self-center">
                                <span wire:loading wire:target="updateFacility" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateFacility"></em>
                        </button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>