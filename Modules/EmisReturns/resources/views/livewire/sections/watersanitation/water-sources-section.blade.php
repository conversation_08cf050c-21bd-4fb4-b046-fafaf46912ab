<div>
    
    <livewire:emis-returns.notification-banner :eventName="'notifyWaterSources'" />

    <h4>Main Sources Of Water</h4>
    <div class="table-responsive">
        <table class="table table-hover table-bordered">
            <thead>
                <tr class="bg-secondary">
                    <td colspan="3" class="text-uppercase border-secondary text-white">SECTION E.4: MAIN SOURCE OF WATER</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">MAIN WATER PURPOSE</th>
                    <th class="text-uppercase border-secondary text-dark">MAIN WATER SOURCE TYPE</th>
                    <th class="text-uppercase border-secondary text-dark">DISTANCE TO MAIN SOURCE OF WATER</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="bg-secondary-dim text-uppercase border-secondary text-dark">MAIN SOURCE OF DRINKING WATER</td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span class=" data-value {{ $this->LatestWaterSource === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                            {{ $this->LatestWaterSource }}
                        </span>

                    </td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span class=" data-value {{ $this->LatestWaterSourceDistance === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                            {{ $this->LatestWaterSourceDistance }}
                        </span>

                    </td>
                </tr>
                <tr>
                    <td class="bg-secondary-dim text-uppercase border-secondary text-dark">MAIN SOURCE OF WATER FOR OTHER PURPOSES</td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span class=" data-value {{ $this->LatestWaterSourceOtherPurpose === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                            {{ $this->LatestWaterSourceOtherPurpose }}
                        </span>

                    </td>
                    <td class="text-uppercase border-secondary text-dark">
                        <span class=" data-value {{ $this->LatestWaterSourceOtherPurposeDistance === 'NOT SET' ? 'text-muted font-normal' : 'text-dark font-bold' }}">
                            {{ $this->LatestWaterSourceOtherPurposeDistance }}
                        </span>

                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="d-flex justify-content-center p-3 mb-4">
        <button class="btn bg-dark-teal btn-sm mt-3" data-target="#schoolWaterSourcesModal" data-toggle="modal">
            <span class="text-uppercase">UPDATE MAIN SOURCES OF WATER INFORMATION</span>
        </button>
    </div>


    <!-- water sources modal -->
    <div class="modal fade zoom" tabindex="-1" id="schoolWaterSourcesModal">
        <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="updateWaterSourcesData">
                    <div class="modal-header">
                        <h6 class="modal-title">UPDATE MAIN SOURCE OF WATER</h6>
                    </div>
                    <div class="modal-body">

                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="form-group position-relative">
                                    <div class="form-control-wrap">
                                        <label for="drinkingWaterSourceDistanceId" class="form-label">Main Source Of Drinking Water<span class="text-danger">*</span></label>
                                        <select required wire:model="drinking_water_source_id" class=" text-uppercase form-control bg-primary-dim custom-select pr-5 cursor-pointer">

                                            <option value="">-- Select --</option>
                                            @foreach($drinking_water_sources_names as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>
                                </div>
                            </div><!-- .col -->
                            <div class="col-lg-6">
                                <div class="form-group position-relative">
                                    <div class="form-control-wrap">
                                        <label for="drinkingWaterSourceDistanceId" class="form-label">Distance to Main Source Of Drinking Water<span class="text-danger">*</span></label>
                                        <select required wire:model="drinking_water_source_distance_id" class=" text-uppercase form-control bg-primary-dim custom-select pr-5 cursor-pointer">

                                            <option value="">-- Select --</option>
                                            @foreach($drinking_water_source_distance_names as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>
                                </div>
                            </div><!-- .col -->
                        </div><!-- .row -->
                        <hr class="border-dark-teal my-4">
                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="form-group position-relative">
                                    <div class="form-control-wrap">
                                        <label for="drinkingWaterSourceDistanceId" class="form-label">Main Source Of Water For Other Purpose<span class="text-danger">*</span></label>
                                        <select required wire:model="water_source_other_purposes_id" class=" text-uppercase form-control bg-primary-dim custom-select pr-5 cursor-pointer">

                                            <option value="">-- Select --</option>
                                            @foreach($water_source_other_purposes_names as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>
                                </div>
                            </div><!-- .col -->
                            <div class="col-lg-6">
                                <div class="form-group position-relative cursor-pointer">
                                    <div class="form-control-wrap">
                                        <label for="drinkingWaterSourceDistanceId" class="form-label">Distance to Main Source Of Water For Other Purpose<span class="text-danger">*</span></label>
                                        <select required wire:model="water_source_other_purposes_distance_id" class=" text-uppercase form-control bg-primary-dim custom-select pr-5 cursor-pointer">

                                            <option value="">-- Select --</option>
                                            @foreach($water_source_other_purposes_distance_names as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>
                                </div>
                            </div><!-- .col -->
                        </div><!-- .row -->
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button  type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex " wire:target="updateWaterSourcesData" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateWaterSourcesData" class="align-self-center">Save</span>
                            <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateWaterSourcesData"></em>
                            <span wire:loading wire:target="updateWaterSourcesData" class="align-self-center">
                                <span wire:loading wire:target="updateWaterSourcesData" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateWaterSourcesData"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- water sources modal -->

</div>