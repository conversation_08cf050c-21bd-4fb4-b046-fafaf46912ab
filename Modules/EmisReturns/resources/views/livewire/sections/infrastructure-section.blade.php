<div style="width:100%; max-height: 500px; overflow-y: auto;">

    @foreach($infrastructureTypes as $category)
    @php
    $schoolCategory = \Modules\Core\Models\Institutions\SchoolBuildingStatusUpdate::with(['inter_education_level'])
    ->where('school_id', $institution->id)
    ->where('building_id', $category->id)
    ->get();
    @endphp


    <div class="w-full mb-5 ">
        <div class="nk-block-head nk-block-head-sm">
            <div class="nk-block-between">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ $category->name }}</h5>
                </div>
                <div class="nk-block-head-content">
                    <div class="toggle-wrap nk-block-tools-toggle">
                        <button
                            class="cursor btn bg-dark-teal btn-md d-sm-none"
                            data-toggle="modal"
                            data-target="#schoolInfrastructureModal{{ $category->id }}">
                            <em class="icon ni ni-plus-circle-fill text-white"></em><span>ADD</span>
                        </button>


                        <div class="toggle-expand-content" data-content="pageMenu">
                            <div>
                                <button
                                    wire:click.prevent="startCreate({{ $category->id }})"
                                    class="cursor btn bg-dark-teal btn-md"
                                    data-toggle="modal"
                                    data-target="#schoolInfrastructureModal{{ $category->id }}">
                                    <em class="icon ni ni-plus-circle-fill text-white"></em><span>ADD {{ $category->name }}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- notification -->
        <livewire:emis-returns.notification-banner :key="$category->id" :eventName="'notifyInfrastructure'.$category->id" />
        
        <!-- modal -->
        <div wire:ignore.self class="modal fade zoom" tabindex="-1" id="schoolInfrastructureModal{{ $category->id }}">
            <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a wire:click="resetForm" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <form wire:submit.prevent="{{ $edit ? 'update' : 'create(' . $category->id . ')' }}">
                        <div class="modal-header">
                            <h6 class="modal-title">{{ $modalTitle }} {{ $category->name }}</h6>
                        </div>
                        <div class="modal-body">


                            @if ($institution->school_type_id === 7)
                            <div class="row g-4">
                                <div class="col-12">

                                    <div class="form-group position-relative">
                                        <div class="form-control-wrap">
                                            <label for="inter_education_level{{ $category->id }}" class="form-label">
                                                Education Level <span class="text-danger">*</span>
                                            </label>
                                            <select required wire:model="inter_education_level_id" id="inter_education_level{{ $category->id }}" class=" max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                                <option value="">--Select--</option>
                                                @foreach ($inter_education_levels as $id => $name)
                                                <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>

                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif


                            <div class="row g-4">
                                <div class="col-12">

                                    <div class="form-group position-relative">
                                        <div class="form-control-wrap">
                                            <label for="building_usage_mode{{ $category->id }}" class="form-label">
                                                Infrastructure Type <span class="text-danger">*</span>
                                            </label>
                                            <select required wire:model="usage_mode" id="building_usage_mode{{ $category->id }}" class="  max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                                <option value="">--Select All--</option>
                                                <option value="1">Permanent</option>
                                                <option value="2">Temporary</option>
                                            </select>

                                            <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                                <em class="icon ni ni-chevron-down text-muted"></em>
                                            </span>
                                        </div>
                                    </div>


                                </div>
                            </div>

                            <div class="row g-4">
                                <div class="col-12">

                                    <div class="form-control-wrap">
                                        <label for="building_completion_status{{ $category->id }}" class="form-label">
                                            Completion Status <span class="text-danger">*</span>
                                        </label>
                                        <select required wire:model="completion_status" id="building_completion_status{{ $category->id }}" class="  max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                            <option value="">--Select All--</option>
                                            <option value="1">Complete</option>
                                            <option value="2">Incomplete</option>
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>

                                </div>
                            </div>

                            @if($category->gender_usage_yn)
                            <div class="row g-4">
                                <div class="col-12">

                                    <div class="form-control-wrap">
                                        <label for="building_gender_usage{{ $category->id }}" class="form-label">
                                            Usage By Gender <span class="text-danger">*</span>
                                        </label>
                                        <select required wire:model="gender_usage" id="building_gender_usage{{ $category->id }}" class="  max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                            <option value="">--Select All--</option>
                                            <option value="1">Male</option>
                                            <option value="2">Female</option>
                                        </select>

                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>

                                </div>
                            </div>
                            @endif

                            @if($category->user_category_yn)
                            <div class="row g-4">
                                <div class="col-12">

                                    <div class="form-control-wrap">
                                        <label for="building_user_category{{ $category->id }}" class="form-label">
                                            Usage Category <span class="text-danger">*</span>
                                        </label>
                                        <select required wire:model="user_category" id="building_user_category{{ $category->id }}" class="  max-h-10 scrollable form-control bg-primary-dim custom-select pr-5 cursor-pointer">
                                            <option value="">--Select All--</option>
                                            <option value="1">Learners Only</option>
                                            <option value="2">{{ $getLevelTitle }} Only</option>
                                            <option value="3">Learners & {{ $getLevelTitle }}</option>
                                            <option value="4">Learners & {{ $getLevelTitle }} With Disability(SNE)</option>
                                        </select>
                                        <span class="position-absolute" style="top: 70%; right: 1rem; transform: translateY(-50%); pointer-events: none;">
                                            <em class="icon ni ni-chevron-down text-muted"></em>
                                        </span>
                                    </div>

                                </div>
                            </div>
                            @endif

                            <div class="row g-4">
                                @if($category->is_area_captured_yn)
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="classrooms_total_area" class="form-label">
                                                Total Area Per Room <span class="text-danger">*</span>
                                            </label>
                                        </div>
                                        <div class="form-control-group">
                                            <div class="form-control-wrap">
                                                <div class="form-text-hint bg-primary-dim">
                                                    <span class="overline-title">SQ<sup>M</sup></span>
                                                </div>
                                                <input
                                                    value="0"
                                                    min="1"
                                                    wire:model.number="area"
                                                    id="classrooms_total_area"
                                                    type="number"
                                                    class="form-control text-center bg-primary-dim"
                                                    required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-label-group">
                                            <label for="classrooms_quantity" class="form-label">
                                                @if($category->is_area_captured_yn)
                                                Total Count of Rooms with Above Area <span class="text-danger">*</span>
                                                @else
                                                Total Count <span class="text-danger">*</span>
                                                @endif
                                            </label>
                                        </div>
                                        <div class="form-control-group">
                                            <input
                                                min="1"
                                                wire:model.number="total_number"
                                                id="classrooms_quantity"
                                                type="number"
                                                class="form-control text-center bg-primary-dim"
                                                required>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- .row -->
                        </div>

                        <div class="modal-footer d-flex justify-content-center">
                            <button wire:click="resetForm" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                <em class="icon ni ni-cross"></em><span>Cancel</span>
                            </button>
                            <button type="submit" class="btn btn-primary  d-flex " wire:target="create,update" wire:loading.attr="disabled">
                                <span wire:loading.remove wire:target="create,update" class="align-self-center">Save</span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="create,update"></em>
                                <span wire:loading wire:target="create,update" class="align-self-center">
                                    <span wire:loading wire:target="create,update" class="align-self-center d-flex align-items-center">
                                        <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                        Saving...
                                    </span>
                                    <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="create,update"></em>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- modal -->


        <div class="table-responsive">
            <div class="table table-bordered table-hover table-lg">
                <div class=" nk-tb-list nk-tb-ulist is-compact">
                    <div class=" nk-tb-item nk-tb-head bg-secondary ">
                        @if($institution->school_type_id === 7)
                        <div class="nk-tb-col"><span class="sub-text text-white">Education Level</span></div>
                        @endif
                        <div class="nk-tb-col"><span class="sub-text text-white">Infrastructure Type</span></div>
                        <div class="nk-tb-col"><span class="sub-text text-white">Completion Status</span></div>
                        @if($category->gender_usage_yn)
                        <div class="nk-tb-col text-center"><span class="sub-text text-white">Usage By Gender</span></div>
                        @endif
                        @if($category->user_category_yn)
                        <div class="nk-tb-col text-center"><span class="sub-text text-white">User Category</span></div>
                        @endif
                        @if($category->is_area_captured_yn)
                        <div class="nk-tb-col text-center"><span class="sub-text text-white">Total Area Per Room</span></div>
                        @endif
                        <div class="nk-tb-col text-center">
                            <span class="sub-text text-white">
                                {{ $category->is_area_captured_yn ? 'Total Count of Rooms' : 'Total Count' }}
                            </span>
                        </div>
                        <div class="nk-tb-col text-center"><span class="text-white">ACTIONS</span></div>
                    </div>

                    @forelse ($schoolCategory as $facility)
                    <div class="nk-tb-item">
                        @if ($facility->inter_education_level_id !== '' && $institution->school_type_id === 7)
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ $facility->inter_education_level->name ?? '' }}</span>
                        </div>
                        @endif

                        <div class="nk-tb-col ucap">
                            <span class="text-secondary">
                                @if($facility->usage_mode === 1) Permanent
                                @elseif($facility->usage_mode === 2) Temporary
                                @endif
                            </span>
                        </div>

                        <div class="nk-tb-col ucap">
                            <span class="text-secondary">
                                @if($facility->completion_status === 1) Complete
                                @elseif($facility->completion_status === 2) Incomplete
                                @endif
                            </span>
                        </div>

                        @if ($facility->gender_usage !== '' && $category->gender_usage_yn)
                        <div class="nk-tb-col text-center ucap">
                            <span class="text-secondary">
                                @if($facility->gender_usage === 1) Male
                                @elseif($facility->gender_usage === 2) Female
                                @elseif($facility->gender_usage === 3) Both Male & Female
                                @endif
                            </span>
                        </div>
                        @endif

                        @if ($facility->user_category !== '' && $category->user_category_yn)
                        <div class="nk-tb-col text-center ucap">
                            <span class="text-secondary">
                                @if($facility->user_category === 1) Learners
                                @elseif($facility->user_category === 2) {{ $getLevelTitle }}
                                @elseif($facility->user_category === 3) Learners & {{ $getLevelTitle }}
                                @elseif($facility->user_category === 4) Learners & {{ $getLevelTitle }} With Disability(SNE)
                                @endif
                            </span>
                        </div>
                        @endif

                        @if ($facility->area !== '' && $category->is_area_captured_yn)
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ $facility->area }} SQ<sup>M</sup></span>
                        </div>
                        @endif

                        @if ($facility->total_number !== '')
                        <div class="nk-tb-col text-center">
                            <span class="text-secondary">{{ $facility->total_number }}</span>
                        </div>
                        @endif

                        <div class="nk-tb-col text-center">
                            <span
                                wire:click="editInfrastructure({{ $facility->id }})"
                                data-toggle="modal"
                                data-target="#schoolInfrastructureModal{{ $facility->building_id }}"
                                data-placement="top"
                                title="Edit Building!"
                                class="cursor lead text-dark-teal mr-1">
                                <em class="icon ni ni-edit-fill"></em>
                            </span>
                        </div>
                    </div>

                    @empty
                    <div colspan="100%" class=" nk-tb-item  card card-stretch w-100 " style="box-shadow: none;">
                        <div class="card-inner-group">
                            <div class="card-body">
                                <div class="my-5 mx-1  alert alert-secondary alert-icon">
                                    <em class="icon ni ni-alert-circle"></em>
                                    There is no infrastructure information to display at the moment.
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforelse

                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>