<div>
<div>
    <h4>Teaching Staff Section</h4>
    <form>
        <div class="form-group">
            <label for="teacherName">Teacher Name</label>
            <input type="text" id="teacherName" class="form-control" placeholder="Enter teacher name">
        </div>
        <div class="form-group">
            <label for="teacherSubject">Subject</label>
            <input type="text" id="teacherSubject" class="form-control" placeholder="Enter subject taught">
        </div>
        <div class="form-group">
            <label for="teacherExperience">Years of Experience</label>
            <input type="number" id="teacherExperience" class="form-control" placeholder="Enter years of experience">
        </div>
        <button type="submit" class="btn btn-primary">Save</button>
    </form>
</div>
<div class="nk-block">
    <div class="card">
        <div class="card-inner">
            <h5 class="card-title">{{ $formData['title'] }}</h5>
            <div class="row g-4">
                @foreach($formData['fields'] as $key => $field)
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label class="form-label">{{ $field['label'] }}</label>
                            <input type="{{ $field['type'] }}" class="form-control" wire:model="formData.fields.{{ $key }}.value" placeholder="Enter {{ $field['label'] }}">
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
</div>
