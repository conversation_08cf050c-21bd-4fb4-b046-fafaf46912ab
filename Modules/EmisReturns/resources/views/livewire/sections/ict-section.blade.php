<div class="card card-preview">
    <div class="nk-block-head nk-block-head-lg">
        <div class="w-100">
            <div class="nk-block">
                <div class="card">
                    <div class="card-inner-group">
                        <div class="card-inner">
                            <livewire:emis-returns.notification-banner />
                            {{-- Internet Source --}}
                            <div class="w-100">
                                <div class="row">
                                    <div class="col-12 d-flex flex-row">
                                        <div class="d-flex flex-row">
                                            <div class="d-flex flex-row align-self-center">
                                                <div class="preview-icon-wrap mr-1 pb-0">
                                                    <em class="{{ $internetSource === null || $internetSource->internet_source_id == '' || $internetSource->internet_source_id == 1 ? 'text-muted' : 'text-dark-teal' }} ni ni-signal"></em>
                                                </div>
                                                <h5 class="nice-title align-self-center">School Internet Source:</h5>
                                            </div>
                                            <span @class([ 'fs-15px ml-lg-3 align-self-center font-italic' , 'text-muted'=>
                                                $internetSource === null || $internetSource->internet_source_id == '',
                                                'text-uppercase' =>
                                                $internetSource !== null && $internetSource->internet_source_id != '',
                                                ])>
                                                {{ $internetSource === null || $internetSource->internet_source_id == '' ? 'Not Set' : strtoupper($internetSource->source->name ?? '') }}
                                            </span>
                                        </div>
                                        <div class="d-flex ml-3">
                                            <button type="button" class="btn btn-sm bg-dark-teal align-self-center" data-toggle="modal" data-target="#internetSourcesModal">Update</button>
                                        </div>
                                    </div>
                                    <div class="col-12 d-flex mt-3 flex-row">
                                        <div class="d-flex flex-row">
                                            <div class="d-flex flex-row">
                                                <div class="preview-icon-wrap align-self-center mr-1 pb-0">
                                                    @if ($internetSource !== null && $internetSource->connectivity_status == 1)
                                                        <em class="ni ni-wifi text-dark-teal"></em>
                                                    @else
                                                        <em class="ni ni-wifi-off text-muted"></em>
                                                    @endif
                                                </div>
                                                <h5 class="nice-title align-self-center">Internet Connectivity Status:
                                                </h5>
                                            </div>
                                            <div class="d-flex flex-row">
                                                <label class="mr-2 ml-lg-3 mb-0 align-self-center" for="internetConnectivityStatus">In-Active</label>
                                                <div class="custom-control custom-switch custom-control-inline align-self-center">
                                                    <input type="checkbox" class="custom-control-input custom-control-input-dark-teal" id="internetConnectivityStatus" wire:change="updateInternetConnectivityStatus($event.target.checked)" {{ $internetSource && $internetSource->connectivity_status == 1 ? 'checked' : '' }} {{ $internetSource === null || $internetSource->internet_source_id === '' || $internetSource->internet_source_id === 1 ? 'disabled' : '' }}>
                                                    <label class="custom-control-label custom-control-label-dark-teal" for="internetConnectivityStatus">Active</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- Modal for updating Internet Source --}}
                                    <div class="modal fade zoom" tabindex="-1" id="internetSourcesModal">
                                        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <a class="cursor close" data-dismiss="modal" aria-label="Close"><em class="icon ni ni-cross"></em></a>
                                                <form wire:submit="updateInternetSource">
                                                    <div class="modal-header">
                                                        <h6 class="modal-title">Update Internet Source</h6>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row g-4">
                                                            <div class="col-12">
                                                                <div class="form-group">
                                                                    <label class="form-label">Internet Source</label>
                                                                    <select wire:model="selected_internet_source_id" required id="internetSourceId" data-placeholder="--Select--" class="form-select-sm form-control">
                                                                        <option disabled>--Select--</option>
                                                                        @php
                                                                            $selectedSource = optional($internetSource)
                                                                            ->internet_source_id;
                                                                            $selectedSourceName = optional(
                                                                            collect(
                                                                            $allInternetSources,
                                                                            )->firstWhere('id', $selectedSource),
                                                                            )->name;
                                                                        @endphp

                                                                        @if ($selectedSourceName)
                                                                            <option selected>{{ $selectedSourceName }}</option>
                                                                        @endif

                                                                        @foreach ($allInternetSources as $source)
                                                                            <option value="{{ $source->id }}" {{ $selectedSource == $source->id ? 'selected' : '' }}>{{ strtoupper($source->name) }}</option>
                                                                        @endforeach
                                                                    </select>
                                                                    @error('selected_internet_source_id')
                                                                        <span class="text-danger">{{ $message }}</span>
                                                                    @enderror
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer d-flex justify-content-center">
                                                        <button wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                                                        </button>
                                                        <button type="submit" class="btn btn-primary d-flex" wire:loading.attr="disabled">
                                                            <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                            <span wire:loading class="align-self-center">Saving...</span>
                                                            <span wire:loading class="sr-only">Loading...</span>
                                                            <span wire:loading.remove class="align-self-center">Save</span><em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- ICT Facilities Table --}}
                            <div class="nk-tb-list nk-tb-ulist is-compact">
                                <div class="nk-tb-item bg-secondary nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text text-uppercase text-white">Facility User</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Functional Computers</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Non Functional Computers</span></div>
                                    @if (($schoolType && $schoolType->id == 4) || ($schoolType && $schoolType->id == 5) || ($schoolType && $schoolType->id == 6) || ($schoolType && $schoolType->id == 7))
                                        <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Over Head Projector</span></div>
                                        <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Distance Learning Facilities</span></div>
                                    @endif
                                    <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Computers with Internet Access</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Total</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text text-uppercase text-white">Actions</span></div>
                                </div>
                                {{-- Learners Row --}}
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="text-dark text-uppercase">{{ $schoolType && in_array($schoolType->id, [4, 5, 6]) ? 'STUDENTS' : 'LEARNERS' }}</span>
                                    </div>

                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">{{ optional($allIctFacilities->where('is_for_teachers', 0)->first())->total_computers_functional ?? 'Not Set' }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">{{ optional($allIctFacilities->where('is_for_teachers', 0)->first())->total_computers_non_functional ?? 'Not Set' }}</span>
                                    </div>
                                    @if (($schoolType && $schoolType->id == 4) || ($schoolType && $schoolType->id == 5) || ($schoolType && $schoolType->id == 6) || ($schoolType && $schoolType->id == 7))
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 0)->first())->total_over_head_projectors ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 0)->first())->distance_learning_facilities ?? 'Not Set' }}
                                            </span>
                                        </div>
                                    @endif
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            {{ optional($allIctFacilities->where('is_for_teachers', 0)->first())->total_computers_with_internet ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            @php
                                                $learner = $allIctFacilities->where('is_for_teachers', 0)->first();
                                                $total = $learner
                                                ? $learner->total_computers_functional +
                                                $learner->total_computers_non_functional +
                                                $learner->total_over_head_projectors +
                                                $learner->distance_learning_facilities
                                                : null;
                                            @endphp
                                            {{ $total ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <button class="btn btn-sm bg-dark-teal" type="button" data-toggle="modal" wire:click='editFacility({{ 0 }})' data-target="#ictFacilitiesModal">Update</button>
                                    </div>
                                </div>
                                {{-- Teachers Row --}}
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="text-dark text-uppercase">
                                            {{ $schoolType && in_array($schoolType->id, [4, 5, 6, 7]) ? 'ACADEMIC STAFF' : 'TEACHERS' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            {{ optional($allIctFacilities->where('is_for_teachers', 1)->first())->total_computers_functional ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            {{ optional($allIctFacilities->where('is_for_teachers', 1)->first())->total_computers_non_functional ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    @if (($schoolType && $schoolType->id == 4) || ($schoolType && $schoolType->id == 5) || ($schoolType && $schoolType->id == 6) || ($schoolType && $schoolType->id == 7))
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 1)->first())->total_over_head_projectors ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 1)->first())->distance_learning_facilities ?? '' }}
                                            </span>
                                        </div>
                                    @endif
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            {{ optional($allIctFacilities->where('is_for_teachers', 1)->first())->total_computers_with_internet ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="text-dark text-uppercase font-italic">
                                            @php
                                                $teacher = $allIctFacilities->where('is_for_teachers', 1)->first();
                                                $total = $teacher
                                                ? $teacher->total_computers_functional +
                                                $teacher->total_computers_non_functional +
                                                $teacher->total_over_head_projectors +
                                                $teacher->distance_learning_facilities
                                                : null;
                                            @endphp
                                            {{ $total ?? 'Not Set' }}
                                        </span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <button class="btn btn-sm bg-dark-teal" type="button" data-toggle="modal" wire:click='editFacility({{ 1 }})' data-target="#ictFacilitiesModal">Update</button>
                                    </div>
                                </div>

                                {{-- staff and learners row --}}
                                @if ($schoolType && in_array($schoolType->id, [4, 5, 6, 7]))
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col"><span class="text-dark text-uppercase">STAFF & LEARNERS</span></div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 3)->first())->total_computers_functional ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 3)->first())->total_computers_non_functional ?? 'Not Set' }}
                                            </span>
                                        </div>

                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 3)->first())->total_over_head_projectors ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 3)->first())->distance_learning_facilities ?? '' }}
                                            </span>
                                        </div>

                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 3)->first())->total_computers_with_internet ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                @php
                                                    $teacher = $allIctFacilities->where('is_for_teachers', 3)->first();
                                                    $total = $teacher
                                                    ? $teacher->total_computers_functional +
                                                    $teacher->total_computers_non_functional +
                                                    $teacher->total_over_head_projectors +
                                                    $teacher->distance_learning_facilities
                                                    : null;
                                                @endphp
                                                {{ $total ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <button class="btn btn-sm bg-dark-teal" type="button" data-toggle="modal" wire:click='editFacility({{ 3 }})' data-target="#ictFacilitiesModal">Update</button>
                                        </div>
                                    </div>
                                    {{-- Administrative Staff --}}
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col"><span class="text-dark text-uppercase">ADMINISTRATIVE STAFF</span></div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 2)->first())->total_computers_functional ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 2)->first())->total_computers_non_functional ?? 'Not Set' }}
                                            </span>
                                        </div>

                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 2)->first())->total_over_head_projectors ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 2)->first())->distance_learning_facilities ?? '' }}
                                            </span>
                                        </div>

                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                {{ optional($allIctFacilities->where('is_for_teachers', 2)->first())->total_computers_with_internet ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-dark text-uppercase font-italic">
                                                @php
                                                    $teacher = $allIctFacilities->where('is_for_teachers', 2)->first();
                                                    $total = $teacher
                                                    ? $teacher->total_computers_functional +
                                                    $teacher->total_computers_non_functional +
                                                    $teacher->total_over_head_projectors +
                                                    $teacher->distance_learning_facilities
                                                    : null;
                                                @endphp
                                                {{ $total ?? 'Not Set' }}
                                            </span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <button class="btn btn-sm bg-dark-teal" type="button" data-toggle="modal" wire:click='editFacility({{ 2 }})' data-target="#ictFacilitiesModal">Update</button>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            {{-- Modal for updating ICT Facilities --}}
                            <div class="modal fade zoom" tabindex="-1" id="ictFacilitiesModal" wire:ignore.self>
                                <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
                                    <div class="modal-content">
                                        <a class="cursor close" data-dismiss="modal" aria-label="Close">
                                            <em class="icon ni ni-cross"></em>
                                        </a>
                                        <form wire:submit.prevent="updateIctFacilities">
                                            <div class="modal-header">
                                                <h6 class="modal-title">Edit
                                                    {{ $form_ict_facility['is_for_teachers'] == 1 ? 'Teacher ICT Facilities' : 'Learner ICT Facilities' }}
                                                </h6>
                                            </div>
                                            <div class="modal-body">
                                                @if ($errors->any())
                                                    <div class="alert alert-danger">
                                                        <ul>
                                                            @foreach ($errors->all() as $error)
                                                                <li>{{ $error }}</li>
                                                            @endforeach
                                                        </ul>
                                                    </div>
                                                @endif
                                                <div class="row g-4">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="ictFacilityTotalComputersFunctional" class="form-label">Total Functional Computers <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input min="0" wire:model.defer="form_ict_facility.total_computers_functional" id="ictFacilityTotalComputersFunctional" type="number" class="form-control text-center bg-primary-dim" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row g-4">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="ictFacilityTotalComputersNonFunctional" class="form-label">Total Non Functional Computers
                                                                    <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input min="0" wire:model.defer="form_ict_facility.total_computers_non_functional" id="ictFacilityTotalComputersNonFunctional" type="number" class="form-control text-center bg-primary-dim" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @if ($schoolType && in_array($schoolType->id, [4, 5, 6, 7]))
                                                    <div class="row g-4">
                                                        <div class="col-12">
                                                            <div class="form-group">
                                                                <div class="form-label-group">
                                                                    <label for="ictFacilityTotalComputersNonFunctional" class="form-label">Total Over Head Projectors
                                                                        <span class="text-danger">*</span></label>
                                                                </div>
                                                                <div class="form-control-group">
                                                                    <input min="0" wire:model.defer="form_ict_facility.total_over_head_projectors" id="ictFacilityTotalOverHeadProjectors" type="number" class="form-control text-center bg-primary-dim" required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                                <div class="row g-4">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <div class="form-label-group">
                                                                <label for="ictFacilityTotalComputersWithInternetAccess" class="form-label">Total Computers With Internet
                                                                    Access <span class="text-danger">*</span></label>
                                                            </div>
                                                            <div class="form-control-group">
                                                                <input min="0" wire:model.defer="form_ict_facility.total_computers_with_internet" id="ictFacilityTotalComputersWithInternetAccess" type="number" class="form-control text-center bg-primary-dim" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer d-flex justify-content-center">
                                                <button wire:loading.attr="disabled" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                                                    <em class="icon ni ni-cross"></em><span>Cancel</span>
                                                </button>
                                                <button type="submit" class="btn btn-primary d-flex">
                                                    <span wire:loading class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                    <span wire:loading class="align-self-center">Saving...</span>
                                                    <span wire:loading.remove class="align-self-center">Save</span>
                                                    <em wire:loading.remove class="ni ni-arrow-right ml-2"></em>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            {{-- /Modal --}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
