<div>

    <!-- For Pre-Primary -->
    @if ($institution->school_type_id === 1)
    <!-- Notification -->
    <livewire:emis-returns.notification-banner :eventName="'notifySportsEquipment'" />
    <!-- Notification -->

    <!-- data item -->
    <div class="data-item py-1">
        <div class="">
            <span class="data-label" style="font-size: initial">How many play grounds does the ECCE centre have/own?</span>
            <span class="ml-lg-5 data-value text-dark">
                {{ $no_of_play_grounds !== null ? $no_of_play_grounds : 'NOT SET' }}
            </span>

            <span v-else class="ml-lg-5 data-value text-dark"></span>
        </div>
        <div class="data-col data-col-end">
            <button data-target="#playGroundModal" data-toggle="modal" type="button" class="btn btn-sm bg-dark-teal">
                <em class="ni ni-edit-fill text-white mr-1"></em>Update
            </button>
        </div>
    </div>
    <!-- data item -->

    <!-- data item -->
    <div class="nk-data data-list">
        <div class="data-item py-1">
            <div class="data-col">
                <span class="data-label" style="font-size: initial">What is the size of the play grounds in Square Meters?</span>
                <span class="ml-lg-5 data-value text-dark">
                    {{ $size_of_play_grounds !== null ? $size_of_play_grounds : 'NOT SET' }}
                </span>
                <span v-else class="ml-lg-1 data-value text-dark"> SQM</span>
            </div>

        </div><!-- data-item -->
        <div class="data-col-end"></div>
    </div>
    <!-- data item -->

    <!-- data item modal -->
    <div class="modal fade zoom" tabindex="-1" id="playGroundModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a class="cursor close" data-dismiss="modal" wire:click="loadPrePrimarySportsEquipments" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="savePlayGrounds">
                    <div class="modal-header">
                        <h5 class="modal-title">UPDATE ECCE CENTRE PLAY GROUNDS</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row px-4">
                            <div class="col-12">
                                <div class="form-group d-flex flex-column justify-content-center">
                                    <label class="form-label">How many play grounds does the ECCE centre have/own?</label>
                                    <input type="number" wire:model.defer="no_of_play_grounds" placeholder="Enter Number" class="form-control">
                                </div>
                            </div>

                        </div>
                        <div class="row px-4">
                            <div class="col-12 mt-3">
                                <div class="form-group d-flex flex-column justify-content-center">
                                    <label class="form-label">What is the size of the play grounds in Square Meters?</label>
                                    <input type="number" wire:model.defer="size_of_play_grounds" placeholder="Enter Number" class="form-control">
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button wire:click="loadPrePrimarySportsEquipments" type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex" wire:target="savePlayGrounds" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="savePlayGrounds" class="align-self-center">Save</span>
                            <span wire:loading wire:target="savePlayGrounds" class="align-self-center">
                                <span wire:loading wire:target="savePlayGrounds" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="savePlayGrounds"></em>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- data item modal -->

    <!-- equipments table -->
    <div class="table-responsive mt-5">
        <table class="table border border-dark-teal">
            <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Sports Equipment</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
            </thead>
            <tbody class="border-top-0 border-dark-teal">
                @foreach($pre_primary_sports_equipments as $equipment)
                <tr>
                    <td class="align-middle ucap">{{ $equipment['name'] }}</td>

                    <td class="align-middle border-left text-center">
                        <div class="form-group mx-auto">
                            <div class="form-control-wrap">
                                @if($editingEquipmentId === $equipment['id'])
                                <input type="number"
                                    wire:model.defer="equipments.{{ $equipment['id'] }}"
                                    class=" form-control text-center border-dark-teal" />
                                @else
                                <span>{{ $equipment['quantity'] }}</span>
                                @endif
                            </div>
                        </div>
                    </td>

                    <td class="align-middle border-left text-center">
                        @if($editingEquipmentId === $equipment['id'])
                        <span wire:click="saveEquipment({{ $equipment['id'] }})" class="cursor mr-1 btn-sm bg-dark-teal">
                            <em class="icon ni ni-check"></em><span>Save</span>
                        </span>
                        <span wire:click="cancelEdit" class="cursor btn-sm text-white bg-secondary">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </span>
                        @else
                        <span wire:click="editEquipment({{ $equipment['id'] }})" class="cursor btn-sm btn-primary">
                            <em class="icon ni ni-edit-fill"></em><span>Update</span>
                        </span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- equipments table -->
    @endif
    <!-- For Pre Primary -->


    <!-- school sports equipments for  primary , secondary, certicate, diploma and international schools -->
    @if($institution->school_type_id === 2 || $institution->school_type_id === 3 || $institution->school_type_id === 4 || $institution->school_type_id === 5 )

    <ul class="nav nav-tabs">
        <li class="nav-item">
            <a class="nav-link active" id="sports-tab" data-toggle="tab" href="#sports" role="tab">SPORTS EQUIPMENT</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="activities-tab" data-toggle="tab" href="#activities" role="tab">PARTICIPATION IN EXTRA-CURRICULAR ACTIVITIES</a>
        </li>
    </ul>
    @endif


    @if($institution->school_type_id === 2 || $institution->school_type_id === 3 || $institution->school_type_id === 4 || $institution->school_type_id === 5 || $institution->school_type_id === 7)
    <div class="tab-content pt-3" style="max-height:600px; overflow-y:auto">
        <div class="tab-pane fade show active" id="sports" role="tabpanel" aria-labelledby="sports-tab">
            <!-- SPORTS EQUIPMENT TAB -->
            <div class="w-100">
                <div class="nk-block-head">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h6 class="nk-block-title text-uppercase">Sports Equipment</h6>
                            <div class="nk-block-des">
                                <p>How many Sports equipment does this institution have?</p>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div>

                <!-- Notification -->
                <livewire:emis-returns.notification-banner :eventName="'notifySportsEquipment'" />
                <!-- Notification -->

                <div class="table-responsive">
                    <table class="table border border-secondary">
                        <thead class="bg-secondary">
                            <tr>
                                <th class="text-white align-middle text-uppercase">Category</th>
                                <th class="text-white align-middle text-uppercase border-left border-white">Sports Equipment</th>
                                <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                                <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="border-top-0 border-left border-secondary">
                            @foreach($all_other_school_types_sports_equipments as $equipment)
                            <tr class="border-bottom border-secondary">
                                <td class="text-left pl-3 border-left border-secondary ucap">
                                    {{ $equipment['category'] }}
                                </td>
                                <td class="text-left pl-3 border-left border-secondary ucap">
                                    {{ $equipment['name'] }}
                                </td>
                                <td class="align-middle border text-center border-secondary">
                                    <div class=" mx-auto">
                                        <div class="form-control-wrap">
                                            @if($editingEquipmentId === $equipment['id'])
                                            <input type="number"
                                                wire:model.defer="equipments.{{ $equipment['id'] }}"
                                                class=" form-control text-center border-dark-teal" />
                                            @else
                                            <span>{{ $equipment['quantity'] }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="align-middle border border-secondary text-center">
                                    @if($editingEquipmentId === $equipment['id'])
                                    <span wire:click="saveEquipment({{ $equipment['id'] }})" class="cursor mr-1 btn-sm bg-dark-teal">
                                        <em class="icon ni ni-check"></em><span>Save</span>
                                    </span>
                                    <span wire:click="cancelEdit" class="cursor btn-sm text-white bg-secondary">
                                        <em class="icon ni ni-cross"></em><span>Cancel</span>
                                    </span>
                                    @else
                                    <span wire:click="editAllOtherSchoolTypesSportsEquipment({{ $equipment['id'] }})" class="cursor btn-sm btn-primary">
                                        <em class="icon ni ni-edit-fill"></em><span>Update</span>
                                    </span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>

                    </table>
                </div>
            </div>
              
            @if($institution->school_type_id === 4 || $institution->school_type_id === 5)
            <!-- include sports activities  -->
            <livewire:emis-returns.sections.pe-sports.sports-activities-section :survey="$survey" />
            <!-- include sports activities  -->
             @endif
             
            <!-- include sports facilties  -->
            <livewire:emis-returns.sections.pe-sports.sports-facilities-section :survey="$survey" />
            <!-- include sports facilities  -->

        </div>


        <div class="tab-pane fade" id="activities" role="tabpanel" aria-labelledby="activities-tab">
            <!-- EXTRA CO-CURRICULAR TAB-->
            <p>This is the extra-curricular activities section.</p>
              <!-- EXTRA CO-CURRICULAR TAB-->
        </div>
    </div>
    @endif



</div>