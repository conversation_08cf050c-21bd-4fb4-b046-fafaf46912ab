<div class="w-100 mt-5">
    <div class="nk-block-head">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h6 class="nk-block-title text-uppercase">Sports Facilities</h6>
                <div class="nk-block-des">
                    <p>How many Sports facilities does this institution have?</p>
                </div>
            </div><!-- .nk-block-head-content -->
        </div><!-- .nk-block-between -->
    </div>

    <!-- Notification -->
    <livewire:emis-returns.notification-banner :eventName="'notifySportsFacility'" />
    <!-- Notification -->

    <div class="table-responsive">
        <table class="table border border-secondary">
            <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Sports Facility</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Number</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
            </thead>
            <tbody class="border-top-0 border-left border-secondary">
                @foreach($all_sports_facilities as $facility)
                <tr class="border-bottom border-secondary">

                    <td class="text-left pl-3 border-left border-secondary ucap">
                        {{ $facility['name'] }}
                    </td>
                    <td class="align-middle border text-center border-secondary">
                        <div class=" mx-auto">
                            <div class="form-control-wrap">
                                @if($editingFacilityId === $facility['id'])
                                <input type="number"
                                    wire:model.defer="facilities.{{ $facility['id'] }}"
                                    class=" form-control text-center border-dark-teal" />
                                @else
                                <span>{{ $facility['quantity'] }}</span>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td class="align-middle border border-secondary text-center">
                        @if($editingFacilityId === $facility['id'])
                        <span wire:click="saveFacility({{ $facility['id'] }})" class="cursor mr-1 btn-sm bg-dark-teal">
                            <em class="icon ni ni-check"></em><span>Save</span>
                        </span>
                        <span wire:click="cancelEdit" class="cursor btn-sm text-white bg-secondary">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </span>
                        @else
                        <span wire:click="editFacility({{ $facility['id'] }})" class="cursor btn-sm btn-primary">
                            <em class="icon ni ni-edit-fill"></em><span>Update</span>
                        </span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>