<div class="w-100 mt-5">
    <div class="nk-block-head">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h6 class="nk-block-title text-uppercase">Sports Activities</h6>
                <div class="nk-block-des">
                    <p>Does this institution offer/engage in these activities below?</p>
                </div>
            </div><!-- .nk-block-head-content -->
        </div><!-- .nk-block-between -->
    </div>

    <!-- notification -->
    <livewire:emis-returns.notification-banner :eventName="'notifySportsActivity'" />
    <!-- notification -->

    <div class="table-responsive">
        <table class="table border border-dark-teal">
            <thead class="bg-secondary">
                <tr>
                    <th class="text-white align-middle text-uppercase w-45">Activity</th>
                    <th class="text-white align-middle text-uppercase text-center border-left border-white py-2">Present</th>
                    <th class="py-2 text-center text-white text-uppercase border-left border-white">Actions</th>
                </tr>
            </thead>
            <tbody class="border-top-0 border-secondary">
                @foreach ($activityNames as $id => $name)
                <tr>
                    <td class="align-middle border-left border-secondary">{{ strtoupper($name) }}</td>
                    <td class="align-middle border-left border-secondary text-center">
                        <div class="preview-icon-wrap">
                            <span class="text-dark-teal text-uppercase">
                                {!! isset($activityStates[$id])
                                ? ($activityStates[$id]
                                ? '<span>YES</span>'
                                : '<span class="text-danger ">NO</span>')
                                : '<span class="text-muted fst-italic">Not Set</span>' !!}

                            </span>
                        </div>
                    </td>
                    <td class="align-middle border-left border-secondary text-center">
                        <button wire:click="selectSportsActivity('{{ $name }}')" data-toggle="modal" data-target="#updateSchoolSportsActivitiesModal" class="btn bg-dark-teal btn-xs align-self-center">
                            <em class="icon ni ni-edit-fill"></em>
                            <span>Update</span>
                        </button>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>


    <div wire:ignore.self class="modal fade zoom" tabindex="-1" id="updateSchoolSportsActivitiesModal">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <a href="#" class="cursor close" data-dismiss="modal" aria-label="Close">
                    <em class="icon ni ni-cross"></em>
                </a>
                <form wire:submit="updateActivity">
                    <div class="modal-header">
                        <h6 class="modal-title">Update Sports Activity</h6>
                    </div>
                    <div class="modal-body">
                        <div id="notifyError"></div>
                        <div class="row py-5">
                            <div class="col-12 d-flex flex-column">

                                <span class="font-weight-bold lead align-self-center">Does this Institution have {{ $selectedActivityName }} ?</span>
                                <div class="align-self-center mt-2">
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input wire:model.defer="presentInSchool" type="radio" id="activity_yes" value="1" class="custom-control-input" name="activity_present">
                                        <label class="custom-control-label" for="activity_yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input wire:model.defer="presentInSchool" type="radio" id="activity_no" value="0" class="custom-control-input" name="activity_present">
                                        <label class="custom-control-label" for="activity_no">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" data-dismiss="modal" class="btn btn-light mr-2">
                            <em class="icon ni ni-cross"></em><span>Cancel</span>
                        </button>
                        <button type="submit" class="btn btn-primary  d-flex" wire:target="updateActivity" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="updateActivity" class="align-self-center">Save</span>
                            <span wire:loading wire:target="updateActivity" class="align-self-center">
                                <span wire:loading wire:target="updateActivity" class="align-self-center d-flex align-items-center">
                                    <span class="spinner-border spinner-border-sm text-white me-2" role="status" aria-hidden="true"></span>
                                    Saving...
                                </span>
                                <em class="ni ni-arrow-right ml-2" wire:loading.remove wire:target="updateActivity"></em>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>