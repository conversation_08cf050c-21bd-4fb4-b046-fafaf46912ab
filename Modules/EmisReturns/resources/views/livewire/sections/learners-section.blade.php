@php use Carbon\Carbon; @endphp
<div class="card card-preview">
    <div class="w-100">
        <livewire:emis-returns.notification-banner/>
        <div class="table-responsive">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">SECTION C: LEARNERS' INFORMATION</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">CLASS</th>
                    <th class="text-uppercase border-secondary text-dark">SEX</th>
                    <th class="text-uppercase border-secondary text-dark text-center">TOTAL</th>
                </tr>
                </thead>
                @if($schoolType === 'international')
                    {{-- International School: Show curriculum grades --}}
                    @foreach($international_curriculums as $curriculum)
                        <tbody class="border-secondary border-top">
                        <tr>
                            <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                <span>{{ $curriculum->name }}</span>
                            </th>
                            <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                <span>MALE</span>
                            </th>
                            <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                <span>{{ $curriculum->male_count ?? 0 }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                <span>FEMALE</span>
                            </th>
                            <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                <span>{{ $curriculum->female_count ?? 0 }}</span>
                            </td>
                        </tr>
                        </tbody>
                    @endforeach
                @else
                    {{-- Regular Schools: Show education grades --}}
                    @foreach($education_grades as $education_grade)
                        <tbody class="border-secondary border-top">
                        <tr>
                            <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                                <span>{{ $education_grade->name }}</span>
                            </th>
                            <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                <span>MALE</span>
                            </th>
                            <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                <span>{{ $education_grade->male_count }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th class="align-middle text-uppercase text-dark border-secondary px-2">
                                <span>FEMALE</span>
                            </th>
                            <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                                <span>{{ $education_grade->female_count }}</span>
                            </td>
                        </tr>
                        </tbody>
                    @endforeach
                @endif
                <tbody class="border-secondary border-top">
                <tr>
                    <th rowspan="2" class="align-middle text-uppercase text-dark border-secondary bg-secondary-dim">
                        <span>Total</span>
                    </th>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span>MALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        @if($schoolType === 'international')
                            <span>{{ collect($international_curriculums)->sum('male_count') }}</span>
                        @else
                            <span>{{ collect($education_grades)->sum('male_count') }}</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <th class="align-middle text-uppercase text-dark border-secondary px-2">
                        <span>FEMALE</span>
                    </th>
                    <td class="align-middle text-uppercase text-dark border-secondary text-center px-2">
                        @if($schoolType === 'international')
                            <span>{{ collect($international_curriculums)->sum('female_count') }}</span>
                        @else
                            <span>{{ collect($education_grades)->sum('female_count') }}</span>
                        @endif
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="table-responsive mt-5">
            <table class="table table-sm table-hover table-bordered">
                <thead>
                <tr class="bg-secondary">
                    <td colspan="4" class="text-uppercase border-secondary text-white">RECENTLY APPROVED LEARNERS</td>
                </tr>
                <tr class="bg-secondary-dim">
                    <th class="text-uppercase border-secondary text-dark">LEARNER</th>
                    <th class="text-uppercase border-secondary text-dark">SEX</th>
                    <th class="text-uppercase border-secondary text-dark">AGE</th>
                    <th class="text-uppercase border-secondary text-dark">CLASS</th>
                </tr>
                </thead>
                <tbody>
                @foreach($recent_learners as $enrolment)
                    <tr>
                        <td class="text-uppercase text-dark border-secondary">
                            <a target="_blank"
                               href="/institution/learners/profile/{{ $enrolment->learner->encrypted_lin }}">
                                <div class="user-card">
                                    <div class="user-avatar">
                                        <img src="{{ $enrolment->learner->person->photo_url }}" class="rounded-0"
                                             alt="{{ $enrolment->learner->person->full_name }}">
                                    </div>
                                    <div class="user-name text-uppercase">
                                        <span
                                            class="tb-lead cursor text-dark-teal">{{ $enrolment->learner->person->full_name }}</span>
                                    </div>
                                </div>
                            </a>
                        </td>
                        <td class="align-middle text-uppercase text-dark border-secondary">
                            @if($enrolment->learner->person->gender === 'M')
                                <span>MALE</span>
                            @else
                                <span>FEMALE</span>
                            @endif
                        </td>
                        <td class="align-middle text-uppercase text-dark border-secondary">
                            <span>{{ Carbon::parse($enrolment->learner->person->birth_date)->age }}</span>
                        </td>
                        <td class="align-middle text-uppercase text-dark border-secondary">
                            @if($schoolType === 'international')
                                @if($enrolment->international_education_grade)
                                    <span>{{ $enrolment->international_education_grade->name }}</span>
                                @elseif($enrolment->education_grade)
                                    <span>{{ $enrolment->education_grade->name }}</span>
                                @else
                                    <span>Not Set</span>
                                @endif
                            @else
                                @if($enrolment->education_grade)
                                    <span>{{ $enrolment->education_grade->name }}</span>
                                @else
                                    <span>Not Set</span>
                                @endif
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-center">
            <button data-toggle="modal" data-target="#updateLearnerModal" class="btn bg-dark-teal btn-sm mt-3">
                <span class="text-uppercase">SUBMIT LEARNERS' INFORMATION</span>
            </button>
        </div>
        <div class="modal fade zoom" tabindex="-1" id="updateLearnerModal" wire:ignore.self>
            <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
                <div class="modal-content">
                    <a wire:click="resetForm" class="cursor close" data-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                    <div class="modal-header">
                        <h6 class="modal-title">SUBMIT LEARNERS' INFORMATION</h6>
                    </div>
                    <div class="modal-body pt-0">
                        <ul class="nav nav-tabs">
                            <li wire:click="loadSection('form-create')" class="nav-item">
                                <span class="nav-link cursor {{ $active_section === 'form-create' ? 'active' : '' }}"
                                      href="#addLearnerFormTab">Add Single Learner</span>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane {{ $active_section === 'form-create' ? 'active' : '' }}" id="addLearnerFormTab">
                                <div class="w-100">
                                    @include('emisreturns::livewire.sections.forms.create-learner-form')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <style>
        .iti {
            width: 100%;
        }

        .iti__flag-container {
            z-index: 999;
        }
    </style>
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Only clean up after modal is fully hidden
            function cleanUpModal() {
                setTimeout(function () {
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open').css('padding-right', '');
                }, 100); // Delay to ensure modal is fully hidden
            }

            // Listen for the close-modal event from Livewire
            Livewire.on('close-modal', () => {
                // Hide modals
                $('#updateLearnerModal').modal('hide');
                cleanUpModal();
            });

            // Also clean up when modals are hidden through other means
            $('#updateLearnerModal').on('hidden.bs.modal', function () {
                cleanUpModal();
            });

            // Listen for gender changes
            Livewire.on('photo-gender-updated', function () {
                updateDefaultPhoto();
            });

            //For Levels
            Alpine.data('educationGradeWatcher', (boundValue) => ({
                educationGradeId: boundValue,

                init() {
                    this.$watch('educationGradeId', (value) => {
                        const isAlevel = ['16', '17'].includes(value);
                        this.$wire.call('setAlevelStatus', isAlevel);
                    });

                    setTimeout(() => {
                        const isAlevel = ['16', '17'].includes(this.educationGradeId);
                        this.$wire.call('setAlevelStatus', isAlevel);
                    }, 100);
                }
            }));

            // For Country
            Alpine.data('countryWatcher', (boundCountryId) => ({
                countryId: boundCountryId,

                init() {
                    // Initialize Select2
                    $('#learnerCountryId').select2({
                        minimumResultsForSearch: 0,
                        containerCssClass: 'bg-primary-dim',
                    });

                    // Watch for changes in countryId and update Uganda status
                    this.$watch('countryId', (value) => {
                        const isUganda = value == '221';
                        this.$wire.call('setUgandaStatus', isUganda);
                    });

                    // Set initial state
                    setTimeout(() => {
                        const isUganda = this.countryId == '221';
                        this.$wire.call('setUgandaStatus', isUganda);
                    }, 100);
                }
            }));

            // For Subjects
            Alpine.data('subjectFunctions', () => ({
                selected: {
                    principal: [],
                    subsidiary: [],
                },

                init() {
                    this.selected.principal = [...this.$wire.form_learner.principal_subjects];
                    this.selected.subsidiary = [...this.$wire.form_learner.subsidiary_subject];
                },

                toggle(type, id) {
                    const list = this.selected[type];
                    const index = list.indexOf(id);
                    index === -1 ? list.push(id) : list.splice(index, 1);
                    this.$wire.form_learner[`${type}_subjects`] = list;
                },

                isChecked(type, id) {
                    return this.selected[type].includes(id);
                },

                isDisabled(type, id, max) {
                    const list = this.selected[type];
                    return list.length >= max && !list.includes(id);
                }
            }));
        });

        function getDefaultPhotoUrl(gender) {
            if (gender === 'F') {
                return "{{ asset('/images/default_female.jpg') }}";
            } else {
                return "{{ asset('/images/default_male.jpg') }}";
            }
        }

        function updateDefaultPhoto() {
            const preview = document.getElementById('photoPreview');
            const input = document.getElementById('photoInput');
            if (!input || !preview) return;
            // Only update if no custom photo is selected
            if (!input.files || input.files.length === 0) {
                const gender = document.querySelector('[wire\\:id]').__livewire.gender;
                preview.src = getDefaultPhotoUrl(gender);
            }
        }

        function handlePhotoChange(input) {
            const file = input.files[0];
            const preview = document.getElementById('photoPreview');
            const button = document.getElementById('photoButton');
            const icon = document.getElementById('photoIcon');
            const buttonText = document.getElementById('photoButtonText');
            if (!preview || !button || !icon || !buttonText) return;
            if (file) {
                // Create a URL for the selected file
                const fileURL = URL.createObjectURL(file);
                // Update the preview image
                preview.src = fileURL;
                // Update button to show "Remove Photo"
                button.removeAttribute('onclick');
                icon.className = 'icon ni ni-trash';
                buttonText.textContent = 'Remove Photo';
            }
        }

        document.addEventListener('alpine:init', () => {

        });
    </script>
@endpush

