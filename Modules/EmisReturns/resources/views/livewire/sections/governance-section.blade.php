<div class="w-100">
    <livewire:emis-returns.notification-banner key="governance" :eventName="'notifyGovernance'"/>
        <div class="data-item py-1">
            <div class="">
                @if($schoolType === 'preprimary')
                    <span class="data-label">Does this school have a Center Management Committee (CMC)?</span>
                @elseif($schoolType === 'primary')
                    <span class="data-label">Does this school have a School Management Committee (SMC)?</span>
                @elseif($schoolType === 'secondary')
                    <span class="data-label">Does this school have Board Of Governors (BOG)?</span>
                @endif
                @if(!$governanceData)
                    <span class="ml-lg-5 data-value text-dark">NOT SET</span>
                @elseif($governanceData->has_cmc_smc_or_bog_yn)
                    <span class="ml-lg-5 data-value text-dark">YES</span>
                @else
                    <span class="pl-lg-5 ml-lg-5 data-value text-dark">NO</span>
                @endif
            </div>
            <div class="data-col data-col-end">
                <button 
                    x-on:click="$dispatch('open-governance-modal')"
                    type="button" 
                    class="btn btn-sm bg-dark-teal">
                    <em class="ni ni-edit-fill text-white mr-1"></em>Update
                </button>
            </div>
        </div>
        <div class="data-col-end"></div>
        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">
                        How many 
                        {{$schoolType === 'preprimary' ? 'CMC' : ($schoolType === 'primary' ? 'SMC' : 'BOG')}} 
                        meetings were held in the last 12 months?
                    </span>
                    @if(!$governanceData)
                        <span class="ml-lg-5 data-value text-dark">NOT SET</span>
                    @else
                        <span class="ml-lg-5 data-value text-dark">{{ $governanceData->no_of_meetings }}</span>
                    @endif
                </div>
            </div>
            <div class="data-col-end"></div>
        </div>
        <div class="nk-data data-list">
            <div class="data-item py-1">
                <div class="data-col">
                    <span class="data-label">Does this School hold assemblies?</span>
                    @if(!$governanceData)
                        <span class="ml-lg-5 data-value text-dark">NOT SET</span>
                    @elseif($governanceData->holds_assemblies_yn)
                        <span class="ml-lg-5 data-value text-dark text-uppercase"> YES, {{ $governanceData->assembly_frequency->name}} </span>
                    @else
                        <span class="ml-lg-5 data-value text-dark">NO</span>
                    @endif
                </div>
            </div>
            <div class="data-col-end"></div>
        </div>


    {{-- SMC-CMC-BOG Stat summary table --}}
    <livewire:emis-returns.sections.governance.governance-members 
        :school-type="$schoolType" 
        :institution="$institution"/>

    {{-- Recently added SMC-BOG-CMC Members --}}
    <livewire:emis-returns.sections.governance.recently-added-members 
        :school-type="$schoolType" 
        :institution="$institution" />

    {{-- Add Button --}}
    <div class="d-flex justify-content-center">
        <button x-on:click="$dispatch('open-add-member-modal')" class="btn bg-dark-teal btn-sm mt-3">
            <span class="text-uppercase">
                ADD 
                {{ $schoolType === 'preprimary' ? 'CMC' : ($schoolType === 'primary' ? 'SMC' : 'BOG') }} 
                MEMBERS INFORMATION
            </span>
        </button>
    </div>

    {{-- Add member's modal --}}
    <livewire:emis-returns.sections.governance.modals.add-member-modal 
        :school-type="$schoolType"
        :institution="$institution"
        :academic-year-id="$academicYearId"
        :survey="$survey" />

    {{-- Update school governance modal --}}
    <livewire:emis-returns.sections.governance.modals.update-school-governance-modal 
        :school-type="$schoolType" 
        :institution="$institution" 
        :academic-year-id="$academicYearId"/>
</div>
