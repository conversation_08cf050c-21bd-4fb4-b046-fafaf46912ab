<div>
    @foreach($notifications as $index => $notification)
        <div
            x-data="{ show: true }"
            x-init="
                setTimeout(() => {
                    show = false;
                    $wire.clear({{ $index }});
                }, 5000)
            "
            x-show="show"
            x-transition:leave.duration.500ms
            x-transition:leave.opacity.duration.500ms
            x-transition:leave.transform.duration.500ms
            x-transition:leave-start="opacity-100 translate-y-0"
            x-transition:leave-end="opacity-0 -translate-y-4"
            class="alert alert-icon alert-dismissible d-flex mb-4 pl-3
                {{ $notification['status'] === 'error' ? 'alert-danger' : 'alert-dark-teal' }}"
        >
            <div class="preview-icon-wrap p-0 d-inline mr-2 my-auto">
                @if($notification['status'] === 'error')
                    <em class="ni ni-alert-fill"></em>
                @else
                    <em class="ni ni-check-circle-fill"></em>
                @endif
            </div>
            <span class="my-auto">
                <strong>{{ $notification['title'] }}</strong>
                <span>{!! $notification['message'] !!}</span>
            </span>
            <button
                @click="show = false; $wire.clear({{ $index }})"
                type="button"
                class="close"
            ></button>
        </div>
    @endforeach
</div>
