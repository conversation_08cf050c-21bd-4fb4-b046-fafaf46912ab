<?php

namespace Modules\EmisReturns\tests\Unit\Learners;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Modules\EmisReturns\Livewire\Traits\Learners\LearnerVerificationTrait;
use Tests\TestCase;

class LearnerVerificationTraitTest extends TestCase
{
    use RefreshDatabase;

    protected $component;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test class that uses the trait
        $this->component = Mockery::mock(TestComponent::class)->makePartial();

        // Set up the component with necessary properties
        $this->component->loading = false;
        $this->component->form_learner = [];
        $this->component->uneb_verify = false;
        $this->component->learner_verify = false;
        $this->component->parent_verify = false;
        $this->component->uganda = false;
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_verify_index_number_success()
    {
        // Arrange
        $this->component->has_index_number = 'yes';
        $this->component->aLevel = false;
        $this->component->form_learner = [
            'exam_year' => '2022',
            'index_number' => 'ABC123'
        ];

        // Mock the UnebApiController
        $unebApiMock = Mockery::mock('Modules\Core\Http\Controllers\Api\V1\UNEB\UnebApiController');

        // Set the mock controller on the component
        $this->component->mockUnebController = $unebApiMock;

        // Mock the pleLearnerInfo method to return test data
        $testData = [
            'first_name' => 'John',
            'surname' => 'Doe',
            'other_names' => 'Smith',
            'gender' => 'M',
            'birth_date' => Carbon::parse('2000-01-01'),
            'exam_year' => '2022',
            'code_type' => 'PLE'
        ];
        $unebApiMock->shouldReceive('pleLearnerInfo')->once()->andReturn($testData);

        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', Mockery::any())->once();
        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyIndexNumber();

        // Assert
        $this->assertTrue($this->component->uneb_verify);
        $this->assertEquals('John', $this->component->form_learner['first_name']);
        $this->assertEquals('Doe', $this->component->form_learner['surname']);
        $this->assertEquals('Smith', $this->component->form_learner['other_names']);
        $this->assertEquals('M', $this->component->form_learner['gender']);
        $this->assertEquals('2022', $this->component->form_learner['exam_year']);
        $this->assertEquals('PLE', $this->component->form_learner['code_type']);
    }

    public function test_verify_index_number_failure()
    {
        // Arrange
        $this->component->has_index_number = 'yes';
        $this->component->aLevel = false;
        $this->component->form_learner = [
            'exam_year' => '2022',
            'index_number' => 'INVALID'
        ];

        // Mock the UnebApiController
        $unebApiMock = Mockery::mock('Modules\Core\Http\Controllers\Api\V1\UNEB\UnebApiController');

        // Set the mock controller on the component
        $this->component->mockUnebController = $unebApiMock;

        // Mock the pleLearnerInfo method to return null (verification failed)
        $unebApiMock->shouldReceive('pleLearnerInfo')->once()->andReturn(null);

        // Mock the dispatch method for error notification
        $this->component->shouldReceive('dispatch')->with('notify', [
            'status' => 'error',
            'title' => 'Error:',
            'message' => 'Failed to verify UNEB details. Try again!'
        ])->once();
        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyIndexNumber();

        // Assert
        $this->assertFalse($this->component->uneb_verify);
    }

    public function test_verify_learner_nin_success()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'CM12345678'
        ];

        // Mock the NiraApiController
        $niraApiMock = Mockery::mock('Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController');

        // Set the mock controller on the component
        $this->component->mockNiraController = $niraApiMock;

        // Mock the userInfo method to return test data
        $testResponse = ['success' => true, 'data' => [
            'given_names' => 'John',
            'surname' => 'Doe',
            'gender' => 'M',
            'birth_date' => '2000-01-01'
        ]];
        $niraApiMock->shouldReceive('userInfo')->once()->andReturn($testResponse);

        // Mock the normalizeNiraResponse method
        $normalizedData = [
            'given_names' => 'John',
            'surname' => 'Doe',
            'gender' => 'M',
            'birth_date' => '2000-01-01'
        ];

        // Override the normalizeNiraResponse method in the component
        $this->component->normalizeNiraResponse = function($response) use ($normalizedData) {
            return $normalizedData;
        };

        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', Mockery::any())->once();
        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyLearnerNIN();

        // Assert
        $this->assertTrue($this->component->learner_verify);
        $this->assertTrue($this->component->uganda);
        $this->assertEquals('John', $this->component->form_learner['first_name']);
        $this->assertEquals('Doe', $this->component->form_learner['surname']);
        $this->assertEquals('M', $this->component->form_learner['gender']);
    }

    public function test_verify_learner_nin_failure()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'INVALID'
        ];

        // Mock the NiraApiController
        $niraApiMock = Mockery::mock('Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController');

        // Set the mock controller on the component
        $this->component->mockNiraController = $niraApiMock;

        // Mock the userInfo method to return null (verification failed)
        $niraApiMock->shouldReceive('userInfo')->once()->andReturn(null);

        // Override the normalizeNiraResponse method in the component to return null
        $this->component->normalizeNiraResponse = function($response) {
            return null;
        };

        // Mock the dispatch method for error notification
        $this->component->shouldReceive('dispatch')->with('notify', [
            'status' => 'error',
            'title' => 'Error:',
            'message' => 'Learner NIN verification failed. Try again!'
        ])->once();
        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyLearnerNIN();

        // Assert
        $this->assertFalse($this->component->learner_verify);
    }

    public function test_verify_parent_nin_success()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'CM12345678',
            'parent_nin' => 'CM87654321'
        ];

        // Mock the isNinSame method to return false (different NINs)
        $this->component->shouldReceive('isNinSame')->andReturn(false);

        // Mock the NiraApiController
        $niraApiMock = Mockery::mock('Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController');

        // Set the mock controller on the component
        $this->component->mockNiraController = $niraApiMock;

        // Mock the userInfo method to return test data
        $testResponse = ['success' => true, 'data' => [
            'given_names' => 'Jane',
            'surname' => 'Doe',
            'gender' => 'F',
            'date_of_birth' => '1975-05-15'
        ]];
        $niraApiMock->shouldReceive('userInfo')
            ->once()
            ->withArgs(function (Request $request) {
                return $request->get('id_number') === 'CM87654321';
            })
            ->andReturn($testResponse);

        // Override the normalizeNiraResponse method in the component
        $this->component->normalizeNiraResponse = function($response) {
            return [
                'given_names' => 'Jane',
                'surname' => 'Doe',
                'gender' => 'F',
                'date_of_birth' => '1975-05-15'
            ];
        };

        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', Mockery::any())->once();
        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyParentNIN();

        // Assert
        $this->assertTrue($this->component->parent_verify);
        $this->assertEquals('Jane', $this->component->form_learner['parent_first_name']);
        $this->assertEquals('Doe', $this->component->form_learner['parent_surname']);
        $this->assertEquals('F', $this->component->form_learner['parent_gender']);
        $this->assertEquals('1975-05-15', $this->component->form_learner['parent_birth_date']);
    }

    public function test_verify_parent_nin_same_as_learner()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'CM12345678',
            'parent_nin' => 'CM12345678'
        ];

        // Mock the isNinSame method to return true (same NINs)
        $this->component->shouldReceive('isNinSame')->andReturn(true);

        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', [
            'status' => 'error',
            'title' => 'Error!',
            'message' => 'Parent NIN cannot be the same as that of a learner. Provide a different NIN!',
        ])->once();

        // Act
        $this->component->verifyParentNIN();

        // Assert
        $this->assertFalse($this->component->parent_verify);
    }

    public function test_verify_passport()
    {
        // Arrange
        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Parent Passport verified successfully.'
        ])->once();

        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyPassport();

        // Assert
        $this->assertTrue($this->component->parent_verify);
    }

    public function test_verify_refugee_number()
    {
        // Arrange
        // Mock the dispatch method
        $this->component->shouldReceive('dispatch')->with('notify', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Parent Refugee Number verified successfully.'
        ])->once();

        $this->component->shouldReceive('getFormData')->once();
        $this->component->shouldReceive('getInstitution')->once()->andReturn(null);

        // Act
        $this->component->verifyRefugeeNumber();

        // Assert
        $this->assertTrue($this->component->parent_verify);
    }

    public function test_is_nin_same_returns_true_when_nins_match()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'CM12345678',
            'parent_nin' => 'CM12345678'
        ];

        // Act
        $result = $this->component->isNinSame();

        // Assert
        $this->assertTrue($result);
    }

    public function test_is_nin_same_returns_false_when_nins_differ()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => 'CM12345678',
            'parent_nin' => 'CM87654321'
        ];

        // Act
        $result = $this->component->isNinSame();

        // Assert
        $this->assertFalse($result);
    }

    public function test_is_nin_same_returns_false_when_nins_missing()
    {
        // Arrange
        $this->component->form_learner = [
            'nin' => '',
            'parent_nin' => 'CM87654321'
        ];

        // Act
        $result = $this->component->isNinSame();

        // Assert
        $this->assertFalse($result);
    }
}

// Test class that uses the trait
class TestComponent
{
    use LearnerVerificationTrait;

    public $loading;
    public $form_learner;
    public $uneb_verify;
    public $learner_verify;
    public $parent_verify;
    public $uganda;
    public $has_index_number;
    public $aLevel;
    public $schoolType;
    public $learner_nin;
    public $nira_learner;
    public $nira_parent;
    public $uneb_learner;

    // Mock controller to be set in tests
    public $mockUnebController;
    public $mockNiraController;

    public function dispatch($event, $data = [])
    {
        // Mock implementation
    }

    public function getFormData($institution)
    {
        // Mock implementation
    }

    public function getInstitution()
    {
        // Mock implementation
        return null;
    }

    // Override the controller creation in the trait
    protected function getUnebApiController()
    {
        return $this->mockUnebController;
    }

    protected function getNiraApiController()
    {
        return $this->mockNiraController;
    }

    // Helper method to normalize NIRA response
    protected function normalizeNiraResponse($response)
    {
        // This will be overridden in tests if needed
        // Default implementation for tests
        if (is_array($response) && isset($response['success']) && isset($response['data'])) {
            return $response['data'];
        }
        return null;
    }
}
