<?php

namespace Modules\EmisReturns\tests\Unit\Learners;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\EmisReturns\Livewire\Sections\LearnersSection;
use Tests\TestCase;

class LearnersSectionNationalityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test countries
        Country::create(['id' => 221, 'name' => 'UGANDA', 'code' => 'UGA']);
        Country::create(['id' => 178, 'name' => 'RWANDA', 'code' => 'RWA']);
        Country::create(['id' => 111, 'name' => 'KENYA', 'code' => 'KEN']);
    }

    public function test_it_enables_refugee_radio_buttons_when_non_uganda_nationality_is_selected()
    {
        // Create a mock component to test the method directly
        $component = Mockery::mock(LearnersSection::class)->makePartial();

        // Set initial state - Uganda selected
        $component->uganda = true;
        $component->learner_refugee_no = 'no';
        $component->learner_nin = 'yes';
        $component->learner_verify = true;
        $component->parent_verify = true;

        // Test selecting Rwanda (non-Uganda country)
        $component->setUgandaStatus(false);

        // Uganda should now be false, enabling refugee radio buttons
        $this->assertFalse($component->uganda);

        // Refugee status should remain as it was (not automatically changed when switching to non-Uganda)
        $this->assertEquals('no', $component->learner_refugee_no);

        // NIN should be set to 'no' since NIN is only for Ugandans
        $this->assertEquals('no', $component->learner_nin);

        // Verification states should be reset
        $this->assertFalse($component->learner_verify);
        $this->assertFalse($component->parent_verify);
    }

    public function test_it_disables_refugee_radio_buttons_when_uganda_is_selected()
    {
        // Create a mock component to test the method directly
        $component = Mockery::mock(LearnersSection::class)->makePartial();

        // Set initial state - non-Uganda country selected
        $component->uganda = false;
        $component->learner_refugee_no = 'yes';

        // Test selecting Uganda
        $component->setUgandaStatus(true);

        // Uganda should be true and refugee status should be reset to 'no'
        $this->assertTrue($component->uganda);
        $this->assertEquals('no', $component->learner_refugee_no);
    }

    public function test_it_resets_refugee_status_when_uganda_is_selected()
    {
        // Create a mock component to test the method directly
        $component = Mockery::mock(LearnersSection::class)->makePartial();

        // Set initial state - refugee from another country
        $component->uganda = false;
        $component->learner_refugee_no = 'yes';

        // Test selecting Uganda - should reset refugee status
        $component->setUgandaStatus(true);

        // Uganda should be true and refugee status should be automatically reset to 'no'
        $this->assertTrue($component->uganda);
        $this->assertEquals('no', $component->learner_refugee_no);
    }

    public function test_it_disables_nin_radio_buttons_when_non_uganda_country_is_selected()
    {
        // Create a mock component to test the method directly
        $component = Mockery::mock(LearnersSection::class)->makePartial();

        // Set initial state - Uganda selected with NIN
        $component->uganda = true;
        $component->learner_nin = 'yes';
        $component->learner_verify = true;

        // Test selecting non-Uganda country
        $component->setUgandaStatus(false);

        // NIN should be automatically set to 'no' since NIN is only for Ugandans
        $this->assertEquals('no', $component->learner_nin);

        // Verification should be reset
        $this->assertFalse($component->learner_verify);
        $this->assertFalse($component->parent_verify);
    }
}
