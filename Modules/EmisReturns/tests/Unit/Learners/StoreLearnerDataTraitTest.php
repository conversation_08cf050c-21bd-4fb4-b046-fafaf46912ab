<?php

namespace Modules\EmisReturns\Tests\Unit\Learners;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Modules\Core\Models\Person;
use Modules\Core\Models\Institutions\Learner;
use Modules\Core\Models\Institutions\LearnerIndexNumber;
use Modules\Core\Models\Institutions\LearnerEquatedCode;
use Modules\Core\Models\Settings\IdentityDocumentType;
use Modules\EmisReturns\Livewire\Traits\Learners\StoreLearnerDataTrait;
use Tests\TestCase;
use Mockery;

class StoreLearnerDataTraitTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }
    #[\PHPUnit\Framework\Attributes\Test]
    public function test_it_validates_required_fields()
    {
        $trait = $this->getTraitMock();
        $trait->form_learner = [];
        $this->setProtectedProperty($trait, 'rules', [
            'form_learner.first_name' => 'required',
        ]);
        $this->setProtectedProperty($trait, 'messages', [
            'form_learner.first_name.required' => 'First name required',
        ]);
        $this->expectException(ValidationException::class);
        $trait->validate($this->getProtectedProperty($trait, 'rules'), $this->getProtectedProperty($trait, 'messages'));
    }
    /**
     * Helper to set protected property on an object
     */
    protected function setProtectedProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        do {
            if ($reflection->hasProperty($property)) {
                $prop = $reflection->getProperty($property);
                $prop->setAccessible(true);
                $prop->setValue($object, $value);
                return;
            }
        } while ($reflection = $reflection->getParentClass());
    }

    /**
     * Helper to get protected property from an object
     */
    protected function getProtectedProperty($object, $property)
    {
        $reflection = new \ReflectionClass($object);
        do {
            if ($reflection->hasProperty($property)) {
                $prop = $reflection->getProperty($property);
                $prop->setAccessible(true);
                return $prop->getValue($object);
            }
        } while ($reflection = $reflection->getParentClass());
        return null;
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function test_it_prevents_duplicate_learner_nin_and_parent_nin()
    {
        $trait = $this->getTraitMock();
        $trait->form_learner = [
            'nin' => 'CF678283829138',
            'parent_nin' => 'CF678283829138',
            'first_name' => 'Test',
            'surname' => 'User',
            'birth_date' => '2000-01-01',
            'gender' => 'M',
            'country_id' => 221,
        ];
        $trait->learner_nin = 'no';
        $trait->learner_verify = false;
        $trait->photo = null;
        $trait->school = (object)['school_type_id' => 1, 'has_male_students' => true, 'has_female_students' => true];
        $trait->dispatch = function($event, $data = []) use (&$dispatched) {
            $dispatched[$event] = $data;
        };
        $trait->setGetFormData(function() {});
        $trait->setGetInstitution(function() { return (object)['id' => 1]; });
        $trait->survey = (object)[
            'id' => 1,
            'academic_year' => (object)[],
            // Add more properties if needed by StoreLearnerDataTrait
        ];
        // Create an active AcademicYearTeachingPeriod for the helper to return
        \Modules\Core\Models\Settings\AcademicYearTeachingPeriod::create([
            'teaching_period_id' => 1,
            'academic_year_id' => 1,
            'status' => 'active',
        ]);
        $trait->saveLearner();
        $this->assertArrayHasKey('notify', $dispatched);
        $this->assertStringContainsString('Learner NIN cannot be the same', $dispatched['notify']['message']);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function test_it_checks_for_duplicate_index_number()
    {
        // Ensure OperationalStatus 'ACTIVE' exists for SchoolObserver
        if (class_exists('Modules\\Core\\Models\\Settings\\OperationalStatus')) {
            $opStatusClass = 'Modules\\Core\\Models\\Settings\\OperationalStatus';
            $opStatusClass::firstOrCreate(['name' => 'ACTIVE']);
        }
        // Create required region and local government for observer dependencies
        if (class_exists('Modules\Core\Models\Settings\AdminUnits\Region')) {
            $regionClass = 'Modules\Core\Models\Settings\AdminUnits\Region';
            $regionClass::firstOrCreate(
                ['id' => 1],
                ['name' => 'Test Region', 'region_code' => 'R001']
            );
        }
        if (class_exists('Modules\Core\Models\Settings\AdminUnits\LocalGovernment')) {
            $lgClass = 'Modules\Core\Models\Settings\AdminUnits\LocalGovernment';
            $lgClass::firstOrCreate(['id' => 1], ['name' => 'Test LG']);
        }
        if (class_exists('Modules\Core\Models\Settings\AdminUnits\District')) {
            $districtClass = 'Modules\Core\Models\Settings\AdminUnits\District';
            $districtClass::firstOrCreate(['id' => 1], ['name' => 'Test District']);
        }
        if (class_exists('Modules\Core\Models\Settings\AdminUnits\County')) {
            $countyClass = 'Modules\Core\Models\Settings\AdminUnits\County';
            $countyClass::firstOrCreate(['id' => 1], ['name' => 'Test County', 'local_government_id' => 1]);
        }

        // Ensure country with id 221 exists for foreign key constraint
        if (class_exists('Modules\Core\Models\Settings\AdminUnits\Country')) {
            $countryClass = 'Modules\\Core\\Models\\Settings\\AdminUnits\\Country';
            $countryClass::firstOrCreate(['id' => 221], ['name' => 'Uganda', 'code' => 'UGA']);
        }

        // Ensure identity type exists for trait logic
        \Modules\Core\Models\Settings\IdentityDocumentType::create(['name' => 'NATIONAL ID']);

        // Ensure related school and education grade exist for foreign key constraints
        if (class_exists('Modules\Core\Models\Institutions\School')) {
            $schoolClass = 'Modules\Core\Models\Institutions\School';
            $schoolClass::firstOrCreate([
                'id' => 1
            ], [
                'name' => 'Test School',
                'school_type_id' => 3,
                'county_id' => 1,
                'district_id' => 1
            ]);
        }
        // Ensure education grade with id 14 exists for foreign key constraint
        if (class_exists('Modules\\Core\\Models\\Settings\\SchoolEducationGrade')) {
            $gradeClass = 'Modules\\Core\\Models\\Settings\\SchoolEducationGrade';
            $gradeClass::firstOrCreate(['id' => 14], ['name' => 'Grade 14']);
        }
        // Ensure familiar language with id 1 exists for foreign key constraint
        if (class_exists('Modules\\Core\\Models\\Settings\\FamiliarLanguage')) {
            $familiarLanguageClass = 'Modules\\Core\\Models\\Settings\\FamiliarLanguage';
            $familiarLanguageClass::firstOrCreate(['id' => 1], ['name' => 'English']);
        }

        // Ensure survey with id 1 exists for foreign key constraint
        if (class_exists('Modules\\Core\\Models\\Settings\\Survey')) {
            $surveyClass = 'Modules\\Core\\Models\\Settings\\Survey';
            $surveyClass::firstOrCreate(['id' => 1], ['name' => 'Test Survey']);
        }

        // Ensure academic year with id 1 exists for foreign key constraint
        if (class_exists('Modules\\Core\\Models\\Settings\\AcademicYear')) {
            $academicYearClass = 'Modules\\Core\\Models\\Settings\\AcademicYear';
            $academicYearClass::firstOrCreate(['id' => 1], ['name' => '2025', 'start_date' => '2025-01-01', 'end_date' => '2025-12-31']);
        }

        // Ensure teaching period with id 1 exists for foreign key constraint
        if (class_exists('Modules\\Core\\Models\\Settings\\TeachingPeriod')) {
            $teachingPeriodClass = 'Modules\\Core\\Models\\Settings\\TeachingPeriod';
            $teachingPeriodClass::firstOrCreate(['id' => 1], ['name' => 'Term 1']);
        }

        $trait = $this->getTraitMock();
        // Use the same person details as the manually created Person so the trait will use the same person_id
        $trait->form_learner = [
            'nin' => null,
            'index_number' => '006567/003',
            'exam_year' => '2015',
            'first_name' => 'Test',
            'surname' => 'User',
            'other_names' => null,
            'student_pass' => null,
            'student_refugee_number' => null,
            'photo' => null,
            'birth_date' => '2000-01-01',
            'gender' => 'M',
            'country_id' => 221,
            'is_orphan' => false,
            'orphan_type' => null,
            'education_grade_id' => 14,
            'equated_code' => null,
            'equated_year' => null,
            'exam_level' => null,
            'code_type' => null,
            'in_national_curriculum' => null,
            'inter_sch_calendar_id' => null,
            'inter_sch_curriculum_id' => null,
            'inter_sch_education_grade_id' => null,
            'inter_sch_education_level_id' => null,
            'district_of_birth_id' => 1,
            'is_offering_examinable_course' => null,
            'post_primary_institution_course_id' => null,
            'institution_examined_course_id' => null,
            'course_id' => null,
            'parent_nin' => 'CF678283829138',
            'parent_passport' => null,
            'parent_refugee_number' => null,
            'parent_photo' => null,
            'parent_first_name' => 'TEST',
            'parent_surname' => 'PARENT',
            'parent_other_names' => null,
            'parent_birth_date' => '1986-01-01',
            'parent_gender' => 'F',
            'parent_relationship' => 'Parent',
            'parent_phone_1' => '**********',
            'parent_phone_2' => null,
            'parent_email' => null,
            'learner_special_needs' => null,
            'learner_health_issues' => null,
            'learner_talents' => null,
            'learner_practical_skills' => null,
            'familiar_language_id' => 1,
            'principal_subjects' => null,
            'subsidiary_subject' => null,
        ];
        $trait->school = (object)[
            'id' => 1,
            'school_type_id' => 3,
            'has_male_students' => true,
            'has_female_students' => true
        ];
        // Create a valid Person and Learner for the index number
        $person = Person::create([
            'first_name' => 'Test',
            'surname' => 'User',
            'other_names' => null,
            'birth_date' => '2000-01-01',
            'gender' => 'M',
            'country_id' => 221,
        ]);
        // Add required fields for Learner model
        $learner = Learner::create([
            'person_id' => $person->id,
            'current_school_id' => 1,
            'current_education_grade_id' => 14
        ]);
        $this->assertNotNull($learner->person_id, 'Learner was not created properly, person_id is null');
        $this->assertNotNull($learner->person_id, 'Learner was not created properly, person_id is null');
        LearnerIndexNumber::create([
            'learner_id' => $learner->person_id,
            'index_number' => '006567/003',
            'exam_year' => '2015'
        ]);
        $trait->dispatch = function($event, $data = []) use (&$dispatched) {
            $dispatched[$event] = $data;
        };
        $trait->setGetFormData(function() {});
        $trait->setGetInstitution(function() { return (object)['id' => 1]; });
        $trait->survey = (object)[
            'id' => 1,
            'academic_year' => (object)['id' => 1],
            'sections' => [],
            'section_items' => [],
        ];
        // Create an active AcademicYearTeachingPeriod for the helper to return
        \Modules\Core\Models\Settings\AcademicYearTeachingPeriod::create([
            'teaching_period_id' => 1,
            'academic_year_id' => 1,
            'status' => 'active',
        ]);
        $this->assertNotNull(get_active_teaching_period(), 'get_active_teaching_period() did not return a value. Check migrations and model.');
        $trait->saveLearner();
    }
    #[\PHPUnit\Framework\Attributes\Test]
    public function test_it_creates_new_person_with_identity()
    {
        $trait = $this->getTraitMock();
        $form = [
            'nin' => 'CF678283829138',
            'country_id' => 221,
            'first_name' => 'MARY',
            'surname' => 'JANE',
            'other_names' => 'EMIS',
            'birth_date' => '2000-01-01',
            'gender' => 'M',
        ];
        IdentityDocumentType::create(['name' => 'NATIONAL ID']);
        $person = $trait->callCreateNewPerson($form, $trait->getLearnerConfig());
        $this->assertInstanceOf(Person::class, $person);
        $this->assertEquals('CF678283829138', $person->id_number);
    }

    protected function getTraitMock()
    {
        return new class {
            public $schoolType = null;
            public function dispatch($event, $data = []) { if (is_callable($this->dispatch)) { return ($this->dispatch)($event, $data); } }
            use StoreLearnerDataTrait;
            // Do NOT redeclare $rules, $messages, $learnerConfig, $parentConfig, etc. Let the trait's definitions be used.
            public $form_learner = [];
            public $learner_nin = 'no';
            public $learner_verify = false;
            public $photo = null;
            public $school;
            public $survey;
            public $dispatch;
            private $getFormData;
            private $getInstitution;
            public function validate($rules, $messages) {
                if (empty($this->form_learner['first_name'])) {
                    throw ValidationException::withMessages(['form_learner.first_name' => ['First name required']]);
                }
            }
            public function setGetFormData($fn) { $this->getFormData = $fn; }
            public function setGetInstitution($fn) { $this->getInstitution = $fn; }
            public function getFormData($school) { if ($this->getFormData) { ($this->getFormData)($school); } }
            public function getInstitution() { return $this->getInstitution ? ($this->getInstitution)() : (object)['id' => 1]; }
            public function callCreateNewPerson($form, $config) { return $this->createNewPerson($form, $config); }
            public function getLearnerConfig() { return $this->learnerConfig; }
            // Stub for trait dependency
            public function getPersonWithoutIdentityDocument($surname, $first_name, $birth_date, $gender) {
                return null;
            }
            public function getPersonWithIdentity($id_value, $country_id, $type) {
                return null;
            }
            // Stub for trait dependency: reset
            public function reset(...$args) {
                // No-op for test double
            }
        };
    }
}
