<?php

namespace Modules\EmisReturns\Livewire\Traits\Learners;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\Learner;
use Modules\Core\Models\Institutions\LearnerDisability;
use Modules\Core\Models\Institutions\LearnerEnrolment;
use Modules\Core\Models\Institutions\LearnerEquatedCode;
use Modules\Core\Models\Institutions\LearnerFamiliarLanguage;
use Modules\Core\Models\Institutions\LearnerHealthIssue;
use Modules\Core\Models\Institutions\LearnerIndexNumber;
use Modules\Core\Models\Institutions\LearnerPracticalSkill;
use Modules\Core\Models\Institutions\LearnerSecondarySchoolSubject;
use Modules\Core\Models\Institutions\LearnerTalent;
use Modules\Core\Models\Institutions\SchoolRegistrationApproval;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\Survey;
use Modules\EmisReturns\Models\LearnerEnrolmentApplication;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\Core\Models\Settings\EnrolmentType;
use Modules\Core\Models\Settings\IdentityDocumentType;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Services\LearnerService;
use Modules\LearnerManagement\Jobs\DetectLearnerDuplicatesJob;

trait StoreLearnerDataTrait
{
    protected $rules = [
        'form_learner.first_name' => 'required|string',
        'form_learner.surname' => 'required|string',
        'form_learner.other_names' => 'nullable|string',
        'form_learner.birth_date' => 'required|string',
        'form_learner.nin' => 'nullable',
        'form_learner.student_pass' => 'nullable|regex:/^ST[0-9]{7}$/',
        'form_learner.gender' => 'required',
        'form_learner.country_id' => 'required',
    ];

    protected $messages = [
        'form_learner.first_name.required' => 'Learner First Name is required',
        'form_learner.surname.required' => 'Learner Surname is also required',
        'form_learner.other_names.string' => 'Learner Other Names must contain only letters',
        'form_learner.birth_date.required' => 'Date of Birth is also required',
        'form_learner.gender.required' => 'Sex is also required',
        'form_learner.country_id.required' => 'The Nationality is also required',
        'form_learner.education_grade_id.required' => 'Education Grade is also required',
        'form_learner.nin.regex' => "The NIN you provided for the learner is in the wrong format. Try again",
        'form_learner.student_pass.regex' => "The Student Pass you provided for the learner is in the wrong format. Try again",
        'form_learner.parent_nin.regex' => "The NIN you provided for the Parent is in the wrong format. Try again",
    ];

    // Parent rules for the learner form
    protected $parentRules = [
        'form_learner.parent_first_name' => 'required|string',
        'form_learner.parent_surname' => 'required|string',
        'form_learner.parent_other_names' => 'nullable|string',
        'form_learner.parent_birth_date' => 'required',
        'form_learner.parent_nin' => 'nullable',
        'form_learner.parent_passport' => 'nullable',
        'form_learner.parent_refugee_number' => 'nullable',
        'form_learner.parent_gender' => 'required',
        'form_learner.parent_relationship' => 'required',
    ];

    protected $learnerConfig = [
        'nin' => 'nin',
        'student_pass' => 'student_pass',
        'refugee' => 'student_refugee_number',
        'first_name' => 'first_name',
        'surname' => 'surname',
        'other_names' => 'other_names',
        'birth_date' => 'birth_date',
        'gender' => 'gender',
        'phone_1' => '',
        'phone_2' => '',
        'email' => '',
    ];

    protected $parentConfig = [
        'nin' => 'parent_nin',
        'passport' => 'parent_passport',
        'refugee' => 'parent_refugee_number',
        'first_name' => 'parent_first_name',
        'surname' => 'parent_surname',
        'other_names' => 'parent_other_names',
        'birth_date' => 'parent_birth_date',
        'gender' => 'parent_gender',
        'phone_1' => 'parent_phone_1',
        'phone_2' => 'parent_phone_2',
        'email' => 'parent_email',
    ];

    public function updatedSchoolTypeId(): void
    {
        // Dynamically update rules and messages based on the school_type_id
        $rules = [];
        $messages = [];

        if (in_array($this->school->school_type_id, [1, 2])) {
            $rules = array_merge($this->parentRules, [
                'form_learner.familiar_language_id' => 'required',
            ]);

            $messages['form_learner.familiar_language_id.required'] = 'Select Familiar Language to proceed.';
        }

        if ($this->school->school_type_id === 3) {
            $rules = $this->parentRules;

            $country = Country::find($this->form_learner['country_id'] ?? null);
            if ($country && $country->code === 'UGA') {
                if (($this->form_learner['hasEquatedCode'] ?? 'NO') === 'NO') {
                    $rules['form_learner.index_number'] = 'required';
                    $rules['form_learner.exam_year'] = 'required';

                    $messages['form_learner.index_number.required'] = 'Index number is required to proceed.';
                    $messages['form_learner.exam_year.required'] = 'Exam Year is required to proceed.';
                } else {
                    $rules['form_learner.equated_code'] = 'required';
                    $rules['form_learner.equated_year'] = 'required';
                    $rules['form_learner.exam_level'] = 'required';

                    $messages['form_learner.equated_code.required'] = 'Index number is required.';
                    $messages['form_learner.equated_year.required'] = 'Equated Year is required.';
                    $messages['form_learner.exam_level.required'] = 'Exam Level is required.';
                }
            }
        }

        if (in_array($this->school->school_type_id, [4, 5])) {
            $rules = [
                'form_learner.post_primary_institution_course_id' => $this->form_learner['is_offering_examinable_course'] === 'yes' ? 'required' : 'nullable',
                'form_learner.institution_examined_course_id' => $this->form_learner['is_offering_examinable_course'] === 'no' ? 'required' : 'nullable',
            ];

            $messages['form_learner.post_primary_institution_course_id.required'] = 'Select an examinable course to proceed';
            $messages['form_learner.institution_examined_course_id.required'] = 'Select a non-examinable course to proceed';
        }

        if ($this->school->school_type_id === 6) {
            $rules = [
                'form_learner.sponsor_id' => 'required',
                'form_learner.entry_mode_id' => 'required',
                'form_learner.course_id' => 'required',
            ];

            $messages = array_merge($messages, [
                'form_learner.sponsor_id.required' => __('validation.custom.sponsor_id.required'),
                'form_learner.entry_mode_id.required' => __('validation.custom.entry_mode_id.required'),
                'form_learner.course_id.required' => __('validation.custom.course_id.required'),
            ]);
        }

        // International schools (school_type_id === 7)
        if ($this->school->school_type_id === 7) {
            $rules = array_merge($rules, [
                'form_learner.inter_sch_curriculum_id' => 'required',
                'form_learner.inter_sch_education_grade_id' => 'required',
                'form_learner.inter_sch_calendar_id' => 'required',
            ]);

            $messages = array_merge($messages, [
                'form_learner.inter_sch_curriculum_id.required' => 'Curriculum is required for international schools.',
                'form_learner.inter_sch_education_grade_id.required' => 'Class is required for international schools.',
                'form_learner.inter_sch_calendar_id.required' => 'Calendar is required for international schools.',
            ]);
        } else {
            // For non-international schools, education_grade_id is required
            $rules['form_learner.education_grade_id'] = 'required';
        }

        $this->rules = $rules;
        $this->messages = $messages;
    }

    public function saveLearnerDraft()
    {
        $this->saveLearner(true);
    }

    public function saveLearner($isDraft = false)
    {
        // Validate the form data
        try {
            $this->validate($this->rules, $this->messages);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Convert validation errors array to a readable string
            $errorMessages = [];
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $errorMessages[] = $message;
                }
            }
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Validation Error:',
                'message' => implode(' ', $errorMessages),
            ]);
            $this->getFormData($this->getInstitution());
            return;
        }

        //Check if the application already exists

        // Check if the learner has both a photo and a validated NIN
        if ($this->photo && $this->learner_nin === 'yes' && $this->learner_verify) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Validation Error:',
                'message' => 'Learners with a validated NIN should not have a photo uploaded. Please remove the photo or change the learner NIN option to "NO".',
            ]);
            return;
        }

        // Get the form data
        $form = $this->form_learner;

        // Set is_draft_yn status if saving as draft
        if ($isDraft) {
            $form['is_draft_yn'] = true;
        } else {
            $form['is_draft_yn'] = false;
        }

        // Check if Learner NIN is the same as Parent NIN
        if (!empty($form['nin']) && !empty($form['parent_nin']) &&
            strtoupper($form['nin']) === strtoupper($form['parent_nin'])
        ) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'Learner NIN cannot be the same as that of a parent. Provide a different NIN!',
            ]);
            return;
        }

        // Check for duplicate index number or equated code (for school type secondary)
        if ($this->school->school_type_id === 3) {
            if (!empty($form['index_number']) && !empty($form['exam_year']) &&
                LearnerIndexNumber::where('index_number', $form['index_number'])
                    ->where('exam_year', $form['exam_year'])
                    ->exists()
            ) {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error!',
                    'message' => 'This index number has already been registered for the ' . $form['exam_year'] . ' exam year. Try again',
                ]);
            }

            if (!empty($form['equated_code']) &&
                !empty($form['equated_year']) &&
                LearnerEquatedCode::where('equated_code', $form['equated_code'])
                    ->where('equated_year', $form['equated_year'])
                    ->exists()
            ) {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error!',
                    'message' => 'This equated code has already been registered for the ' . $form['equated_year'] . ' equated year. Try again',
                ]);
            }
        }

        // Check if Learner with the same names, gender, date of birth and parent nin already exists
        $learnerService = new LearnerService();
        $learner = $learnerService->checkIfLearnerExists($form);
        // Prevent duplicate learner
        if ($learner) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'Learner with same details already exists! Try learner transfer instead.',
            ]);
            return;
        }

        // Check learner gender against school's allowed gender composition
        $gender = $form['gender'] ?? null;
        if ($gender === 'F' && $this->school->has_male_students === true && $this->school->has_female_students === false) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'You can only add Male Learners to this school!',
            ]);
            return;
        }
        if ($gender === 'M' && $this->school->has_female_students === true && $this->school->has_male_students === false) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'You can only add Female Learners to this school!',
            ]);
            return;
        }

        // Proceed to save learner data
        try {
            $survey = $this->survey;
            $academicYear = $survey->academic_year;
            $term = get_active_teaching_period();

            // Handle the saving of the learner and related data with transaction
            $this->saveLearnerEnrolmentApplication($term, $academicYear, $survey, $form);
            $this->refreshDataAfterSave(); // Refresh the data displayed in the UI

        } catch (ModelNotFoundException $exception) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'We cannot find the Survey you are trying to update',
            ]);
        }

        $this->dispatch('close-modal'); // Close modal first
        $this->reset('form_learner','uneb_learner','nira_learner','nira_parent','parent_verify','learner_verify','uneb_verify','learner_nin','photo');  // Reset form data
        // Dispatch event to update totals and recent learners in the UI
        // This is done after modal closing to avoid interference
        $this->dispatch('learner-saved');
    }

    protected function resolvePerson($application, array $config): ?Person
    {
        // Accept both array and model, convert model to array if needed
        if ($application instanceof Arrayable) {
            $application = $application->toArray();
        }

        if (!empty($application[$config['nin'] ?? ''])) {
            return $this->getPersonWithIdentity($application[$config['nin']], intval($application['country_id']), 'nin');
        }

        if (!empty($application[$config['passport'] ?? ''])) {
            return $this->getPersonWithIdentity($application[$config['passport']], intval($application['country_id']), 'passport');
        }

        if (!empty($application[$config['student_pass'] ?? ''])) {
            return $this->getPersonWithIdentity($application[$config['student_pass']], intval($application['country_id']), 'student_pass');
        }

        if (!empty($application[$config['refugee'] ?? ''])) {
            return $this->getPersonWithIdentity($application[$config['refugee']], intval($application['country_id']), 'refugee_number');
        }

        return $this->getPersonWithoutIdentityDocument(
            $application[$config['surname']],
            $application[$config['first_name']],
            $config['birth_date'] ? Carbon::parse($application[$config['birth_date']]) : null,
            $application[$config['gender']]
        );
    }

    protected function createNewPerson($application, array $config): Person
    {
        // Accept both array and model, convert model to array if needed
        if ($application instanceof Arrayable) {
            $application = $application->toArray();
        }

        $idNumber = null;
        if (!empty($application[$config['nin'] ?? ''])) {
            $idNumber = $application[$config['nin']];
        } elseif (!empty($application[$config['passport'] ?? ''])) {
            $idNumber = $application[$config['passport']];
        } elseif (!empty($application[$config['student_pass'] ?? ''])) {
            $idNumber = $application[$config['student_pass']];
        } elseif (!empty($application[$config['refugee'] ?? ''])) {
            $idNumber = $application[$config['refugee']];
        }

        $identityType = match (true) {
            !empty($application[$config['nin'] ?? '']) => 'NATIONAL ID',
            !empty($application[$config['passport'] ?? '']) => 'PASSPORT',
            !empty($application[$config['student_pass'] ?? '']) => 'STUDENT PASS',
            !empty($application[$config['refugee'] ?? '']) => 'REFUGEE NUMBER',
            default => null
        };

        $identityTypeId = $identityType
            ? IdentityDocumentType::where('name', $identityType)->first()?->id
            : null;

        return Person::create([
            'id_number' => $idNumber,
            'identity_type_id' => $identityTypeId,
            'country_id' => $application['country_id'],
            'first_name' => $application[$config['first_name']],
            'surname' => $application[$config['surname']],
            'other_names' => $application[$config['other_names'] ?? 'other_names'] ?? null,
            'birth_date' => $config['birth_date'] ? $application[$config['birth_date']] : null,
            'gender' => $application[$config['gender']],
            'phone_1' => $application[$config['phone_1'] ?? ''] ?? null,
            'phone_2' => $application[$config['phone_2'] ?? ''] ?? null,
            'email' => $application[$config['email'] ?? ''] ?? null,
        ]);
    }

    public function saveLearnerEnrolmentApplication($term, $academicYear, $survey, $form): void
    {
        DB::beginTransaction(); // Start a transaction

        try {
            $application = LearnerEnrolmentApplication::create([
                'application_number' => $this->nextApplicationNumber(),
                'school_id' => $this->school->id,
                'academic_year_id' => $academicYear->id,
                'teaching_period_id' => $term->teaching_period_id,
                'survey_id' => $survey->id,
                'nin' => nullIfEmpty(Str::upper($form['nin'])),
                'first_name' => Str::upper($form['first_name']),
                'surname' => Str::upper($form['surname']),
                'other_names' => nullIfEmpty(Str::upper($form['other_names'])),
                'student_pass' => nullIfEmpty(Str::upper($form['student_pass'])),
                'student_refugee_number' => nullIfEmpty(Str::upper($form['student_refugee_number'])),
                'birth_date' => $form['birth_date'],
                'photo' => nullIfEmpty($form['photo']),
                'gender' => $form['gender'],
                'country_id' => $form['country_id'],
                'district_of_birth_id' => nullIfEmpty($form['district_of_birth_id']),
                'parent_nin' => nullIfEmpty(Str::upper($form['parent_nin'])),
                'parent_passport' => nullIfEmpty(Str::upper($form['parent_passport'])),
                'parent_refugee_number' => nullIfEmpty(Str::upper($form['parent_refugee_number'])),
                'parent_first_name' => Str::upper($form['parent_first_name']),
                'parent_surname' => Str::upper($form['parent_surname']),
                'parent_other_names' => Str::upper($form['parent_other_names']),
                'parent_photo' => $form['parent_photo'],
                'parent_birth_date' => $form['parent_birth_date'],
                'parent_gender' => $form['parent_gender'],
                'parent_country_id' => !empty($form['parent_nin']) ? 221 : $form['country_id'],
                'parent_relationship' => $form['parent_relationship'],
                'parent_phone_1' => $form['parent_phone_1'],
                'parent_phone_2' => $form['parent_phone_2'],
                'parent_email' => Str::lower($form['parent_email']),
                'education_grade_id' => $form['education_grade_id'],
                'index_number' => nullIfEmpty(Str::upper($form['index_number'])),
                'exam_year' => nullIfEmpty($form['exam_year']),
                'equated_code' => nullIfEmpty($form['equated_code']),
                'equated_year' => nullIfEmpty($form['equated_year']),
                'exam_level' => nullIfEmpty($form['exam_level']),
                'code_type' => nullIfEmpty($form['code_type']),
                'post_primary_institution_course_id' => $form['is_offering_examinable_course'] === 'yes' ? nullIfEmpty($form['post_primary_institution_course_id']) : null,
                'institution_examined_course_id' => $form['is_offering_examinable_course'] === 'no' ? nullIfEmpty($form['institution_examined_course_id']) : null,
                'is_offering_examinable_course' => nullIfEmpty($form['is_offering_examinable_course']),
                'is_orphan' => $form['is_orphan'],
                'orphan_type' => $form['orphan_type'],
                'learner_special_needs' => json_encode($form['learner_special_needs']),
                'learner_health_issues' => json_encode($form['learner_health_issues']),
                'learner_talents' => json_encode($form['learner_talents']),
                'learner_practical_skills' => json_encode($form['learner_practical_skills']),
                'familiar_language_id' => nullIfEmpty($form['familiar_language_id']),
                'principal_subjects' => json_encode($form['principal_subjects']),
                'subsidiary_subject' => json_encode($form['subsidiary_subject']),
                'in_national_curriculum' => nullIfEmpty($form['in_national_curriculum']),
                'inter_sch_calendar_id' => nullIfEmpty($form['inter_sch_calendar_id']),
                'inter_sch_curriculum_id' => nullIfEmpty($form['inter_sch_curriculum_id']),
                'inter_sch_education_grade_id' => nullIfEmpty($form['inter_sch_education_grade_id']),
                'inter_sch_education_level_id' => nullIfEmpty($form['inter_sch_education_level_id']),
                'approval_status' => 'pending',
                'is_draft_yn' => $form['is_draft_yn'] ?? false,
            ]);

            // Log activity
            AdminLogActivity::addToLog('Create Learner Pre-Registration Application', '1', 'Create', "User added learner pre-registration application", 'school');

            DB::commit(); // Save all changes

            // Dispatch success notification based on draft status
            if ($form['is_draft_yn'] ?? FALSE) {
                $this->dispatch('notify', [
                    'status' => 'success',
                    'title' => 'Success:',
                    'message' => 'Learner Pre-Registration Application Saved As Draft successfully.'
                ]);
            } else {
                $this->dispatch('notify', [
                    'status' => 'success',
                    'title' => 'Success:',
                    'message' => 'Learner Pre-Registration Application Submitted Successfully.'
                ]);
            }

            // Dispatch job to detect duplicate learners
            DetectLearnerDuplicatesJob::dispatch($application->id);

        } catch (ModelNotFoundException $exception) {
            DB::rollBack(); // Revert all changes
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'Learner pre-registration application could not be saved. Please try again later.',
            ]);
        }
    }

    public function saveLearnerEnrolment($application): void
    {
        DB::beginTransaction(); // Start a transaction

        try {
            // Create/update SchoolSurvey
            $schoolSurvey = SchoolSurvey::updateOrCreate([
                'school_id' => $application['school_id'],
                'survey_id' => $application['survey_id'],
            ], [
                'last_saved' => now(),
            ]);

            $survey = Survey::where('id', $application['survey_id'])->with(['sections', 'section_items'])->firstOrFail();
            $schoolSurvey->sections()->sync($survey->sections);
            $schoolSurvey->section_items()->sync($survey->section_items);
            $schoolSurvey->sections()->updateExistingPivot(3, [
                'is_complete_yn' => true,
            ]);

            // Resolve learner person
            $learnerPerson = $this->resolvePerson($application, $this->learnerConfig);
            if ($learnerPerson === null) {
                $learnerPerson = $this->createNewPerson($application, $this->learnerConfig);
            }

            // Save identity document
            if (!empty($application['nin'])) {
                $this->updateIdentityDocument($learnerPerson, 'NATIONAL ID', $application['nin'], $application['country_id']);
            } elseif (!empty($application['student_pass'])) {
                $this->updateIdentityDocument($learnerPerson, 'STUDENT PASS', $application['student_pass'], $application['country_id']);
            } elseif (!empty($application['student_refugee_number'])) {
                $this->updateIdentityDocument($learnerPerson, 'REFUGEE NUMBER', $application['student_refugee_number'], $application['country_id']);
            }

            // Save learner record into learners table
            $learner = Learner::updateOrCreate([
                'person_id' => $learnerPerson->id,
            ], [
                'is_father_dead' => in_array($application['orphan_type'], ['both-dead', 'father']),
                'is_mother_dead' => in_array($application['orphan_type'], ['both-dead', 'mother']),
                'current_education_grade_id' => nullIfEmpty($application['education_grade_id']),
                'in_national_curriculum' => nullIfEmpty($application['in_national_curriculum']),
                'inter_sch_calendar_id' => nullIfEmpty($application['inter_sch_calendar_id']),
                'inter_sch_curriculum_id' => nullIfEmpty($application['inter_sch_curriculum_id']),
                'inter_sch_education_grade_id' => nullIfEmpty($application['inter_sch_education_grade_id']),
                'inter_sch_education_level_id' => nullIfEmpty($application['inter_sch_education_level_id']),
                'district_of_birth_id' => nullIfEmpty($application['district_of_birth_id']),
                'current_school_id' => $application->school_id,
                'current_post_primary_institution_course_id' => $application['is_offering_examinable_course'] === 'yes' ? nullIfEmpty($application['post_primary_institution_course_id']) : null,
                'current_institution_examined_course_id' => $application['is_offering_examinable_course'] === 'no' ? nullIfEmpty($application['institution_examined_course_id']) : null,
            ]);

            // Save index number
            if (!empty($application['index_number']) && !empty($application['exam_year'])) {
                $grade = SchoolEducationGrade::find($application['education_grade_id']);
                if ($grade) {
                    $learner->index_numbers()->updateOrCreate([
                        'index_number' => $application['index_number'],
                        'exam_year' => $application['exam_year'],
                        'level' => $grade->grade_rank <= 4 ? 'PLE' : 'UCE',
                    ]);
                }
            }

            // Save equated code
            if (!empty($application['equated_code']) && !empty($application['equated_year']) && !empty($application['exam_level'])) {
                LearnerEquatedCode::updateOrCreate([
                    'learner_id' => $learner->person_id,
                    'education_grade_id' => $application['education_grade_id'],
                    'equated_code' => $application['equated_code'],
                    'equated_year' => $application['equated_year'],
                    'exam_level' => $application['exam_level'],
                    'exam_year' => $application['exam_year'],
                    'code_type' => $application['code_type'],
                ]);
            }

            $learnerPerson->photo = $this->uploadAvatar($learner, $application['photo']); // Upload learner photo
            $learnerPerson->save();
            $this->syncParent($learner, $application); // Sync Parent
            $this->handleLearnerEnrolment($learner, $learnerPerson,$application, $schoolSurvey); // Handle Enrolment

            // Log activity
            AdminLogActivity::addToLog('Add Learner', '1', 'Create', "User added a learner {$learnerPerson->full_name}", 'school');

            DB::commit(); // Save all changes

        } catch (ModelNotFoundException $exception) {
            DB::rollBack(); // Revert all changes
            abort(500, 'Failed to save learner data. Please try again later.');
        }
    }

    protected function syncParent(Learner $learner, $application): void
    {
        // Use generic resolver/creator
        $parentPerson = $this->resolvePerson($application, $this->parentConfig);
        if ($parentPerson === null) {
            $parentPerson = $this->createNewPerson($application, $this->parentConfig);
        }

        // Update parent contact
        $parentPerson->update([
            'phone_1' => $application['parent_phone_1'],
            'phone_2' => $application['parent_phone_2'],
            'email' => $application['parent_email'],
        ]);

        // Attach parent to learner
        $learner->parents()->syncWithoutDetaching([
            $parentPerson->id => [
                'is_parent_yn' => $application['parent_relationship'] === 'parent',
                'is_next_of_kin' => $application->school_type_id === 6,
            ]
        ]);

        // Sync identity document
        if (!empty($application['parent_nin'])) {
            $this->updateIdentityDocument($parentPerson, 'NATIONAL ID', $application['parent_nin'], $application['country_id']);
        } elseif (!empty($application['parent_passport'])) {
            $this->updateIdentityDocument($parentPerson, 'PASSPORT', $application['parent_passport'], $application['country_id']);
        } elseif (!empty($application['parent_refugee_number'])) {
            $this->updateIdentityDocument($parentPerson, 'REFUGEE NUMBER', $application['parent_refugee_number'], $application['country_id']);
        }
    }

    protected function updateIdentityDocument(Person $person, string $type, string $document, $countryId): void
    {
        $identityTypeId = IdentityDocumentType::where('name', $type)->first()?->id;
        $status = $type === 'NATIONAL ID' ? 1 : 3;

        $person->identity_documents()->updateOrCreate([
            'identity_type_id' => $identityTypeId,
            'country_id' => $countryId,
        ], [
            'document_id' => $document,
            'verification_status' => $status,
        ]);
    }

    protected function handleLearnerEnrolment($learner, $learnerPerson, $application, $schoolSurvey): void
    {
        // Prepare enrolment data based on a school type
        $enrol = [
            'photo' => $learner->photo,
            'education_grade_id' => $application->school->school_type->name === 'international' ? null : nullIfEmpty($application['education_grade_id']),
            'enrolment_type_id' => EnrolmentType::where('name', 'BASELINE')->first()?->id,
        ];

        if (in_array($application->school->school_type_id, [4, 5])) {
            $enrol['post_primary_institution_course_id'] = $application['is_offering_examinable_course'] === 'yes' ? nullIfEmpty($application['post_primary_institution_course_id']) : null;
            $enrol['institution_examined_course_id'] = $application['is_offering_examinable_course'] === 'no' ? nullIfEmpty($application['institution_examined_course_id']) : null;
        } elseif ($application->school->school_type_id === 6) {
            $enrol['sponsor_id'] = nullIfEmpty($application['sponsor_id']);
            $enrol['course_id'] = nullIfEmpty($application['course_id']);
            $enrol['entry_mode_id'] = nullIfEmpty($application['entry_mode_id']);
        } else {
            $enrol = array_merge($enrol, [
                'familiar_language_id' => nullIfEmpty($application['familiar_language_id']),
                'in_national_curriculum' => nullIfEmpty($application['in_national_curriculum']),
                'inter_sch_calendar_id' => nullIfEmpty($application['inter_sch_calendar_id']),
                'inter_sch_curriculum_id' => nullIfEmpty($application['inter_sch_curriculum_id']),
                'inter_sch_education_grade_id' => nullIfEmpty($application['inter_sch_education_grade_id']),
                'inter_sch_education_level_id' => nullIfEmpty($application['inter_sch_education_level_id']),
                'is_father_alive_yn' => in_array($application['orphan_type'], ['both-dead', 'father']),
                'is_mother_alive_yn' => in_array($application['orphan_type'], ['both-dead', 'mother']),
            ]);
        }

        // Create learner enrolment
        LearnerEnrolment::updateOrCreate([
            'learner_id' => $learnerPerson->id,
            'school_id' => $application['school_id'],
            'academic_year_id' => $application['academic_year_id'],
            'teaching_period_id' => $application['teaching_period_id'],
            'school_survey_id' => $schoolSurvey->id,
        ], $enrol);

        // Store Familiar Language
        if (!empty($application['familiar_language_id'])) {
            LearnerFamiliarLanguage::updateOrCreate([
                'learner_id' => $learner->person_id,
                'familiar_language_id' => $application['familiar_language_id'],
            ]);
        }

        // Store Special Needs
        if (is_string($application['learner_special_needs'] ?? null)) {
            $specialNeeds = json_decode($application['learner_special_needs'], true) ?? [];
        }
        foreach ($specialNeeds ?? [] as $specialNeedId) {
            LearnerDisability::updateOrCreate([
                'learner_id' => $learnerPerson->id,
                'disability_type_id' => $specialNeedId,
                'school_id' => $application['school_id'],
                'academic_year_id' => $application['academic_year_id'],
                'teaching_period_id' => $application['teaching_period_id'],
                'school_survey_id' => $schoolSurvey->id,
            ]);
        }

        // Store Health Issues
        if (is_string($application['learner_health_issues'] ?? null)) {
            $healthIssues = json_decode($application['learner_health_issues'], true) ?? [];
        }
        foreach ($healthIssues ?? [] as $issueId) {
            LearnerHealthIssue::updateOrCreate([
                'learner_id' => $learnerPerson->id,
                'health_issue_id' => $issueId,
                'school_id' => $application['school_id'],
                'academic_year_id' => $application['academic_year_id'],
                'teaching_period_id' => $application['teaching_period_id'],
                'school_survey_id' => $schoolSurvey->id,
            ], [
                'is_recovered_yn' => false,
            ]);
        }

        // Store Talents
        if (is_string($application['learner_talents'] ?? null)) {
            $talents = json_decode($application['learner_talents'], true) ?? [];
        }
        foreach ($talents ?? [] as $talentId) {
            LearnerTalent::updateOrCreate([
                'learner_id' => $learnerPerson->id,
                'talent_id' => $talentId,
                'school_id' => $application['school_id'],
                'academic_year_id' => $application['academic_year_id'],
                'teaching_period_id' => $application['teaching_period_id'],
                'school_survey_id' => $schoolSurvey->id,
            ]);
        }

        // Store Practical Skills
        if (is_string($application['learner_practical_skills'] ?? null)) {
            $practicalSkills = json_decode($application['learner_practical_skills'], true) ?? [];
        }
        foreach ($practicalSkills ?? [] as $skillId) {
            LearnerPracticalSkill::updateOrCreate([
                'learner_id' => $learnerPerson->id,
                'practical_skill_id' => $skillId,
                'school_id' => $application['school_id'],
                'academic_year_id' => $application['academic_year_id'],
                'teaching_period_id' => $application['teaching_period_id'],
                'school_survey_id' => $schoolSurvey->id,
            ]);
        }

        // Store Subjects
        if (is_string($application['principal_subjects'] ?? null)) {
            $principalSubjects = json_decode($application['principal_subjects'], true) ?? [];
        }
        foreach ($principalSubjects ?? [] as $subjectId) {
            LearnerSecondarySchoolSubject::updateOrCreate([
                'learner_id' => $learnerPerson->id,
                'subject_id' => $subjectId,
                'school_id' => $application['school_id'],
                'academic_year_id' => $application['academic_year_id'],
                'teaching_period_id' => $application['teaching_period_id'],
                'school_survey_id' => $schoolSurvey->id,
            ]);
        }

        // Store Subsidiary Subjects
        $subsidiarySubject = $application['subsidiary_subject'] ?? null;
        if (is_string($subsidiarySubject)) {
            $subsidiarySubject = json_decode($subsidiarySubject, true);
        }
        if (is_array($subsidiarySubject) && !empty($subsidiarySubject)) {
            foreach ($subsidiarySubject as $subjectId) {
                if (!empty($subjectId)) {
                    LearnerSecondarySchoolSubject::updateOrCreate([
                        'learner_id' => $learnerPerson->id,
                        'subject_id' => $subjectId,
                        'school_id' => $application['school_id'],
                        'academic_year_id' => $application['academic_year_id'],
                        'teaching_period_id' => $application['teaching_period_id'],
                        'school_survey_id' => $schoolSurvey->id,
                    ]);
                }
            }
        }
    }
    /**
     * Refresh the data displayed in the UI after saving a learner
     * Uses the existing LoadFormDataTrait methods to avoid code duplication
     */
    protected function refreshDataAfterSave(): void
    {
        $school = $this->getInstitution();
        // Clear any cached data to ensure fresh data is loaded
        Cache::forget('getGrades'.$school->id);
        Cache::forget('schoolCurriculums'.$school->id);
        // Use the existing getFormData method from LoadFormDataTrait
        $this->getFormData($school);
    }
    /**
     * Upload the learner's image.
     *
     * This method validates the uploaded image and stores it in the 'photos' directory.
     * The image is stored in the 'public' disk.
     *
     * @param $learner The learner object
     * @param $photo The Livewire uploaded file
     * @return string|null The filename of the uploaded photo
     */
    private function uploadAvatar($learner, $photo): ?string
    {
        AdminLogActivity::addToLog('Learners','1','Upload','User Uploaded learner photo', 'school');

        $photoFilename = null;

        // save photo if uploaded
        if ($photo) {
            try {
                $path = 'images/avatars/' . now()->format('Y');
                Storage::disk('local-public')->makeDirectory($path);
                // Generate unique filename using Person ID
                $photoFilename = Str::lower($learner->person_id).'-'.Str::lower(strong_pass()).'.'.$this->photo->extension();
                // Store the file
                $this->photo->storeAs($path, $photoFilename, 'local-public');

                AdminLogActivity::addToLog('Learners','1','Upload',"Successfully uploaded photo: {$photoFilename}", 'school');
            } catch (\Exception $e) {
                AdminLogActivity::addToLog('Learners','1','Upload',"Failed to upload photo: {$e->getMessage()}", 'school');
                return null;
            }
        }
        return $photoFilename;
    }

    private function nextApplicationNumber(): string
    {
        $prefix = now()->startOfDay()->timestamp;
        $application = LearnerEnrolmentApplication::where('application_number', 'iLike', $prefix.'%')->latest()->first();

        $suffix = '';
        if (is_null($application)) {
            $suffix .= 'LP-RA01';
        } else {
            $current_number = substr($application->application_number, -3);
            $suffix .= ++$current_number;
        }

        return $prefix.$suffix;
    }
}


