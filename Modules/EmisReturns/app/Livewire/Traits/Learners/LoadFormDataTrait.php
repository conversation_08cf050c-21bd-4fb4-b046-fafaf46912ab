<?php

namespace Modules\EmisReturns\Livewire\Traits\Learners;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Modules\Core\Models\Institutions\LearnerEnrolment;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\Core\Models\Settings\AdminUnits\District;
use Modules\Core\Models\Settings\DisabilityType;
use Modules\Core\Models\Settings\FamiliarLanguage;
use Modules\Core\Models\Settings\HealthIssue;
use Modules\Core\Models\Settings\IntegratedHealthServiceType;
use Modules\Core\Models\Settings\PracticalSkill;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Settings\SchoolFoodSource;
use Modules\Core\Models\Settings\SchoolMealType;
use Modules\Core\Models\Settings\SecondarySchoolSubject;
use Modules\Core\Models\Settings\SettingPrimarySchoolSubject;
use Modules\Core\Models\Settings\SettingInterSchEducationLevel;
use Modules\Core\Models\Settings\Talent;

trait LoadFormDataTrait
{
    /**
     * Load form data for the learners section.
     *
     * This method initializes the form data with default values and retrieves necessary settings
     * and options for the learners section.
     */
    public function getFormData($school): void
    {
        // Check if $school is a valid model that has the load method
        if (!$school || !method_exists($school, 'load')) {
            return;
        }

        $this->school = $school->load([
            'school_type',
            'institution_examined_courses',
            'certificate_institution_courses.post_primary_institution_course',
        ]);

        $schoolId = $school->id;
        $schoolTypeId = $school->school_type->id;
        $this->education_grades = $this->getGrades($schoolId, $schoolTypeId);
        $this->academicYears = $this->getAcademicYears();
        $this->loadSelectOptions();
        $this->loadSubjects($schoolTypeId);
        $this->loadInternationalSchoolsData($school);
        $this->loadRecentLearners($schoolId);
    }

    public function getAcademicYears()
    {
        $years = [];
        $currentYear = now()->year - 1;
        for ($i = $currentYear; $i >= 2003; $i--) {
            $years[] = $i;
        }
        return $years;
    }

    protected function getGrades($schoolId, $schoolTypeId)
    {
        return Cache::remember('getGrades'.$schoolId, 3600, function () use ($schoolId, $schoolTypeId) {
            return SchoolEducationGrade::schoolType($schoolTypeId)
                ->with('primary_subjects', 'secondary_subjects')
                ->withCount([
                    'learners AS male_count' => function (Builder $builder) use ($schoolId) {
                        $builder->where('school_id', $schoolId)
                            ->where('is_enrolment_active_yn', true)
                            ->where('gender', 'M');
                    },
                    'learners AS female_count' => function (Builder $builder) use ($schoolId) {
                        $builder->where('school_id', $schoolId)
                            ->where('is_enrolment_active_yn', true)
                            ->where('gender', 'F');
                    },
                ])->get();
        });
    }

    private function loadSelectOptions(): void
    {
        $this->countries = Country::select(['id', 'name'])->get();
        $this->districts = District::select(['id', 'name'])->get();
        $this->special_needs = DisabilityType::select(['id', 'name'])->get();
        $this->familiar_languages = FamiliarLanguage::select('id', 'name')->get();
        $this->health_issues = HealthIssue::select(['id', 'name'])->get();
        $this->talents = Talent::select(['id', 'name'])->get();
        $this->practical_skills = PracticalSkill::select(['id', 'name'])->get();
        $this->meal_types = SchoolMealType::select(['id', 'name'])->get();
        $this->food_sources = SchoolFoodSource::select(['id', 'name'])->get();
        $this->health_services_learners = IntegratedHealthServiceType::where('is_for_learner', true)->get();
        $this->health_services_parents = IntegratedHealthServiceType::where('is_for_learner', false)->get();
    }

    private function loadSubjects(int $schoolTypeId): void
    {
        $this->secondarySubjects = '[]';
        $this->primarySubjects = '[]';

        if ($schoolTypeId == 3) {
            $this->secondarySubjects = Cache::remember('subjects0', 7200, function () {
                return SecondarySchoolSubject::all()->toArray();
            });
        }

        if ($schoolTypeId == 2) {
            $this->primarySubjects = Cache::remember('primarySubjects0', 7200, function () {
                return SettingPrimarySchoolSubject::with('reference_books')->get()->toArray();
            });
        }
    }

    private function loadRecentLearners(int $schoolId): void
    {
        $relationships = ['learner.person', 'education_grade'];

        // Add international grade relationship for international schools
        if ($this->schoolType === 'international') {
            $relationships[] = 'international_curriculum';
            $relationships[] = 'international_education_grade';
        }

        // Get recent learners with their relationships
        $this->recent_learners = LearnerEnrolment::has('learner')->with($relationships)->where('school_id', $schoolId)->where('is_enrolment_active_yn', true)->latest()->take(5)->get();

        // Encrypt learner identification numbers for security
        $this->encryptLinMap($this->recent_learners);
    }

    /**
     * Load international school specific data
     */
    private function loadInternationalSchoolsData($school): void
    {
        if ($this->schoolType === 'international') {
            // Load international education levels
            $this->international_education_levels = SettingInterSchEducationLevel::select('id', 'name')->get();

            // Load international calendars
            $school = $school->load(['international_calendars']);
            $this->international_calendars = $school->international_calendars ?? [];

            // Load international curriculums with grades and learner counts
            $schoolId = $school->id;
            $this->international_curriculums = Cache::remember('schoolCurriculums'.$schoolId, 3600, function () use ($school, $schoolId) {
                return $school->international_curriculums()->with('grades')->withCount([
                    'learners AS male_count' => function (Builder $builder) use ($schoolId) {
                        $builder->where('school_id', $schoolId)
                            ->where('is_enrolment_active_yn', true)
                            ->where('gender', 'M');
                    },
                    'learners AS female_count' => function (Builder $builder) use ($schoolId) {
                        $builder->where('school_id', $schoolId)
                            ->where('is_enrolment_active_yn', true)
                            ->where('gender', 'F');
                    },
                ])->get();
            });

            // Initialize an empty grades array
            $this->international_grades = [];
        }
    }
}
