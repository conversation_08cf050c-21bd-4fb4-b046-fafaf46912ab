<?php

namespace Modules\EmisReturns\Livewire\Traits\Learners;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController;
use Modules\Core\Http\Controllers\Api\V1\UNEB\UnebApiController;

trait LearnerVerificationTrait
{
    /**
     * Verify the UNEB details of the learner.
     *
     * This method sets the `verify` property to true and dispatches a notification.
     *
     * @return void
     */
    public function verifyIndexNumber(): void
    {
        $this->loading = true;
        try {
            $controller = $this->getUnebApiController();

            if ($this->has_index_number === 'yes') {
                $request = new Request([
                    'exam_year' => $this->form_learner['exam_year'],
                    'index_number' => strtoupper($this->form_learner['index_number'])
                ]);
                $data = $this->aLevel ? $controller->uceLearnerInfo($request) : $controller->pleLearnerInfo($request);
            } else {
                $request = new Request([
                    'exam_level' => $this->form_learner['exam_level'],
                    'equated_year' => $this->form_learner['equated_year'],
                    'equated_code' => strtoupper($this->form_learner['equated_code'])
                ]);
                $data = $controller->equatedData($request);
            }

            if ($data) {
                $this->uneb_learner = $data;
                $this->form_learner['first_name'] = $this->uneb_learner['first_name'] ?? '';
                $this->form_learner['surname'] = $this->uneb_learner['surname'] ?? '';
                $this->form_learner['other_names'] = $this->uneb_learner['other_names'] ?? '';
                $this->form_learner['gender'] = $this->uneb_learner['gender'] ?? '';

                // Convert UNEB birth_date to HTML5 date format (YYYY-MM-DD)
                $birthDate = '';
                if (isset($this->uneb_learner['birth_date']) && $this->uneb_learner['birth_date']) {
                    try {
                        // The birth_date accessor from UnebData trait returns a Carbon instance
                        $birthDate = $this->uneb_learner['birth_date']->format('Y-m-d');
                    } catch (Exception $e) {
                        // Fallback: try to parse the raw date_of_birth string
                        if (isset($this->uneb_learner['date_of_birth'])) {
                            try {
                                $birthDate = Carbon::parse($this->uneb_learner['date_of_birth'])->format('Y-m-d');
                            } catch (Exception $e) {
                                $birthDate = '';
                            }
                        }
                    }
                }
                $this->form_learner['birth_date'] = $birthDate;
                $this->form_learner['exam_year'] = $this->uneb_learner['exam_year'] ?? '';
                $this->form_learner['code_type'] = $this->uneb_learner['code_type'] ?? '';
                $this->uneb_verify = true;

                // Set default learner_nin to 'no' for secondary schools after verification
                if ($this->schoolType === 'secondary')
                    $this->learner_nin = 'no';

                $this->dispatch('notify', [
                    'status' => 'success',
                    'title' => 'Success:',
                    'message' => 'UNEB details verified successfully.'
                ]);
            } else {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Failed to verify UNEB details. Try again!'
                ]);
            }

        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage()
            ]);
        }
        $this->loading = false;
        $this->getFormData($this->getInstitution());
    }
    /**
     * Verifies the learner's National Identification Number (NIN) through the NIRA API.
     *
     * This method sends a request to the NIRA API with the learner's NIN, processes the response,
     * and updates related properties with the retrieved data if verification is successful.
     * It also handles notifications for success or error scenarios.
     *
     * @return void
     */
    public function verifyLearnerNIN(): void
    {
        $this->loading = true;
        try {
            $controller = $this->getNiraApiController();
            $request = new Request([
                'id_number' => strtoupper($this->form_learner['nin']),
            ]);
            $response = $controller->userInfo($request);
            $data = $this->normalizeNiraResponse($response);

            if ($data) {
                $this->nira_learner = $data;
                $this->learner_verify = true;
                $this->uganda = true;
                $this->form_learner['first_name'] = $this->nira_learner['given_names'] ?? '';
                $this->form_learner['surname'] = $this->nira_learner['surname'] ?? '';
                $this->form_learner['gender'] = $this->nira_learner['gender'] ?? '';
                $this->form_learner['birth_date'] = Carbon::parse($this->nira_learner['birth_date'] ?? now())->format('j F, Y');
                $this->dispatch('notify', [
                    'status' => 'success',
                    'title' => 'Success:',
                    'message' => 'Learner NIN verified successfully.'
                ]);
            } else {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Learner NIN verification failed. Try again!'
                ]);
            }
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage()
            ]);
        }
        $this->loading = false;
        $this->getFormData($this->getInstitution());
    }
    /**
     * Verify the parent's NIN.
     *
     * This method sets the `ninParentVerify` property to true and dispatches a notification.
     *
     * @return void
     */
    public function verifyParentNIN(): void
    {
        if ($this->isNinSame()) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error!',
                'message' => 'Parent NIN cannot be the same as that of a learner. Provide a different NIN!',
            ]);
            return;
        }
        $this->loading = true;
        try {
            $controller = $this->getNiraApiController();
            $request = new Request([
                'id_number' => strtoupper($this->form_learner['parent_nin']),
            ]);
            $response = $controller->userInfo($request);
            $data = $this->normalizeNiraResponse($response);

            if ($data) {
                $this->nira_parent = $data;
                $this->parent_verify = true;
                $this->form_learner['parent_first_name'] = $this->nira_parent['given_names'] ?? '';
                $this->form_learner['parent_surname'] = $this->nira_parent['surname'] ?? '';
                $this->form_learner['parent_gender'] = $this->nira_parent['gender'] ?? '';
                $this->form_learner['parent_birth_date'] = $this->nira_parent['date_of_birth'] ?? '';
                $this->form_learner['parent_country_id'] = $this->nira_parent['nationality'] ?? '';
                if (isset($this->nira_parent['photo']) && $this->nira_parent['photo'] !== null) {
                    if (str_contains($this->nira_parent['photo'], '.png')) {
                        $this->form_learner['parent_photo'] = $this->nira_parent['photo'];
                    } else {
                        $this->form_learner['parent_photo'] = 'data:image/png;base64,' . $this->nira_parent['photo'];
                    }
                }
                $this->dispatch('notify', [
                    'status' => 'success',
                    'title' => 'Success:',
                    'message' => 'Parent NIN verified successfully.'
                ]);
            } else {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Parent NIN verification failed. Try again!'
                ]);
            }
        } catch (Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage()
            ]);
        }
        $this->loading = false;
        $this->getFormData($this->getInstitution());
    }

    public function verifyPassport(): void
    {
        $this->loading = true;
        try {
            // Simulate API call
            sleep(1);
            $this->parent_verify = true;
            $this->loading = false;
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Parent Passport verified successfully.'
            ]);
        } catch (Exception $e) {
            $this->loading = false;
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage()
            ]);
        }
        $this->getFormData($this->getInstitution());
    }
    public function verifyRefugeeNumber(): void
    {
        $this->loading = true;
        try {
            // Simulate API call
            sleep(1);
            $this->parent_verify = true;
            $this->loading = false;
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Parent Refugee Number verified successfully.'
            ]);
        } catch (Exception $e) {
            $this->loading = false;
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $e->getMessage()
            ]);
        }
        $this->getFormData($this->getInstitution());
    }
    public function isNinSame(): bool
    {
        $learnerNin = strtoupper($this->form_learner['nin'] ?? '');
        $parentNin = strtoupper($this->form_learner['parent_nin'] ?? '');
        // Check if both NINs are provided and compare them
        return $learnerNin && $parentNin && $learnerNin === $parentNin;
    }

    /**
     * Get an instance of the UnebApiController.
     * This method can be overridden in tests to provide a mock controller.
     *
     * @return UnebApiController
     */
    protected function getUnebApiController(): UnebApiController
    {
        return new UnebApiController();
    }

    /**
     * Get an instance of the NiraApiController.
     * This method can be overridden in tests to provide a mock controller.
     *
     * @return NiraApiController
     */
    protected function getNiraApiController(): NiraApiController
    {
        return new NiraApiController();
    }

    /**
     * Normalize the NIRA API response.
     * This method can be overridden in tests to provide mock data.
     *
     * @param mixed $response The response from the NIRA API
     * @return array|null The normalized data or null if normalization failed
     */
    protected function normalizeNiraResponse($response)
    {
        // Check if the global function exists
        if (function_exists('normalizeNiraResponse')) {
            return normalizeNiraResponse($response);
        }

        // If the global function doesn't exist, implement the logic directly
        if ($response instanceof \Illuminate\Http\JsonResponse) {
            return $response->getData(true);
        }

        if ($response instanceof \Modules\Core\Models\NIRA\Nira) {
            return $response->toArray();
        }

        // If the response is already an array with 'success' and 'data' keys (test scenario)
        if (is_array($response) && isset($response['success']) && isset($response['data'])) {
            return $response['data'];
        }

        return null;
    }
}
