<?php

namespace Modules\EmisReturns\Livewire\Traits;

/**
 * Trait InstitutionContext
 *
 * Provides a reusable method to get the institution context for the current authenticated user.
 * This is used by Livewire components to determine which school/institution the user is associated with.
 */
trait InstitutionContext
{
    /**
     * Get the institution (school) context for the current authenticated user.
     *
     * @return \Modules\Core\Models\Institutions\School|null
     *   The School model instance if the user is institution-admin or institution-user, otherwise null.
     */
    protected function getInstitution()
    {
        $user = auth()->user();

         // Check if the user is authenticated
         if (!$user) {
            return redirect()->route('institution-dashboard');      
        }

        // If the user is an institution admin, return their school
        if ($user->hasRole('institution-admin')) {
            return $user->school;
        }

        // If the user is an institution user, return the school from their contact
        if ($user->hasRole('institution-user')) {
            return $user->school_contact->school;
        }

        // For super-admin, support-officer, or other roles, no institution context is available
        return null;
    }
}
