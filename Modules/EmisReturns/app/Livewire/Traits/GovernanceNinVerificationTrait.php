<?php

namespace Modules\EmisReturns\Livewire\Traits;

use Illuminate\Http\Request;
use Modules\Core\Http\Controllers\Api\V1\NIRA\NiraApiController;
use Illuminate\Support\Str;

trait GovernanceNinVerificationTrait 
{
    protected $ninRules = [
        'id_number' => 'required|string|min:14',
    ];

    protected $ninMessages = [
        'id_number.required' => 'Nin is required',
        'id_number.min' => 'Nin should be 14 characters',
    ];

    public function verifyMemberNIN()
    {
        try {
            $this->validate($this->ninRules, $this->ninMessages);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errorMessages = [];
            foreach ($e->errors() as $field => $ninMessages) {
                foreach ($ninMessages as $message) {
                    $errorMessages[] = $message;
                }
            }

            $this->dispatch('notifyAddMemberModal', [
                'status' => 'error',
                'title' => 'Validation Error: ',
                'message' => implode(' ', $errorMessages),
            ]);

            return;
        }
        
        try {
            $controller = new NiraApiController();
            $request = new Request([
                'id_number' => Str::upper($this->id_number)
            ]);
            $response = $controller->userInfo($request);

            if($response){
                $this->memberData = $response;
                $this->verificationStatus = true;

                $this->dispatch('notifyAddMemberModal', [
                    'status' => 'success',
                    'title' => 'Success: ',
                    'message' => 'Member NIN verified successfully.'
                ]);
            } else {
                $this->verificationStatus = false;

                $this->dispatch('notifyAddMemberModal', [
                    'status' => 'error',
                    'title' => 'Error: ',
                    'message' => 'Member NIN verification failed. Try again!'
                ]);
            }
        } catch (\Throwable $th) {
            $this->verificationStatus = false;
            $this->dispatch('notifyAddMemberModal', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => $th->getMessage()
            ]);
        }
    }
}
