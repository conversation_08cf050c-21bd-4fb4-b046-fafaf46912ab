<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Modules\Core\Models\Settings\Survey;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Institutions\SchoolEnergySourceUpdate;
use Modules\Core\Models\Settings\EnergySourceType;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

class SourcesOfEnergy extends Component
{
    use InstitutionContext;

    public $survey_id;
    public $section_id;
    public $cooking_source_id;
    public $lighting_source_id;
    public $loading = false;
    public $energySources;
    public $institution;

    public function mount(): void
    {
        $this->institution = $this->getInstitution();
        $this->loadEnergySources();
    }

    public function getCookingSourcesProperty()
    {
        return EnergySourceType::where('is_cooking_yn', true)->orderBy('name')->get();
    }

    public function getLightingSourcesProperty()
    {
        return EnergySourceType::where('is_lighting_yn', true)->orderBy('name')->get();
    }

    public function getEnergySourcesProperty()
    {
        return $this->institution->energy_sources->first();
    }

    private function getActiveSurvey(): Survey
    {
        return Survey::with('academic_year')->findOrFail($this->survey_id);
    }

    private function getOrCreateSchoolSurvey(Survey $survey): SchoolSurvey
    {
        $schoolSurvey = SchoolSurvey::firstOrCreate(
            [
                'school_id' => $this->institution->id,
                'survey_id' => $survey->id,
            ],
            [
                'current_section_id' => $this->section_id,
                'last_saved' => now(),
            ]
        );
        $schoolSurvey->sections()->sync($survey->sections);
        $schoolSurvey->section_items()->sync($survey->section_items);
        return $schoolSurvey;
    }

    public function loadEnergySources(): void
    {
        try {
            $survey = $this->getActiveSurvey();
            $term = get_active_teaching_period();
            $this->energySources = SchoolEnergySourceUpdate::with(['cooking', 'lighting'])
                ->where([
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $survey->academic_year_id,
                    'teaching_period_id' => $term->teaching_period_id,
                ])
                ->first();

            if ($this->energySources) {
                $this->cooking_source_id = $this->energySources->cooking_source_id;
                $this->lighting_source_id = $this->energySources->lighting_source_id;
            }
        } catch (ModelNotFoundException $e) {
            $this->addError('survey_id', 'Unable to load energy sources. Survey not found.');
        }
    }

    public function updateEnergySources(): void
    {
        $this->validate([
            'survey_id' => 'required|integer',
            'cooking_source_id' => 'required|integer',
            'lighting_source_id' => 'required|integer',
        ]);
        $this->loading = true;

        try {
            AdminLogActivity::addToLog('Energy Sources', '1', 'Update', 'User Updated School Energy Sources Updates', 'school');

            $survey = Survey::with(['academic_year.teaching_periods', 'sections', 'section_items'])->findOrFail($this->survey_id);

            $term = get_active_teaching_period();
            $schoolSurvey = $this->getOrCreateSchoolSurvey($survey);

            SchoolEnergySourceUpdate::updateOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $survey->academic_year_id,
                    'teaching_period_id' => $term->teaching_period_id,
                    'school_survey_id' => $schoolSurvey->id,
                ],
                [
                    'cooking_source_id' => $this->cooking_source_id,
                    'lighting_source_id' => $this->lighting_source_id,
                ]
            );
//            $this->institution->refresh()->load(['energy_sources.cooking', 'energy_sources.lighting']);
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Energy Sources updated successfully.',
            ]);
            $this->dispatch('close-modal');
            $this->loadEnergySources();
        } catch (ModelNotFoundException $e) {
            $this->addError('survey_id', 'Survey not found.');
        } catch (\Throwable $e) {
            Log::error('Failed to update energy sources', ['error' => $e]);
            $this->addError('general', 'Error updating energy sources: ' . $e->getMessage());
        } finally {
            $this->loading = false;
        }
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.sources-of-energy');
    }
}
