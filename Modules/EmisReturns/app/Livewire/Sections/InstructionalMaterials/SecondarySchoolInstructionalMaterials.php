<?php

namespace Modules\EmisReturns\Livewire\Sections\InstructionalMaterials;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolLabEquipmentUpdate;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolLabReagentsUpdate;
use Modules\Core\Models\Settings\SettingLabEquipment;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Institutions\SchoolLabEquipment;
use Modules\Core\Models\Settings\SettingLabReagent;
use Modules\Core\Models\Institutions\SchoolLabReagent;

class SecondarySchoolInstructionalMaterials extends Component
{
    use InstitutionContext;
    public $institution;
    public $institutionId;
    public $survey;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $perPage = 25;
    public $lab_equipment_id;
    public $quantity;
    public $reagents;
    public $setLabReagents;
    public $lab_reagent_id;
    public $reagent_quantity;

    public function mount($survey)
    {
        $this->institution = $this->getInstitution();
        $this->initializeContext($survey);
        $this->setLabReagents = SettingLabReagent::all();
    }

    private function initializeContext($survey): void
    {
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    // MANAGE LAB EQUIPMENTS
    public function getLabEquipmentChemicalsProperty()
    {
        AdminLogActivity::addToLog('Lab Equipment', '1', 'View', 'User checked lab equipment updates', 'school');

        $items = SettingLabEquipment::all();

        $equipments = $this->institution->lab_equipment()->orderBy('id', 'desc')
            ->with(['equipment'])
            ->whereNotNull('lab_equipment_id')->get();
        return compact('items', 'equipments');
    }

    public function saveLabEquipment()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }
        AdminLogActivity::addToLog('Lab Equipment', '1', 'Create', 'User added lab equipment update', 'school');
        $this->validate([
            'lab_equipment_id' => 'required|integer',
            'quantity' => 'required|integer',
        ], [
            'lab_equipment_id.required' => 'Lab Equipment is required.',
            'quantity.required' => 'Enter Quantity.',
        ]);

        try {
            $term = get_active_teaching_period();
            $sectionId = $this->survey->sections->first()->id ?? null;

            if (!$sectionId) {
                throw new \Exception('No sections found for this survey');
            }

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $sectionId,
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);
            $schoolSurvey->sections()->updateExistingPivot($sectionId, [
                'is_complete_yn' => true,
            ]);

            SchoolLabEquipmentUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'lab_equipment_id' => $this->lab_equipment_id,
            ], [
                'quantity' => $this->quantity,
            ]);

            SchoolLabEquipment::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'lab_equipment_id' => $this->lab_equipment_id,
            ], [
                'quantity' => $this->quantity,
            ]);

            $this->institution->refresh();
            $this->institution->load('lab_equipment.equipment');

            $this->reset(['lab_equipment_id', 'quantity']);
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Lab equipment updated successfully.',
            ]);
            $this->dispatch('close-modal');

        } catch (\Exception $e) {
            Log::error('Error saving lab equipment: ' . $e->getMessage());
            session()->flash('error', 'Failed to save lab equipment: ' . $e->getMessage());
        }
    }

    public function updateLabEquipment()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        AdminLogActivity::addToLog('Lab Equipment', '1', 'Update', 'User updated lab equipment entry', 'school');

        $this->validate([
            'lab_equipment_id' => 'required|integer',
            'quantity' => 'required|integer',
        ], [
            'lab_equipment_id.required' => 'Lab Equipment is required.',
            'quantity.required' => 'Enter Quantity.',
        ]);

        try {
            $term = get_active_teaching_period();
            $sectionId = $this->survey->sections->first()->id ?? null;

            if (!$sectionId) {
                throw new \Exception('No sections found for this survey');
            }

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $sectionId,
                'last_saved' => now(),
            ]);

            // Ensure associations are in sync
            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);

            $schoolSurvey->sections()->updateExistingPivot($sectionId, [
                'is_complete_yn' => true,
            ]);

            // Find and update existing lab equipment update entry
            $labUpdate = SchoolLabEquipmentUpdate::where([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'lab_equipment_id' => $this->lab_equipment_id,
            ])->first();

            if (!$labUpdate) {
                throw new \Exception('Lab equipment entry not found for update.');
            }

            $labUpdate->update([
                'quantity' => $this->quantity,
            ]);

            // Update the school's actual equipment stock
            $equipment = SchoolLabEquipment::where([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'lab_equipment_id' => $this->lab_equipment_id,
            ])->first();

            if ($equipment) {
                $equipment->update(['quantity' => $this->quantity]);
            }

            $this->institution->refresh();
            $this->institution->load('lab_equipment.equipment');

            $this->reset(['lab_equipment_id', 'quantity']);
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Lab equipment successfully updated.',
            ]);
            $this->dispatch('close-modal');

        } catch (\Exception $e) {
            Log::error('Error updating lab equipment: ' . $e->getMessage());
            session()->flash('error', 'Failed to update lab equipment: ' . $e->getMessage());
        }
    }

    // MANAGE REAGENTS
    public function getReagentsProperty()
    {
        AdminLogActivity::addToLog('Reagents', '1', 'View', 'User checked reagents updates', 'school');
        return $this->institution->lab_reagents()
            ->with(['reagent', 'school'])
            ->whereNotNull('lab_reagent_id')
            ->orderBy('id', 'asc')
            ->get();
    }

    public function saveReagent()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        AdminLogActivity::addToLog('Lab Reagents', '1', 'Create', 'User created lab reagent update', 'school');

        $this->validate([
            'lab_reagent_id' => 'required|integer',
            'reagent_quantity' => 'required|integer|min:0',
        ], [
            'lab_reagent_id.required' => 'Lab Reagent is required.',
            'reagent_quantity.required' => 'Enter Quantity.',
        ]);

        try {
            $term = get_active_teaching_period();
            $sectionId = $this->survey->sections->first()->id ?? null;

            if (!$sectionId) {
                throw new \Exception('No sections found for this survey');
            }

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $sectionId,
                'last_saved' => now(),
            ]);

            // Sync survey sections and items
            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);

            $schoolSurvey->sections()->updateExistingPivot($sectionId, [
                'is_complete_yn' => true,
            ]);

            SchoolLabReagentsUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'lab_reagent_id' => $this->lab_reagent_id,
            ], [
                'quantity' => $this->reagent_quantity,
            ]);

            SchoolLabReagent::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'lab_reagent_id' => $this->lab_reagent_id,
            ], [
                'quantity' => $this->reagent_quantity,
            ]);
            $this->institution->refresh();
            $this->institution->load('lab_reagents.reagent');
            $this->reset(['lab_reagent_id', 'reagent_quantity']);
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Lab reagent saved successfully.',
            ]);
            $this->dispatch('close-modal');

        } catch (\Exception $e) {
            Log::error('Error saving lab reagent: ' . $e->getMessage());
            session()->flash('error', 'Failed to save lab reagent: ' . $e->getMessage());
        }
    }

    public function updateReagent()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        AdminLogActivity::addToLog('Lab Reagents', '1', 'Update', 'User updated lab reagent', 'school');

        $this->validate([
            'lab_reagent_id' => 'required|integer',
            'reagent_quantity' => 'required|integer|min:0',
        ], [
            'lab_reagent_id.required' => 'Lab Reagent is required.',
            'reagent_quantity.required' => 'Enter Quantity.',
        ]);

        try {
            $term = get_active_teaching_period();
            $sectionId = $this->survey->sections->first()->id ?? null;

            if (!$sectionId) {
                throw new \Exception('No sections found for this survey');
            }

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $sectionId,
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);

            $schoolSurvey->sections()->updateExistingPivot($sectionId, [
                'is_complete_yn' => true,
            ]);

            SchoolLabReagentsUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'lab_reagent_id' => $this->lab_reagent_id,
            ], [
                'quantity' => $this->reagent_quantity,
            ]);

            SchoolLabReagent::where([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'lab_reagent_id' => $this->lab_reagent_id,
            ])->update([
                'quantity' => $this->reagent_quantity,
            ]);

            $this->institution->refresh();
            $this->institution->load('lab_reagents.reagent');

            $this->reset(['lab_reagent_id', 'reagent_quantity']);

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Lab reagent updated successfully.',
            ]);
            $this->dispatch('close-modal');

        } catch (\Exception $e) {
            Log::error('Error updating lab reagent: ' . $e->getMessage());
            session()->flash('error', 'Failed to update lab reagent: ' . $e->getMessage());
        }
    }


    public function render()
    {
        return view('emisreturns::livewire.sections.instructional-materials.secondary-school-instructional-materials', [
            'labEquipmentChemicals' => $this->labEquipmentChemicals,
            'labReagents' => $this->Reagents
        ]);
    }

}
