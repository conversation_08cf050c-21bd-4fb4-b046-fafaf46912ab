<?php
namespace Modules\EmisReturns\Livewire\Sections\InstructionalMaterials;
use Livewire\Component;
use Modules\Core\Models\Settings\LearnAndPlayMaterialCategory;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Institutions\SchoolLearningAndPlayingMaterialsUpdate;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Helpers\AdminLogActivity;

class PrePrimarySchoolInstructionalMaterials extends Component
{
    use InstitutionContext;

    public $institution;
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $schoolSurveyId;
    public $quantity = 0;
    public $editMaterial = null;
    public $materialGradeStates = [];
    public $survey;

    public function mount($survey)
    {
        $this->institution = $this->getInstitution();
        $this->surveyId = is_object($survey) ? $survey->id : $survey;

        $this->initializeContext();
    }

    private function initializeContext(): void
    {
        $this->institutionId = $this->institution->id;

        $survey = \Modules\Core\Models\Settings\Survey::select('id', 'academic_year_id')->find($this->surveyId);
        $this->academicYearId = $survey?->academic_year_id;

        $this->termId = get_active_teaching_period()->teaching_period_id ?? null;

        $this->schoolSurveyId = SchoolSurvey::where('school_id', $this->institutionId)
            ->where('survey_id', $this->surveyId)
            ->value('id');
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function getCategoriesProperty()
    {
        return LearnAndPlayMaterialCategory::with('materials')->get();
    }

    public function getGradesProperty()
    {
        return SchoolEducationGrade::where('school_type_id', 1)->orderBy('name')->get();
    }

    public function getMaterialGradeMapProperty()
    {
        if (!$this->schoolSurveyId) {
            return collect();
        }

        $updates = SchoolLearningAndPlayingMaterialsUpdate::where([
            ['school_id', $this->institutionId],
            ['academic_year_id', $this->academicYearId],
            ['teaching_period_id', $this->termId],
            ['school_survey_id', $this->schoolSurveyId],
        ])->get();

        return $updates->groupBy('learning_material_id');
    }

    public function toggleUpdate($materialId)
    {
        if ($this->editMaterial === $materialId) {
            // Cancel edit mode
            $this->editMaterial = null;
            $this->materialGradeStates = [];
            return;
        }

        $this->editMaterial = $materialId;

        // Pre-fill values from existing quantities
        $entries = $this->materialGradeMap[$materialId] ?? collect();
        foreach ($this->grades as $grade) {
            $match = $entries->firstWhere('education_grade_id', $grade->id);
            $this->materialGradeStates[$materialId][$grade->id] = $match ? $match->quantity : 0;
        }
    }

    public function saveUpdates($materialId)
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        AdminLogActivity::addToLog(
            'Learning & Playing Materials',
            '1',
            'Update',
            'User Updated School Learning & Playing Materials Updates',
            'school'
        );

        $schoolSurvey = SchoolSurvey::firstOrCreate(
            [
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ],
            [
                'current_section_id' => $this->survey->sections->first()->id ?? null,
                'last_saved' => now(),
            ]
        );
        $this->syncSurveySections($schoolSurvey);
        $schoolSurvey->sections()->updateExistingPivot(
            $this->survey->sections->first()->id ?? null,
            ['is_complete_yn' => true]
        );
        $selectedGrades = collect($this->materialGradeStates[$materialId] ?? [])
            ->filter(fn($value) => $value !== null && $value !== '')
            ->keys()
            ->toArray();
        SchoolLearningAndPlayingMaterialsUpdate::where([
            ['school_id', $this->institutionId],
            ['academic_year_id', $this->academicYearId],
            ['teaching_period_id', $this->termId],
            ['school_survey_id', $schoolSurvey->id],
            ['learning_material_id', $materialId],
        ])->delete();

        foreach ($selectedGrades as $gradeId) {
            SchoolLearningAndPlayingMaterialsUpdate::create([
                'school_id' => $this->institutionId,
                'academic_year_id' => $this->academicYearId,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'learning_material_id' => $materialId,
                'education_grade_id' => $gradeId,
                'quantity' => (int) ($this->materialGradeStates[$materialId][$gradeId] ?? 0),
            ]);
        }

        $this->dispatch('notify', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Learning and playing materials updated successfully.',
        ]);

        $this->editMaterial = null;
        $this->materialGradeStates = [];
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.instructional-materials.pre-primary-school-instructional-materials', [
            'categories' => $this->categories,
            'grades' => $this->grades,
            'materialGradeMap' => $this->materialGradeMap,
        ]);
    }
}
