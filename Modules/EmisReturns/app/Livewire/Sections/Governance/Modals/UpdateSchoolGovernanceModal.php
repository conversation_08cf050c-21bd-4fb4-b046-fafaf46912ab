<?php

namespace Modules\EmisReturns\Livewire\Sections\Governance\Modals;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolGovernanceUpdate;
use Modules\Core\Models\Settings\SchoolAssemblyParade;
use Modules\Core\Helpers\AdminLogActivity;

class UpdateSchoolGovernanceModal extends Component
{
    public $governanceData;
    public $has_cmc_smc_or_bog_yn;
    public $no_of_meetings;
    public $holds_assemblies_yn;
    public $assemblyFrequency = [];
    public $assembly_frequency_id;
    public $schoolType;
    public $institution;
    public $teachingPeriodId;
    public $academicYearId;
    public $loading = true;

    protected $listeners = [
        'open-governance-modal' => 'loadModal', 
        'governance-data-loaded' => 'loadingState',
    ];

    public function mount($schoolType, $institution, $academicYearId) {
        $this->schoolType = $schoolType;
        $this->institution = $institution;
        $this->teachingPeriodId = get_active_teaching_period()->teaching_period_id;
        $this->academicYearId = $academicYearId;
    }

    public function loadingState()
    {
        $this->loading = false;
    }
    
    public function loadModal()
    {
        $this->governanceData = SchoolGovernanceUpdate::where('school_id', $this->institution->id)
            ->with('assembly_frequency')
            ->first();
        $this->assemblyFrequency = SchoolAssemblyParade::pluck('name', 'id')->toArray();

        if($this->governanceData) {
            $this->has_cmc_smc_or_bog_yn = $this->governanceData->has_cmc_smc_or_bog_yn ? 'true' : 'false';
            $this->no_of_meetings = $this->governanceData->no_of_meetings ?? null;
            $this->holds_assemblies_yn = $this->governanceData->holds_assemblies_yn ? 'true' : 'false';
            $this->assembly_frequency_id = $this->governanceData->assembly_frequency->id ?? null;
        } else {
            $this->has_cmc_smc_or_bog_yn = null;
            $this->no_of_meetings = null;
            $this->holds_assemblies_yn = null;
            $this->assembly_frequency_id = null;
        }
        
        $this->dispatch('governance-data-loaded')->self();
    }

    public function updateGovernance()
    {
        AdminLogActivity::addToLog('Governance', '1', 'Update', 'User Updated School Governance Updates', 'school');

        $governance = SchoolGovernanceUpdate::where('school_id', $this->institution->id)->first();

        if(!$governance) {

            $data = [
                'school_id' => $this->institution->id,
                'teaching_period_id' => $this->teachingPeriodId,
                'academic_year_id' => $this->academicYearId,
                'has_cmc_smc_or_bog_yn' => filter_var($this->has_cmc_smc_or_bog_yn, FILTER_VALIDATE_BOOLEAN),
            ];

            if (filter_var($this->has_cmc_smc_or_bog_yn, FILTER_VALIDATE_BOOLEAN)) {
                $data['no_of_meetings'] = $this->no_of_meetings;
                $data['holds_assemblies_yn'] = filter_var($this->holds_assemblies_yn, FILTER_VALIDATE_BOOLEAN);
                if (!filter_var($this->holds_assemblies_yn, FILTER_VALIDATE_BOOLEAN)) {
                    $data['assembly_frequency_id'] = null;
                } else {
                    $data['assembly_frequency_id'] = $this->assembly_frequency_id;
                }
            } else {
                $data['no_of_meetings'] = null;
                $data['holds_assemblies_yn'] = null;
                $data['assembly_frequency_id'] = null;
            }

            $governance = SchoolGovernanceUpdate::create($data);
        } else {

            $governance->teaching_period_id = $this->teachingPeriodId;
            $governance->academic_year_id = $this->academicYearId;
            $governance->has_cmc_smc_or_bog_yn = filter_var($this->has_cmc_smc_or_bog_yn, FILTER_VALIDATE_BOOLEAN);
        
            if(!$governance->has_cmc_smc_or_bog_yn){
                $governance->no_of_meetings = null;
                $governance->holds_assemblies_yn = null;
                $governance->assembly_frequency_id = null;
            } else {
                $governance->no_of_meetings = $this->no_of_meetings;
                $governance->holds_assemblies_yn = filter_var($this->holds_assemblies_yn, FILTER_VALIDATE_BOOLEAN);
                if (!$governance->holds_assemblies_yn) {
                    $governance->assembly_frequency_id = null;
                } else {
                    $governance->assembly_frequency_id = $this->assembly_frequency_id;
                }
            }

            $governance->save();
        }

        $this->dispatch('notifyGovernance', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'School governance updated successfully.',
        ]);

        $this->dispatch('governanceUpdated');
        $this->dispatch('close-governance-modal');
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.governance.modals.update-school-governance-modal');
    }
}
