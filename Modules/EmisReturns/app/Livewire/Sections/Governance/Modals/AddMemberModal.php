<?php

namespace Modules\EmisReturns\Livewire\Sections\Governance\Modals;

use Livewire\Component;
use Modules\EmisReturns\Livewire\Traits\GovernanceNinVerificationTrait;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSmcBogMember;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Modules\Core\Models\Institutions\CommitteeMemberTrainingUpdate;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Traits\InteractsWithPerson;
use Illuminate\Support\Facades\DB;

class AddMemberModal extends Component
{
    use GovernanceNinVerificationTrait, InteractsWithPerson; 
    
    public $schoolType;
    public $institution;

    public $id_number;
    public $phone_1;
    public $email;
    public $appointment_date;
    public $is_trained_yn = false;
    public $teachingPeriodId;
    public $academicYearId;
    public $survey;

    public $memberData;
    public $verificationStatus = false;

    protected $listeners = [
        'open-add-member-modal' => 'openModal',
        'close-add-member-modal' => 'resetInputFields',
    ];

    protected $rules = [
        'phone_1' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/|min:9',
        'email' => 'nullable|email:rfc,dns',
        'appointment_date' => 'required|date',
        'is_trained_yn' => 'required',
    ];

    protected $messages = [
        'phone_1.required' => 'Phone Number is required',
        'phone_1.regex' => 'Phone number you provided is invalid',
        'phone_1.min' => 'Phone number should be 9 characters',
        'email.email' => 'Enter valid email address',
        'appointment_date.required' => 'Appointment date is required',
        'is_trained_yn.required' => 'Trained status is required',
    ];

    public function mount($schoolType, $institution, $academicYearId, $survey)
    {
        $this->schoolType = $schoolType;
        $this->institution = $institution;
        $this->teachingPeriodId = get_active_teaching_period()->teaching_period_id;
        $this->academicYearId = $academicYearId;
        $this->survey = $survey;
    }

    public function addMember()
    {
        try {
            $this->validate($this->rules, $this->messages);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errorMessages = [];
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $errorMessages[] = $message;
                }
            }

            $this->dispatch('notifyAddMemberModal', [
                'status' => 'error',
                'title' => 'Validation Error: ',
                'message' => implode(' ', $errorMessages),
            ]);

            return;
        }
    
        AdminLogActivity::addToLog('Governance', '1', 'Create', 'User Created School Governance Updates', 'school');

        try {
            DB::transaction(function () {
                // Check total number of committee members
                $members = SchoolSmcBogMember::where('school_id', $this->institution->id)->count();
                switch ($this->institution->school_type_id) {
                    case 1:
                        if ($members > 8) {
                            abort(500, 'You cannot add more than 9 CMC Members.');
                        }
                        break;
                    case 2:
                        if ($members > 11) {
                            abort(500, 'You cannot add more than 12 SMC Members.');
                        }
                        break;
                    case 3:
                        if ($members > 13) {
                            abort(500, 'You cannot add more than 14 BOG Members.');
                        }
                        break;
                    default:
                        abort(401, 'No School Type found!!. Try again...');
                        break;
                }

                //Check if person with this nin is set and return this person
                $uganda = Country::where('name', 'UGANDA')->first();
                $person = $this->getPersonWithIdentity($this->id_number, $uganda->id, 'nin');

                if (!$person) {
                    $person = Person::create([
                        'first_name' => $this->memberData->given_names,
                        'surname' => $this->memberData->surname,
                        'other_names' => $this->memberData->other_names,
                        'birth_date' => Carbon::createFromFormat('d/m/Y', $this->memberData->date_of_birth)->format('j F, Y'),
                        'identity_type_id' => 1,
                        'id_number' => Str::upper($this->id_number),
                        'gender' => $this->memberData->gender,
                        'phone_1' => $this->phone_1,
                        'email' => strtolower($this->email),
                        'country_id' => $uganda->id,
                    ]);
                }

                // save identity document
                $person->identity_documents()->updateOrCreate([
                    'identity_type_id' => 1,
                    'document_id' => $this->id_number,
                    'verification_status' => 1,
                    'country_id' => $uganda->id,
                ]);

                // School survey creation or update
                $schoolSurvey = SchoolSurvey::firstOrCreate([
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ], [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]);

                $this->syncSurveySections($schoolSurvey);

                // Update members trained status
                CommitteeMemberTrainingUpdate::updateOrCreate([
                    'school_id' => $this->institution->id,
                    'person_id' => $person->id,
                    'academic_year_id' => $this->academicYearId,
                    'teaching_period_id' => $this->teachingPeriodId,
                    'school_survey_id' => $schoolSurvey->id,
                ], [
                    'is_trained_yn' => $this->is_trained_yn,
                ]);

                // Update member appointment date
                SchoolSmcBogMember::updateOrCreate([
                    'school_id' => $this->institution->id,
                    'person_id' => $person->id,
                ], [
                    'appointment_date' => Carbon::parse($this->appointment_date)->format('j F, Y'),
                ]); 
            });

            $this->dispatch('close-add-member-modal');

            $this->dispatch('notifyGovernance', [
                'status' => 'success',
                'title' => 'Success',
                'message' => 'Member added successfully',
            ]);

            $this->dispatch('membersAddedSuccessfully');
        } catch (ModelNotFoundException $exception) {
            abort(500, 'We cannot find the Survey you are trying to update');
        } catch(\Throwable $e) {
            $this->dispatch('close-add-member-modal');

            $this->dispatch('notifyGovernance', [
                'status' => 'error',
                'title' => 'Error',
                'message' => 'Failed to add member. Please try again later ',
            ]);
        }
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey)
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function resetInputFields()
    {
        $this->verificationStatus = false;
        $this->memberData = null;
        $this->id_number = '';
        $this->phone_1 = '';
        $this->email = '';
        $this->appointment_date = '';
        $this->is_trained_yn = false;
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.governance.modals.add-member-modal');
    }
}
