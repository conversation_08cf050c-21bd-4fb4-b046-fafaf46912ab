<?php

namespace Modules\EmisReturns\Livewire\Sections\Governance;

use Livewire\Component;
use Modules\Core\Models\Institutions\CommitteeMemberTrainingUpdate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;

class GovernanceMembers extends Component
{
    public $schoolType;
    public $institution;
    public $memberUpdates;
    public $totalMaleCount = 0;
    public $totalFemaleCount = 0;
    public $totalMaleTrainedCount = 0;
    public $totalFemaleTrainedCount = 0;

    protected $listeners = ['membersAddedSuccessfully' => 'getMemberUpdates'];

    public function mount($schoolType, $institution) {
        $this->schoolType = $schoolType;
        $this->institution = $institution;
        $this->getMemberUpdates();
    }

    public function getMemberUpdates(){
        $memberUpdates = Cache::remember('getSchoolCommitte'.$this->institution->id, 300, function () {
            return CommitteeMemberTrainingUpdate::where('school_id', $this->institution->id)
                ->withCount([
                    'person AS male_count' => function (Builder $builder) {
                        $builder->where('gender', 'M');
                    },
                    'person AS female_count' => function (Builder $builder) {
                        $builder->where('gender', 'F');
                    },
                    'person AS male_trained_count' => function (Builder $builder) {
                        $builder->where('is_trained_yn', true)
                            ->where('gender', 'M');
                    },
                    'person AS female_trained_count' => function (Builder $builder) {
                        $builder->where('is_trained_yn', true)
                            ->where('gender', 'F');
                    },
                ])->get();
        });
        
        $this->totalMaleCount = $memberUpdates->sum('male_count');
        $this->totalFemaleCount = $memberUpdates->sum('female_count');
        $this->totalMaleTrainedCount = $memberUpdates->sum('male_trained_count');
        $this->totalFemaleTrainedCount = $memberUpdates->sum('female_trained_count');
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.governance.governance-members');
    }
}
