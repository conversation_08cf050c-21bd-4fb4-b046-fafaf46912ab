<?php

namespace Modules\EmisReturns\Livewire\Sections\Governance;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolSmcBogMember;

class RecentlyAddedMembers extends Component
{
    public $schoolType;
    public $institution;
    public $recentlyAddedMembers = [];

    protected $listeners = ['membersAddedSuccessfully' => 'getRecentlyAddedMembers'];

    public function mount($schoolType, $institution)
    {
        $this->schoolType = $schoolType;
        $this->institution = $institution;
        $this->getRecentlyAddedMembers();
    }

    public function getRecentlyAddedMembers()
    {
        $maxMembers = 9;
        switch ($this->schoolType) {
            case 'preprimary':
                $maxMembers = 9; 
                break;
            case 'primary':
                $maxMembers = 12; 
                break;
            case 'secondary':
                $maxMembers = 14; 
            default:
                break;
        };

        $this->recentlyAddedMembers = SchoolSmcBogMember::where('school_id', $this->institution->id)
            ->whereHas('person', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->with(['person', 'person.country'])
            ->orderBy('date_created', 'desc')
            ->take($maxMembers) 
            ->get();
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.governance.recently-added-members');
    }
}
