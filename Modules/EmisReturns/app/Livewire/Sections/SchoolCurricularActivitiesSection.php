<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Institutions\SchoolPracticalSkillUpdate;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\PracticalSkill;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;

class SchoolCurricularActivitiesSection extends Component
{
    use InstitutionContext;

    public $curricularActivities = [];
    public $curricularActivitiesNames = [];
    public $institution;
    public $academicYearId;
    public $survey;
    public $surveyId;
    public $teachingPeriodId;

    // Variables to be loaded on the modal
    public $selectedActivityId = null;
    public $selectedActivityName;
    public $selectedActivityPresent;

    protected $listeners = [
        'open-curricular-modal' => 'openModal',
    ];

    public function mount($survey)
    {
        $this->institution = $this->getInstitution();
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->teachingPeriodId = get_active_teaching_period()->teaching_period_id; 
        $this->loadCurricularActivities();
    }

    protected function loadCurricularActivities()
    {
        $this->curricularActivitiesNames = PracticalSkill::select('id', 'name')->get()->toArray();
        
        $this->curricularActivities = SchoolPracticalSkillUpdate::where('school_id', $this->institution->id)
            ->where('academic_year_id', $this->academicYearId)
            ->with(['practical_skill'])
            ->get()
            ->toArray();
    }

    public function updateActivity()
    {
        try {
            DB::beginTransaction();

            AdminLogActivity::addToLog('Practical Skills', '1', 'Update', 'User Updated School Practical Skills Updates', 'school');

            if ($this->selectedActivityId) {
                $activity = SchoolPracticalSkillUpdate::findOrFail($this->selectedActivityId);

                $activity->update([
                    'present_in_school' => $this->selectedActivityPresent,
                ]);
            } else {
                $practicalSkill = PracticalSkill::where('name', $this->selectedActivityName)->first();

                if (!$practicalSkill) {
                    throw new Exception("Practical Skill '{$this->selectedActivityName}' not found.");
                }

                // School survey creation or update
                $schoolSurvey = SchoolSurvey::firstOrCreate([
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ], [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]);

                $this->syncSurveySections($schoolSurvey);

                SchoolPracticalSkillUpdate::create([
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $this->academicYearId,
                    'practical_skill_id' => $practicalSkill->id,
                    'present_in_school' => $this->selectedActivityPresent,
                    'teaching_period_id' => $this->teachingPeriodId,
                    'school_survey_id' => $schoolSurvey->id
                ]);
            }

            DB::commit();

            $this->loadCurricularActivities();

            $this->dispatch('close-curricular-modal');

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Extra-curricular activity saved successfully.'
            ]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Record not found.'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Failed to save extra-curricular activity.'
            ]);
        }
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey)
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function openModal($id, $name)
    {
        $this->selectedActivityName = $name;

        $activity = SchoolPracticalSkillUpdate::where('school_id', $this->institution->id)
            ->where('academic_year_id', $this->academicYearId)
            ->where('practical_skill_id', $id)
            ->first();

        if ($activity) {
            $this->selectedActivityPresent = $activity->present_in_school;
            $this->selectedActivityId = $activity->id;

        } else {
            $this->selectedActivityPresent = null;
            $this->selectedActivityId = null;
        }

    }

    public function render()
    {
        return view('emisreturns::livewire.sections.school-curricular-activities-section');
    }
}
