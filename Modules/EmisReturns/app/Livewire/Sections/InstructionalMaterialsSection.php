<?php
namespace Modules\EmisReturns\Livewire\Sections;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Institutions\SchoolTextbook;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolTextbookUpdate;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;
use Modules\Core\Models\Settings\SettingReferenceBookType;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolReferenceBooksUpdate;
use Modules\Core\Models\Settings\SettingSneKit;
use Modules\Core\Models\Settings\SettingTextbook;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolSneKitsUpdate;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Institutions\InstructionalMaterialsUpdates\SchoolWallChartUpdate;
use Modules\Core\Models\Institutions\SchoolWallChart;
use Modules\Core\Models\Settings\SettingWallChart;
use Modules\Core\Models\Settings\Survey;
use Modules\Core\Models\Institutions\SchoolSneKits;
use Modules\Core\Models\Settings\SettingPrimarySchoolSubject;
use Modules\Core\Models\Institutions\SchoolReferenceBook;
use Modules\Core\Models\Settings\SchoolType;
class InstructionalMaterialsSection extends Component
{
    use WithPagination;
    use InstitutionContext;

    public $institution;
    public $perPage = 25;
    public $education_grade_id;
    public $subject_id;
    public $book_id;
    public $quantity;
    public $schoolType;

    public $material_name;
    public $formMode = 'create';
    public $editingTextbookId = null;
    public $grades = [];
    public $availableSubjects = [];
    public $availableTextbooks = [];
    public $form_education_grade_id;
    public $form_subject_id;
    public $form_book_id;
    public $form_quantity;
    public $availablePrimarySubjects = [];
    public $availableSecondarySubjects = [];


    //State for REFERENCE BOOK Management
    public $reference_education_grade_id;
    public $reference_book_id;
    public $reference_quantity;
    public $reference_material_name;
    public $reference_formMode = 'create';
    public $reference_editingId = null;
    public $reference_subject_id;
    public $referenceInstructionalMaterials = [];
    public $getReferenceInstructionalMaterialsProperty = [];

    // Dropdown options for Reference Books
    public $reference_grades = [];
    public $referenceBookTypes = [];
    public $availableReferenceBooks = [];
    public $availableReferenceSubjects = [];
    public $id;
    public $survey_id;
    public $section_id;
    public $subjectId;

    // State for the modal
    public $editMode = false;

    //sne kits
    public $sne_kits = [];
    public $educationGrades= [];
    public  $allKits;
    public $sne_kit_id;
    public $sneKits = [];
    public $currentSneKitId;
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $survey;
    public $schoolSurvey;

    //wall charts and work cards
    public $chart_id;
    public $item;
    public $allWallCharts = [];

    public function mount($survey)
    {
        $this->institution = $this->getInstitution();
        $this->initializeContext($survey);
        $this->grades = SchoolEducationGrade::schoolType($this->institution->school_type_id)->get();
        $this->availableSubjects = [];
        $this->availableTextbooks = [];
        $this->schoolType = SchoolType::where('id', $this->institution->school_type_id)->first();

        // Initialize dropdown data for Reference Books
        $this->reference_grades = SchoolEducationGrade::schoolType($this->institution->school_type_id)->get(); // Assuming same grades
        $this->referenceBookTypes = SettingPrimarySchoolSubject::all();
        $this->availableReferenceBooks = collect();

        // call sne kits method
        $this->allKits = SettingSneKit::all();

        //call wall charts and work cards
        $this->allWallCharts = SettingWallChart::all();
    }
    private function initializeContext($survey): void
    {
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

public function updatedFormEducationGradeId($value)
{
    $this->reset([
        'form_subject_id',
        'form_book_id',
        'availablePrimarySubjects',
        'availableSecondarySubjects',
        'availableTextbooks',
    ]);

    if ($value) {
        $grade = SchoolEducationGrade::find($value);

        $this->availablePrimarySubjects = $grade
            ? $grade->primary_subjects()->get()
            : [];

        $this->availableSecondarySubjects = $grade
            ? $grade->secondary_subjects()->get()
            : [];
    } else {
        $this->availablePrimarySubjects = [];
        $this->availableSecondarySubjects = [];
    }
}


    public function updatedFormSubjectId($value)
    {
        $this->reset('form_book_id');
        $this->availableTextbooks = [];

        if ($value && $this->form_education_grade_id) {
            $this->availableTextbooks = SettingTextbook::with('subject')
                ->where('subject_id', $value)
                ->where('education_grade_id', $this->form_education_grade_id)
                ->get()
                ->filter(function ($textbook) {
                    return $textbook->name;
                });
        }
    }

    public function updating($field)
    {
        if (in_array($field, ['education_grade_id', 'material_name'])) {
            $this->resetPage();
        }
    }

    public function getInstructionalMaterialsProperty()
    {
        $query = $this->institution->text_books()
            ->orderBy('id', 'desc')
            ->with(['subject', 'book', 'education_grade']);

        if ($this->education_grade_id) {
            $query->where('education_grade_id', $this->education_grade_id);
        }

        if ($this->material_name) {
            $query->whereHas('book', function ($q) {
                $q->where('name', 'like', '%' . $this->material_name . '%');
            });
        }
        return $query->get();
    }

    protected function rules()
    {
        return [
            'form_education_grade_id' => 'required|exists:setting_education_grades,id',
            'form_subject_id' => 'required|exists:setting_primary_school_subjects,id',
            'form_book_id' => 'required|exists:setting_textbooks,id',
            'form_quantity' => 'required|integer|min:1',
        ];
    }

    public function storeTextbooks()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }
        AdminLogActivity::addToLog(
            'Textbooks',
            '1',
            'Create',
            'User Created School Textbooks Updates',
            'school'
        );
        $this->validate([
            'form_education_grade_id' => 'required',
            'form_subject_id' => 'required',
            'form_quantity' => 'required',
        ], [
            'form_education_grade_id.required' => 'Select Class to proceed',
            'form_subject_id.required' => 'Select Subject to proceed',
            'form_quantity.required' => 'Enter Quantity to proceed',
        ]);
        if (
            $this->schoolType &&
            $this->schoolType->id == 2 &&
            empty($this->form_book_id)
        ) {
            $this->addError('form_book_id', 'No results found');
            return;
        }
        try {
            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]
            );
            $this->syncSurveySections($schoolSurvey);
            SchoolTextbookUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'education_grade_id' => $this->form_education_grade_id,
                'subject_id' => $this->form_subject_id,
                'book_id' => $this->form_book_id,
            ], [
                'quantity' => $this->form_quantity,
            ]);

            SchoolTextbook::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'education_grade_id' => $this->form_education_grade_id,
                'subject_id' => $this->form_subject_id,
                'book_id' => $this->form_book_id,
            ], [
                'quantity' => $this->form_quantity,
            ]);

            $this->reset(['form_education_grade_id', 'form_subject_id', 'form_book_id', 'form_quantity']);
            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Textbook entry saved successfully.',
            ]);

        } catch (\Throwable $e) {
            report($e);
            $this->addError('general', 'An unexpected error occurred while saving textbooks.');
        }
    }

    public function resetTextbookForm()
    {
        $this->form_education_grade_id = null;
        $this->form_subject_id = null;
        $this->form_book_id = null;
        $this->form_quantity = null;
        $this->resetValidation();
        // $this->editingTextbookId = null;
    }

    public function updateTextbooks()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        AdminLogActivity::addToLog(
            'Textbooks',
            '1',
            'Update',
            'User Updated School Textbooks Entry',
            'school'
        );

        $this->validate([
            'form_education_grade_id' => 'required',
            'form_subject_id' => 'required',
            'form_quantity' => 'required',
        ], [
            'form_education_grade_id.required' => 'Select Class to proceed',
            'form_subject_id.required' => 'Select Subject to proceed',
            'form_quantity.required' => 'Enter Quantity to proceed',
        ]);

        if (
            $this->schoolType &&
            $this->schoolType->id == 2 &&
            empty($this->form_book_id)
        ) {
            $this->addError('form_book_id', 'No results found');
            return;
        }

        try {
            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]
            );

            $this->syncSurveySections($schoolSurvey);

            SchoolTextbookUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'education_grade_id' => $this->form_education_grade_id,
                'subject_id' => $this->form_subject_id,
                'book_id' => $this->form_book_id,
            ], [
                'quantity' => $this->form_quantity,
            ]);

            SchoolTextbook::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'education_grade_id' => $this->form_education_grade_id,
                'subject_id' => $this->form_subject_id,
                'book_id' => $this->form_book_id,
            ], [
                'quantity' => $this->form_quantity,
            ]);

            $this->reset(['form_education_grade_id', 'form_subject_id', 'form_book_id', 'form_quantity']);
            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Updated:',
                'message' => 'Textbook entry updated successfully.',
            ]);

        } catch (\Throwable $e) {
            report($e);
            $this->addError('general', 'An unexpected error occurred while updating textbooks.');
        }
    }

    // HANDLING REFERENCE BOOKS FOR PRIMARY
    public function getPrimarySchoolReferenceBooksProperty()
    {
        return $this->institution->reference_books()
            ->orderBy('id', 'asc')
            ->with(['book', 'education_grade'])
            ->paginate($this->perPage);
    }

    public function updatedSubjectId($value)
    {
        $this->reference_book_id = null;

        if ($value) {
            $subject = SettingPrimarySchoolSubject::find($value);

            $this->availableReferenceBooks = $subject
                ? $subject->reference_books()->get()
                : collect();
        } else {
            $this->availableReferenceBooks = collect();
        }
    }

    public function storeReferenceBook()
    {
        AdminLogActivity::addToLog('Reference Books', '1', 'Create', 'User Created School Reference Books Updates', 'school');
        $this->validate([
            'reference_education_grade_id' => 'required|integer',
            'subjectId' => 'required|integer|exists:setting_primary_school_subjects,id',
            'reference_book_id' => 'required|integer',
            'reference_quantity' => 'required|integer|min:1',
        ], [
            'reference_education_grade_id.required' => 'Select Class to proceed',
            'reference_book_id.required' => 'Select Book to proceed',
            'reference_quantity.required' => 'Enter number to proceed',
        ]);


        if ($this->reference_book_id === null) {
            $this->addError('book_id', 'The class or subject you selected does not have books yet, select another one to continue...');
            return;
        }

        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        try {
            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]
            );

            $this->syncSurveySections($schoolSurvey);

            SchoolReferenceBooksUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'education_grade_id' => $this->reference_education_grade_id,
                'book_id' => $this->reference_book_id,
                'subject_id' => $this->subjectId,
            ], [
                'quantity' => $this->reference_quantity,
            ]);

            SchoolReferenceBook::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'education_grade_id' => $this->reference_education_grade_id,
                'book_id' => $this->reference_book_id,
                'subject_id' => $this->subjectId,
            ], [
                'quantity' => $this->reference_quantity,
            ]);

            $this->institution->refresh();
            $this->institution->load('reference_books.education_grade', 'reference_books.book', 'reference_books.subject');
            $this->reset(['reference_education_grade_id', 'reference_book_id', 'subjectId', 'reference_quantity']);

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Reference book created successfully.',
            ]);

        } catch (\Exception $exception) {
            $this->addError('general', 'An error occurred while saving the reference book data');
        }
    }

    public function resetReferenceBookForm()
    {
        $this->reference_education_grade_id = null;
        $this->reference_book_id = null;
        $this->reference_quantity = null;
        $this->reference_material_name = null;
        $this->subjectId = null;
        $this->resetValidation();
    }

    public function updateReferenceBook()
    {
        AdminLogActivity::addToLog('Reference Books', '1', 'Update', 'User Updated School Reference Books Updates', 'school');

        $this->validate([
            'reference_education_grade_id' => 'required|integer',
            'subjectId' => 'required|integer|exists:setting_primary_school_subjects,id',
            'reference_book_id' => 'required|integer',
            'reference_quantity' => 'required|integer|min:1',
        ], [
            'reference_education_grade_id.required' => 'Select Class to proceed',
            'reference_book_id.required' => 'Select Book to proceed',
            'reference_quantity.required' => 'Enter number to proceed',
        ]);

        if ($this->reference_book_id === null) {
            $this->addError('book_id', 'The class or subject you selected does not have books yet, select another one to continue...');
            return;
        }

        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        try {
            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]
            );

            $this->syncSurveySections($schoolSurvey);

            SchoolReferenceBooksUpdate::where([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'education_grade_id' => $this->reference_education_grade_id,
                'book_id' => $this->reference_book_id,
                'subject_id' => $this->subjectId,
            ])->update([
                'quantity' => $this->reference_quantity,
            ]);

            SchoolReferenceBook::where([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'education_grade_id' => $this->reference_education_grade_id,
                'book_id' => $this->reference_book_id,
                'subject_id' => $this->subjectId,
            ])->update([
                'quantity' => $this->reference_quantity,
            ]);

            $this->institution->refresh();
            $this->institution->load('reference_books.education_grade', 'reference_books.book', 'reference_books.subject');

            $this->reset(['reference_education_grade_id', 'reference_book_id', 'subjectId', 'reference_quantity']);

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Reference book updated successfully.',
            ]);

        } catch (\Exception $exception) {
            $this->addError('general', 'An error occurred while updating the reference book data');
        }
    }

    // HANDLE SNE KITS
    public function getSneKitsProperty()
    {
        $this->sne_kits = $this->institution->sne_kits()
            ->with('education_grade', 'kit')
            ->get();
    }

    protected $listeners = [
        'resetModalForm' => 'resetForm'
    ];

    public function createSneKit()
    {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }
        $this->validate([
            'education_grade_id' => 'required|integer',
            'sne_kit_id' => 'required|integer|exists:setting_sne_kits,id',
            'quantity' => 'required|integer|min:1',
        ]);

        $schoolSurvey = SchoolSurvey::firstOrCreate(
            [
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ],
            [
                'current_section_id' => $this->survey->sections->first()->id ?? null,
                'last_saved' => now(),
            ]
        );
        $this->syncSurveySections($schoolSurvey);
        $record = SchoolSneKitsUpdate::create([
            'school_id' => $this->institution->id,
            'education_grade_id' => $this->education_grade_id,
            'sne_kit_id' => $this->sne_kit_id,
            'quantity' => $this->quantity,

            'academic_year_id' => $this->survey->academic_year_id,
            'teaching_period_id' => $this->termId,
            'school_survey_id' => $schoolSurvey->id,
        ]);
        $record = SchoolSneKits::create([
            'school_id' => $this->institution->id,
            'education_grade_id' => $this->education_grade_id,
            'sne_kit_id' => $this->sne_kit_id,
            'quantity' => $this->quantity,

            'academic_year_id' => $this->survey->academic_year_id,
            'teaching_period_id' => $this->termId,
        ]);

        $this->reset(['education_grade_id', 'sne_kit_id', 'quantity']);
        $this->dispatch('close-modal');
        $this->dispatch('notify', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'SNE Kit created successfully.',
        ]);

    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function resetForm()
    {
        $this->education_grade_id = null;
        $this->sne_kit_id = null;
        $this->quantity = null;
        // $this->editMode = false;
        $this->currentSneKitId = null;
        $this->resetValidation();
    }

    public function getPaginatedWallChartsProperty()
    {
        return SchoolWallChart::with(['education_grade', 'chart'])
            ->where('school_id', $this->institution->id)
            ->get();
    }

    public function createWallChart()
    {
        $this->validate([
            'education_grade_id' => 'required',
            'chart_id' => 'required',
            'quantity' => 'required|integer|min:1',
        ]);

        AdminLogActivity::addToLog('Wall Charts', '1', 'Create', 'User Created School Wall Charts Updates', 'school');

        try {
        if (!$this->institution || !$this->institution->id) {
            session()->flash('error', 'Institution data not available. Please refresh the page.');
            return;
        }

        $schoolSurvey = SchoolSurvey::firstOrCreate(
        [
            'school_id' => $this->institution->id,
            'survey_id' => $this->survey->id,
        ],
        [
            'current_section_id' => $this->survey->sections->first()->id ?? null,
            'last_saved' => now(),
        ]
        );
        $this->syncSurveySections($schoolSurvey);
            SchoolWallChartUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'education_grade_id' => $this->education_grade_id,
                'chart_id' => $this->chart_id,
                'quantity' => $this->quantity,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
            ]);
            SchoolWallChart::updateOrCreate([
                'school_id' => $this->institution->id,
                'education_grade_id' => $this->education_grade_id,
                'chart_id' => $this->chart_id,
                'quantity' => $this->quantity,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
            ]);
            $this->reset(['education_grade_id', 'chart_id', 'quantity']);
            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Wall chart created successfully.',
            ]);
            $this->institution->refresh();
            $this->institution->load('wall_charts.education_grade', 'wall_charts.chart');
        } catch (ModelNotFoundException $e) {
            session()->flash('error', 'Failed to create wall chart.');
        }
    }

    public function resetWallChartForm()
    {
        $this->education_grade_id = null;
        $this->chart_id = null;
        $this->quantity = null;
        $this->id = null;
        $this->resetValidation();
    }

    public function updateChart()
    {
        $this->validate([
            'id' => 'required',
            'education_grade_id' => 'required',
            'chart_id' => 'required',
            'quantity' => 'required|integer|min:1',
        ]);

        AdminLogActivity::addToLog('Wall Charts', '1', 'Update', 'User Updated School Wall Charts', 'school');
        try {
            $update = SchoolWallChartUpdate::where('id', $this->id)->firstOrFail();
            $update->education_grade_id = $this->education_grade_id;
            $update->chart_id = $this->chart_id;
            $update->quantity = $this->quantity;
            $update->save();

            $wallChart = SchoolWallChart::findOrFail($update->id);
            $wallChart->education_grade_id = $this->education_grade_id;
            $wallChart->chart_id = $this->chart_id;
            $wallChart->quantity = $this->quantity;
            $wallChart->save();

            $this->institution->refresh();
            $this->institution->load('wall_charts.education_grade', 'wall_charts.chart');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Wall chart updated successfully.',
            ]);
            $this->dispatch('close-modal');

        } catch (ModelNotFoundException $e) {
            session()->flash('error', 'Wall chart not found.');
        }
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.instructional-materials-section', [
            'grades' => $this->grades,
            // 'subjects' => $this->availableSubjects,
                    'primarySubjects' => $this->availablePrimarySubjects,
        'secondarySubjects' => $this->availableSecondarySubjects,
            'textbooks' => $this->availableTextbooks,
            'instructional_materials' => $this->instructionalMaterials,
            'reference_grades' => $this->reference_grades,
            'primary_school_reference_books' => $this->primarySchoolReferenceBooks,
            'sne_kits' => $this->SneKits,
            'wall_charts' => $this->paginatedWallCharts,
        ]);
    }
}
