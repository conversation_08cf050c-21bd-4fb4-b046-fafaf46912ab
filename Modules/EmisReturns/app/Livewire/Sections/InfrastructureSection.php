<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolBuildingStatusUpdate;
use Modules\Core\Models\Settings\InfrastructureType;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\SettingInterSchEducationLevel;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

class InfrastructureSection extends Component
{
    use InstitutionContext;
    public $institution;
    public $infrastructureTypes = [];

    public $formInputs = []; // Holds inputs per category

    public $edit = false;
    public $loading = false;

    public $inter_education_levels = []; // Holds international education levels


    public $survey;
    public $term;
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;

    public $building_id;
    public $facility_id;
    public $usage_mode;
    public $completion_status;
    public $total_number = 0;
    public $area = 0;
    public $gender_usage;
    public $user_category;
    public $subject_id;
    public $inter_education_level_id;

    public $modalTitle = 'ADD';
    public $category;
    public $categoryName;
    public $categoryId;



    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadInfrastructureTypes();
        $this->loadFacilitiesPerCategory();
        $this->loadCategoryNames();
        $this->loadInternationalEducationLevels();
    }

    //intitialize institution details and survey context
    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }


    //map school types to infrastructure types
    protected function loadInfrastructureTypes()
    {
        $typeMap = [
            1 => 'is_for_pre_primary',
            2 => 'is_for_primary',
            3 => 'is_for_secondary',
            4 => 'is_for_certificate',
            5 => 'is_for_diploma',
            6 => 'is_for_degree',
            7 => 'is_for_international',
        ];

        $typeField = $typeMap[$this->institution->school_type_id] ?? null;

        if (!$typeField) {
            abort(401, 'No infrastructure found for this school type');
        }

        $this->infrastructureTypes = InfrastructureType::where($typeField, true)->get();
    }

    //get the user catgory level titles based on institution's school type
    public function getLevelTitle()
    {
        return $this->institution->school_type->name ?? 'Level';
    }



    //load facilities per category for international schools
    protected function loadFacilitiesPerCategory()
    {
        foreach ($this->infrastructureTypes as $type) {
            $this->formInputs[$type->id] = [
                'category' => $type,
                'facilities' => SchoolBuildingStatusUpdate::with('inter_education_level')
                    ->where('school_id', $this->institution->id)
                    ->where('building_id', $type->id)
                    ->get()
                    ->toArray(),
            ];
        }
    }

    //load  education levels for international schools
    public function loadInternationalEducationLevels()
    {
        if ($this->institution->school_type_id === 7) {
            $this->inter_education_levels = SettingInterSchEducationLevel::pluck('name', 'id')->toArray();
        } else {
            $this->inter_education_levels = [];
        }
    }


    //load category ids, names based on the building_id(to be used in notifications)
    public function loadCategoryNames()
    {
        $category = InfrastructureType::find($this->building_id);
        $this->categoryName = $category?->name ?? null;
        $this->categoryId = $category?->id ?? null;
    }



    //create a new infrastructure for the school
    public function create($buildingId)
    {
        $this->edit = false;
        $this->building_id = $buildingId;
        $this->loadCategoryNames();

        AdminLogActivity::addToLog('Infrastructure', '1', 'Create', 'User Created School Infrastructure Updates', 'school');

        $schoolSurvey = SchoolSurvey::firstOrCreate(
            [
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ],
            [
                'current_section_id' => $this->survey->sections->first()->id ?? null,
                'last_saved' => now(),
            ]
        );

        $this->syncSurveySections($schoolSurvey);

        try {

            SchoolBuildingStatusUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'building_id' => $this->building_id,
                'usage_mode' => $this->usage_mode,
                'completion_status' => $this->completion_status,
                'area' => $this->area,
                'gender_usage' => $this->gender_usage,
                'user_category' => $this->user_category,
                'subject_id' => $this->subject_id,
                'international_education_level_id' => $this->inter_education_level_id,
            ], [
                'total_number' => $this->total_number,
            ]);

            $this->dispatch('close-modal');
            $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                'status' => 'success',
                'title' => 'Success:',
                'message' => $this->categoryName ? "{$this->categoryName} Created Successfully." : 'Infrastructure Created Successfully.'
            ]);

            $this->institution->refresh();
            $this->institution->load('infrastructure.subject', 'infrastructure.inter_education_level');

            $this->resetForm();

            return $this->institution;
        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $this->categoryName ? "Error creating {$this->categoryName} . Please try again later!" . ' ' . $e->getMessage() : 'Failed to create infrastructure.'
            ]);
        }
    }


    //reset the form before creating a new infrastructure
    public function startCreate($buildingId)
    {
        $this->resetForm();
        $this->building_id = $buildingId;
    }


    //update infrastructure details
    public function update()
    {
        if (!$this->facility_id) {
            $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'No facility selected to update.'
            ]);
            return;
        }

        try {

            $facility = SchoolBuildingStatusUpdate::find($this->facility_id);

            if (!$facility) {
                $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Facility not found.'
                ]);
                return;
            }

            $facility->update([
                'building_id' => $this->building_id,
                'usage_mode' => $this->usage_mode,
                'completion_status' => $this->completion_status,
                'area' => $this->area,
                'total_number' => $this->total_number,
                'gender_usage' => $this->gender_usage,
                'user_category' => $this->user_category,
                'subject_id' => $this->subject_id,
                'international_education_level_id' => $this->inter_education_level_id,
            ]);

            AdminLogActivity::addToLog('Infrastructure', '1', 'Update', 'User Updated School Infrastructure Info', 'school');

            $this->dispatch('close-modal');
            $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                'status' => 'success',
                'title' => 'Success:',
                'message' => $this->categoryName ? "{$this->categoryName} Updated Successfully." : 'Infrastructure Updated Successfully.'
            ]);

            $this->resetForm();
            $this->institution->refresh();
            $this->institution->load('infrastructure.subject', 'infrastructure.inter_education_level');
        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyInfrastructure'. $this->categoryId, [
                'status' => 'error',
                'title' => 'Error:',
                'message' => $this->categoryName ? "Error updating {$this->categoryName} . Please try again later!" . ' ' . $e->getMessage() : 'Failed to update infrastructure.'
            ]);
        }
    }

    // Retrieve and prepare the facility data for editing
    public function editInfrastructure($facilityId)
    {

        $this->resetForm();
        $facility = SchoolBuildingStatusUpdate::findOrFail($facilityId);

        $this->facility_id = $facility->id; // store ID for update reference
        $this->edit = true;
        $this->modalTitle = 'EDIT';
        $this->building_id = $facility->building_id;
        $this->usage_mode = $facility->usage_mode;
        $this->completion_status = $facility->completion_status;
        $this->area = $facility->area;
        $this->total_number = $facility->total_number;
        $this->gender_usage = $facility->gender_usage;
        $this->user_category = $facility->user_category;
        $this->inter_education_level_id = $facility->international_education_level_id;
        $this->subject_id = $facility->subject_id;

        $this->loadCategoryNames();
    }

    //reset the form inputs to their initial state
    public function resetForm()
    {
        $this->edit = false;
        $this->facility_id = null;
        $this->building_id = null;
        $this->usage_mode = null;
        $this->completion_status = null;
        $this->total_number = 0;
        $this->area = 0;
        $this->gender_usage = null;
        $this->user_category = null;
        $this->subject_id = null;
        $this->inter_education_level_id = null;
        $this->modalTitle = 'ADD';


        // Reset any flags used in formInputs array
        foreach ($this->formInputs as $categoryId => &$data) {
            if (isset($data['editing'])) {
                unset($data['editing']);
            }
        }
    }




    // Sync survey sections and items to the school survey
    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    // render the component view
    public function render()
    {
        return view('emisreturns::livewire.sections.infrastructure-section', [
            'infrastructureTypes' => $this->infrastructureTypes,
            'institution' => $this->institution,
            'formInputs' => $this->formInputs,
            'edit' => $this->edit,
            'loading' => $this->loading,
            'inter_education_levels' => $this->inter_education_levels,
            'getLevelTitle' => $this->getLevelTitle(),
        ]);
    }
}
