<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;

/**
 * Livewire component for managing and displaying the Teaching Staff section of the survey.
 * Handles form data for teaching staff and renders the corresponding view.
 */
class TeachingStaffSection extends Component
{
    /** @var string The current school type (e.g., 'primary', 'secondary') */
    public $schoolType;

    /** @var array The form data for the teaching staff section */
    public $formData = [];

    /**
     * Mount the component and initialize form data.
     *
     * @param string $schoolType
     * @return void
     */
    public function mount($schoolType)
    {
        $this->schoolType = $schoolType;
        $this->loadFormData();
    }

    /**
     * Load the form data for the teaching staff section.
     *
     * @return void
     */
    private function loadFormData()
    {
        $this->formData = [
            'title' => 'Teaching Staff Information',
            'fields' => [
                'total_teachers' => ['label' => 'Total Teachers', 'type' => 'number', 'value' => ''],
                'male_teachers' => ['label' => 'Male Teachers', 'type' => 'number', 'value' => ''],
                'female_teachers' => ['label' => 'Female Teachers', 'type' => 'number', 'value' => ''],
            ],
        ];
    }

    /**
     * Render the Livewire component view for the teaching staff section.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('emisreturns::livewire.sections.teaching-staff-section', [
            'formData' => $this->formData
        ]);
    }
}
