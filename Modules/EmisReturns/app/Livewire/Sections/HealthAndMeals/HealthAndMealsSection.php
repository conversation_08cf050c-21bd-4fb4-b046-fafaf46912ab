<?php

namespace Modules\EmisReturns\Livewire\Sections\HealthAndMeals;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\SchoolMealType;
use Modules\Core\Models\Settings\SchoolFoodSource;
use Modules\Core\Models\Settings\HealthIssue;
use Modules\Core\Models\Settings\IntegratedHealthServiceType;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Settings\SchoolType;
use Modules\Core\Models\Institutions\SchoolIntegratedHealthServiceUpdate;
use Modules\Core\Models\Institutions\HivAidsSupportRecipient;
use Modules\Core\Helpers\AdminLogActivity;
use Illuminate\Validation\ValidationException;

class HealthAndMealsSection extends Component
{
    use InstitutionContext;

    public $institution;
    public $institutionId;
    public $survey;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $schoolType;

    public $mealTypes;
    public $foodSources;
    public $healthIssues;
    public $healthServicesLearners;
    public $healthServicesParents;
    public $registeredHivCases;

    public $has_adopted_sex_hiv_policy;
    public $offer_hot_midday_meal_to_learners;
    public $selected_meal_types = [];
    public $selected_food_sources = [];
    public $editingServiceId = null;
    public $term;
    public $formValues = [];
    public array $integratedServices = [];

    public function mount($survey)
    {
        $this->institution = $this->getInstitution();
        $this->initializeContext($survey);
        $this->mealTypes = SchoolMealType::all();
        $this->foodSources = SchoolFoodSource::all();
        $this->healthIssues = HealthIssue::all();
        $this->healthServicesLearners = IntegratedHealthServiceType::where('is_for_learner', true)->get();
        $this->healthServicesParents = IntegratedHealthServiceType::where('is_for_learner', false)->get();
        $this->schoolType = SchoolType::where('id', $this->institution->school_type_id)->first();
        $this->has_adopted_sex_hiv_policy = $this->survey->has_adopted_sex_hiv_policy;
        $this->registeredHivCases = HivAidsSupportRecipient::where('school_id', $this->institution->id)->first();

        $sectionId = $this->survey->sections->first()->id ?? null;

        if ($sectionId) {
            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $sectionId,
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);

            $this->survey->school_survey_id = $schoolSurvey->id;
        }

        $updates = SchoolIntegratedHealthServiceUpdate::where('school_id', $this->institution->id)
            ->where('academic_year_id', $this->academicYearId)
            ->where('teaching_period_id', $this->termId)
            ->where('school_survey_id', $this->survey->school_survey_id)
            ->get();

        foreach ($updates as $update) {
            $this->integratedServices[$update->health_service_id] = [
                'total_male_learners' => $update->total_male_learners,
                'total_female_learners' => $update->total_female_learners,
                'total_male_teachers' => $update->total_male_teachers,
                'total_female_teachers' => $update->total_female_teachers,
                'total_male_support_staff' => $update->total_male_support_staff,
                'total_female_support_staff' => $update->total_female_support_staff,
                'total_male_parents' => $update->total_male_parents,
                'total_female_parents' => $update->total_female_parents
            ];
        }
    }

    private function initializeContext($survey): void
    {
        $this->survey = $survey;
        $this->surveyId = $survey?->id;
        $this->academicYearId = $survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
        $this->institutionId = $this->institution->id;
    }

    public function loadPolicyValue(string $schoolType)
    {
        $relationshipName = $schoolType . '_school';
        $this->has_adopted_sex_hiv_policy = optional($this->institution->{$relationshipName})->has_adopted_sex_hiv_policy ?? null;
    }

    public function loadPolicyValuePrePrimary()
    {
        $this->loadPolicyValue('pre_primary');
    }

    public function loadPolicyValuePrimary()
    {
        $this->loadPolicyValue('primary');
    }

    public function loadPolicyValueSecondary()
    {
        $this->loadPolicyValue('secondary');
    }

    public function updatePolicy(string $schoolType)
    {
        $relationshipName = $schoolType . '_school';
        $school = $this->institution->{$relationshipName};

        if ($school) {
            $school->update([
                'has_adopted_sex_hiv_policy' => $this->has_adopted_sex_hiv_policy,
            ]);

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Policy updated successfully.',
            ]);
        }
    }

    public function updatePolicyPrimary()
    {
        $this->updatePolicy('primary');
    }

    public function updatePolicySecondary()
    {
        $this->updatePolicy('secondary');
    }

    public function updatePolicyPrePrimary()
    {
        $this->updatePolicy('pre_primary');
    }

    public function loadHotMealValues()
    {
        $relation = match ((int) $this->schoolType->id) {
            1 => 'pre_primary_school',
            2 => 'primary_school',
            3 => 'secondary_school',
            default => null,
        };
        $school = $relation ? $this->institution->{$relation} : null;

        if ($school) {
            $this->offer_hot_midday_meal_to_learners = (int) ($school->offer_hot_midday_meal_to_learners ?? 0);
            $this->selected_meal_types = $school->meal_types()->pluck('meal_type_id')->toArray();
        } else {
            // Fallback defaults
            $this->offer_hot_midday_meal_to_learners = 0;
            $this->selected_meal_types = [];
        }
    }

    public function getLevelSchool()
    {
        return match ($this->schoolType->id) {
            1 => $this->institution->pre_primary_school,
            2 => $this->institution->primary_school,
            3 => $this->institution->secondary_school,
            default => null,
        };
    }

    public function updatedOfferHotMiddayMealToLearners($value)
    {
        if ($value == '0') {
            $this->selected_meal_types = [];
        }
    }

    public function updateMiddayMeal()
    {
        $school = match ($this->schoolType->id) {
            1 => $this->institution->pre_primary_school,
            2 => $this->institution->primary_school,
            3 => $this->institution->secondary_school,
            default => null,
        };

        if ($school) {
            $school->update([
                'offer_hot_midday_meal_to_learners' => $this->offer_hot_midday_meal_to_learners,
            ]);

            if ((int) $this->offer_hot_midday_meal_to_learners === 1) {
                $school->meal_types()->sync($this->selected_meal_types);
            } else {
                $school->meal_types()->sync([]);
            }

            $this->institution->refresh();
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Saved',
                'message' => 'School Hot Midday Meal Updated Successfully.',
            ]);

            $this->dispatch('close-modal');
        }
    }

    public function loadFoodSourceValues()
    {
        $school = match ($this->schoolType->id) {
            1 => $this->institution->pre_primary_school,
            2 => $this->institution->primary_school,
            3 => $this->institution->secondary_school,
            default => null,
        };

        if ($school) {
            $this->selected_food_sources = $school->food_sources()->pluck('food_source_id')->toArray();
        } else {
            $this->selected_food_sources = [];
        }
    }

    public function updateFoodSources()
    {
        $school = match ($this->schoolType->id) {
            1 => $this->institution->pre_primary_school,
            2 => $this->institution->primary_school,
            3 => $this->institution->secondary_school,
            default => null,
        };

        if ($school) {
            $school->food_sources()->sync($this->selected_food_sources);

            $this->institution->refresh();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Saved',
                'message' => 'Food sources updated successfully.',
            ]);

            $this->dispatch('close-modal');
        }
    }

    public function toggleEdit($serviceId)
    {
        if ($this->editingServiceId === $serviceId) {
            $this->editingServiceId = null;
            $this->formValues = [];
        } else {
            $this->editingServiceId = $serviceId;
            $this->formValues = $this->integratedServices[$serviceId] ?? [
                'total_male_learners' => 0,
                'total_female_learners' => 0,
                'total_male_teachers' => 0,
                'total_female_teachers' => 0,
                'total_male_support_staff' => 0,
                'total_female_support_staff' => 0,
            ];
        }
    }

    public function saveIntegratedService($serviceId)
    {
        try {
            AdminLogActivity::addToLog(
                'Health & Meals',
                '1',
                'Update',
                'User Updated School Health & Meals Updates',
                'school'
            );

            $sectionId = $this->survey->sections->first()->id ?? null;

            if (!$sectionId) {
                throw new \Exception('No sections found for this survey');
            }

            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $sectionId,
                    'last_saved' => now(),
                ]
            );

            $schoolSurvey->sections()->sync($this->survey->sections);
            $schoolSurvey->section_items()->sync($this->survey->section_items);
            $schoolSurvey->sections()->updateExistingPivot($sectionId, [
                'is_complete_yn' => true,
            ]);

            $validated = $this->validate([
                'formValues.total_male_learners' => 'nullable|integer|min:0',
                'formValues.total_female_learners' => 'nullable|integer|min:0',
                'formValues.total_male_teachers' => 'nullable|integer|min:0',
                'formValues.total_female_teachers' => 'nullable|integer|min:0',
                'formValues.total_male_support_staff' => 'nullable|integer|min:0',
                'formValues.total_female_support_staff' => 'nullable|integer|min:0',
                'formValues.total_female_parents' => 'nullable|integer|min:0',
                'formValues.total_male_parents' => 'nullable|integer|min:0',
            ], [
                'formValues.total_male_learners.integer' => 'The total male learners must be a number.',
                'formValues.total_female_learners.integer' => 'The total female learners must be a number.',
                'formValues.total_male_teachers.integer' => 'The total male teachers must be a number.',
                'formValues.total_female_teachers.integer' => 'The total female teachers must be a number.',
                'formValues.total_male_support_staff.integer' => 'The total male support staff must be a number.',
                'formValues.total_female_support_staff.integer' => 'The total female support staff must be a number.',
                'formValues.total_female_parents.integer' => 'The total female parents must be a number.',
                'formValues.total_male_parents.integer' => 'The total male parents must be a number.',
            ]);

            SchoolIntegratedHealthServiceUpdate::updateOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $this->academicYearId,
                    'teaching_period_id' => $this->termId,
                    'school_survey_id' => $schoolSurvey->id,
                    'health_service_id' => $serviceId,
                ],
                $validated['formValues']
            );

            $this->integratedServices[$serviceId] = $validated['formValues'];
            $this->editingServiceId = null;

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Saved',
                'message' => 'Integrated health service updated successfully.',
            ]);
        } catch (ValidationException $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Validation Error',
                'message' => collect($e->validator->errors()->all())->first(),
            ]);
        } catch (\Throwable $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error',
                'message' => 'Something went wrong while saving the data.',
            ]);
        }
    }

    public function render()
    {
        $school = $this->institution->load([
            'pre_primary_school',
            'primary_school',
            'secondary_school',
            'meal_types',
            'food_sources',
        ]);
        return view('emisreturns::livewire.sections.health-and-meals.health-and-meals-section', [
            'school' => $school,
        ]);
    }
}
