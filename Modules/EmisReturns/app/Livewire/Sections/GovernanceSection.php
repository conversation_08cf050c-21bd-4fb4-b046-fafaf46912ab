<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolGovernanceUpdate;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
class GovernanceSection extends Component
{
    use InstitutionContext;

    public $institution;
    public $academicYearId;
    public $survey;
    public $surveyId;
    public $schoolType;

    public $governanceData;

    protected $listeners = ['governanceUpdated' => 'loadGovernanceUpdate'];

    public function mount($survey, $schoolType){
        $this->institution = $this->getInstitution();
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id; 
        $this->schoolType = $schoolType;
        $this->loadGovernanceUpdate();
    }

    public function loadGovernanceUpdate() {
        $this->governanceData = SchoolGovernanceUpdate::where('school_id', $this->institution->id)
            ->with('assembly_frequency')
            ->first();
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.governance-section');
    }
}
