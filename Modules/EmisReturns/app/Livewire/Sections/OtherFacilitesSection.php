<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolOtherFacilityUpdate;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\OtherFacilityType;
use Modules\Core\Models\Settings\Survey;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

class OtherFacilitesSection extends Component
{
    use InstitutionContext;
    public $facilityNames = [];
    public $selectedFacilityName = null;
    public $presentInSchool = null;
    public $institution;
    public $survey;
    public $term;
    public $facilityStates = [];
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $isLoading = false;

    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadFacilityData();
    }

    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    public function loadFacilityData(): void
    {
        $this->facilityNames = OtherFacilityType::pluck('name', 'id')->toArray();
        $this->facilityStates = SchoolOtherFacilityUpdate::where('school_id', $this->institutionId)
            ->where('academic_year_id', $this->academicYearId)
            ->pluck('present_in_school', 'infrastructure_type_id')
            ->toArray();
    }

    public function selectFacility($name): void
    {
        $this->selectedFacilityName = $name;
        $facilityId = array_search($name, $this->facilityNames);
        $this->presentInSchool = $facilityId !== false && isset($this->facilityStates[$facilityId])
            ? (int) $this->facilityStates[$facilityId]
            : null;
    }

    public function updateFacility(): void
    {
        $this->isLoading = true;

        AdminLogActivity::addToLog('Other Facilities', '1', 'Update', 'User Updated School Other Facilities Updates', 'school');

        $facility = OtherFacilityType::where('name', $this->selectedFacilityName)->firstOrFail();
        $schoolSurvey = SchoolSurvey::firstOrCreate(
            [
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ],
            [
                'current_section_id' => $this->survey->sections->first()->id ?? null,
                'last_saved' => now(),
            ]
        );
        $this->syncSurveySections($schoolSurvey);

        if (!is_null($this->presentInSchool)) {
            SchoolOtherFacilityUpdate::updateOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $this->survey->academic_year_id,
                    'teaching_period_id' => $this->termId,
                    'school_survey_id' => $schoolSurvey->id,
                    'infrastructure_type_id' => $facility->id,
                ],
                [
                    'present_in_school' => $this->presentInSchool,
                ]
            );
        }

        $this->loadFacilityData();
        $this->isLoading = false;
        $this->dispatch('close-modal');
        $this->dispatch('notify', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Other Facility Updated Successfully.'
        ]);
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.other-facilites-section');
    }
}
