<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Settings\InternetSource;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Settings\Survey;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Modules\Core\Models\Institutions\SchoolIctFacilityUpdates;
use Modules\Core\Models\Settings\SchoolType;

class IctSection extends Component
{
    use InstitutionContext;

    public $allInternetSources;
    public $internetSource;
    public $allIctFacilities;
    public $institution;
    public $survey_id;
    public $section_id;
    public $showInternetSourceModal = false;
    public $showIctFacilitiesModal = false;
    public $selected_internet_source_id;
    public $connectivity_status;
    public $schoolType;
    public $form_ict_facility = [
        'is_for_teachers' => 0,
        'total_computers_functional' => null,
        'total_computers_non_functional' => null,
        'total_computers_with_internet' => null,
    ];

    public function updateInternetSource()
    {
        $this->validate([
            'selected_internet_source_id' => 'required',
            'survey_id' => 'required',
        ]);

        AdminLogActivity::addToLog('Internet Sources', '1', 'Update School Internet Sources', 'User Updated School Internet Sources Updates', 'school');

        try {
            $survey = Survey::where('id', $this->survey_id)
                ->with(['academic_year.teaching_periods', 'sections', 'section_items'])
                ->firstOrFail();

            $term = get_active_teaching_period();

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey_id,
            ], [
                'current_section_id' => $this->section_id,
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($survey->sections);
            $schoolSurvey->section_items()->sync($survey->section_items);

            $schoolSurvey->sections()->updateExistingPivot($this->section_id, [
                'is_complete_yn' => true,
            ]);

            $this->institution->internet_source()->updateOrCreate([
                'school_id' => $this->institution->id,
            ], [
                'academic_year_id' => $survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'internet_source_id' => $this->selected_internet_source_id,
                'connectivity_status' => $this->selected_internet_source_id == '' ? 0 : ($this->connectivity_status ?? 0),
            ]);

            if ($this->selected_internet_source_id == '' || $this->selected_internet_source_id == 1) {
                $this->institution->ict_facilities()->update(['total_computers_with_internet' => 0]);
            }

            $this->institution->refresh();
            $this->institution->load(['internet_source.source', 'ict_facilities']);

            $this->internetSource = $this->institution->internet_source;
            $this->allIctFacilities = $this->institution->ict_facilities()->get();

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Internet source updated successfully.'
            ]);
        } catch (ModelNotFoundException $exception) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Survey not found.'
            ]);
        } catch (\Exception $exception) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'An error occurred while updating the internet source. Please try again.'
            ]);
        }
    }

    public function updateInternetConnectivityStatus()
    {
        if ($this->internetSource) {
            $this->internetSource->connectivity_status = $this->connectivity_status ? 1 : 0;
            $this->internetSource->save();
            $this->internetSource->refresh();
            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Internet connectivity status updated.'
            ]);
        }
    }

    public function editFacility($isForTeachers)
    {
        $this->form_ict_facility['is_for_teachers'] = $isForTeachers;

        $facility = $this->allIctFacilities->where('is_for_teachers', $isForTeachers)->first();

        if ($facility) {
            $this->form_ict_facility['total_computers_functional'] = $facility->total_computers_functional;
            $this->form_ict_facility['total_computers_non_functional'] = $facility->total_computers_non_functional;
            $this->form_ict_facility['total_computers_with_internet'] = $facility->total_computers_with_internet;
            $this->form_ict_facility['total_over_head_projectors'] = $facility->total_over_head_projectors ?? null;
            $this->form_ict_facility['distance_learning_facilities'] = $facility->distance_learning_facilities ?? null;
        } else {
            // Clear form for a new entry
            $this->form_ict_facility['total_computers_functional'] = null;
            $this->form_ict_facility['total_computers_non_functional'] = null;
            $this->form_ict_facility['total_computers_with_internet'] = null;
            $this->form_ict_facility['total_over_head_projectors'] = null;
            $this->form_ict_facility['distance_learning_facilities'] = null;
        }
    }


    public function updateIctFacilities()
    {
        $this->validate([
            // 'survey_id' => 'required',
            // 'section_id' => 'required',
            'form_ict_facility.total_computers_functional' => 'required|integer|min:0',
            'form_ict_facility.total_computers_non_functional' => 'required|integer|min:0',
            'form_ict_facility.total_computers_with_internet' => 'required|integer|min:0|lte:form_ict_facility.total_computers_functional',
        ]);

        try {
            AdminLogActivity::addToLog('ICT Facilities', '1', 'Update', 'User Updated School ICT Facilities Updates', 'school');

            $survey = Survey::where('id', $this->survey_id)
                ->with(['academic_year.teaching_periods', 'sections', 'section_items'])
                ->firstOrFail();

            $term = get_active_teaching_period();

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey_id,
            ], [
                'current_section_id' => $this->section_id,
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($survey->sections);
            $schoolSurvey->section_items()->sync($survey->section_items);

            $schoolSurvey->sections()->updateExistingPivot($this->section_id, [
                'is_complete_yn' => true,
            ]);

            $internetSourceId = optional($this->institution->internet_source)->internet_source_id;

            $computersWithInternet = ($internetSourceId === null || $internetSourceId == 1)
                ? 0
                : $this->form_ict_facility['total_computers_with_internet'];

            SchoolIctFacilityUpdates::updateOrCreate([
                'school_id' => $this->institution->id,
                'is_for_teachers' => $this->form_ict_facility['is_for_teachers'],
            ], [
                'academic_year_id' => $survey->academic_year_id,
                'teaching_period_id' => $term->teaching_period_id,
                'school_survey_id' => $schoolSurvey->id,
                'total_computers_functional' => $this->form_ict_facility['total_computers_functional'],
                'total_computers_non_functional' => $this->form_ict_facility['total_computers_non_functional'],
                'total_computers_with_internet' => $computersWithInternet,
                'total_over_head_projectors' => $this->form_ict_facility['total_over_head_projectors'] ?? null,
                'distance_learning_facilities' => $this->form_ict_facility['distance_learning_facilities'] ?? null,
            ]);

            $this->institution->refresh();
            $this->institution->load(['ict_facilities', 'internet_source.source']);

            $this->allIctFacilities = $this->institution->ict_facilities()->get();

            $this->dispatch('close-modal');
            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'ICT facilities updated successfully.'
            ]);
        } catch (ModelNotFoundException $exception) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Survey not found.'
            ]);
        } catch (\Exception $exception) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'An error occurred while updating ICT facilities. Please try again.'
            ]);
        }
    }

    public function ictFacilities()
    {
        AdminLogActivity::addToLog('ICT Facilities', '1', 'View', 'User Checked School ICT Facilities Updates', 'school');
    }

    public function mount()
    {
        $this->institution = $this->getInstitution();
        $this->institution->load('internet_source.source');
        $this->allInternetSources = InternetSource::all();
        $this->internetSource = $this->institution->internet_source;
        $this->allIctFacilities = $this->institution->ict_facilities()->get();
        $this->schoolType = SchoolType::where('id', $this->institution->school_type_id)->first();
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.ict-section', [
            'allIctFacilities' => $this->allIctFacilities,
            'internetSource' => $this->internetSource,
            'allInternetSources' => $this->allInternetSources,
        ]);
    }
}
