<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\Survey;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Settings\SchoolLegalOwnershipStatus;
use Modules\Core\Models\Settings\SchoolFoundingBody;
use Modules\Core\Models\Settings\SchoolFundingSource;
use Modules\Core\Models\Settings\SchoolDistanceRange;
use Modules\Core\Models\Settings\HealthFacilityDistanceRanges;
use Modules\Core\Models\Settings\DeoOfficeDistanceRanges;
use Modules\Core\Models\Settings\AdminUnits\County;
use Modules\Core\Models\Settings\AdminUnits\District;
use Modules\Core\Models\Settings\AdminUnits\SubCounty;
use Modules\Core\Models\Settings\AdminUnits\Parish;
use Modules\Core\Models\Settings\SchoolAuthority;
use Modules\Core\Models\Settings\SchoolType;
use Modules\Core\Helpers\AdminLogActivity;
use Illuminate\Support\Facades\Cache;

/**
 * Livewire component for managing and displaying school particulars (Section A and B) in a survey.
 * Handles loading, displaying, and updating institution identification and particulars.
 */
class SchoolParticularsSection extends Component
{
    use InstitutionContext;

    /** @var title */
    public $title;
    /** @var array Stores the school's data for display and editing */
    public $schoolData = [];

      /** @var array Stores the campus's data for display and editing */
    public $campusForm = [];

    /** @var int|string|null The current survey ID */
    public $survey_id;

    /** @var Survey|null The current Survey model instance */
    public $survey;

    /** @var SchoolSurvey|null The current SchoolSurvey model instance */
    public $schoolSurvey;

    /** @var int|string|null The current section ID (if any) */
    public $section_id;

    /** @var School|null The institution context for the current user */
    public $institution;

    /** @var string The school type (e.g., 'primary', 'certificate', etc.) */
    public $schoolType;

      
    /** @var array Collection of legal ownership statuses for dropdowns */
    public $legalOwnershipStatuses = [];
    
    /** @var array Collection of founding bodies for dropdowns */
    public $foundingBodies = [];
    
    /** @var array Collection of funding sources for dropdowns */
    public $fundingSources = [];
    
    /** @var array Collection of school distances for dropdowns */
    public $schoolDistances = [];
    
    /** @var array Collection of health facility distances for dropdowns */
    public $healthDistances = [];
    
    /** @var array Collection of DEO office distances for dropdowns */
    public $deoDistances = [];
    
    /** @var array Collection of school authorities for dropdowns */
    public $schoolAuthorities = [];

    /** @var array Collection of districts */
    public $districts = [];

    /** @var array Collection of counties */
    public $counties = [];
    
    /** @var array Collection of counties */
    public $subcounties = [];
     
    /** @var array Collection of counties */
    public $parishes = [];
    /**
     * Validation rules for updating school particulars.
     *
     * @var array
     */
    protected $rules = [
        'schoolData.physical_address' => 'required|string|max:255',
        'schoolData.postal_address' => 'nullable|string|max:255',
        'schoolData.phone' => ['required', 'string', 'regex:/^(\+2567\d{8}|2567\d{8}|07\d{8})$/'],
        'schoolData.website' => ['nullable', 'string', 'max:255', 'regex:/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/'],
        'schoolData.school_land_area' => 'nullable|numeric|min:0',
    ];

    /**
     * Custom validation messages.
     *
     * @var array
     */
    protected $messages = [
        'schoolData.physical_address.required' => 'Physical address is required',
        'schoolData.phone.required' => 'Phone number is required',
        'schoolData.phone.regex' => 'Please enter a valid Ugandan phone number',
        'schoolData.website.regex' => 'Please enter a valid website domain (e.g. www.example.com or example.com)',
        'schoolData.school_land_area.numeric' => 'Land area must be a number',
        'schoolData.school_land_area.min' => 'Land area cannot be negative',
    ];

    /**
     * Mount the component and initialize school data.
     *
     * @param int|string|null $survey_id
     * @param string|null $schoolType
     * @return void
     */
    public function mount($survey_id = null, $schoolType = null)
    {
        $this->survey_id = $survey_id;
        $this->schoolType = $this->normalizeSchoolType($schoolType);
        $this->institution = $this->getInstitution();
        $this->loadReferenceData();
        $this->loadSchoolData();
        // Initialize the campus form with empty values 
        $this->campusForm = [
        'name' => '',
        'phone' => '',
        'district_id' => '',
        'county_id' => '',
        'sub_county_id' => '',
        'parish_id' => '',
    ];
    }
     /**
     * Load reference data for select dropdowns.
     *
     * @return void
     */
    protected function loadReferenceData()
    {
        // Load all the reference data needed for dropdowns
        $this->legalOwnershipStatuses = SchoolLegalOwnershipStatus::all();
        $this->foundingBodies = SchoolFoundingBody::all();
        $this->fundingSources = SchoolFundingSource::all();
        $this->schoolDistances = SchoolDistanceRange::all();
        $this->healthDistances = HealthFacilityDistanceRanges::all();
        $this->deoDistances = DeoOfficeDistanceRanges::all();
        $this->districts = District::all();
        $this->counties = County::all();
        $this->subcounties = SubCounty::all();
        $this->parishes = Parish::all();

        
        // Load school authorities based on school type
        if ($this->institution) {
            $schoolTypeId = $this->institution->school_type_id;
            
            if ($schoolTypeId === 4 || $schoolTypeId === 5) {
                $this->schoolAuthorities = Cache::remember('setting_registering_authorities4', 3600, function () {
                    return SchoolAuthority::where('school_type_id', 4)->get();
                });
            } else {
                $this->schoolAuthorities = Cache::remember('setting_registering_authorities' . $schoolTypeId, 3600, function () use ($schoolTypeId) {
                    return SchoolType::with('registering_authorities')
                        ->where('id', $schoolTypeId)
                        ->first()
                        ->registering_authorities;
                });
            }
        }
    }

    /**
     * Load the school data for the current institution.
     *
     * Populates $this->schoolData with the school's details.
     *
     * @return void
     */
    protected function loadSchoolData()
    {
        $school = School::with([
            'school_type', 'region', 'district', 'county', 'sub_county', 'parish',
            'legal_ownership_status', 'founding_body', 'registration_status',
            'pre_primary_school.attached_primary_school',
            'primary_school', 'secondary_school',
            'certificate_school', 'diploma_school', 'degree_school', 'international_school',
            'health_facility_distance',
            'certificate_school.school_type.institution_category.examining_body',
            'certificate_school.school_type.institution_category.registering_body',
            'diploma_school.school_type.institution_category.examining_body',
            'diploma_school.school_type.institution_category.registering_body',
            'degree_school.school_type.institution_category.examining_body',
            'degree_school.school_type.institution_category.registering_body',
            'international_school', // just load the base relation
        ])->find($this->institution?->id);

        if (!$school) return;

        // Set school type if not already set
        if (!$this->schoolType) {
            $this->schoolType = $this->normalizeSchoolType($school->school_type?->name ?? '');
        }
      

        // Common fields for all types
        $this->schoolData = [
            'name' => $school->name,
            'emis_number' => $school->emis_number,
            'region' => $school->region?->name,
            'district' => $school->district?->name,
            'county' => $school->county?->name,
            'sub_county' => $school->sub_county?->name,
            'parish' => $school->parish?->name,
            'physical_address' => $school->physical_address,
            'postal_address' => $school->postal_address,
            'email' => $school->email,
            'phone' => $school->phone,
            'website' => $school->website,
            'school_land_area' => $school->school_land_area,
            // Section B defaults
            'ownership_status' => $school->ownership_status?->name,
            'legal_ownership_status' => $school->legal_ownership_status?->name,
            'legal_ownership_status_id' => $school->legal_ownership_status?->id,

            'school_ownership_status_id' => $school->school_ownership_status_id,
            'founder' => $school->founding_body?->name,
            'founding_body_id' => $school->founding_body?->id,
            'year_founded' => $school->year_founded,
            'registration_status' => match($school->registration_status_id) {
                1 => 'REGISTERED',
                2 => 'LICENSED',
                0 => 'NOT LICENSED',
                default => 'NOT SET'
            },
            'registration_number' => $school->registration_number,
            'sex_composition' => $this->getSexComposition($school),
            'day_or_boarding' => $this->getDayBoardingStatus($school),
            'capital_for_establishment' => $this->formatCapital($school->capital_for_establishment),
            'distance_to_deo' => $school->deo_office_distance?->name ?? 'NOT SET',
            'distance_to_health_facility' => $school->health_facility_distance?->name ?? 'NOT SET',
            
            'estimated_distance_to_deo_office_id' => $school->deo_office_distance?->id,
            'health_facility_distance_range_id' => $school->health_facility_distance?->id,
            // Pre-primary
            'ecce_center_type' => null,
            'attached_to_primary_school' => null,
            'primary_school_emis' => $school->pre_primary_school?->attached_primary_school?->emis_number,
            'primary_school_phone' => null,
            'distance_to_nearest_preprimary' => null,
            'estimated_distance_to_ecce_school_id'=>null,
            // Primary
            'center_number' => $school->primary_school?->center_number,
            'use_status' => $school->primary_school?->use_school,
            'distance_to_nearest_primary' => $school->school_distance_range?->name,

            // Tertiary
            'funding_source' => $school->funding_source?->name,
            'funding_source_id' => $school->funding_source?->id,
            'registering_authority' => $school->registering_authority?->name,
            'license_number' => $school->license_number,
            'licence_certificate_expiry_date' => $school->licence_certificate_expiry_date,
            'has_campuses' => false,
            'campuses' => [],
        ];

        // Section B: School type specific fields
        switch($this->schoolType) {
            case 'certificate':
            case 'diploma':
            case 'degree':
                $model = $school->{$this->schoolType . '_school'};
                $schoolTypeModel = $model?->school_type;
                $institutionCategory = $schoolTypeModel?->institution_category;
                $examiningBody = $institutionCategory?->examining_body;
                $registeringBody = $institutionCategory?->registering_body;

                $this->schoolData['institution_type'] = $schoolTypeModel?->name ?? 'NOT SET';
                $this->schoolData['ownership_status'] = $school->ownership_status?->name ?? 'NOT SET';

                // Legal Ownership Status logic (government/private)
                $this->schoolData['legal_ownership_status'] = $school->school_ownership_status_id == 1
                    ? 'GOVERNMENT'
                    : ($school->legal_ownership_status?->name ?? 'NOT SET');

                $this->schoolData['founder'] = $school->founding_body?->name ?? 'NOT SET';
                $this->schoolData['funding_source'] = $school->funding_source?->name ?? 'NOT SET';

                // Registering Authority logic
                if ($this->schoolType === 'diploma' && $model?->diploma_awarding_school_type_id) {
                    $this->schoolData['registering_authority'] = $registeringBody?->name ?? 'NOT SET';
                } elseif ($this->schoolType === 'certificate' && $model?->certificate_awarding_school_type_id) {
                    $this->schoolData['registering_authority'] = $registeringBody?->name ?? 'NOT SET';
                } elseif ($this->schoolType === 'degree') {
                    $this->schoolData['registering_authority'] = $model?->authority?->name ?? 'NOT SET';
                } else {
                    $this->schoolData['registering_authority'] = $model?->authority?->name ?? 'NOT SET';
                }

                $this->schoolData['year_founded'] = $school->year_founded ?? 'NOT SET';

                // Registration Status logic
                if ($school->school_ownership_status_id == 2) {
                    $this->schoolData['registration_status'] = match($school->registration_status_id) {
                        1 => 'REGISTERED',
                        2 => 'LICENSED',
                        0 => 'NOT LICENSED',
                        default => 'NOT SET'
                    };
                } else {
                    $this->schoolData['registration_status'] = $school->registration_status?->name ?? 'NOT SET';
                }

                $this->schoolData['registration_number'] = ($school->registration_status_id == 1 && $school->school_ownership_status_id == 2)
                    ? ($school->registration_number ?? 'NOT SET')
                    : null;

                $this->schoolData['license_number'] = ($school->registration_status_id == 2 && $school->school_ownership_status_id == 2)
                    ? ($school->license_number ?? 'NOT SET')
                    : null;

                $this->schoolData['licence_certificate_expiry_date'] = ($school->registration_status_id == 2 && $school->school_ownership_status_id == 2)
                    ? ($school->licence_certificate_expiry_date ? \Carbon\Carbon::parse($school->licence_certificate_expiry_date)->format('d F, Y') : 'NOT SET')
                    : null;

                // Supply Number (government)
                $this->schoolData['supply_number'] = ($school->school_ownership_status_id == 1)
                    ? ($model?->supply_number ?? 'NOT SET')
                    : null;

                // Examining Body Center Number
                $this->schoolData['examining_body_acronym'] = $examiningBody?->acronym ?? '';
                $this->schoolData['center_number'] = $school->center_number ?? 'NOT SET';

                // Sex Composition
                if ($school->has_male_students && $school->has_female_students) {
                    $this->schoolData['sex_composition'] = 'Mixed';
                } elseif ($school->has_male_students && !$school->has_female_students) {
                    $this->schoolData['sex_composition'] = 'Males Only';
                } elseif (!$school->has_male_students && $school->has_female_students) {
                    $this->schoolData['sex_composition'] = 'Females Only';
                } else {
                    $this->schoolData['sex_composition'] = 'NOT SET';
                }

                // Residential/Non-Residential
                if ($model?->admits_day_scholars_yn && $model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'RESIDENTIAL & NON-RESIDENTIAL';
                } elseif (!$model?->admits_day_scholars_yn && $model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'RESIDENTIAL ONLY';
                } elseif ($model?->admits_day_scholars_yn && !$model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'NON-RESIDENTIAL ONLY';
                } else {
                    $this->schoolData['day_or_boarding'] = 'NOT SET';
                }

                // Highest Award
                $this->schoolData['highest_award'] = $schoolTypeModel?->name
                    ? strtoupper($schoolTypeModel->name)
                    : (ucfirst($this->schoolType) . 's');

                // Capital for Establishment (private only)
                $this->schoolData['capital_for_establishment'] = ($school->school_ownership_status_id == 2)
                    ? ($this->formatCapital($school->capital_for_establishment) ?? 'NOT SET')
                    : null;

                // Distance to nearest Health Facility
                $this->schoolData['distance_to_health_facility'] = $school->health_facility_distance?->name ?? 'NOT SET';

                // Has Campuses
                $this->schoolData['has_campuses'] = $model && isset($model->campuses) && count($model->campuses) > 0;
                $this->schoolData['campuses'] = $model?->campuses ?? [];
                break;
            case 'international':
                $model = $school->international_school;
                // Use the main school_type for international
                $schoolTypeModel = $school->school_type;
                $institutionCategory = $schoolTypeModel?->institution_category;
                $examiningBody = $institutionCategory?->examining_body;
                $registeringBody = $institutionCategory?->registering_body;

                $this->schoolData['institution_type'] = $schoolTypeModel?->name ?? 'NOT SET';
                $this->schoolData['ownership_status'] = $school->ownership_status?->name ?? 'NOT SET';
                $this->schoolData['legal_ownership_status'] = $school->school_ownership_status_id == 1
                    ? 'GOVERNMENT'
                    : ($school->legal_ownership_status?->name ?? 'NOT SET');
                $this->schoolData['founder'] = $school->founding_body?->name ?? 'NOT SET';
                $this->schoolData['funding_source'] = $school->funding_source?->name ?? 'NOT SET';
                $this->schoolData['registering_authority'] = $registeringBody?->name ?? 'NOT SET';
                $this->schoolData['year_founded'] = $school->year_founded ?? 'NOT SET';
                $this->schoolData['registration_number'] = $school->registration_number ?? 'NOT SET';
                $this->schoolData['license_number'] = $school->license_number ?? 'NOT SET';
                $this->schoolData['licence_certificate_expiry_date'] = $school->licence_certificate_expiry_date ? \Carbon\Carbon::parse($school->licence_certificate_expiry_date)->format('d F, Y') : 'NOT SET';
                $this->schoolData['examining_body_acronym'] = $examiningBody?->acronym ?? '';
                $this->schoolData['center_number'] = $school->center_number ?? 'NOT SET';

              

                // Residential/Non-Residential
                if ($model?->admits_day_scholars_yn && $model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'Day and Boarding';
                } elseif (!$model?->admits_day_scholars_yn && $model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'Boarding Only';
                } elseif ($model?->admits_day_scholars_yn && !$model?->admits_boarders_yn) {
                    $this->schoolData['day_or_boarding'] = 'Day Only';
                } else {
                    $this->schoolData['day_or_boarding'] = 'NOT SET';
                }

                $this->schoolData['highest_award'] = 'INTERNATIONAL PROGRAMS';
                $this->schoolData['capital_for_establishment'] = $this->formatCapital($school->capital_for_establishment) ?? null;
                $this->schoolData['distance_to_deo'] = $model?->estimated_distance_to_deo_office_id ? $model->deo_office_distance?->name : null;
                $this->schoolData['distance_to_nearest_preprimary'] = $model?->estimated_distance_to_international_school_id ? $model->school_distance_range?->name : null;
                $this->schoolData['estimated_distance_to_deo_office_id'] = $model?->estimated_distance_to_deo_office_id ? $model->deo_office_distance?->id : null;
                $this->schoolData['estimated_distance_to_international_school_id'] = $model?->estimated_distance_to_international_school_id ? $model->school_distance_range?->id : null;
                $this->schoolData['distance_to_health_facility'] = $school->health_facility_distance?->name ?? null;
                $this->schoolData['has_campuses'] = $model && isset($model->campuses) && count($model->campuses) > 0;
                $this->schoolData['campuses'] = $model?->campuses ?? [];
                break;
            case 'pre-primary':
                $this->schoolData['ecce_center_type'] = match($school->pre_primary_school?->has_daycare_center . $school->pre_primary_school?->has_nursery_section) {
                    '11' => 'both',
                    '10' => 'daycare',
                    '01' => 'nursery',
                    default => null
                };
                $this->schoolData['attached_to_primary_school'] = $school->pre_primary_school?->attached_primary_school ? 1 : 0;
                $this->schoolData['primary_school_name'] = $school->pre_primary_school?->attached_primary_school?->name;
                $this->schoolData['primary_school_phone'] = $school->pre_primary_school?->attached_primary_school?->phone;

            
                $this->schoolData['distance_to_deo'] = $school->pre_primary_school?->deo_office_distance?->name
                    ?? ($school->pre_primary_school?->deo_office_distance_name ?? 'NOT SET');
                $this->schoolData['estimated_distance_to_deo_office_id'] = $school->pre_primary_school?->deo_office_distance?->id
                    ?? ($school->pre_primary_school?->deo_office_distance_id ?? 'NOT SET');
                $this->schoolData['distance_to_nearest_preprimary'] = $school->pre_primary_school?->school_distance_range?->name
                    ?? ($school->pre_primary_school?->school_distance_range_name ?? 'NOT SET');
                $this->schoolData['estimated_distance_to_ecce_school_id'] = $school->pre_primary_school?->school_distance_range?->id
                    ?? ($school->pre_primary_school?->school_distance_range_id ?? 'NOT SET');

                $this->schoolData['distance_to_deo'] = $this->schoolData['distance_to_deo'] ?: 'NOT SET';
                $this->schoolData['distance_to_nearest_preprimary'] = $this->schoolData['distance_to_nearest_preprimary'] ?: 'NOT SET';
                
                $this->schoolData['distance_to_health_facility'] = $this->schoolData['distance_to_health_facility'] ?: 'NOT SET';

                $this->schoolData['capital_for_establishment'] = $this->formatCapital($school->capital_for_establishment) ?? 'NOT SET';
                break;
            case 'primary':
                $this->schoolData['center_number'] = $school->primary_school?->center_number;
                $this->schoolData['use_status'] = $school->primary_school?->use_school ? 'USE' : 'NON-USE';
                $this->schoolData['supply_number'] = $school->primary_school?->supply_number;
                $this->schoolData['uneb_center_number'] =  $school->primary_school?->uneb_center_number;
                $this->schoolData['distance_to_nearest_preprimary'] = $school->school_distance_range?->name;
                 $this->schoolData['distance_to_nearest_primary'] = $school->primary_school?->school_distance_range?->name;
                 $this->schoolData['distance_to_nearest_primary_id'] = $school->primary_school?->school_distance_range?->id;

                $this->schoolData['distance_to_deo'] = $school->primary_school?->deo_office_distance?->name;
                $this->schoolData['distance_to_deo_id'] = $school->primary_school?->deo_office_distance?->id;

                break;
            case 'secondary':
                $this->schoolData['uneb_center_number'] =  $school->secondary_school?->uneb_center_number;
                $this->schoolData['supply_number'] = $school->secondary_school?->supply_number;
                $this->schoolData['use_upolet'] = ($school->secondary_school?->is_use_yn && $school->secondary_school?->is_upolet_yn) ? 'USE/UPOLET' :
                                                   ($school->secondary_school?->is_use_yn ? 'USE' :
                                                   ($school->secondary_school?->is_upolet_yn ? 'USE/UPOLET' : 'NON-USE'));
                $this->schoolData['school_level'] = $school->secondary_school->has_olevel_yn && $school->secondary_school->has_alevel_yn
                                                   ? "Both O & A' Levels"
                                                   : ($school->secondary_school->has_olevel_yn
                                                       ? "O' Level"
                                                       : ($school->secondary_school->has_alevel_yn
                                                           ? "A' Level"
                                                           : "NOT SET"));
                $this->schoolData['distance_to_nearest_secondary'] = $school->secondary_school?->school_distance_range?->name;
                $this->schoolData['distance_to_nearest_secondary_id'] = $school->secondary_school?->school_distance_range?->id;
                
                $this->schoolData['distance_to_deo'] = $school->secondary_school?->deo_office_distance?->name;
                $this->schoolData['distance_to_deo_id'] = $school->secondary_school?->deo_office_distance?->id;

                // Determine the school level based on user selection
                $schoolLevel = $this->schoolData['school_level'] ?? '';

                // Set boolean values based on the dropdown selection
                $this->schoolData['has_olevel_yn'] = ($schoolLevel === "O' Level" || $schoolLevel === "Both O & A' Levels");
                $this->schoolData['has_alevel_yn'] = ($schoolLevel === "A' Level" || $schoolLevel === "Both O & A' Levels");

                break;
        }
    }

    /**
     * Get the ECCE center type for the school.
     *
     * @param School $school
     * @return string|null
     */
    protected function getEcceCenterType($school)
    {
        if (!$school->pre_primary_school) return null;
        $pre = $school->pre_primary_school;

        if ($pre->has_daycare_center && $pre->has_nursery_section) {
            return 'both';
        } elseif ($pre->has_daycare_center) {
            return 'daycare';
        } elseif ($pre->has_nursery_section) {
            return 'nursery';
        }
        return null;
    }

    /**
     * Get the registration status as a string.
     *
     * @param School $school
     * @return string|null
     */
    protected function getRegistrationStatus($school)
    {
        return match($school->registration_status_id) {
            1 => 'REGISTERED',
            2 => 'LICENSED',
            0 => 'NOT LICENSED',
            default => null
        };
    }

    /**
     * Get the sex composition of the school.
     *
     * @param School $school
     * @return string|null
     */
    protected function getSexComposition($school)
    {
        if ($school->has_male_students && $school->has_female_students) {
            return 'mixed';
        } elseif ($school->has_male_students) {
            return 'male';
        } elseif ($school->has_female_students) {
            return 'female';
        }
        return null;
    }

    /**
     * Get the day/boarding status of the school.
     *
     * @param School $school
     * @return string|null
     */
    protected function getDayBoardingStatus($school)
    {
        $model = $school->pre_primary_school ?? $school->primary_school ?? $school->secondary_school;
        if (!$model) return null;

        if ($model->admits_day_scholars_yn && $model->admits_boarders_yn) {
            return 'both';
        } elseif ($model->admits_day_scholars_yn) {
            return 'day';
        } elseif ($model->admits_boarders_yn) {
            return 'boarding';
        }
        return null;
    }

    /**
     * Format the capital value for display.
     *
     * @param mixed $value
     * @return string|null
     */
    protected function formatCapital($value)
    {
        if (!$value) return null;
        return is_numeric($value) ? 'UGX ' . number_format($value) : $value;
    }

    /**
     * Save Section A (Institution Identification) updates.
     *
     * @return void
     */
    public function saveSectionA()
    {
        $this->validate();

        $institution = $this->institution;

        if (!$institution) {
            $this->dispatch('n', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Institution not found.'
            ]);
            return;
        }

        // Update institution details
        $institution->update([
            'name' => $this->schoolData['name'] ?? $institution->name,
            'district_id' => $institution->district_id,
            'county_id' => $institution->county_id,
            'sub_county_id' => $institution->sub_county_id,
            'parish_id' => $institution->parish_id,
            'physical_address' => $this->schoolData['physical_address'],
            'postal_address' => $this->schoolData['postal_address'],
            'email' => $institution->email,
            'phone' => $this->schoolData['phone'],
            'website' => $this->schoolData['website'],
            'school_land_area' => $this->schoolData['school_land_area'],
        ]);

        // Sync survey sections/items as in controller
        $survey = Survey::where('id', $this->survey_id)
            ->with(['academic_year', 'sections', 'section_items'])
            ->first();

        if ($survey) {
            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $institution->id,
                'survey_id' => $this->survey_id,
            ], [
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($survey->sections);
            $schoolSurvey->section_items()->sync($survey->section_items);

            // Optionally update pivot for section completion if section_id is available
            if ($this->section_id) {
                $schoolSurvey->sections()->updateExistingPivot($this->section_id, [
                    'is_complete_yn' => true,
                ]);
            }
        }

        $this->dispatch('notifySectionA', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Institution identification updated successfully.'
        ]);

        $this->dispatch('close-modal');
        $this->loadSchoolData();
    }

    /**
     * Render the Livewire component view.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('emisreturns::livewire.sections.school-particulars-section');
    }

    /**
     * Normalize the school type string to a standard format.
     *
     * @param string|null $type
     * @return string|null
     */
    protected function normalizeSchoolType($type)
    {
        $type = strtolower(trim($type));
        // Normalize all possible variants to match your Blade checks
        return match($type) {
            'preprimary', 'pre-primary', 'pre primary' => 'pre-primary',
            'primary' => 'primary',
            'secondary' => 'secondary',
            'certificate' => 'certificate',
            'diploma' => 'diploma',
            'degree' => 'degree',
            'international' => 'international',
            default => $type,
        };
    }

    /**
     * Save Section B (School Particulars) updates.
     * @return void
     */
    
    protected function sectionBRules()
    {
        switch ($this->schoolType) {
            case 'pre-primary':
                return [
                    'schoolData.ecce_center_type' => 'required|in:both,nursery,daycare',
                    'schoolData.attached_to_primary_school' => 'required|boolean',
                    'schoolData.primary_school_emis' => 'required_if:schoolData.attached_to_primary_school,1|nullable|string',
                    'schoolData.legal_ownership_status_id' => 'required|integer',
                    'schoolData.founding_body_id' => 'required|integer',
                    'schoolData.year_founded' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
                    'schoolData.registration_status' => 'required|in:REGISTERED,LICENSED,NOT LICENSED',
                    'schoolData.registration_number' => 'required_if:schoolData.registration_status,REGISTERED|nullable|string',
                    'schoolData.license_number' => 'required_if:schoolData.registration_status,LICENSED|nullable|string',
                  //  'schoolData.licence_certificate_expiry_date' => 'required_if:schoolData.registration_status,LICENSED|nullable|date',
                    'schoolData.sex_composition' => 'required|in:mixed,male,female',
                    'schoolData.day_or_boarding' => 'required|in:both,day,boarding',
                    'schoolData.capital_for_establishment' => 'min:0',
                    'schoolData.estimated_distance_to_deo_office_id' => 'required|integer',
                    'schoolData.estimated_distance_to_ecce_school_id' => 'required|integer',
                    'schoolData.health_facility_distance_range_id' => 'required|integer',
                ];
            case 'primary':
                return [
                    'schoolData.legal_ownership_status_id' => 'required|integer',
                    'schoolData.founding_body_id' => 'required|integer',
                    'schoolData.year_founded' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
                    'schoolData.supply_number' => 'nullable|string',
                    'schoolData.uneb_center_number' => 'nullable|string',
                    'schoolData.sex_composition' => 'required|in:mixed,male,female',
                    'schoolData.day_or_boarding' => 'required|in:both,day,boarding',
                    'schoolData.capital_for_establishment' => 'min:0',
                    'schoolData.distance_to_deo_id' => 'required|integer',
                    'schoolData.distance_to_nearest_primary_id' => 'required|integer',
                    'schoolData.health_facility_distance_range_id' => 'required|integer',
                ];
            case 'secondary':
                return [
                    'schoolData.ownership_status' => 'required|string',
                    'schoolData.legal_ownership_status_id' => 'required|integer',
                    'schoolData.founding_body_id' => 'required|integer',
                    'schoolData.year_founded' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
                    'schoolData.supply_number' => 'nullable|string',
                    'schoolData.uneb_center_number' => 'nullable|string',
                    'schoolData.sex_composition' => 'required|in:mixed,male,female',
                    'schoolData.day_or_boarding' => 'required|in:both,day,boarding',
                    'schoolData.use_upolet' => 'required|in:USE,UPOLET,USE/UPOLET,NON-USE',
                    'schoolData.school_level' => 'required|in:O\' Level,A\' Level,Both O & A\' Levels',
                    'schoolData.capital_for_establishment' => 'min:0',
                    'schoolData.distance_to_deo_id' => 'required|integer',
                    'schoolData.distance_to_nearest_secondary_id' => 'required|integer',
                    'schoolData.health_facility_distance_range_id' => 'required|integer',
                ];
            case 'certificate':
            case 'diploma':
            case 'degree':
                return [
                    'schoolData.legal_ownership_status_id' => 'required|integer',
                    'schoolData.founding_body_id' => 'required|integer',
                    'schoolData.funding_source_id' => 'required|integer',
                    'schoolData.year_founded' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
                    'schoolData.registration_status' => 'required|in:REGISTERED,LICENSED,NOT LICENSED',
                    'schoolData.registration_number' => 'required_if:schoolData.registration_status,REGISTERED|nullable|string',
                    'schoolData.license_number' => 'required_if:schoolData.registration_status,LICENSED|nullable|string',
                    'schoolData.supply_number' => 'nullable|string',
                    'schoolData.center_number' => 'nullable|string',
                    'schoolData.sex_composition' => 'required|in:mixed,male,female',
                    'schoolData.day_or_boarding' => 'required|in:RESIDENTIAL & NON-RESIDENTIAL,RESIDENTIAL ONLY,NON-RESIDENTIAL ONLY',
                    'schoolData.capital_for_establishment' => 'min:0',
                    'schoolData.health_facility_distance_range_id' => 'required|integer',
                ];
            case 'international':
                return [
                    'schoolData.legal_ownership_status_id' => 'required|integer',
                    'schoolData.founding_body_id' => 'required|integer',
                    'schoolData.funding_source_id' => 'required|integer',
                    'schoolData.year_founded' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
                    'schoolData.registration_status' => 'required|in:REGISTERED,LICENSED,NOT LICENSED',
                    'schoolData.registration_number' => 'required_if:schoolData.registration_status,REGISTERED|nullable|string',
                    'schoolData.license_number' => 'required_if:schoolData.registration_status,LICENSED|nullable|string',
                   // 'schoolData.licence_certificate_expiry_date' => 'required_if:schoolData.registration_status,LICENSED|nullable|date',
                    'schoolData.sex_composition' => 'required|in:mixed,male,female',
                    'schoolData.day_or_boarding' => 'required|in:Day and Boarding,Day Only,Boarding Only',
                    'schoolData.capital_for_establishment' => 'min:0',
                    'schoolData.estimated_distance_to_deo_office_id' => 'required|integer',
                    'schoolData.estimated_distance_to_international_school_id' => 'required|integer',
                    'schoolData.health_facility_distance_range_id' => 'required|integer',
                ];
            default:
                return [];
        }
    }
    public function saveSectionB()
    {   
        $this->validate($this->sectionBRules());
        $institution = $this->institution;

        if (!$institution) {
            $this->dispatch('notifySectionB', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Institution not found.'
            ]);
            return;
        }

        AdminLogActivity::addToLog('School Particulars', '1', 'Update', 'User Updated School Particulars', 'school');
        
        $survey = Survey::where('id', $this->survey_id)->with(['academic_year', 'sections', 'section_items'])->firstOrFail();

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $institution->id,
            'survey_id' => $this->survey_id,
        ], [
            'last_saved' => now(),
        ]);

        $schoolSurvey->sections()->sync($survey->sections);
        $schoolSurvey->section_items()->sync($survey->section_items);

        // Optionally update pivot for section completion if section_id is available
        if ($this->section_id) {
            $schoolSurvey->sections()->updateExistingPivot($this->section_id, [
            'is_complete_yn' => true,
         ]);
        }

        if($this->schoolType=='pre-primary'){
        if ($this->schoolData['attached_to_primary_school'] == 1) {
            try {
                $primarySchool = School::where('school_type_id', 2)
                    ->where(function ($query) {
                        $query->where('emis_number', $this->schoolData['primary_school_emis'])
                              ->orWhere('old_emis_number', $this->schoolData['primary_school_emis']);
                    })
                    ->firstOrFail();
            } catch (ModelNotFoundException $exception) {
                abort(500, 'This Primary School does not exist in our system');
            }
        }
  
            $primarySchoolId = null;
            if ($this->schoolData['attached_to_primary_school'] == 1) {
                $primarySchoolId = School::where('school_type_id', 2)
                    ->where(fn ($q) => $q->where('emis_number', $this->schoolData['primary_school_emis'] ?? '')
                                       ->orWhere('old_emis_number', $this->schoolData['primary_school_emis'] ?? ''))
                    ->value('id');
                if (!$primarySchoolId) {
                    throw ValidationException::withMessages([
                        'sectionBData.primary_school_emis' => 'This Primary School does not exist in our system',
                    ]);
                }
            }
           //Registration status map
            $statusMap = [
                'REGISTERED'    => 1,
                'LICENSED'      => 2,
                'NOT LICENSED'  => 0,
            ];
            $registrationStatus = $this->schoolData['registration_status'];

            // Update school model
            $institution->update([
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $statusMap[$registrationStatus] ?? null,
                'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
                'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'year_founded'                      => $this->schoolData['year_founded'],
                'founding_body_id'                  => $this->schoolData['founding_body_id'],
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            ]);
        
            // Update pre-primary pivot
            $institution->pre_primary_school->update([
                'has_daycare_center'                   => in_array($this->schoolData['ecce_center_type'] ?? '', ['both', 'daycare']),
                'has_nursery_section'                  => in_array($this->schoolData['ecce_center_type'] ?? '', ['both', 'nursery']),
                'admits_day_scholars_yn'               => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'day']),
                'admits_boarders_yn'                   => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'boarding']),
                'primary_school_id'                    => $primarySchoolId,
                'estimated_distance_to_deo_office_id'  => $this->schoolData['estimated_distance_to_deo_office_id'] ?? null,
                'estimated_distance_to_ecce_school_id' => $this->schoolData['estimated_distance_to_ecce_school_id'] ?? null,
            ]);
       
        }

        if ($this->schoolType == 'primary') {
            //  dd([
            //     'legal_ownership_status_id'       => $this->schoolData['legal_ownership_status_id'] ?? null,
            //     'founding_body_id'                => $this->schoolData['founding_body_id'] ?? null,
            //     'year_founded'                    => $this->schoolData['year_founded'] ?? null,
            //     'has_male_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
            //     'has_female_students'             => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
            //     'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
            //     'capital_for_establishment'       => $this->schoolData['capital_for_establishment'] ?? null,
            // ]);
            $institution->update([
              
                'legal_ownership_status_id'       => $this->schoolData['legal_ownership_status_id'] ?? null,
                'founding_body_id'                => $this->schoolData['founding_body_id'] ?? null,
                'year_founded'                    => $this->schoolData['year_founded'] ?? null,
                'has_male_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
                'has_female_students'             => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'capital_for_establishment'       => $this->schoolData['capital_for_establishment'] ?? null,
            ]);

            if ($institution->primary_school) {
                $institution->primary_school->update([
                    'supply_number'                          => $this->schoolData['supply_number'] ?? null,
                    'uneb_center_number'                     => $this->schoolData['uneb_center_number'] ?? null,
                    'admits_day_scholars_yn'                 => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'day']),
                    'admits_boarders_yn'                     => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'boarding']),
                    'estimated_distance_to_deo_office_id'    => $this->schoolData['distance_to_deo_id'] ?? null,
                    'estimated_distance_to_primary_school_id'=> $this->schoolData['distance_to_nearest_primary_id'] ?? null,
                ]);
            }
        }
        
        if ($this->schoolType == 'secondary') {
                        // Determine use and upolet status
                        $useUpoletStatus = $this->schoolData['use_upolet'] ?? '';

                        // Set boolean values based on the dropdown selection
                        $isUse = $useUpoletStatus === 'USE';
                        $isUpolet = $useUpoletStatus === 'USE/UPOLET';
                        $isNonUse = $useUpoletStatus === 'NON_USE';



                        $schoolLevel = $this->schoolData['school_level'] ?? '';

                        // Set boolean values based on the dropdown selection
                        $this->schoolData['has_olevel_yn'] = ($schoolLevel === "O' Level" || $schoolLevel === "Both O & A' Levels");
                        $this->schoolData['has_alevel_yn'] = ($schoolLevel === "A' Level" || $schoolLevel === "Both O & A' Levels");
    //  // Debugging the values being parsed for both institution and secondary school
    //  dd([
    //     'institution' => [
    //         'registration_number'               => $this->schoolData['registration_number'] ?? null,
    //         'license_number'                    => $this->schoolData['license_number'] ?? null,
    //         'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
    //         'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
    //         'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
    //         'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
    //         'year_founded'                      => $this->schoolData['year_founded'] ?? null,
    //         'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
    //         'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
    //         'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
    //         'is_use_yn' => $isUse,
    //         'is_upolet_yn' => $isUpolet,
    //         'is_non_use' => $isNonUse,
    //     ],
    //     'secondary_school' => [
    //         'supply_number'                          => $this->schoolData['supply_number'] ?? null,
    //         'uneb_center_number'                     => $this->schoolData['uneb_center_number'] ?? null,
    //         'admits_day_scholars_yn'                 => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'day']),
    //         'admits_boarders_yn'                     => in_array($this->schoolData['day_or_boarding'] ?? '', ['both', 'boarding']),
    //         'is_use_yn' => $isUse,
    //         'is_upolet_yn' => $isUpolet,
    //         'is_non_use' => $isNonUse,
    //         'has_olevel_yn' => $this->schoolData['has_olevel_yn'],
    //         'has_alevel_yn' => $this->schoolData['has_alevel_yn'],
    //         'estimated_distance_to_deo_office_id'    => $this->schoolData['distance_to_deo_id'] ?? null,
    //         'estimated_distance_to_secondary_school_id' => $this->schoolData['distance_to_nearest_secondary_id'] ?? null,
    //     ],
    // ]);        
           
            $statusMap = [
                'REGISTERED'    => 1,
                'LICENSED'      => 2,
                'NOT LICENSED'  => 0,
            ];
            $registrationStatus = $this->schoolData['registration_status'];

        
                
            $institution->update([
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $statusMap[$registrationStatus] ?? null,
                'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
                'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'year_founded'                      => $this->schoolData['year_founded'] ?? null,
                'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            
            ]);
        
            if ($institution->secondary_school) {
                $institution->secondary_school->update([
                    'supply_number'                          => $this->schoolData['supply_number'] ?? null,
                    'uneb_center_number'                     => $this->schoolData['uneb_center_number'] ?? null,
                    'admits_day_scholars_yn'                 => in_array($this->schoolData['day_or_boarding'] ?? '', ['Day and Boarding', 'DAY ONLY']),
                    'admits_boarders_yn'                     => in_array($this->schoolData['day_or_boarding'] ?? '', ['Day and Boarding', 'BOARDING ONLY']),
                    'estimated_distance_to_deo_office_id'    => $this->schoolData['distance_to_deo_id'] ?? null,
                    'estimated_distance_to_secondary_school_id' => $this->schoolData['distance_to_nearest_secondary_id'] ?? null,
                    'has_olevel_yn' => $this->schoolData['has_olevel_yn'],
                    'has_alevel_yn' => $this->schoolData['has_alevel_yn'],
                    'is_use_yn' => $isUse,
                    'is_upolet_yn' => $isUpolet,
                ]);
            }
        }

        if ($this->schoolType == 'international') {
            $statusMap = [
                'REGISTERED'    => 1,
                'LICENSED'      => 2,
                'NOT LICENSED'  => 0,
            ];
            $registrationStatus = $this->schoolData['registration_status'];

            $dayOrBoarding = $this->schoolData['day_or_boarding'] ?? '';



            $institution->update([
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $statusMap[$registrationStatus] ?? null,
                'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'male']),
                'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['mixed', 'female']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'year_founded'                      => $this->schoolData['year_founded'] ?? null,
                'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
                'funding_source_id'                 => $this->schoolData['funding_source_id'] ?? null,
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            ]);

            if ($institution->international_school) {
                $institution->international_school->update([
                    'admits_day_scholars_yn' => ($dayOrBoarding === 'Day Only' || $dayOrBoarding === 'Day and Boarding'),
                    'admits_boarders_yn' => ($dayOrBoarding === 'Boarding Only' || $dayOrBoarding === 'Day and Boarding'),
                    'estimated_distance_to_deo_office_id'    => $this->schoolData['estimated_distance_to_deo_office_id'] ?? null,
                    'estimated_distance_to_international_school_id' => $this->schoolData['estimated_distance_to_international_school_id'] ?? null,
                ]);
            }
        }

        if ($this->schoolType == 'certificate') {
            $statusMap = [
                'REGISTERED'    => 1,
                'LICENSED'      => 2,
                'NOT LICENSED'  => 0,
            ];
            $registrationStatus = $this->schoolData['registration_status'];

            $institution->update([
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $statusMap[$registrationStatus] ?? null,
                'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['Mixed', 'Males Only']),
                'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['Mixed', 'Females Only']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'year_founded'                      => $this->schoolData['year_founded'] ?? null,
                'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
                'funding_source_id'                 => $this->schoolData['funding_source_id'] ?? null,
                'center_number'                     => $this->schoolData['center_number'] ?? null,
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            ]);

            if ($institution->certificate_school) {
                $institution->certificate_school->registering_body_id = $this->schoolData['registering_body_id'] ?? null;

                if (!empty($this->schoolData['certificate_awarding_school_type_id'])) {
                    $institution->certificate_school->certificate_awarding_school_type_id = $this->schoolData['certificate_awarding_school_type_id'];
                }

                $institution->certificate_school->supply_number = $this->schoolData['supply_number'] ?? null;
                $institution->certificate_school->admits_day_scholars_yn = in_array($this->schoolData['day_or_boarding'] ?? '', ['RESIDENTIAL & NON-RESIDENTIAL', 'NON-RESIDENTIAL ONLY']);
                $institution->certificate_school->admits_boarders_yn = in_array($this->schoolData['day_or_boarding'] ?? '', ['RESIDENTIAL & NON-RESIDENTIAL', 'RESIDENTIAL ONLY']);
                $institution->certificate_school->save();
            }
        }
        if ($this->schoolType == 'diploma') {
            $statusMap = [
                'REGISTERED'    => 1,
                'LICENSED'      => 2,
                'NOT LICENSED'  => 0,
            ];
            $registrationStatus = $this->schoolData['registration_status'];

            $institution->update([
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $statusMap[$registrationStatus] ?? null,
                'has_male_students'                 => in_array($this->schoolData['sex_composition'] ?? '', ['Mixed', 'Males Only']),
                'has_female_students'               => in_array($this->schoolData['sex_composition'] ?? '', ['Mixed', 'Females Only']),
                'health_facility_distance_range_id' => $this->schoolData['health_facility_distance_range_id'] ?? null,
                'year_founded'                      => $this->schoolData['year_founded'] ?? null,
                'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
                'funding_source_id'                 => $this->schoolData['funding_source_id'] ?? null,
                'center_number'                     => $this->schoolData['center_number'] ?? null,
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            ]);

            if ($institution->diploma_school) {
                $institution->diploma_school->registering_body_id = $this->schoolData['registering_body_id'] ?? null;

                if (!empty($this->schoolData['certificate_awarding_school_type_id'])) {
                    $institution->diploma_school->certificate_awarding_school_type_id = $this->schoolData['certificate_awarding_school_type_id'];
                }

                $institution->diploma_school->supply_number = $this->schoolData['supply_number'] ?? null;
                $institution->diploma_school->admits_day_scholars_yn = in_array($this->schoolData['day_or_boarding'] ?? '', ['RESIDENTIAL & NON-RESIDENTIAL', 'NON-RESIDENTIAL ONLY']);
                $institution->diploma_school->admits_boarders_yn = in_array($this->schoolData['day_or_boarding'] ?? '', ['RESIDENTIAL & NON-RESIDENTIAL', 'RESIDENTIAL ONLY']);
                $institution->diploma_school->save();
            }
        }

        if ($this->schoolType == 'degree') {
            $institution->update([
                'legal_ownership_status_id'         => $this->schoolData['legal_ownership_status_id'] ?? null,
                'registration_number'               => $this->schoolData['registration_number'] ?? null,
                'license_number'                    => $this->schoolData['license_number'] ?? null,
                'licence_certificate_expiry_date'   => $this->schoolData['licence_certificate_expiry_date'] ?? null,
                'registration_status_id'            => $this->schoolData['registration_status_id'] ?? null,
                'health_facility_distance_range_id' => (isset($this->schoolData['has_health_facility_yn']) && $this->schoolData['has_health_facility_yn'] === 'no')
                                                        ? $this->schoolData['health_facility_distance_range_id'] ?? null
                                                        : null,
                'year_founded'                      => $this->schoolData['year_founded'] ?? null,
                'founding_body_id'                  => $this->schoolData['founding_body_id'] ?? null,
                'funding_source_id'                 => $this->schoolData['funding_source_id'] ?? null,
                'center_number'                     => $this->schoolData['center_number'] ?? null,
                'has_health_facility_yn'            => (isset($this->schoolData['has_health_facility_yn']) && $this->schoolData['has_health_facility_yn'] === 'yes'),
                'capital_for_establishment'         => $this->schoolData['capital_for_establishment'] ?? null,
            ]);

            if ($institution->degree_school) {
                $institution->degree_school->registering_body_id = $this->schoolData['registering_body_id'] ?? null;
                $institution->degree_school->supply_number = $this->schoolData['supply_number'] ?? null;
                $institution->degree_school->save();
            }
        }

       

        $this->dispatch('notifySectionB', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Institution identification updated successfully.'
        ]);
        $this->dispatch('close-modal');
        
        $this->loadSchoolData();

    }
    /**
     * Save or update a campus/branch of the institution.
     *
     * @return void
     */
    public function saveCampus()
    {

       // dd($this->campusForm);
        $this->validate([
            'campusForm.name' => 'required|string|max:255',
            'campusForm.phone' => 'required|string|max:20',
            'campusForm.district_id' => 'required|integer',
            'campusForm.county_id' => 'required|integer',
            'campusForm.sub_county_id' => 'required|integer',
            'campusForm.parish_id' => 'required|integer',
        ], [
            'campusForm.name.required' => 'Campus name is required.',
            'campusForm.phone.required' => 'Campus phone is required.',
            'campusForm.district_id.required' => 'Select a district.',
            'campusForm.county_id.required' => 'Select a county.',
            'campusForm.sub_county_id.required' => 'Select a sub county.',
            'campusForm.parish_id.required' => 'Select a parish.',
        ]);
    
        $institution = $this->institution;
    
        if (!$institution) {
            $this->dispatch('notifyCampus', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Institution not found.'
            ]);
            return;
        }
    
        try {
            AdminLogActivity::addToLog('Campuses/Branches', '1', 'Update', 'User updated or added a campus/branch', 'school');
    
            // Update or create the campus
            $campus = \Modules\Core\Models\Institutions\SchoolCampus::updateOrCreate([
                'school_id' => $institution->id,
                'name' => $this->campusForm['name'],
            ], [
                'phone' => $this->campusForm['phone'],
                'district_id' => $this->campusForm['district_id'],
                'county_id' => $this->campusForm['county_id'],
                'sub_county_id' => $this->campusForm['sub_county_id'],
                'parish_id' => $this->campusForm['parish_id'],
            ]);
    
            // Optionally, sync survey progress if needed (similar to saveSectionB)
            $survey = Survey::where('id', $this->survey_id)
                ->with(['academic_year', 'sections', 'section_items'])
                ->first();
    
            if ($survey) {
                $schoolSurvey = SchoolSurvey::firstOrCreate([
                    'school_id' => $institution->id,
                    'survey_id' => $this->survey_id,
                ], [
                    'last_saved' => now(),
                ]);
                $schoolSurvey->sections()->sync($survey->sections);
                $schoolSurvey->section_items()->sync($survey->section_items);
                if ($this->section_id) {
                    $schoolSurvey->sections()->updateExistingPivot($this->section_id, [
                        'is_complete_yn' => true,
                    ]);
                }
            }
    
            $this->dispatch('notifyCampus', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Campus/Branch saved successfully.'
            ]);
            $this->dispatch('close-modal');
            $this->loadSchoolData();
    
            // Reset the form for next use
            $this->campusForm = [
                'name' => '',
                'phone' => '',
                'district_id' => '',
                'county_id' => '',
                'sub_county_id' => '',
                'parish_id' => '',
            ];
    
        } catch (\Exception $e) {
            $this->dispatch('notifyCampus', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Failed to save campus: ' . $e->getMessage()
            ]);
        }
    }
    
}
