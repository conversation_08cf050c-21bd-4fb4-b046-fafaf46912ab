<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;

class WaterSanitationSection extends Component
{
    public $survey;

    public function mount($survey)
    {
        $this->survey = $survey;
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.water-sanitation-section', [
            'survey' => $this->survey,
        ]);
    }
}

