<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Livewire\WithPagination;
use Modules\Core\Models\Finance\SchoolIncomeUpdate;
use Modules\Core\Models\Finance\SchoolBudgetUpdate;
use Modules\Core\Models\Finance\SchoolExpensesUpdate;
use Modules\Core\Models\Settings\SettingFundingPurpose;
use Modules\Core\Models\Settings\SettingIncomeSource;
use Modules\Core\Models\Settings\SettingBudgetItem;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\Survey;
use Modules\Core\Models\Settings\SettingGenericExpenseItem;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Livewire\Attributes\On;

class FinanceSection extends Component
{
    use WithPagination, InstitutionContext;

    public $active_tab = 'income';
    // Income properties
    public $total_incomes = 0;
    public $income_sources = [];
    public $income_purposes = [];
    public $select_all_incomes = false;
    public $selected_incomes = [];
    public $filtering_income = false;
    public $edit_income = false;

    // Budget properties
    public $total_budgets = 0;
    public $budget_items = [];
    public $select_all_budgets = false;
    public $selected_budgets = [];
    public $filtering_budget = false;
    public $edit_budget = false;

    // Expense properties
    public $total_expenses = 0;
    public $generic_expense_items = [];
    public $select_all_expenses = false;
    public $selected_expenses = [];
    public $filtering_expense = false;
    public $edit_expense = false;

    public $institution;
    public $survey_id;
    public $income_source_id;

    // Income form data
    public $income = [
        'id' => '',
        'amount' => '',
        'income_source_id' => '',
        'income_purpose_id' => '',
    ];

    // Budget form data
    public $budget = [
        'id' => '',
        'amount' => '',
        'budget_item_id' => '',
    ];

    // Expense form data
    public $expense = [
        'id' => '',
        'amount' => '',
        'expense_item_id' => '',
        'expense_type' => 'generic-expenses',
    ];

    // Filter data
    public $income_filter = [
        'income_source_id' => '',
    ];

    public $budget_filter = [
        'budget_item_id' => '',
    ];

    public $expense_filter = [
        'expense_item_id' => '',
    ];

    protected $perPage = 12;

    public function mount($survey_id = null)
    {
        $this->survey_id = $survey_id;
        $this->institution = $this->getInstitution();
        $this->loadIncomeSources();
        $this->loadBudgetItems();
        $this->loadExpenseItems();
        $this->calculateTotalIncomes();
        $this->calculateTotalBudgets();
        $this->calculateTotalExpenses();
        $this->load_income_purposes();
    }

    public function render()
    {
        $incomes = $this->getIncomes();
        $budgets = $this->getBudgets();
        $expenses = $this->getExpenses();
        $purposes = $this->load_income_purposes();

        return view('emisreturns::livewire.sections.finance-section', [
            'incomes' => $incomes,
            'budgets' => $budgets,
            'expenses' => $expenses,
            'purposes' => $purposes
        ]);
    }

    public function setActiveTab($tab)
    {
        $this->active_tab = $tab;
    }
    public function loadIncomeSources()
    {
        $this->income_sources = SettingIncomeSource::with('income_purposes')->orderBy('name')->get();
    }

    public function load_income_purposes()
    {
        return $income_purposes = SettingFundingPurpose::all();
    }

    public function loadBudgetItems()
    {
        $institution = $this->getInstitution();

        if ($institution && $institution->school_ownership_status_id == 1) {
            $this->budget_items = SettingBudgetItem::where('for_public', 0)->orderBy('name')->get();
        } else {
            $this->budget_items = SettingBudgetItem::orderBy('name')->get();
        }
    }

    public function loadExpenseItems()
    {
        $institution = $this->getInstitution();

        if ($institution && $institution->school_ownership_status_id == 1) {
            $this->generic_expense_items = SettingGenericExpenseItem::where('for_public', 0)->orderBy('name')->get();
        } else {
            $this->generic_expense_items = SettingGenericExpenseItem::orderBy('name')->get();
        }
    }

    // Get incomes data for the current page
    private function getIncomes()
    {
        $institution = $this->getInstitution();

        if (!$institution) {
            return collect([])->paginate($this->perPage);
        }

        $query = SchoolIncomeUpdate::where('school_id', $institution->id)
            ->with('income_source', 'income_purpose');

        if ($this->filtering_income && !empty($this->income_filter['income_source_id'])) {
            $query->where('income_source_id', $this->income_filter['income_source_id']);
        }

        return $query->latest()->paginate($this->perPage, ['*'], 'incomes');
    }

    // Get budgets data for the current page
    private function getBudgets()
    {
        $institution = $this->getInstitution();

        if (!$institution) {
            return collect([])->paginate($this->perPage);
        }

        $query = SchoolBudgetUpdate::where('school_id', $institution->id)
            ->with('budget_item');

        if ($this->filtering_budget && !empty($this->budget_filter['budget_item_id'])) {
            $query->where('budget_item_id', $this->budget_filter['budget_item_id']);
        }

        return $query->latest()->paginate($this->perPage, ['*'], 'budgets');
    }

    // Get expenses data for the current page
    private function getExpenses()
    {
        $institution = $this->getInstitution();

        if (!$institution) {
            return collect([])->paginate($this->perPage);
        }

        $query = SchoolExpensesUpdate::where('school_id', $institution->id)
            ->with('expenseable');

        if ($this->filtering_expense && !empty($this->expense_filter['expense_item_id'])) {
            $query->where('expenseable_id', $this->expense_filter['expense_item_id']);
        }

        return $query->latest()->paginate($this->perPage, ['*'], 'expenses');
    }

    // Calculate total incomes based on current filters
    private function calculateTotalIncomes()
    {
        $institution = $this->getInstitution();

        if (!$institution) {
            $this->total_incomes = 0;
            return;
        }

        $query = SchoolIncomeUpdate::where('school_id', $institution->id);

        if ($this->filtering_income && !empty($this->income_filter['income_source_id'])) {
            $query->where('income_source_id', $this->income_filter['income_source_id']);
        }

        $this->total_incomes = $query->sum('amount');
    }

    // Calculate total budgets based on current filters
    private function calculateTotalBudgets()
    {
        $institution = $this->getInstitution();

        if (!$institution) {
            $this->total_budgets = 0;
            return;
        }

        $query = SchoolBudgetUpdate::where('school_id', $institution->id);

        if ($this->filtering_budget && !empty($this->budget_filter['budget_item_id'])) {
            $query->where('budget_item_id', $this->budget_filter['budget_item_id']);
        }

        $this->total_budgets = $query->sum('amount');
    }

    // Calculate total expenses based on current filters
    private function calculateTotalExpenses()
    {
        $institution = $this->getInstitution();
        if (!$institution) {
            $this->total_expenses = 0;
            return;
        }

        $query = SchoolExpensesUpdate::where('school_id', $institution->id);

        if ($this->filtering_expense && !empty($this->expense_filter['expense_item_id'])) {
            $query->where('expenseable_id', $this->expense_filter['expense_item_id']);
        }

        $this->total_expenses = $query->sum('amount');
    }

    // Income methods
    public function updatedIncomeIncomeSourceId()
    {
        $this->income['income_purpose_id'] = '';
        $this->loadIncomePurposes();
    }

    public function loadIncomePurposes()
    {
        if (!empty($this->income['income_source_id'])) {
            $source = collect($this->income_sources)->firstWhere('id', $this->income['income_source_id']);
            $this->income_purposes = $source ? $source->income_purposes : [];
        } else {
            $this->income_purposes = [];
        }
    }

    public function toggleAllIncomes()
    {
        $incomes = $this->getIncomes();

        if ($this->select_all_incomes) {
            $this->selected_incomes = $incomes->pluck('id')->toArray();
        } else {
            $this->selected_incomes = [];
        }
    }

    public function toggleOneIncome()
    {
        $incomes = $this->getIncomes();
        $this->select_all_incomes = count($this->selected_incomes) === $incomes->count();
    }

    public function createIncome()
    {
        $this->validate([
            'income.amount' => 'required',
            'income_source_id' => 'required|exists:setting_income_sources,id',
            'income.income_purpose_id' => 'required|exists:setting_funding_purposes,id',
        ], [
            'income_source_id.required' => 'Select an Income Source to proceed',
            'income.income_purpose_id.required' => 'Select a Purpose to proceed',
            'income.amount.required' => 'Amount is required',
        ]);

        $institution = $this->getInstitution();

        if (!$institution) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Institution not found'
            ]);
            return;
        }
        $survey = Survey::where('id', $this->survey_id)->with(['academic_year.teaching_periods', 'sections', 'section_items'])->firstOrFail();
        $academic_year_id = $survey->academic_year_id;
        $teaching_period_id = get_active_teaching_period()->teaching_period_id;

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $institution->id,
            'survey_id' => $this->survey_id,
        ], [
            'last_saved' => now(),
        ]);

        $schoolSurvey->sections()->sync($survey->sections);
        $schoolSurvey->section_items()->sync($survey->section_items);

        try {
            $amount = preg_replace('/[^0-9]/', '', $this->income['amount']);
            SchoolIncomeUpdate::create([
                'school_id' => $institution->id,
                'academic_year_id' => $academic_year_id,
                'school_survey_id' => $schoolSurvey->id,
                'teaching_period_id' => $teaching_period_id,
                'amount' => (int) $amount,
                'income_source_id' => $this->income_source_id,
                'income_purpose_id' => $this->income['income_purpose_id'],
            ]);

            $this->calculateTotalIncomes();
            $this->resetIncome();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success: ',
                'message' => 'Income Entry Created Successfully'
            ]);

            $this->dispatch('close-modal', 'incomeModal');

        } catch (\Exception $e) {
            $this->dispatch('close-modal', 'incomeModal');

            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Failed to create income entry'
            ]);
        }
    }

    public function editIncome($incomeId)
    {
        $income = SchoolIncomeUpdate::with(['income_source', 'income_purpose'])->findOrFail($incomeId);
        $this->edit_income = true;
        $this->income_source_id = $income->income_source_id;
        $this->loadIncomePurposes();
        $this->income = [
            'id' => $income->id,
            'amount' => number_format($income->amount),
            'income_source_id' => $income->income_source_id,
            'income_purpose_id' => $income->income_purpose_id,
        ];
    }

    public function updateIncome()
    {
        $this->validate([
            'income.amount' => 'required',
            'income.income_source_id' => 'required|exists:setting_income_sources,id',
            'income.income_purpose_id' => 'required|exists:setting_funding_purposes,id',
        ]);

        try {
            $institution = $this->getInstitution();
            if (!$institution) {
                $this->dispatch('notify', [
                    'status' => 'error',
                    'title' => 'Error: ',
                    'message' => 'Institution not found'
                ]);
                return;
            }
            $survey = Survey::where('id', $this->survey_id)->with(['academic_year.teaching_periods', 'sections', 'section_items'])->firstOrFail();
            $academic_year_id = $survey->academic_year_id;
            $teaching_period_id = get_active_teaching_period()->teaching_period_id;

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $institution->id,
                'survey_id' => $this->survey_id,
            ], [
                'last_saved' => now(),
            ]);

            $schoolSurvey->sections()->sync($survey->sections);
            $schoolSurvey->section_items()->sync($survey->section_items);
            $amount = preg_replace('/[^0-9]/', '', $this->income['amount']);

            $income = SchoolIncomeUpdate::findOrFail($this->income['id']);
            $income->school_survey_id = $schoolSurvey->id;
            $income->income_purpose_id = $this->income['income_purpose_id'];
            $income->amount = (int) $amount;
            $income->save();

            $this->calculateTotalIncomes();
            $this->resetIncome();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success: ',
                'message' => 'Income Entry Updated Successfully'
            ]);

            $this->dispatch('close-modal', 'incomeModal');

        } catch (\Exception $e) {
            $this->dispatch('close-modal', 'incomeModal');

            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Income Entry Not Found'
            ]);
        }
    }

    public function resetIncome()
    {
        $this->edit_income = false;
        $this->income = [
            'id' => '',
            'amount' => '',
            'income_source_id' => '',
            'income_purpose_id' => '',
        ];
        $this->income_purposes = [];
        $this->resetValidation();
    }

    public function filterIncomes()
    {
        if (empty($this->income_filter['income_source_id'])) {
            return;
        }

        $this->filtering_income = true;
        $this->resetPage('incomes');
        $this->calculateTotalIncomes();
    }

    public function resetIncomeFilter()
    {
        $this->income_filter = ['income_source_id' => ''];
        $this->filtering_income = false;
        $this->resetPage('incomes');
        $this->calculateTotalIncomes();
    }

    // Budget methods
    public function toggleAllBudgets()
    {
        $budgets = $this->getBudgets();

        if ($this->select_all_budgets) {
            $this->selected_budgets = $budgets->pluck('id')->toArray();
        } else {
            $this->selected_budgets = [];
        }
    }

    public function toggleOneBudget()
    {
        $budgets = $this->getBudgets();
        $this->select_all_budgets = count($this->selected_budgets) === $budgets->count();
    }

    public function createBudget()
    {
        $this->validate([
            'budget.amount' => 'required',
            'budget.budget_item_id' => 'required|exists:setting_budget_items,id',
        ], [
            'budget.budget_item_id.required' => 'Select a Budget Item to proceed',
            'budget.amount.required' => 'Amount is required'
        ]);

        $institution = $this->getInstitution();

        if (!$institution) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Institution not found'
            ]);
            return;
        }

        $survey = Survey::where('id', $this->survey_id)->with(['academic_year.teaching_periods', 'sections', 'section_items'])->firstOrFail();
        $academic_year_id = $survey->academic_year_id;
        $teaching_period_id = get_active_teaching_period()->teaching_period_id;

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $institution->id,
            'survey_id' => $this->survey_id,
        ], [
            'last_saved' => now(),
        ]);

        $schoolSurvey->sections()->sync($survey->sections);
        $schoolSurvey->section_items()->sync($survey->section_items);

        try {
            $amount = preg_replace('/[^0-9]/', '', $this->budget['amount']);

            SchoolBudgetUpdate::create([
                'school_id' => $institution->id,
                'academic_year_id' => $academic_year_id,
                'school_survey_id' => $schoolSurvey->id,
                'teaching_period_id' => $teaching_period_id,
                'amount' => $amount,
                'budget_item_id' => $this->budget['budget_item_id'],
            ]);

            $this->calculateTotalBudgets();
            $this->resetBudget();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success: ',
                'message' => 'Budget Entry Created Successfully'
            ]);

            $this->dispatch('close-modal', 'budgetModal');

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Failed to create budget entry'
            ]);
            $this->dispatch('close-modal', 'budgetModal');
        }
    }

    #[On('edit-budget')]
    public function editBudget($id)
    {
        $this->edit_budget = true;
        $budget = SchoolBudgetUpdate::findOrFail($id);
        if ($budget) {
            $this->budget['id'] = $budget->id;
            $this->budget['amount'] = number_format($budget->amount);
            $this->budget['budget_item_id'] = $budget->budget_item_id;
        }
        $this->resetErrorBag();
    }

    public function updateBudget()
    {
        $this->validate([
            'budget.amount' => 'required',
            'budget.budget_item_id' => 'required|exists:setting_budget_items,id',
        ]);

        try {
            $budget = SchoolBudgetUpdate::findOrFail($this->budget['id']);
            $amount = preg_replace('/[^0-9]/', '', $this->budget['amount']);

            $budget->update([
                'amount' => $amount,
                'budget_item_id' => $this->budget['budget_item_id'],
            ]);

            $this->calculateTotalBudgets();
            $this->resetBudget();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success ',
                'message' => 'Budget Entry Updated Successfully'
            ]);

            $this->dispatch('close-modal', 'budgetModal');

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error ',
                'message' => 'Failed to update budget entry: ' . $e->getMessage()
            ]);
        }
    }

    public function resetBudget()
    {
        $this->edit_budget = false;
        $this->budget = [
            'id' => '',
            'amount' => '',
            'budget_item_id' => '',
        ];
        $this->resetValidation();
    }

    public function filterBudgets()
    {
        if (empty($this->budget_filter['budget_item_id'])) {
            return;
        }

        $this->filtering_budget = true;
        $this->resetPage('budgets');
        $this->calculateTotalBudgets();
    }

    public function resetBudgetFilter()
    {
        $this->budget_filter = ['budget_item_id' => ''];
        $this->filtering_budget = false;
        $this->resetPage('budgets');
        $this->calculateTotalBudgets();
    }

    // Expense methods
    public function toggleAllExpenses()
    {
        $expenses = $this->getExpenses();

        if ($this->select_all_expenses) {
            $this->selected_expenses = $expenses->pluck('id')->toArray();
        } else {
            $this->selected_expenses = [];
        }
    }

    public function toggleOneExpense()
    {
        $expenses = $this->getExpenses();
        $this->select_all_expenses = count($this->selected_expenses) === $expenses->count();
    }

    public function createExpense()
    {
        $this->validate([
            'expense.amount' => 'required',
            'expense.expense_item_id' => 'required|exists:setting_generic_expense_items,id',
        ], [
            'expense.expense_item_id.required' => 'Select an Expense Item to proceed',
            'expense.amount.required' => 'Amount is required'
        ]);

        $institution = $this->getInstitution();

        if (!$institution) {
            $this->dispatch('notify', [
                'type' => 'error',
                'title' => 'Error',
                'message' => 'Institution not found'
            ]);
            return;
        }

        $survey = Survey::where('id', $this->survey_id)->with(['academic_year.teaching_periods', 'sections', 'section_items'])->firstOrFail();
        $academic_year_id = $survey->academic_year_id;
        $teaching_period_id = get_active_teaching_period()->teaching_period_id;

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $institution->id,
            'survey_id' => $this->survey_id,
        ], [
            'last_saved' => now(),
        ]);

        $schoolSurvey->sections()->sync($survey->sections);
        $schoolSurvey->section_items()->sync($survey->section_items);

        try {
            $amount = preg_replace('/[^0-9]/', '', $this->expense['amount']);

            $genericExpenseItem = SettingGenericExpenseItem::findOrFail($this->expense['expense_item_id']);
            $expense = new SchoolExpensesUpdate();
            $expense->school_id = $institution->id;
            $expense->academic_year_id = $academic_year_id;
            $expense->school_survey_id = $schoolSurvey->id;
            $expense->teaching_period_id = $teaching_period_id;
            $expense->amount = $amount;

            $genericExpenseItem->expense()->save($expense);

            $this->calculateTotalExpenses();
            $this->resetExpense();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success: ',
                'message' => 'Expense Entry Created Successfully'
            ]);

            $this->dispatch('close-modal', 'expenseModal');

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error: ',
                'message' => 'Failed to create expense entry: ' . $e->getMessage()
            ]);
            $this->dispatch('close-modal', 'expenseModal');
        }
    }

    #[On('edit-expense')]
    public function editExpense($id)
    {
        $this->edit_expense = true;
        $expense = SchoolExpensesUpdate::findOrFail($id);
        $this->expense['id'] = $expense->id;
        $this->expense['amount'] = number_format($expense->amount);
        $this->expense['expense_item_id'] = $expense->expenseable_id;
        $this->resetErrorBag();
    }

    public function updateExpense()
    {
        $this->validate([
            'expense.amount' => 'required',
            'expense.expense_item_id' => 'required|exists:setting_generic_expense_items,id',
        ]);

        try {
            $expense = SchoolExpensesUpdate::findOrFail($this->expense['id']);
            $amount = preg_replace('/[^0-9]/', '', $this->expense['amount']);

            $expense->update([
                'amount' => $amount,
            ]);

            $this->calculateTotalExpenses();
            $this->resetExpense();

            $this->dispatch('notify', [
                'status' => 'success',
                'title' => 'Success ',
                'message' => 'Expense Entry Updated Successfully'
            ]);

            $this->dispatch('close-modal', 'expenseModal');

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'status' => 'error',
                'title' => 'Error ',
                'message' => 'Failed to update expense entry: ' . $e->getMessage()
            ]);
        }
    }

    public function resetExpense()
    {
        $this->edit_expense = false;
        $this->expense = [
            'id' => '',
            'amount' => '',
            'expense_item_id' => '',
            'expense_type' => 'generic-expenses',
        ];
        $this->resetValidation();
    }

    public function filterExpenses()
    {
        if (empty($this->expense_filter['expense_item_id'])) {
            return;
        }

        $this->filtering_expense = true;
        $this->resetPage('expenses');
        $this->calculateTotalExpenses();
    }

    public function resetExpenseFilter()
    {
        $this->expense_filter = ['expense_item_id' => ''];
        $this->filtering_expense = false;
        $this->resetPage('expenses');
        $this->calculateTotalExpenses();
    }

    public function formatMoney($amount)
    {
        return number_format($amount);
    }
}
