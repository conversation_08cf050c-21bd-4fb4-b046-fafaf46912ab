<?php

namespace Modules\EmisReturns\Livewire\Sections;

use AllowDynamicProperties;
use Livewire\Component;
use Livewire\WithFileUploads;
use Modules\Core\Traits\EncryptLinTrait;
use Modules\Core\Traits\InteractsWithPerson;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\EmisReturns\Livewire\Traits\Learners\LearnerVerificationTrait;
use Modules\EmisReturns\Livewire\Traits\Learners\LoadFormDataTrait;
use Modules\EmisReturns\Livewire\Traits\Learners\StoreLearnerDataTrait;

/**
 *
 */
#[AllowDynamicProperties] class LearnersSection extends Component
{
    use InstitutionContext, LoadFormDataTrait, LearnerVerificationTrait, StoreLearnerDataTrait, InteractsWithPerson, EncryptLinTrait, WithFileUploads;

    /** @var string The current school type (e.g., 'primary', 'secondary') */
    public $schoolType;
    public $school;
    public $survey;
    public $learnerData = [];

    protected $listeners = [
        'learner-saved' => 'handleLearnerSaved',
    ];
    public bool $showModal = false;
    public string $active_section = 'form-create';
    public bool $loading = false;
    public string $learner_nin = 'yes';
    public string $learner_refugee_no = 'no';
    public bool $verify = false;
    public bool $uneb_verify = false;
    public bool $learner_verify = false;
    public bool $parent_verify = false;
    public string $has_index_number = 'yes';
    public bool $uganda = true;
    public $education_grades = [];
    public $ninVerify = false;
    public $ninParentVerify = false;
    public $recent_learners = [];
    public $health_services_learners = [];
    public $health_services_parents = [];
    public $countries = [];
    public $districts = [];
    public $special_needs = [];
    public $familiar_languages = [];
    public $health_issues = [];
    public $meal_types = [];
    public $food_sources = [];
    public $talents = [];
    public $practical_skills = [];
    public $educationGrades = [];
    public $academicYears = [];
    public $photo;
    public $selectedGradeId;
    public $selectedExamYear;
    public $aLevel = false;
    public $learner_gender;
//    public $learner_index_number;
    public $learner_exam_year;
    public $secondarySubjects = [];
    public $primarySubjects = [];

    // International school properties
    public $international_calendars = [];
    public $international_curriculums = [];
    public $international_grades = [];
    public $international_education_levels = [];
    public $form_learner = [
        'nin' => '',
        'first_name' => '',
        'surname' => '',
        'other_names' => '',
        'student_pass' => '',
        'student_refugee_number' => '',
        'birth_date' => '',
        'photo' => null,
        'photo_url' => '',
        'gender' => 'M',
        'country_id' => 221,
        'district_of_birth_id' => '',
        'parent_nin' => '',
        'parent_passport' => '',
        'parent_refugee_number' => '',
        'parent_first_name' => '',
        'parent_surname' => '',
        'parent_other_names' => '',
        'parent_birth_date' => '',
        'parent_gender' => 'M',
        'parent_relationship' => '',
        'parent_phone_1' => '',
        'parent_phone_2' => '',
        'parent_email' => '',
        'parent_photo' => '',
        'education_grade_id' => '',
        'index_number' => '',
        'exam_year' => '',
        'equated_code' => '',
        'equated_year' => '',
        'exam_level' => '',
        'code_type' => '',
        'post_primary_institution_course_id' => '',
        'institution_examined_course_id' => '',
        'is_offering_examinable_course' => 'no',
        'is_orphan' => 'no',
        'orphan_type' => '',
        'learner_special_needs' => [],
        'learner_health_issues' => [],
        'learner_talents' => [],
        'learner_practical_skills' => [],
        'familiar_language_id' => '',
        'principal_subjects' => [],
        'subsidiary_subject' => [],
        'in_national_curriculum' => '',
        'inter_sch_calendar_id' => '',
        'inter_sch_curriculum_id' => '',
        'inter_sch_education_grade_id' => '',
        'inter_sch_education_level_id' => '',
    ];
    public $uneb_learner = [
        'name' => '',
        'index_number' => '',
        'exam_year' => '',
        'date_of_birth' => '',
        'gender' => '',
        'first_name' => '',
        'surname' => '',
        'other_names' => ''
    ];
    public $nira_parent = [
        'national_id' => '',
        'surname' => '',
        'given_names' => '',
        'date_of_birth' => '',
        'gender' => '',
        'nationality' => '',
        'photo' => null,
        'alive' => true,
    ];
    public $nira_learner = [
        'national_id' => '',
        'surname' => '',
        'given_names' => '',
        'date_of_birth' => '',
        'gender' => '',
        'nationality' => '',
        'photo' => null,
        'alive' => true,
    ];
    /**
     * Mount the component and initialize form data.
     *
     * @param string $schoolType
     */
    public function mount(string $schoolType, $survey)
    {
        $school = $this->getInstitution();
        $this->schoolType = $schoolType;
        $this->survey = $survey;

        // Set the default learner_nin based on a school type
        if ($schoolType !== 'secondary') {
            $this->learner_nin = 'yes'; // Default to 'yes' for non-secondary schools
        }
        $this->getFormData($school);
    }

    /**
     * Get the principal and subsidiary subjects based on the school type.
     *
     */
    public function getPrincipalSubjectsProperty()
    {
        if ($this->schoolType == 'secondary') {
            return collect($this->secondarySubjects)->filter(function ($subject) {
                return $subject['is_principal_subject'];
            })->values();
        }
    }
    /**
     * Get the subsidiary subjects based on the school type.
     *
     */
    public function getSubsidiarySubjectsProperty()
    {
        if ($this->schoolType == 'secondary') {
            return collect($this->secondarySubjects)->filter(function ($subject) {
                return !$subject['is_principal_subject'] && str_starts_with($subject['name'], 'SUB');
            })->values();
        }
    }
    /**
     * Check if a principal subject is disabled based on the learner's current subjects.
     *
     * @param int $subjectId
     * @return bool
     */
    public function getPrincipalSubjectDisabled(int $subjectId): bool
    {
        return count($this->form_learner['principal_subjects']) >= 3 &&
            !in_array($subjectId, $this->form_learner['principal_subjects']);
    }
    /**
     * Check if a subsidiary subject is disabled based on the learner's current subjects.
     *
     * @param int $subjectId
     * @return bool
     */
    public function getSubSubjectDisabled(int $subjectId): bool
    {
        return count($this->form_learner['subsidiary_subject']) >= 1 &&
            !in_array($subjectId, $this->form_learner['subsidiary_subject']);
    }

    public function resetForm(): void
    {
        $this->reset(
            'form_learner',
            'uneb_learner',
            'nira_learner',
            'nira_parent',
            'parent_verify',
            'learner_verify',
            'uneb_verify',
            'learner_nin',
            'photo'
        );
        $this->getFormData($this->getInstitution());
    }

    /**
     * Handle the learner-saved event to refresh data
     */
    public function handleLearnerSaved(): void
    {
        // Refresh the data after a learner is saved
        $this->getFormData($this->getInstitution());
    }

    /**
     * Remove the uploaded photo and reset the property.
     */
    public function removePhoto()
    {
        $this->photo = null;
        $this->dispatch('photo-removed');
        $this->getFormData($this->getInstitution());
    }

    /**
     * Hook that's triggered when the photo property is updated.
     * This ensures form data is preserved when a photo is uploaded.
     */
    public function updatedPhoto()
    {
        $this->getFormData($this->getInstitution());
    }

    /**
     * Update default photo and country when gender changes
     */
    public function updateDefaultPhoto(): void
    {
        // Dispatch event to update a photo component with the current gender
        $this->dispatch('gender-changed', ['gender' => $this->form_learner['gender'] ?? 'M']);
        $this->getFormData($this->getInstitution());
    }

    /**
     * Handle changes to learner NIN radio button
     */
    public function updatedLearnerNin($value): void
    {
        // Reset verification states when the NIN option changes
        if ($value === 'no') {
            $this->learner_verify = false;
            $this->parent_verify = false;
        }

        // Call getFormData after restoring learner_nin
        $this->getFormData($this->getInstitution());
    }

    /**
     * Set Uganda status directly (called from Alpine.js)
     */
    public function setUgandaStatus(bool $isUganda): void
    {
        $this->uganda = $isUganda;

        if ($this->uganda) {
            // If Uganda is selected, reset refugee status to 'no'
            $this->learner_refugee_no = 'no';
        } else {
            // If non-Uganda country is selected, set learner_nin to 'no' since NIN is only for Ugandans
            $this->learner_nin = 'no';
            // Also reset any verification states
            $this->learner_verify = false;
            $this->parent_verify = false;
        }
        $this->getFormData($this->getInstitution());
    }

    /**
     * Set aLevel status directly (called from Alpine.js)
     */
    public function setAlevelStatus(bool $gradeId): void
    {
        $this->aLevel = $gradeId;
        if ($this->aLevel) {
            $this->aLevel = true;
        } else {
            $this->aLevel = false;
        }
        $this->getFormData($this->getInstitution());
    }

    /**
     * Get the appropriate grade label based on a school type
     */
    public function getGradeLabelProperty(): string
    {
        $higherEducationTypes = ['certificate', 'diploma', 'degree'];
        return in_array($this->schoolType, $higherEducationTypes) ? 'Year of study' : 'Class';
    }
    /**
     * Get the appropriate parent label based on a school type
     */
    public function getParentLabelProperty(): string
    {
        $higherEducationTypes = ['certificate', 'diploma', 'degree'];
        return in_array($this->schoolType, $higherEducationTypes) ? 'Next of Kin' : 'Parent';
    }

    /**
     * Handle curriculum change for international schools
     */
    public function changeCurriculum($curriculumId): void
    {
        // Clear current grade selection
        $this->form_learner['inter_sch_education_grade_id'] = '';

        // Load grades for selected curriculum
        if ($this->schoolType === 'international' && $curriculumId) {
            $curriculum = collect($this->international_curriculums)->firstWhere('id', (int)$curriculumId);

            if ($curriculum && isset($curriculum->grades)) {
                $this->international_grades = $curriculum->grades->toArray();
            } else {
                $this->international_grades = [];
            }
        } else {
            $this->international_grades = [];
        }
    }

    /**
     * Update education level when a grade is selected
     */
    public function updateEducationLevel($gradeId): void
    {
        if ($this->schoolType === 'international' && $gradeId) {
            $grade = collect($this->international_grades)->firstWhere('id', (int)$gradeId);

            if ($grade && isset($grade['inter_sch_education_level_id'])) {
                $this->form_learner['inter_sch_education_level_id'] = $grade['inter_sch_education_level_id'];
            } else {
                $this->form_learner['inter_sch_education_level_id'] = '';
            }
        } else {
            $this->form_learner['inter_sch_education_level_id'] = '';
        }
    }

    /**
     * Render the Livewire component view for the learners section.*
     */
    public function render()
    {
        return view('emisreturns::livewire.sections.learners-section', [
            'principalSubjects' => $this->principalSubjects,
            'subsidiarySubjects' => $this->subsidiarySubjects,
        ]);
    }
}
