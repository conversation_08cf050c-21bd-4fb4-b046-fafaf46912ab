<?php

namespace Modules\EmisReturns\Livewire\Sections;

use Livewire\Component;
use Modules\Core\Models\Settings\SettingInterSchCurriculum;
use Modules\Core\Models\Settings\SettingInterSchEducationLevel;
use Modules\Core\Models\Institutions\InternationalSchoolCurriculum;
use Modules\Core\Models\Institutions\InternationalSchoolCurriculumSection;

class CurriculumSection extends Component
{
    public $allCurriculums = [];
    public $allSections = [];
    public $assignedCurriculums = [];
    public $selectedCurriculums = [];
    public $selectedSections = [];
    public $currentCurriculumId = null;
    public $currentCurriculumName = '';
    public $showCurriculumModal = false;
    public $showSectionModal = false;

    public $allCalendars = [];
    public $assignedCalendars = [];
    public $selectedCalendars = [];
    public $showCalendarModal = false;

    public function mount()
    {
        $this->loadAllCurriculums();
        $this->loadAllSections();
        $this->loadAssignedCurriculums();
        $this->loadAllCalendars();
        $this->loadAssignedCalendars();
    }

    public function loadAllCurriculums()
    {
        $this->allCurriculums = SettingInterSchCurriculum::all();
    }

    public function loadAllSections()
    {
        $this->allSections = SettingInterSchEducationLevel::select('id', 'name', 'level_rank')->get();
    }

    public function loadAssignedCurriculums()
    {
        $schoolId = auth()->user()->school->id;
        // Get all assigned curriculums for this school
        $curriculums = \Modules\Core\Models\Institutions\InternationalSchoolCurriculum::where('school_id', $schoolId)
            ->with('international_curriculums')
            ->get();

        // For each curriculum, attach a 'sections' array like the API/Vue
        $result = [];
        foreach ($curriculums as $curriculum) {
            $curriculumName = $curriculum->international_curriculums->name ?? null;
            if (!$curriculumName) {
                continue; // Skip if name is null or empty
            }
            $curriculumData = [
                'id' => $curriculum->inter_sch_curriculum_id,
                'name' => $curriculumName,
            ];
            $sections = \Modules\Core\Models\Settings\SettingInterSchEducationLevel::whereIn('id',
                \Modules\Core\Models\Institutions\InternationalSchoolCurriculumSection::where('school_id', $schoolId)
                    ->where('inter_sch_curriculum_id', $curriculum->inter_sch_curriculum_id)
                    ->pluck('inter_sch_section_id')
            )->get(['id', 'name']);
            $curriculumData['sections'] = $sections;
            $result[] = (object) $curriculumData;
        }
        $this->assignedCurriculums = collect($result);
    }

    public function loadAllCalendars()
    {
        $this->allCalendars = \Modules\Core\Models\Settings\SettingInternationalSchoolCalender::select('id', 'name')->get();
    }

    public function loadAssignedCalendars()
    {
        $schoolId = auth()->user()->school->id;
        $this->assignedCalendars = \Modules\Core\Models\Institutions\InternationalSchoolCalendar::where('school_id', $schoolId)
            ->with('international_calendars')
            ->get()
            ->map(function($item) {
                return (object)[
                    'id' => $item->inter_sch_calendar_id,
                    'name' => $item->international_calendars->name ?? '',
                ];
            });
    }

    public function openCurriculumModal()
    {
        $this->selectedCurriculums = InternationalSchoolCurriculum::where('school_id', auth()->user()->school->id)
            ->pluck('inter_sch_curriculum_id')
            ->toArray();
        $this->showCurriculumModal = true;
    }

    public function saveCurriculums()
    {
        $this->validate([
            'selectedCurriculums' => 'required|array|min:1',
        ], [
            'selectedCurriculums.required' => 'Select at least one curriculum to proceed',
            'selectedCurriculums.min' => 'Select at least one curriculum to proceed',
        ]);

        InternationalSchoolCurriculum::where('school_id', auth()->user()->school->id)->delete();
        foreach ($this->selectedCurriculums as $curriculumId) {
            InternationalSchoolCurriculum::create([
                'school_id' => auth()->user()->school->id,
                'inter_sch_curriculum_id' => $curriculumId,
            ]);
        }
        $this->loadAssignedCurriculums();
        $this->showCurriculumModal = false;
        $this->dispatch('notifyCirriculum', [
            'status' => 'success',
            'title' => 'Success!',
            'message' => 'Curriculum updated successfully',
            'key' => 'curriculum'
        ]);
    }

    public function openSectionModal($curriculumId, $curriculumName)
    {
        $this->currentCurriculumId = $curriculumId;
        $this->currentCurriculumName = $curriculumName;
        $this->selectedSections = InternationalSchoolCurriculumSection::where('school_id', auth()->user()->school->id)
            ->where('inter_sch_curriculum_id', $curriculumId)
            ->pluck('inter_sch_section_id')
            ->toArray();
        $this->showSectionModal = true;
    }

    public function saveSections()
    {
        $this->validate([
            'selectedSections' => 'required|array|min:1',
        ], [
            'selectedSections.required' => 'Select at least one section to proceed',
            'selectedSections.min' => 'Select at least one section to proceed',
        ]);

        InternationalSchoolCurriculumSection::where('school_id', auth()->user()->school->id)
            ->where('inter_sch_curriculum_id', $this->currentCurriculumId)
            ->delete();

        foreach ($this->selectedSections as $sectionId) {
            InternationalSchoolCurriculumSection::create([
                'school_id' => auth()->user()->school->id,
                'inter_sch_curriculum_id' => $this->currentCurriculumId,
                'inter_sch_section_id' => $sectionId,
            ]);
        }
        $this->loadAssignedCurriculums();
        $this->showSectionModal = false;
        $this->dispatch('notify', [
            'status' => 'success',
            'title' => 'Success!',
            'message' => 'Sections updated successfully',
            'key' => 'curriculum'
        ]);
    }

    public function openCalendarModal()
    {
        $schoolId = auth()->user()->school->id;
        $this->selectedCalendars = \Modules\Core\Models\Institutions\InternationalSchoolCalendar::where('school_id', $schoolId)
            ->pluck('inter_sch_calendar_id')
            ->toArray();
        $this->showCalendarModal = true;
    }

    public function saveCalendars()
    {
        $this->validate([
            'selectedCalendars' => 'required|array|min:1',
        ], [
            'selectedCalendars.required' => 'Select at least one calendar to proceed',
            'selectedCalendars.min' => 'Select at least one calendar to proceed',
        ]);

        $schoolId = auth()->user()->school->id;
        \Modules\Core\Models\Institutions\InternationalSchoolCalendar::where('school_id', $schoolId)->delete();
        foreach ($this->selectedCalendars as $calendarId) {
            \Modules\Core\Models\Institutions\InternationalSchoolCalendar::create([
                'school_id' => $schoolId,
                'inter_sch_calendar_id' => $calendarId,
            ]);
        }
        $this->loadAssignedCalendars();
        $this->showCalendarModal = false;
        $this->dispatch('notifyCalendar', [
            'status' => 'success',
            'title' => 'Success!',
            'message' => 'Calendars updated successfully',
            'key' => 'calendar'
        ]);
    }

    public function closeCurriculumModal() { $this->showCurriculumModal = false; }
    public function closeSectionModal() { $this->showSectionModal = false; }
    public function closeCalendarModal() { $this->showCalendarModal = false; }

    public function render()
    {
        return view('emisreturns::livewire.sections.curriculum-section', [
            'assignedCurriculums' => $this->assignedCurriculums,
            'allCurriculums' => $this->allCurriculums,
            'allSections' => $this->allSections,
            'showCurriculumModal' => $this->showCurriculumModal,
            'showSectionModal' => $this->showSectionModal,
            'selectedCurriculums' => $this->selectedCurriculums,
            'selectedSections' => $this->selectedSections,
            'currentCurriculumName' => $this->currentCurriculumName,
            'assignedCalendars' => $this->assignedCalendars,
            'allCalendars' => $this->allCalendars,
            'selectedCalendars' => $this->selectedCalendars,
            'showCalendarModal' => $this->showCalendarModal,
        ]);
    }
}

