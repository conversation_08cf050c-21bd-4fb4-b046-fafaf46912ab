<?php

namespace Modules\EmisReturns\Livewire\Sections\Pesports;

use Livewire\Component;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSportsFacilityUpdate;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\SchoolSportsFacility;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

class SportsFacilitiesSection extends Component
{
    use InstitutionContext;
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $institution;
    public $survey;

    public $sports_facilities = []; // Holds sports facilities data
    public $all_sports_facilities = []; // Holds all sports facilities with their quantities
    public $editingFacilityId = null; // Tracks which row is being edited for sports facilities
    public $facilities = []; // ['facility_id' => quantity]


    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadSportsFacilities(); // Load sports facilities data
    }


    // Initialize the context with institution and survey details
    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    public function loadSportsFacilities()
    {
        $schoolId = $this->institution->id;

        $facilities = SchoolSportsFacility::pluck('name', 'id');

        $updates = SchoolSportsFacilityUpdate::where('school_id', $schoolId)
            ->pluck('quantity', 'sports_facility_id')
            ->toArray();

        $this->all_sports_facilities = $facilities->map(function ($name, $id) use ($updates) {
            return [
                'id' => $id,
                'name' => $name,
                'quantity' => $updates[$id] ?? 0,
            ];
        })->values()->toArray();
    }


    //edit sports facility
    public function editFacility($id)
    {
        $facility = collect($this->all_sports_facilities)
            ->firstWhere('id', $id);

        $this->facilities[$id] = $facility['quantity'] ?? 0;

        $this->editingFacilityId = $id;
    }

    //cancel edit  sports facility
    public function cancelEdit()
    {
        $this->editingFacilityId = null;
    }


    //save sports facility number(quantity)!
    public function saveFacility($id)
    {

        AdminLogActivity::addToLog('Sports Facility', '1', 'Update', 'User Updated School Sports Facility Updates', 'school');

        try {

            $survey = $this->survey;

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $survey->id,
            ], [
                'current_section_id' => $survey->sections->first()?->id,
                'last_saved' => now(),
            ]);


            $this->syncSurveySections($schoolSurvey);
            $quantity = $this->facilities[$id] ?? 0;

            SchoolSportsFacilityUpdate::updateOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $this->academicYearId,
                    'academic_year_id' => $survey->academic_year_id,
                    'teaching_period_id' => $this->termId,
                    'school_survey_id' => $schoolSurvey->id,
                    'sports_facility_id' => $id,
                ],
                [
                    'quantity' => $quantity,
                ]

            );

            // Update the local sports facilities array so UI updates without refresh
            foreach ($this->all_sports_facilities as &$facility) {
                if ($facility['id'] === $id) {
                    $facility['quantity'] = $quantity;
                    break;
                }
            }

            $this->editingFacilityId = null;

            $this->dispatch('notifySportsFacility', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Sports Facility   Updated Successfully'
            ]);
        } catch (\Exception $e) {

            $this->dispatch('notifySportsFacility', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error updating sports Facility: ' . $e->getMessage()
            ]);
        }
    }


    //Sync the survey sections and items to the school survey.
    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.pesports.sports-facilities-section');
    }
}
