<?php

namespace Modules\EmisReturns\Livewire\Sections\Pesports;

use Livewire\Component;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSportsActivityUpdate;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Models\Settings\SchoolSportsActivity;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

class SportsActivitiesSection extends Component
{

    use InstitutionContext;
    public $activityNames = []; //holds activity names
    public $selectedActivityName = null;
    public $presentInSchool = null;
    public $institution;
    public $survey;
    public $term;
    public $activityStates = []; //holds activity states (present in school or not(YES or NO))
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;

    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadSportsActivitiesData();
    }

    // Initialize the context with institution and survey details
    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    // Load sports activities and their states
    public function loadSportsActivitiesData(): void
    {
        $this->activityNames = SchoolSportsActivity::pluck('name', 'id')->toArray();
        $this->activityStates = SchoolSportsActivityUpdate::where('school_id', $this->institutionId)
            ->where('academic_year_id', $this->academicYearId)
            ->pluck('present_in_school', 'sports_activity_id')
            ->toArray();
    }

    // Select a sports activity prepare to set its state
    public function selectSportsActivity($name): void
    {
        $this->selectedActivityName = $name;
        $activityId = array_search($name, $this->activityNames);
        $this->presentInSchool = $activityId !== false && isset($this->activityStates[$activityId])
            ? (int) $this->activityStates[$activityId]
            : null;
    }

    // Update the selected sports activity state
    public function updateActivity(): void
    {


        AdminLogActivity::addToLog(' School Sports Activities', '1', 'Update', 'User Updated School Sports Activity Updates', 'school');

        try {

            $activity = SchoolSportsActivity::where('name', $this->selectedActivityName)->firstOrFail();

            if (is_null($activity)) {
                $this->dispatch('close-modal');
                $this->dispatch('notifySportsActivity', [
                    'status' => 'error',
                    'title' => 'Error:',
                    'message' => 'Selected Sports Activity not found.'
                ]);
                return;
            }
            $schoolSurvey = SchoolSurvey::firstOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'survey_id' => $this->survey->id,
                ],
                [
                    'current_section_id' => $this->survey->sections->first()->id ?? null,
                    'last_saved' => now(),
                ]
            );
            $this->syncSurveySections($schoolSurvey);

            if (!is_null($this->presentInSchool)) {
                SchoolSportsActivityUpdate::updateOrCreate(
                    [
                        'school_id' => $this->institution->id,
                        'academic_year_id' => $this->survey->academic_year_id,
                        'teaching_period_id' => $this->termId,
                        'school_survey_id' => $schoolSurvey->id,
                        'sports_activity_id' => $activity->id,
                    ],
                    [
                        'present_in_school' => $this->presentInSchool,
                    ]
                );
            }

            $this->loadSportsActivitiesData();

            $this->dispatch('close-modal');
            $this->dispatch('notifySportsActivity', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'School Sports Activity Updated Successfully.'
            ]);
        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifySportsActivity', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error updating School Sports Activity: ' . $e->getMessage()
            ]);
        }
    }


    // Sync the survey sections and items with the school survey
    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }

    // Render the view for the sports activities section
    public function render()
    {
        return view('emisreturns::livewire.sections.pesports.sports-activities-section');
    }
}
