<?php

namespace Modules\EmisReturns\Livewire\Sections\Pesports;

use Livewire\Component;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSportsEquipmentUpdate;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Settings\SportsEquipment;

class SportsEquipmentSection extends Component
{

    use InstitutionContext;
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;
    public $institution;
    public $survey;
    public $editingEquipmentId = null; // Tracks which row is being edited for pre-primary sports equipment
    public $equipments = []; // ['equipment_id' => quantity]

    public $pre_primary_sports_equipments = []; //holds sports equipments for pre-primary schools
    public $no_of_play_grounds;
    public $size_of_play_grounds;

    public $all_other_school_types_sports_equipments = []; //holds sports equipments for all other school types(primary, secondary, certificate, diploma and international schools)


    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadPrePrimarySportsEquipments(); //load pre-primary sports equipments and existing play ground info
        $this->loadAllOtherSchoolTypesSportsEquipments(); //load all other school types sports equipments

    }

    // Initialize the context with institution and survey details
    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }


    // Load pre-primary sports equipments and existing play ground info
    public function loadPrePrimarySportsEquipments()
    {
        $schoolId = $this->institution->id;

        $equipments = SportsEquipment::where('is_pre_primary_yn', true)->get();

        $updates = SchoolSportsEquipmentUpdate::where('school_id', $schoolId)
            ->pluck('quantity', 'equipment_id')
            ->toArray();

        $this->pre_primary_sports_equipments = $equipments->map(function ($equipment) use ($updates) {
            return [
                'id' => $equipment->id,
                'name' => $equipment->name,
                'quantity' => $updates[$equipment->id] ?? 0,
            ];
        })->toArray();

        // Load existing play ground info
        $record = SchoolSportsEquipmentUpdate::where('school_id', $this->institution->id)->first();
        $this->no_of_play_grounds = $record?->no_of_play_grounds;
        $this->size_of_play_grounds = $record?->size_of_play_grounds;
    }


    //edit pre-primary sports equipment
    public function editEquipment($id)
    {
        $equipment = collect($this->pre_primary_sports_equipments)
            ->firstWhere('id', $id);

        $this->equipments[$id] = $equipment['quantity'] ?? 0;

        $this->editingEquipmentId = $id;
    }

    //cancel edit  sports equipment
    public function cancelEdit()
    {
        $this->editingEquipmentId = null;
    }


    //save both pre-primary and all other school types(all types) sports equipments
    public function saveEquipment($id)
    {

        AdminLogActivity::addToLog('Sports Equipment', '1', 'Update', 'User Updated School Sports Equipment Updates', 'school');

        try {

            $survey = $this->survey;

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $survey->id,
            ], [
                'current_section_id' => $survey->sections->first()?->id,
                'last_saved' => now(),
            ]);


            $this->syncSurveySections($schoolSurvey);
            $quantity = $this->equipments[$id] ?? 0;

            SchoolSportsEquipmentUpdate::updateOrCreate(
                [
                    'school_id' => $this->institution->id,
                    'academic_year_id' => $this->academicYearId,
                    'academic_year_id' => $survey->academic_year_id,
                    'teaching_period_id' => $this->termId,
                    'school_survey_id' => $schoolSurvey->id,
                    'equipment_id' => $id,
                ],
                [
                    'quantity' => $quantity,
                ]


            );

            // Update the local pre_primary_sports_equipments array so UI updates without refresh
            foreach ($this->pre_primary_sports_equipments as &$equipment) {
                if ($equipment['id'] === $id) {
                    $equipment['quantity'] = $quantity;
                    break;
                }
            }

            // Update the local all_other_school_types_sports_equipments array too
            foreach ($this->all_other_school_types_sports_equipments as &$equipment) {
                if ($equipment['id'] === $id) {
                    $equipment['quantity'] = $quantity;
                    break;
                }
            }
            $this->editingEquipmentId = null;

            $this->dispatch('notifySportsEquipment', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Sports Equipment quantity  Updated Successfully'
            ]);
        } catch (\Exception $e) {

            $this->dispatch('notifySportsEquipment', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error updating sports equipment: ' . $e->getMessage()
            ]);
        }
    }


    //save pre primary play grounds
    public function savePlayGrounds()
    {
        AdminLogActivity::addToLog('Sports Equipment', '1', 'Update', 'User Updated School Sports Equipment Updates', 'school');

        try {
            $updated = SchoolSportsEquipmentUpdate::where('school_id', $this->institution->id)->update([
                'no_of_play_grounds' => $this->no_of_play_grounds,
                'size_of_play_grounds' => $this->size_of_play_grounds,
            ]);

            if ($updated === 0) {
                SchoolSportsEquipmentUpdate::create([
                    'school_id' => $this->institution->id,
                    'no_of_play_grounds' => $this->no_of_play_grounds,
                    'size_of_play_grounds' => $this->size_of_play_grounds,
                ]);
            }

            $this->dispatch('close-modal');
            $this->dispatch('notifySportsEquipment', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Sports Equipment quantity  Updated Successfully'
            ]);
        } catch (\Exception $e) {

            $this->dispatch('close-modal');
            $this->dispatch('notifySportsEquipment', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error updating play grounds: ' . $e->getMessage()
            ]);
        }
    }


    public function loadAllOtherSchoolTypesSportsEquipments()
    {
        $schoolId = $this->institution->id;

        // Fetch all equipment where is_pre_primary_yn is NULL (i.e., other school types)
        $equipments = SportsEquipment::with('sports_equipment_category')
            ->whereNull('is_pre_primary_yn')
            ->get();

        // Get the update records for this school, indexed by equipment_id
        $updates = SchoolSportsEquipmentUpdate::where('school_id', $schoolId)
            ->get()
            ->keyBy('equipment_id');

        // Merge the data
        $this->all_other_school_types_sports_equipments = $equipments->map(function ($equipment) use ($updates) {
            $update = $updates->get($equipment->id);

            return [
                'id' => $equipment->id,
                'name' => $equipment->name,
                'category' => optional($equipment->sports_equipment_category)->name,
                'quantity' => $update?->quantity ?? 0,
            ];
        })->toArray();
    }


    //edit all other school types sports equipments
    public function editAllOtherSchoolTypesSportsEquipment($id)
    {
        $equipment = collect($this->all_other_school_types_sports_equipments)
            ->firstWhere('id', $id);

        $this->equipments[$id] = $equipment['quantity'] ?? 0;

        $this->editingEquipmentId = $id;
    }




    //Sync the survey sections and items to the school survey.
    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }


    public function render()
    {
        return view('emisreturns::livewire.sections.pesports.sports-equipment-section');
    }
}
