<?php

namespace Modules\EmisReturns\Livewire\Sections\watersanitation;

use Modules\Core\Models\Institutions\SchoolGarbageDisposalMethodUpdate;
use Modules\Core\Models\Settings\GarbageDisposalMethod;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

use Livewire\Component;

class GarbageDisposalSection extends Component
{  
    use InstitutionContext;
    
    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;

    public $institution;
    public $survey;
    public $term;

    public $garbage_disposal_method_names = [];
    public $garbage_disposal_method_id;
    public $latestMethodName = 'NOT SET';

    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadFacilityData();
    }

    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;

    }

    public function loadFacilityData():void
    {

    $this->garbage_disposal_method_names = GarbageDisposalMethod::pluck( 'name','id')->toArray();


    $latestUpdate = SchoolGarbageDisposalMethodUpdate::where('school_id', $this->institution->id)
        ->orderByDesc('garbage_disposal_method_id')
        ->first();

    $this->latestMethodName = $latestUpdate?->garbage?->name ?? 'NOT SET';
    $this->garbage_disposal_method_id = $latestUpdate?->garbage_disposal_method_id ?? null;
    
    }

    public function getLatestMethodNameProperty()
    {
        return SchoolGarbageDisposalMethodUpdate::where('school_id', $this->institution->id)
            ->orderByDesc('garbage_disposal_method_id')
            ->first()?->garbage?->name ?? 'NOT SET';
    }

    public function updateGarbageDisposalMethod():void
    {
        AdminLogActivity::addToLog('Garbage Disposal Method', '1', 'Update', 'User Updated School Garbage Disposal Method', 'school');
    
        try{

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $this->institution->id,
            'survey_id' => $this->survey->id,
        ], [
            'current_section_id' => $this->survey->sections->first()->id ?? null,
            'last_saved' => now(),
        ]);

        $this->syncSurveySections($schoolSurvey);
    
        SchoolGarbageDisposalMethodUpdate::updateOrCreate([
            'school_id' => $this->institution->id,
            'academic_year_id' => $this->survey->academic_year_id,
            'teaching_period_id' => $this->termId,
            'school_survey_id' => $schoolSurvey->id,
        ], [
            'garbage_disposal_method_id' => $this->garbage_disposal_method_id,
        ]);

        $this->dispatch('notifyGarbage', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Garbage Disposal method Updated Successfully.'
        ]);
    
        $this->dispatch('close-modal');
        $this->loadFacilityData();

        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyGarbage', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error Updating Garbage Disposal method: ' . $e->getMessage()
            ]);
        }
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
    $schoolSurvey->sections()->sync($this->survey->sections);
    $schoolSurvey->section_items()->sync($this->survey->section_items);
    $firstSectionId = $this->survey->sections->first()->id ?? null;

    if ($firstSectionId) {
        $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
            'is_complete_yn' => true,
        ]);
    }
    }

    public function render()
    {
        return view('emisreturns::livewire.sections.watersanitation.garbage-disposal-section');
    }
}
