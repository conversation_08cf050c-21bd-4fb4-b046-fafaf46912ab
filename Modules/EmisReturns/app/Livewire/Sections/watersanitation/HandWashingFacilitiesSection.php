<?php

namespace Modules\EmisReturns\Livewire\Sections\watersanitation;

use Livewire\Component;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\Core\Models\Institutions\SchoolHandWashingFacilityUpdate;
use Modules\Core\Models\Settings\HandwashingFacilityType;

class HandWashingFacilitiesSection extends Component
{
    use InstitutionContext;

    public $facilityNames = [];
    public $selectedFacilityName = null;
    public $presentInSchool = null;

    public $institution;
    public $survey;
    public $term;
    public $facilityStates = [];
    public $hand_washing_method_names = [];
    public $hand_washing_method_id;
    public $latestMethodName = 'NOT SET';

    public $institutionId;
    public $surveyId;
    public $academicYearId;
    public $termId;



    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadFacilityData();
    }

    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->institutionId = $this->institution->id;
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }


    public function loadFacilityData(): void
    {
        $this->facilityNames = HandwashingFacilityType::where('is_for_hands_yn', false)
            ->pluck('name', 'id')
            ->toArray();


        $this->facilityStates = SchoolHandWashingFacilityUpdate::where('school_id', $this->institutionId)
            ->whereNotNull('present_in_school')
            ->pluck('present_in_school', 'hand_washing_facility_id')
            ->toArray();

        $this->hand_washing_method_names = HandwashingFacilityType::where('is_for_hands_yn', true)

            ->pluck('name', 'id')
            ->toArray();

        $latestUpdate = SchoolHandWashingFacilityUpdate::where('school_id', $this->institution->id)
            ->orderByDesc('id')
            ->first();

        $this->latestMethodName = $latestUpdate?->method?->name ?? 'NOT SET';
        $this->hand_washing_method_id = $latestUpdate?->hand_washing_method_id ?? null;
    }

    public function getLatestMethodNameProperty()
    {
        return SchoolHandWashingFacilityUpdate::where('school_id', $this->institution->id)
            ->orderByDesc('id')
            ->first()?->method?->name ?? 'NOT SET';
    }

    public function selectFacility($name): void
    {
        $this->selectedFacilityName = $name;

        $facilityId = array_search($name, $this->facilityNames);

        if ($facilityId !== false && isset($this->facilityStates[$facilityId])) {
            $this->presentInSchool = (int) $this->facilityStates[$facilityId];
        } else {
            $this->presentInSchool = null;
        }
    }


    public function updateHandWashingMethod(): void
    {
        AdminLogActivity::addToLog('Sanitation', '1', 'Update', 'User Updated Hand Washing Method', 'school');
      
        try{

        $term = get_active_teaching_period();
        $survey = $this->survey;

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $this->institution->id,
            'survey_id' => $survey->id,
        ], [
            'current_section_id' => $survey->sections->first()?->id,
            'last_saved' => now(),
        ]);

        $this->syncSurveySections($schoolSurvey);

        $handWashingFacilityTypes = HandwashingFacilityType::where('is_for_hands_yn', false)->get();

        if (
            $this->institution->sanitation()
            ->where('academic_year_id', $survey->academic_year_id)
            ->count() === 0
        ) {
            $handWashingFacilityTypes->each(function ($type) use ($schoolSurvey, $survey, $term) {
                $this->institution->sanitation()->create([
                    'hand_washing_method_id' => $this->hand_washing_method_id,
                    'hand_washing_facility_id' => $type->id,
                    'academic_year_id' => $survey->academic_year_id,
                    'teaching_period_id' => $term->teaching_period_id,
                    'school_survey_id' => $schoolSurvey->id,
                ]);
            });
        }

        SchoolHandWashingFacilityUpdate::where('school_id', $this->institution->id)
            ->update(['hand_washing_method_id' => $this->hand_washing_method_id]);

        $this->dispatch('notifyHandWashing', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Hand Washing Method Updated Successfully.',
        ]);

        $this->dispatch('close-modal');
        $this->loadFacilityData();

        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyHandWashing', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error Updating Hand Washing Method: ' . $e->getMessage(),
            ]);
        }
    }


    public function updateFacility(): void
    {

        AdminLogActivity::addToLog('Sanitation', '1', 'Update', 'User Updated School Hand Washing Facilities', 'school');

        try {

        $facility =  HandwashingFacilityType::where('name', $this->selectedFacilityName)->firstOrFail();

        $schoolSurvey = SchoolSurvey::firstOrCreate([
            'school_id' => $this->institution->id,
            'survey_id' => $this->survey->id,
        ], [
            'current_section_id' => $this->survey->sections->first()->id ?? null,
            'last_saved' => now(),
        ]);

        $this->syncSurveySections($schoolSurvey);

        if (!is_null($this->presentInSchool)) {
            SchoolHandWashingFacilityUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'hand_washing_facility_id' => $facility->id,
            ], [
                'present_in_school' => $this->presentInSchool,
            ]);
        }

        $this->dispatch('notifyHandWashing', [
            'status' => 'success',
            'title' => 'Success:',
            'message' => 'Hand Washing Facility Updated Successfully.'
        ]);

        $this->dispatch('close-modal');

        $this->loadFacilityData();

        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyHandWashing', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error Updating Hand Washing Facility: ' . $e->getMessage(),
            ]);
        }
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }


    public function render()
    {
        return view('emisreturns::livewire.sections.watersanitation.hand-washing-facilities-section');
    }
}
