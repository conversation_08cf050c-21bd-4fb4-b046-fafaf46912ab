<?php

namespace Modules\EmisReturns\Livewire\Sections\watersanitation;

use Modules\Core\Models\Institutions\SchoolSanitationWaterUpdate;
use Modules\Core\Models\Settings\WaterSourceDistanceRange;
use Modules\Core\Models\Settings\WaterSourceType;
use Modules\Core\Helpers\AdminLogActivity;
use Modules\Core\Models\Institutions\SchoolSurvey;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;

use Livewire\Component;

class WaterSourcesSection extends Component
{
    use InstitutionContext;

    public $surveyId;
    public $academicYearId;
    public $termId;

    public $institution;
    public $survey;
    public $term;

    public $drinking_water_sources_names = [];
    public $drinking_water_source_id;

    public $water_source_other_purposes_names = [];
    public $water_source_other_purposes_id;

    public $drinking_water_source_distance_names = [];
    public $drinking_water_source_distance_id;

    public $water_source_other_purposes_distance_names = [];
    public $water_source_other_purposes_distance_id;

    public $latestWaterSources;
    public $latestWaterSourcesDistance;
    public $latestWaterSourcesOtherPurpose;
    public $latestWaterSourcesOtherPurposeDistance;


    public function mount($survey)
    {
        $this->initializeContext($survey);
        $this->loadWaterSourceData();
    }

    private function initializeContext($survey): void
    {
        $this->institution = $this->getInstitution();
        $this->survey = $survey;
        $this->surveyId = $this->survey?->id;
        $this->academicYearId = $this->survey?->academic_year_id;
        $this->termId = get_active_teaching_period()->teaching_period_id;
    }

    public function loadWaterSourceData(): void
    {

        $this->drinking_water_sources_names = WaterSourceType::pluck('name', 'id')->toArray();

        $this->water_source_other_purposes_names = WaterSourceType::pluck('name', 'id')->toArray();

        $this->drinking_water_source_distance_names = WaterSourceDistanceRange::pluck('name', 'id')->toArray();

        $this->water_source_other_purposes_distance_names = WaterSourceDistanceRange::pluck('name', 'id')->toArray();

        $latestUpdate = SchoolSanitationWaterUpdate::where('school_id', $this->institution->id)
            ->orderByDesc('id')
            ->first();

        $this->latestWaterSources = $latestUpdate?->drinking_water_source?->name ?? 'NOT SET';
        $this->drinking_water_source_id = $latestUpdate?->drinking_water_source_id ?? null;

        $this->latestWaterSourcesDistance = $latestUpdate?->drinking_water_source_distance?->name ?? 'NOT SET';
        $this->drinking_water_source_distance_id = $latestUpdate?->drinking_water_source_distance_id ?? null;


        $this->latestWaterSourcesOtherPurpose = $latestUpdate?->water_source_other_purposes?->name ?? 'NOT SET';
        $this->water_source_other_purposes_id = $latestUpdate?->water_source_other_purposes_id ?? null;

        $this->latestWaterSourcesOtherPurposeDistance = $latestUpdate?->water_source_other_purposes_distance?->name ?? 'NOT SET';
        $this->water_source_other_purposes_distance_id = $latestUpdate?->water_source_other_purposes_distance_id ?? null;
    }


    protected function fetchLatestWaterSourcesDataBy(string $relationName): string
    {
        $latestUpdate = SchoolSanitationWaterUpdate::where('school_id', $this->institution->id)
            ->orderByDesc('id')
            ->first();

        return $latestUpdate?->$relationName?->name ?? 'NOT SET';
    }


    public function getLatestWaterSourceDistanceProperty()
    {
        return $this->fetchLatestWaterSourcesDataBy(
            'drinking_water_source_distance'
        );
    }

    public function getLatestWaterSourceOtherPurposeDistanceProperty()
    {
        return $this->fetchLatestWaterSourcesDataBy(
            'water_source_other_purposes_distance'
        );
    }

    public function getLatestWaterSourceOtherPurposeProperty()
    {
        return $this->fetchLatestWaterSourcesDataBy(
            'water_source_other_purposes'
        );
    }

    public function getLatestWaterSourceProperty()
    {
        return $this->fetchLatestWaterSourcesDataBy(
            'drinking_water_source'
        );
    }



    public function updateWaterSourcesData(): void
    {
        AdminLogActivity::addToLog('Water Sources', '1', 'Update', 'User Updated Water Sources', 'school');

        try {

            $schoolSurvey = SchoolSurvey::firstOrCreate([
                'school_id' => $this->institution->id,
                'survey_id' => $this->survey->id,
            ], [
                'current_section_id' => $this->survey->sections->first()->id ?? null,
                'last_saved' => now(),
            ]);

            $this->syncSurveySections($schoolSurvey);

            SchoolSanitationWaterUpdate::updateOrCreate([
                'school_id' => $this->institution->id,
                'academic_year_id' => $this->survey->academic_year_id,
                'teaching_period_id' => $this->termId,
                'school_survey_id' => $schoolSurvey->id,
                'drinking_water_source_id' => $this->drinking_water_source_id,
                'water_source_other_purposes_id' => $this->water_source_other_purposes_id,
                'drinking_water_source_distance_id' => $this->drinking_water_source_distance_id,
                'water_source_other_purposes_distance_id' => $this->water_source_other_purposes_distance_id,
            ]);


            $this->dispatch('notifyWaterSources', [
                'status' => 'success',
                'title' => 'Success:',
                'message' => 'Sources Of Water Updated Successfully.'
            ]);

            $this->dispatch('close-modal');
            $this->loadWaterSourceData();
        } catch (\Exception $e) {
            $this->dispatch('close-modal');
            $this->dispatch('notifyWaterSources', [
                'status' => 'error',
                'title' => 'Error:',
                'message' => 'Error Updating Water Sources: ' . $e->getMessage(),
            ]);
        }
    }

    private function syncSurveySections(SchoolSurvey $schoolSurvey): void
    {
        $schoolSurvey->sections()->sync($this->survey->sections);
        $schoolSurvey->section_items()->sync($this->survey->section_items);
        $firstSectionId = $this->survey->sections->first()->id ?? null;

        if ($firstSectionId) {
            $schoolSurvey->sections()->updateExistingPivot($firstSectionId, [
                'is_complete_yn' => true,
            ]);
        }
    }


    public function render()
    {
        return view('emisreturns::livewire.sections.watersanitation.water-sources-section');
    }
}
