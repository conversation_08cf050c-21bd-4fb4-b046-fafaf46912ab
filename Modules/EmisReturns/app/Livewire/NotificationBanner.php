<?php

namespace Modules\EmisReturns\Livewire;

use Livewire\Component;

/**
 * Livewire component for displaying notification banners.
 * Handles displaying, adding, and clearing notifications for the user.
 */
class NotificationBanner extends Component
{
    /** @var array List of notifications to display */
    public $notifications = [];

    public $eventName = 'notify';

    /**
     * Add a new notification to the banner.
     *
     * @param array $notification
     * @return void
     */
    public function notify($notification)
    {
        $this->notifications[] = $notification;
    }

    /**
     * Clear a notification at the given index.
     *
     * @param int $index
     * @return void
     */
    public function clear($index)
    {
        unset($this->notifications[$index]);
        $this->notifications = array_values($this->notifications);
    }

    /**
     * Render the notification banner view.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('emisreturns::livewire.notification-banner');
    }

    protected function getListeners()
    {
        return [
            "{$this->eventName}" => 'notify',
        ];
    }
}
