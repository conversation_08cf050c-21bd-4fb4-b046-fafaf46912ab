<?php

namespace Modules\EmisReturns\Livewire;

use Illuminate\Contracts\Support\Arrayable;
use Livewire\Component;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use JsonSerializable;
use Modules\Core\Models\Settings\Survey;
use Modules\EmisReturns\Livewire\Sections\GovernanceSection;
use Modules\EmisReturns\Livewire\Sections\SchoolParticularsSection;
use Modules\EmisReturns\Livewire\Sections\LearnersSection;
use Modules\EmisReturns\Livewire\Sections\TeachingStaffSection;
use Modules\EmisReturns\Livewire\Sections\OtherFacilitesSection;
use Modules\EmisReturns\Livewire\Sections\WaterSanitationSection;
use Modules\EmisReturns\Livewire\Sections\SchoolCurricularActivitiesSection;
use Modules\EmisReturns\Livewire\Sections\InfrastructureSection;
use Modules\EmisReturns\Livewire\Traits\InstitutionContext;
use Modules\EmisReturns\Livewire\Sections\IctSection;
use Modules\EmisReturns\Livewire\Sections\SourcesOfEnergy;
use Modules\EmisReturns\Livewire\Sections\PeSportsSection;
use Modules\EmisReturns\Livewire\Sections\InstructionalMaterialsSection;
use Modules\EmisReturns\Livewire\Sections\HealthAndMeals\HealthAndMealsSection;
use Modules\EmisReturns\Livewire\Sections\CurriculumSection;
use Modules\EmisReturns\Livewire\Sections\FinanceSection;



/**
 * Livewire component for managing the survey container and section navigation.
 * Handles loading the correct survey for the user's institution context and
 * switching between survey sections.
 */
class SurveyContainer extends Component
{
    use InstitutionContext;

    /** @var string The current school type name (e.g., 'primary') */
    public $schoolType = '';

    /** @var string The logical name of the current section */
    public $currentSection;// = 'school';

    protected $listeners = ['navigate-to-section' => 'changeSectionById'];

    /** @var bool Whether the side menu is open */
    public $sideMenu = false;

    /** @var int|string|null The current survey ID */
    public $surveyId;

    /** @var Survey|null The current Survey model instance */
    public $survey;

    /** @var int|string|null The current survey ID (for compatibility) */
    public $survey_id;

        /** @var int|string|null The current survey ID (for compatibility) */
    public $section_id;

    /**
     * List of survey sections.
     *
     * @var array
     */

    public $sections;

    public function __construct($id = null)
    {
        $rawSections = [
            ['id' => 1, 'name' => 'School', 'title'=> 'School Particulars', 'component'=> SchoolParticularsSection::class],
            ['id' => 2, 'name' => 'Learners', 'component'=> LearnersSection::class],
            ['id' => 3, 'name' => 'Teaching Staff', 'component'=> TeachingStaffSection::class],
            ['id' => 4, 'name' => 'Support Staff'], //'component'=> SupportStaffSection::class],
            ['id' => 5, 'name' => 'Infrastructure','component'=> InfrastructureSection::class],
            ['id' => 6, 'name' => 'Other Facilities', 'component'=> OtherFacilitesSection::class],
            ['id' => 7, 'name' => 'Water & Sanitation','component'=> WaterSanitationSection::class],
            ['id' => 8, 'name' => 'Sources Of Energy', 'component'=> SourcesOfEnergy::class],
            ['id' => 9, 'name' => 'ICT', 'component'=>IctSection::class],
            ['id' => 10, 'name' => 'Instructional Materials','component'=>InstructionalMaterialsSection::class],
            ['id' => 11, 'name' => 'Health & Meals','component' => HealthAndMealsSection::class],
            ['id' => 12, 'name' => 'P.E. & Sports', 'component'=> PeSportsSection::class],
            ['id' => 13, 'name' => 'Governance', 'component'=> GovernanceSection::class],
            ['id' => 14, 'name' => 'GPS Location'],
            ['id' => 15, 'name' => 'Finance', 'component'=> FinanceSection::class],
            ['id' => 16, 'name' => 'Extra Curricular Activities', 'component'=> SchoolCurricularActivitiesSection::class],
            ['id' => 17, 'name' => 'Curriculum','description'=>'A summary of all curriculums in your school', 'title'=> 'Cirriculum Information', 'component'=> CurriculumSection::class],

        ];
        $this->sections = $rawSections;
        // $this->sections = array_map(fn($data) => new Section($data), $rawSections);
       //parent::__construct($id);
       // Initialize the current section to the first section by default
    //    dd($this->sections[0]->toArray());
       $this->currentSection =  $this->sections[0];//->toArray();
    }
    /**
     * Mount the component and load the survey context for the user's institution.
     *
     * @param int|string $survey_id
     * @return void
     */
    public function mount($survey_id, $section_id=null)
    {
        $this->survey_id = $survey_id;
        $this->section_id = $section_id;
        $institution = $this->getInstitution();

        $schoolTypeId = $institution?->school_type_id ?? null;
        $this->schoolType = $institution?->school_type?->name ?? 'primary';

        // Load the survey for the institution's school type and open status
        $query = Survey::where('id', $survey_id)
            ->where(function ($query) {
                $query->where('end_date', '>=', now())
                      ->orWhere('extension_date', '>=', now());
            })
            ->with(['school_type', 'academic_year.teaching_periods', 'sections', 'section_items']);

        if ($institution) {
            $query->where('school_type_id', $schoolTypeId);
        }
        foreach ($this->sections as $item) {
            if($item['id'] == intval($section_id)){
                // dd($item);
                // $this->selectedSection = $item['logical'];
                $this->currentSection = $item;//['logical'];
                // $this->changeSection($item['logical']);
                break;
            }
        }
        // $sectionCollection = collect($this->sections);
        // $currentSection0 = ($sectionCollection->first(function ($value, int $key) use ($section_id){
        //     // dd($value['id'] == $section_id);
        //     // dd($value['id'] == $section_id);
        //     // dd($value['id'], intval($section_id));
        //     return $value['id'] == intval($section_id);
        // })) ?? $sectionCollection[0];
        // // $currentSection =  $sectionCollection[0];
        // // dd($currentSection['logical']);
        // // if($currentSection){
        // $this->currentSection =  $currentSection0['logical'];///xxxx
        // dd($currentSection0['logical'] == 'learners');
        // $this->currentSection = 'learners';
        // }
        // $this->currentSection =  "learners";
        $this->survey = $query->first();

        if (!$this->survey) {
            abort(404, 'Survey not found');
        }
    }
    /**
     * Change the current section being displayed.
     *
     * @param string $sectionId
     * @return void
     */
    public function changeSectionById($sectionId)
    {
        foreach ($this->sections as $item) {
            if ((int) $item['id'] === (int) $sectionId) {
                $this->currentSection = $item;//['logical'];
                break;
            }
        }
    }
    /**
     * Change the current section being displayed.
     *
     * @param string $sectionLogical
     * @return void
     */
    public function changeSection($sectionLogical)
    {
        // dd($sectionLogical);
        $this->currentSection = $sectionLogical;
    }

    /**
     * Toggle the visibility of the side menu.
     *
     * @return void
     */
    public function toggleSideMenu()
    {
        $this->sideMenu = !$this->sideMenu;
    }

    /**
     * Render the Livewire component view.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        // dd($this->currentSection);
        // dd( $this->getSectionComponent());
        return view('emisreturns::livewire.survey-container', [
            // 'currentSectionComponent' => $this->getSectionComponent(),
            'survey' => $this->survey,
            'sections' => $this->sections,
            'currentSection' => $this->currentSection,
            'sideMenu' => $this->sideMenu,
            'schoolType' => $this->schoolType,
            'title' => $this->currentSection['title'] ?? Str::slug($this->currentSection['name']),
            'survey_id' => $this->survey_id,
            'institution' => $this->survey->institution,
        ])->layout('core::institution.layouts.design');
    }

    /**
     * Get the Livewire component class for the current section.
     *
     * @return string|null
     */
    private function getSectionComponent()
    {
        // $sections = [
        //     'school' => SchoolParticularsSection::class,
        //     'learners' => LearnersSection::class,
        //     'teaching_staff' => TeachingStaffSection::class,
        //     'support_staff' => SupportStaffSection::class,
        //     'sources_of_energy' => SourcesOfEnergy::class,
        //     'other_facilities' => OtherFacilitesSection::class,
        //     // Add other section mappings as needed
        // ];
        // dd($this->currentSection->class);
        // return SchoolParticularsSection::class;
        return $this->currentSection->component ?? null;
        // return $sections['school'] ?? null;
        // return $sections[$this->currentSection] ?? null;
        $sections = [
            'school' => SchoolParticularsSection::class,
            'learners' => LearnersSection::class,
            'teaching_staff' => TeachingStaffSection::class,
            'support_staff' => SupportStaffSection::class,
            'sources_of_energy' => SourcesOfEnergy::class,
            'other_facilities' => OtherFacilitesSection::class,
            'instructional_materials' => InstructionalMaterialsSection::class,
            'extra_curricular_activities' => SchoolCurricularActivitiesSection::class,
            'curriculum' => CurriculumSection::class,

            // Add other section mappings as needed
        ];

        return $sections[$this->currentSection] ?? null;
    }

    public function submitSurvey()
    {
        /**
         * Handle the submission of the survey.
         *
         * This method can include any necessary logic to process the survey data
         * before redirecting the user. Currently, it redirects to the dashboard
         * of the institution after submission.
         *
         * @return \Illuminate\Http\RedirectResponse
         */

        // Logic to handle survey submission can be added here if needed

        return redirect('/institution/dashboard');
    }
}
