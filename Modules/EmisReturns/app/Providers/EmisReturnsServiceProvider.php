<?php

namespace Modules\EmisReturns\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Modules\EmisReturns\Livewire\Sections\PersonPhotoUpload;
use Modules\EmisReturns\Livewire\SurveyContainer;
use Modules\EmisReturns\Livewire\Sections\SchoolParticularsSection;
use Modules\EmisReturns\Livewire\Sections\LearnersSection;
use Modules\EmisReturns\Livewire\Sections\TeachingStaffSection;
use Modules\EmisReturns\Livewire\Sections\IctSection;
use Modules\EmisReturns\Livewire\Sections\SourcesOfEnergy;
use Modules\EmisReturns\Livewire\Sections\OtherFacilitesSection;
use Modules\EmisReturns\Livewire\Sections\InstructionalMaterialsSection;
use Modules\EmisReturns\Livewire\Sections\WaterSanitationSection;
use Modules\EmisReturns\Livewire\Sections\watersanitation\HandWashingFacilitiesSection;
use Modules\EmisReturns\Livewire\Sections\SchoolCurricularActivitiesSection;
use Modules\EmisReturns\Livewire\Sections\watersanitation\WaterSourcesSection;
use Modules\EmisReturns\Livewire\Sections\watersanitation\GarbageDisposalSection;
use Modules\EmisReturns\Livewire\Sections\InstructionalMaterials\SecondarySchoolInstructionalMaterials;
use Modules\EmisReturns\Livewire\Sections\InstructionalMaterials\PrePrimarySchoolInstructionalMaterials;
use Modules\EmisReturns\Livewire\Sections\HealthAndMeals\HealthAndMealsSection;
use Modules\EmisReturns\Livewire\Sections\InfrastructureSection;
use Modules\EmisReturns\Livewire\Sections\CurriculumSection;
use Modules\EmisReturns\Livewire\Sections\PeSportsSection;
use Modules\EmisReturns\Livewire\Sections\Pesports\SportsEquipmentSection;
use Modules\EmisReturns\Livewire\NotificationBanner;
use Modules\EmisReturns\Livewire\Sections\Pesports\SportsActivitiesSection;
use Modules\EmisReturns\Livewire\Sections\Pesports\SportsFacilitiesSection;
use Modules\EmisReturns\Livewire\Sections\Governance\GovernanceMembers;
use Modules\EmisReturns\Livewire\Sections\Governance\Modals\AddMemberModal;
use Modules\EmisReturns\Livewire\Sections\Governance\Modals\UpdateSchoolGovernanceModal;
use Modules\EmisReturns\Livewire\Sections\Governance\RecentlyAddedMembers;
use Modules\EmisReturns\Livewire\Sections\GovernanceSection;
use Modules\EmisReturns\Livewire\Sections\FinanceSection;
use Nwidart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;

class EmisReturnsServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'EmisReturns';

    protected string $nameLower = 'emisreturns';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();

        // Let the RouteServiceProvider handle route loading
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));

        // Register Livewire Components
        Livewire::component('emis-returns.survey-container', SurveyContainer::class);
        Livewire::component('emis-returns.notification-banner', NotificationBanner::class);

        // Register Section Components
        Livewire::component('emis-returns.sections.school-particulars', SchoolParticularsSection::class);
        Livewire::component('emis-returns.sections.learners', LearnersSection::class);
        // Livewire::component('emis-returns.sections.teaching-staff', TeachingStaffSection::class);
        Livewire::component('emis-returns.sections.sources-of-energy',SourcesOfEnergy::class);

        Livewire::component('emis-returns.sections.teaching-staff', TeachingStaffSection::class);
        Livewire::component('emis-returns.sections.ict', IctSection::class);
        Livewire::component('emis-returns.sections.other-facilities', OtherFacilitesSection::class);
        Livewire::component('emis-returns.sections.water-sanitation', WaterSanitationSection::class);
        Livewire::component('emis-returns.sections.water-sanitation.hand-washing-facilities-section', HandWashingFacilitiesSection::class);
        Livewire::component('emis-returns.sections.sources-of-energy', SourcesOfEnergy::class);
        Livewire::component('emis-returns.sections.school-curricular-activities', SchoolCurricularActivitiesSection::class);
        Livewire::component('emis-returns.sections.water-sanitation.water-sources-section',WaterSourcesSection::class);
        Livewire::component('emis-returns.sections.water-sanitation.garbage-disposal-section', GarbageDisposalSection::class);
        Livewire::component('emis-returns.sections.instructional-materials-section', InstructionalMaterialsSection::class);
        Livewire::component('emis-returns.sections.instructional-materials.secondary-school-instructional-materials', SecondarySchoolInstructionalMaterials::class);
        Livewire::component('emis-returns.sections.instructional-materials.pre-primary-school-instructional-materials', PrePrimarySchoolInstructionalMaterials::class);
        Livewire::component('emis-returns.sections.infrastructure-section', InfrastructureSection::class);
        Livewire::component('emis-returns.sections.health-and-meals.health-and-meals-section', HealthAndMealsSection::class);
        Livewire::component('emis-returns.sections.pe-sports', PeSportsSection::class);
        Livewire::component('emis-returns.sections.pe-sports.sports-equipment-section', SportsEquipmentSection::class);
        Livewire::component('emis-returns.sections.pe-sports.sports-facilities-section', SportsFacilitiesSection::class);
        Livewire::component('emis-returns.sections.pe-sports.sports-activities-section', SportsActivitiesSection::class);
        Livewire::component('emis-returns.sections.curriculum', CurriculumSection::class);



        Livewire::component('emis-returns.sections.governance', GovernanceSection::class);
        Livewire::component('emis-returns.sections.governance.modals.update-school-governance-modal', UpdateSchoolGovernanceModal::class);
        Livewire::component('emis-returns.sections.governance.governance-members', GovernanceMembers::class);
        Livewire::component('emis-returns.sections.governance.recently-added-members', RecentlyAddedMembers::class);
        Livewire::component('emis-returns.sections.governance.modals.add-member-modal', AddMemberModal::class);
        Livewire::component('emis-returns.sections.finance', FinanceSection::class);
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/'.$this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $configPath = module_path($this->name, config('modules.paths.generator.config.path'));

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $config = str_replace($configPath.DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $config_key = str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $config);
                    $segments = explode('.', $this->nameLower.'.'.$config_key);

                    // Remove duplicated adjacent segments
                    $normalized = [];
                    foreach ($segments as $segment) {
                        if (end($normalized) !== $segment) {
                            $normalized[] = $segment;
                        }
                    }

                    $key = ($config === 'config.php') ? $this->nameLower : implode('.', $normalized);

                    $this->publishes([$file->getPathname() => config_path($config)], 'config');
                    $this->merge_config_from($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Merge config from the given path recursively.
     */
    protected function merge_config_from(string $path, string $key): void
    {
        $existing = config($key, []);
        $module_config = require $path;

        config([$key => array_replace_recursive($existing, $module_config)]);
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/'.$this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        Blade::componentNamespace(config('modules.namespace').'\\' . $this->name . '\\View\\Components', $this->nameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->nameLower)) {
                $paths[] = $path.'/modules/'.$this->nameLower;
            }
        }

        return $paths;
    }
}
