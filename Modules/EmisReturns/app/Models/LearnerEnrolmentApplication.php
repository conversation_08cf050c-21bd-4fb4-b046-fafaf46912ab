<?php

namespace Modules\EmisReturns\Models;

use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Modules\Core\Database\Factories\LearnerEnrolmentApplicationFactory;
use Modules\Core\Models\Institutions\InstitutionExaminedCourse;
use Modules\Core\Models\Institutions\LearnerFamiliarLanguage;
use Modules\Core\Models\Institutions\School;
use Modules\Core\Models\Institutions\SchoolCourse;
use Modules\Core\Models\Person;
use Modules\Core\Models\Settings\AdminUnits\Country;
use Modules\Core\Models\Settings\AdminUnits\District;
use Modules\Core\Models\Settings\FamiliarLanguage;
use Modules\Core\Models\Settings\PostPrimaryInstitutionCourse;
use Modules\Core\Models\Settings\SchoolEducationGrade;
use Modules\Core\Models\Settings\SettingInternationalSchoolCalender;
use Modules\Core\Models\Settings\SettingInterSchCurriculum;
use Modules\Core\Models\Settings\SettingInterSchEducationGrade;
use Modules\Core\Models\Settings\SettingInterSchEducationLevel;
use Modules\Core\Models\User;

class LearnerEnrolmentApplication extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $table = 'learner_enrolment_applications';
    protected $appends = ['full_name','parent_full_name','date_approved_formatted'];

    protected function casts(): array
    {
        return [
            'date_created' => 'datetime',
            'date_approved' => 'datetime',
        ];
    }

    const CREATED_AT = 'date_created';
    const UPDATED_AT = 'date_updated';

    protected static function newFactory(): LearnerEnrolmentApplicationFactory
    {
        return LearnerEnrolmentApplicationFactory::new();
    }

    public function getFullNameAttribute(): string
    {
        $name = $this->first_name.' '.$this->surname;
        if (! is_null($this->other_names)) {
            $name .= ' '.$this->other_names;
        }

        return $name;
    }

    public function getParentFullNameAttribute(): string
    {
        $name = $this->parent_first_name.' '.$this->parent_surname;
        if (! is_null($this->parent_other_names)) {
            $name .= ' '.$this->parent_other_names;
        }

        return $name;
    }
    /**
     * Mutator for birth_date and parent_birth_date attributes.
     * Accepts multiple date formats: Y-m-d, j F, Y, d/m/Y, and falls back to Carbon::parse.
     */
    public function setBirthDateAttribute($birth_date): void
    {
        $this->attributes['birth_date'] = $this->parseFlexibleDate($birth_date);
    }

    public function setParentBirthDateAttribute($parent_birth_date): void
    {
        $this->attributes['parent_birth_date'] = $this->parseFlexibleDate($parent_birth_date);
    }

    public function getDateApprovedFormattedAttribute()
    {
        if (!$this->date_approved) {
            return null;
        }
        return $this->date_approved->timezone(config('app.timezone'))->format('d M, Y');
    }

    /**
     * Helper to parse a date string in multiple formats, returns Carbon instance at start of day.
     */
    protected function parseFlexibleDate($date)
    {
        try {
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                return Carbon::createFromFormat('Y-m-d', $date)->startOfDay();
            }
            return Carbon::createFromFormat('j F, Y', $date)->startOfDay();
        } catch (\Exception $e) {
            try {
                return Carbon::createFromFormat('d/m/Y', $date)->startOfDay();
            } catch (\Exception $e2) {
                return Carbon::parse($date)->startOfDay();
            }
        }
    }

    public function scopeDraft(Builder $query): Builder
    {
        return $query->where('is_draft_yn', true);
    }
    public function scopePending(Builder $query): Builder
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('approval_status', 'rejected');
    }
    public function scopeOfLocalGovernment(Builder $query, mixed $localGovernmentId): Builder
    {
        return $query->whereRelation('school','local_government_id', $localGovernmentId);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class, 'school_id');
    }

    public function education_grade(): BelongsTo
    {
        return $this->belongsTo(SchoolEducationGrade::class, 'education_grade_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function parent_country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'parent_country_id');
    }

    public function district_of_birth(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_of_birth_id');
    }

    public function international_calendar(): BelongsTo
    {
        return $this->belongsTo(SettingInternationalSchoolCalender::class, 'inter_sch_calendar_id');
    }
    public function international_curriculum(): BelongsTo
    {
        return $this->belongsTo(SettingInterSchCurriculum::class, 'inter_sch_curriculum_id');
    }

    public function international_education_grade(): BelongsTo
    {
        return $this->belongsTo(SettingInterSchEducationGrade::class, 'inter_sch_education_grade_id');
    }

    public function international_education_level(): BelongsTo
    {
        return $this->belongsTo(SettingInterSchEducationLevel::class, 'inter_sch_education_level_id');
    }

    public function post_primary_institution_course(): BelongsTo
    {
        return $this->belongsTo(PostPrimaryInstitutionCourse::class, 'post_primary_institution_course_id');
    }

    public function institution_examined_course(): BelongsTo
    {
        return $this->belongsTo(InstitutionExaminedCourse::class, 'institution_examined_course_id');
    }

    public function familiar_language(): BelongsTo
    {
        return $this->belongsTo(FamiliarLanguage::class, 'familiar_language_id');
    }

    /**
     * Get the person's details for the district approver.
     */
    public function deo(): HasOneThrough
    {
        return $this->hasOneThrough(
            User::class,
            School::class,
            'id', // Foreign key on Schools table
            'upper_local_government_id', // Foreign key on Users table
            'school_id', // Local key on LearnerEnrolmentApplication
            'local_government_id' // Local key on Schools table
        )
            ->where('is_active_yn', true)
            ->whereHas('roles', function ($query) {
                $query->where('name', 'district-education-officer');
            });
    }

    /**
     * Get the user account used to approve or reject at ministry level.
     */
    public function user_account(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the person details for the MoES Staff approver.
     */
    public function moes_staff(): HasOneThrough
    {
        return $this->hasOneThrough(
            Person::class,
            User::class,
            'id',
            'email',
            'approved_by',
            'email'
        );
    }

}
