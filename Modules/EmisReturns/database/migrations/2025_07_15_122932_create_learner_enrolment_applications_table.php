<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learner_enrolment_applications', function (Blueprint $table) {
            $table->id()->comment('The unique ID generated automatically.');
            $table->string('application_number')->comment('The application number');
            $table->string('nin')->nullable()->comment('The NIN of the learner');
            $table->string('first_name')->comment('The first name of the learner');
            $table->string('surname')->comment('The surname of the learner');
            $table->string('other_names')->nullable()->comment('The other names of the learner');
            $table->string('student_pass')->nullable()->comment('The student pass if foreigner');
            $table->string('student_refugee_number')->nullable()->comment('The refugee number if refugee learner');
            $table->date('birth_date')->comment('The learner’s date of birth');
            $table->string('photo')->nullable()->comment('The learner’s photo');
            $table->string('gender')->comment('The gender of the learner, M for male & F for female');
            $table->foreignId('country_id')->comment('The country of origin of the learner')->constrained('admin_unit_countries');
            $table->foreignId('district_of_birth_id')->nullable()->comment('The learner’s district of birth')->constrained('admin_unit_districts');
            $table->string('parent_nin')->nullable()->comment('The parent’s NIN');
            $table->foreignId('parent_country_id')->comment('Parent’s country of origin')->constrained('admin_unit_countries');
            $table->string('parent_passport')->nullable()->comment('The parent’s passport');
            $table->string('parent_refugee_number')->nullable()->comment('The parent’s refugee number');
            $table->string('parent_first_name')->comment('The parent’s first name');
            $table->string('parent_surname')->comment('The parent’s surname');
            $table->string('parent_other_names')->nullable()->comment('The parent’s other names');
            $table->date('parent_birth_date')->comment('The parent’s date of birth');
            $table->string('parent_gender')->comment('The parent’s gender,M for male & F for female');
            $table->string('parent_relationship')->comment('The parent’s relationship');
            $table->string('parent_phone_1')->comment('The parent’s phone number');
            $table->string('parent_phone_2')->nullable()->comment('The parent’s secondary phone number');
            $table->string('parent_email')->nullable()->comment('The parent’s email');
            $table->string('parent_photo')->nullable()->comment('The parent’s photo');
            $table->foreignId('education_grade_id')->nullable()->comment('The learner’s class/grade')->constrained('setting_education_grades');
            $table->foreignId('school_id')->comment('The learner’s school')->constrained('schools');
            $table->foreignId('sponsor_id')->nullable()->comment('The learner’s sponsor e.g., Private, Government')->constrained('setting_learner_sponsors');
            $table->string('index_number')->nullable()->comment('The index number of the learner');
            $table->smallInteger('exam_year')->nullable()->comment('The exam year');
            $table->string('equated_code')->nullable()->comment('The equated code');
            $table->smallInteger('equated_year')->nullable()->comment('The equated year');
            $table->string('exam_level')->nullable()->comment('The exam level e.g. PLE');
            $table->string('code_type')->nullable()->comment('The type of the code');
            $table->foreignId('post_primary_institution_course_id')->nullable()->comment('Foreign key from setting_post_primary_institution_courses')->constrained('setting_post_primary_institution_courses');
            $table->foreignId('institution_examined_course_id')->nullable()->comment('Foreign key from institution_examined_courses')->constrained('institution_examined_courses');
            $table->boolean('is_offering_examinable_course')->nullable()->comment('TRUE if offering an examinable course, FALSE if not');
            $table->boolean('is_orphan')->comment('1 if orphan, 0 if not');
            $table->string('orphan_type')->nullable()->comment('If either or both parents are deceased');
            $table->jsonb('learner_special_needs')->nullable()->comment('The special needs of a learner');
            $table->jsonb('learner_health_issues')->nullable()->comment('The health issues of a learner');
            $table->jsonb('learner_talents')->nullable()->comment('The talents of a learner');
            $table->jsonb('learner_practical_skills')->nullable()->comment('The practical skills of a learner');
            $table->foreignId('familiar_language_id')->nullable()->comment('Foreign key from setting_familiar_languages table')->constrained('setting_familiar_languages');
            $table->jsonb('principal_subjects')->nullable()->comment('The principal_subjects subjects offered');
            $table->jsonb('subsidiary_subject')->nullable()->comment('The subsidiary subject being undertaken');
            $table->boolean('in_national_curriculum')->default(true)->nullable()->comment('If the learner is in the national curriculum');
            $table->foreignId('inter_sch_calendar_id')->nullable()->comment('FK from setting_international_school_calendars')->constrained('setting_international_school_calenders');
            $table->foreignId('inter_sch_curriculum_id')->nullable()->comment('FK from setting_international_school_curriculum')->constrained('setting_international_school_curriculum');
            $table->foreignId('inter_sch_education_grade_id')->nullable()->comment('FK from setting_inter_sch_education_grades')->constrained('setting_inter_sch_education_grades');
            $table->foreignId('inter_sch_education_level_id')->nullable()->comment('FK from setting_inter_sch_education_levels')->constrained('setting_inter_sch_education_levels');
            $table->foreignId('survey_id')->comment('Foreign key from setting_surveys')->constrained('setting_surveys');
            $table->foreignId('academic_year_id')->comment('Foreign key from setting_academic_years')->constrained('setting_academic_years');
            $table->foreignId('teaching_period_id')->comment('Foreign key from setting_teaching_periods')->constrained('setting_teaching_periods');
            $table->boolean('is_draft_yn')->default(false)->comment('TRUE if the application is a draft, FALSE if the application is submitted');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->comment('The enrolment application status');
            $table->foreignId('approved_by')->nullable()->comment('FK from the users table')->constrained('users');
            $table->longText('reject_reason')->nullable()->comment('Reason for rejecting the application');
            $table->date('date_approved')->nullable()->comment('The date the application was approved');
            $table->timestamp('date_created')->comment('Date-time timestamp assigned at the creation/submission of the record');
            $table->timestamp('date_updated')->comment('Date-time timestamp assigned at the update of the record');
        });

        DB::statement("COMMENT ON TABLE learner_enrolment_applications IS 'This table stores learner enrolment applications pending approval.'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learner_enrolment_applications');
    }
};
