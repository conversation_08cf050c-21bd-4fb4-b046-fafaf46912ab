<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;
use Modules\AdminTeacher\Http\Controllers\AdminTeacherController;

// Route::middleware(['auth'])->get("adminteachers/sample", function(){
//     return auth()->user();
// });

Volt::route('/adminteachers/sample', 'sample');

// Route::middleware(['auth', 'verified'])->group(function () {
//     Route::resource('adminteachers', AdminTeacherController::class)->names('adminteacher');
// });
