<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;

class CourseCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'unesco_code' => $this->unesco_code,
            'description' => $this->description,
            'course_type' => new CourseTypeResource($this->whenLoaded('course_type')),
        ];
    }

    /**
     * Customize the outgoing response for the resource.
     */
    // public function withResponse(Request $request, Response $response): void {}
}
